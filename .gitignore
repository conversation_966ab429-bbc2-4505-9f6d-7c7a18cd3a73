/app/cache/*
/app/logs/*
/app/config/parameters.yml
/build/
/phpunit.xml
/var/*
!/var/cache
/var/cache/*
!var/cache/.gitkeep
!/var/logs
/var/logs/*
!var/logs/.gitkeep
!/var/sessions
/var/sessions/*
!var/sessions/.gitkeep
!var/SymfonyRequirements.php
/vendor/
/web/bundles/
!/src/AppBundle/Controller/Test
/src/AppBundle/Controller/Test/*
!/app/Resources/views/test
/app/Resources/views/test/*
**/Entity/*~
package.box
Vagrantfile
!/.vagrant
/.vagrant/*
!/.provision
/.provision/*
!/.databases
/.databases/*
/node_modules/*
yarn.lock
.idea