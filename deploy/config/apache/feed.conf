#Listen 80
#Listen 90
#Listen 443

NameVirtualHost *:80
<VirtualHost *:80>
#2017-01-11 online-bet.eu addition
ServerName feed.gr.test
#ServerAlias www.online-bet.eu

    SetEnvIfNoCase ^Authorization$ "(.+)" HTTP_AUTHORIZATION=$1

    DocumentRoot /var/www/feedo/web
    <Directory /var/www/feedo/web>
#        AllowOverride None
#        Order Allow,Deny
#        Allow from All
        Require all granted

        <IfModule mod_rewrite.c>
            Options -MultiViews
            RewriteEngine On
#           RewriteBase /web/
            RewriteCond %{REQUEST_FILENAME} !-f
            RewriteRule ^(.*)$ app.php [QSA,L]
        </IfModule>
    </Directory>

    # uncomment the following lines if you install assets as symlinks
    # or run into problems when compiling LESS/Sass/CoffeScript assets
    # <Directory /var/www/project>
    #     Options FollowSymlinks
    # </Directory>

    ErrorLog /var/log/apache2/feedo_error.log
    CustomLog /var/log/apache2/feedo_access.log combined

</VirtualHost>

NameVirtualHost *:443
<VirtualHost *:443>
  ServerName feed.gr.test
  DocumentRoot /var/www/feedo/web

  SSLEngine on
  SSLCertificateFile    "/etc/ssl/private/feed.gr.test.cert"
  SSLCertificateKeyFile "/etc/ssl/private/feed.gr.test.key"

    <Directory /var/www/feedo/web>
        Require all granted

        <IfModule mod_rewrite.c>
            Options -MultiViews
            RewriteEngine On
#           RewriteBase /web/
            RewriteCond %{REQUEST_FILENAME} !-f
            RewriteRule ^(.*)$ app.php [QSA,L]
        </IfModule>
    </Directory>

</VirtualHost>
