#!/bin/bash

clear

# sh colors
red='\033[1;31m'
green='\033[0;32m'
blue='\033[0;34m'
cyan='\033[0;36m'
reset='\033[0m'

# bash colors
red='\e[31m'
lred='\e[91m'
green='\e[32m'
lgreen='\e[92m'
blue='\e[34m'
cyan='\e[36m'
reset='\e[0m'

# echo -e setting up ${lgreen}sshd_config${reset}
DB_DATABASE='feedo_db'
DB_USERNAME='feedo'
DB_PASSWORD='123'
DB_ROOT_PASSWORD="123"

SFTP="<EMAIL>"
SFTP_MYSQL_DB_PATH="/feedo/databases"
_E_SFTP_MYSQL_DB_PATH='/feedo/databases/'
DATE=$(date +"%Y-%m-%d-%H-%M-%S")
FILENAME="feedo.sql.gz"

SQLFINALNAME='2020-05-20-03-33-01_feedo.sql'

### Update ubuntu
echo -e Updating ${cyan}OS${reset}
sudo apt-get update -y -qq

export DEBIAN_FRONTEND=noninteractive

### self-signed SSL
sudo DEBIAN_FRONTEND=noninteractive openssl req -new -newkey rsa:4096 -days 365 -nodes -x509 \
    -subj "/C=GR/ST=Thessaloniki/L=Thessaloniki/O=BC Greece/CN=feed.gr.test" \
    -keyout /etc/ssl/private/feed.gr.test.key  -out /etc/ssl/private/feed.gr.test.cert

sudo mkdir -p /dev/shm/feedo

#: <<'END'

HTTPDUSER=$(ps axo user,comm | grep -E '[a]pache|[h]ttpd|[_]www|[w]ww-data|[n]ginx' | grep -v root | head -1 | cut -d\  -f1)
sudo setfacl -dR -m u:"$HTTPDUSER":rwX -m u:$(whoami):rwX /dev/shm/feedo/
sudo setfacl -R -m u:"$HTTPDUSER":rwX -m u:$(whoami):rwX /dev/shm/feedo/

### Apache2
echo -e Installing ${cyan}apache${reset}
sudo DEBIAN_FRONTEND=noninteractive apt-get -y install apache2 -qq
sudo a2dissite 000-default.conf
sudo rm -rf /etc/apache2/000-default.conf
sudo rm -rf /etc/apache2/default-ssl.conf
sudo cp /var/www/feedo/deploy/config/apache/feed.conf /etc/apache2/sites-available
sudo a2enmod ssl
sudo a2enmod rewrite
sudo a2ensite feed

# setup logs/cache acl for vagrant/apache2
sudo sed -i 's/APACHE_RUN_USER=www-data/APACHE_RUN_USER=vagrant/' /etc/apache2/envvars
sudo sed -i 's/APACHE_RUN_GROUP=www-data/APACHE_RUN_GROUP=vagrant/' /etc/apache2/envvars
sudo chown -R vagrant:www-data /dev/shm/feedo
sudo service apache2 restart

### Mysql
echo -e Installing ${cyan}MySQL${reset}
sudo apt-get install mysql-server -y -qq

# Make sure that NOBODY can access the server without a password
sudo mysql -e "UPDATE mysql.user SET authentication_string = PASSWORD('$DB_ROOT_PASSWORD') WHERE User = 'root'"
# Kill the anonymous users
sudo mysql -e "DROP USER IF EXISTS ''@'localhost'"
# Because our hostname varies we'll use some Bash magic here.
sudo mysql -e "DROP USER IF EXISTS ''@'$(hostname)'"
# Disallow remote login for root
sudo mysql -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1')"
# Kill off the demo database
sudo mysql -e "DROP DATABASE IF EXISTS test"
# Make our changes take effect
sudo mysql -e "FLUSH PRIVILEGES"

sudo mysql -uroot -p$DB_ROOT_PASSWORD -e "CREATE DATABASE IF NOT EXISTS $DB_DATABASE CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci;"
sudo mysql -uroot -p$DB_ROOT_PASSWORD -e "GRANT ALL PRIVILEGES ON $DB_DATABASE.* TO '$DB_USERNAME'@'%' IDENTIFIED BY '$DB_PASSWORD';"
sudo mysql -uroot -p$DB_ROOT_PASSWORD -e "FLUSH PRIVILEGES;"

cd /var/www/feedo
sudo mysql -u$DB_USERNAME -p$DB_PASSWORD $DB_DATABASE < $SQLFINALNAME
rm -rf $SQLFINALNAME
echo -e ${cyan}mysql${reset} finished setting up

# php
echo -e Installing ${cyan}php7.4 for apache2${reset}
sudo apt-get update
sudo apt-get install software-properties-common -y -qq
sudo add-apt-repository ppa:ondrej/apache2 -y
sudo add-apt-repository ppa:ondrej/php -y
sudo apt-get update -y -qq
sudo apt-get install php7.4 -y -qq
sudo apt-get install php7.4-xml php7.4-mysql php7.4-apcu -y -qq

# composer
sudo apt-get install curl php7.4-cli php7.4-mbstring git unzip -y -qq
sudo service apache2 restart
cd ~/
php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
php -r "if (hash_file('sha384', 'composer-setup.php') === 'e0012edf3e80b6978849f5eff0d4b4e4c79ff1609dd1e613307e16318854d24ae64f26d17af3ef0bf7cfb710ca74755a') { echo 'Installer verified'; } else { echo 'Installer corrupt'; unlink('composer-setup.php'); } echo PHP_EOL;"
sudo php composer-setup.php --install-dir=/usr/local/bin --filename=composer
cd /var/www/feedo
rm -r composer.lock
composer install

# yarn for webpack encore
curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | sudo apt-key add -
echo "deb https://dl.yarnpkg.com/debian/ stable main" | sudo tee /etc/apt/sources.list.d/yarn.list
sudo apt-get update -y -qq
sudo apt-get install yarn -y -qq
cd /var/www/feedo
yarn install

# END