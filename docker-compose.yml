services:
  php:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: symfony_app
    working_dir: /var/www/symfony
    volumes:
      - ./:/var/www/symfony:cached
      - ./docker/php/custom.ini:/usr/local/etc/php/conf.d/custom.ini:ro
      - php_sessions:/var/lib/php/sessions
    networks:
      - symfony_network
    depends_on:
      - mysql
    environment:
      APP_ENV: dev
      APP_DEBUG: '1'
      DATABASE_URL: mysql://root:root_password@mysql:3306/feedo_db
      PHP_INI_DIR: /usr/local/etc/php
    user: www-data

  # Nginx Service
  nginx:
    image: nginx:alpine
    container_name: symfony_nginx
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/symfony
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf:ro
    networks:
      - symfony_network
    depends_on:
      - php

  # MySQL Database
  mysql:
    image: mysql:5.7
    container_name: symfony_mysql
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: feedo_db
      MYSQL_USER: feedo
      MYSQL_PASSWORD: 123
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - symfony_network

  # phpMyAdmin Service
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: symfony_phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      MYSQL_ROOT_PASSWORD: root_password
      UPLOAD_LIMIT: 800M
      MEMORY_LIMIT: 800M
      MAX_EXECUTION_TIME: 600
      PMA_ARBITRARY: 1
    volumes:
      - ./php.ini:/usr/local/etc/php/conf.d/php-phpmyadmin.ini
      - ./phpmyadmin.conf.php:/etc/phpmyadmin/config.user.inc.php
    ports:
      - "8084:80"
    networks:
      - symfony_network
    depends_on:
      - mysql

volumes:
  db_data:
  php_sessions:

networks:
  symfony_network:
