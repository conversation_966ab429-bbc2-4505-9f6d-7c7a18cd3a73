{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "f085cc974e0ea87e82478f1ee683f20b", "packages": [{"name": "behat/transliterator", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/Behat/Transliterator.git", "reference": "3c4ec1d77c3d05caa1f0bf8fb3aae4845005c7fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Transliterator/zipball/3c4ec1d77c3d05caa1f0bf8fb3aae4845005c7fc", "reference": "3c4ec1d77c3d05caa1f0bf8fb3aae4845005c7fc", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"chuyskywalker/rolling-curl": "^3.1", "php-yaoi/php-yaoi": "^1.0", "phpunit/phpunit": "^4.8.36|^6.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Behat\\Transliterator\\": "src/Behat/Transliterator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Artistic-1.0"], "description": "String transliterator", "keywords": ["i18n", "slug", "transliterator"], "time": "2020-01-14T16:39:13+00:00"}, {"name": "doctrine/annotations", "version": "1.10.2", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "b9d758e831c70751155c698c2f7df4665314a1cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/b9d758e831c70751155c698c2f7df4665314a1cb", "reference": "b9d758e831c70751155c698c2f7df4665314a1cb", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1"}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^7.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "http://www.doctrine-project.org", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2020-04-20T09:18:32+00:00"}, {"name": "doctrine/cache", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "382e7f4db9a12dc6c19431743a2b096041bcdd62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/382e7f4db9a12dc6c19431743a2b096041bcdd62", "reference": "382e7f4db9a12dc6c19431743a2b096041bcdd62", "shasum": ""}, "require": {"php": "~7.1"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/coding-standard": "^6.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0", "predis/predis": "~1.0"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "time": "2019-11-29T15:36:20+00:00"}, {"name": "doctrine/collections", "version": "1.6.4", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "6b1e4b2b66f6d6e49983cebfe23a21b7ccc5b0d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/6b1e4b2b66f6d6e49983cebfe23a21b7ccc5b0d7", "reference": "6b1e4b2b66f6d6e49983cebfe23a21b7ccc5b0d7", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan-shim": "^0.9.2", "phpunit/phpunit": "^7.0", "vimeo/psalm": "^3.2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "time": "2019-11-13T13:07:11+00:00"}, {"name": "doctrine/common", "version": "2.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "308728eae8d90412d850c155d40b1cfbede549da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/308728eae8d90412d850c155d40b1cfbede549da", "reference": "308728eae8d90412d850c155d40b1cfbede549da", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/inflector": "^1.0", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.3.3", "doctrine/reflection": "^1.0", "php": "^7.1"}, "require-dev": {"doctrine/coding-standard": "^1.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpunit/phpunit": "^7.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.11.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, persistence interfaces, proxies, event system and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "time": "2020-05-15T05:51:54+00:00"}, {"name": "doctrine/dbal", "version": "2.10.2", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "aab745e7b6b2de3b47019da81e7225e14dcfdac8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/aab745e7b6b2de3b47019da81e7225e14dcfdac8", "reference": "aab745e7b6b2de3b47019da81e7225e14dcfdac8", "shasum": ""}, "require": {"doctrine/cache": "^1.0", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.2"}, "require-dev": {"doctrine/coding-standard": "^6.0", "jetbrains/phpstorm-stubs": "^2019.1", "nikic/php-parser": "^4.4", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^8.4.1", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "^3.11"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.10.x-dev", "dev-develop": "3.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "time": "2020-04-20T17:19:26+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "1.12.8", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "65bb2ebc96bcb9207ee56bb17f6c0251ec358380"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/65bb2ebc96bcb9207ee56bb17f6c0251ec358380", "reference": "65bb2ebc96bcb9207ee56bb17f6c0251ec358380", "shasum": ""}, "require": {"doctrine/dbal": "^2.5.12", "doctrine/doctrine-cache-bundle": "~1.2", "doctrine/persistence": "^1.3.3", "jdorn/sql-formatter": "^1.2.16", "php": "^7.1", "symfony/cache": "^3.4.30|^4.3.3", "symfony/config": "^3.4.30|^4.3.3", "symfony/console": "^3.4.30|^4.3.3", "symfony/dependency-injection": "^3.4.30|^4.3.3", "symfony/doctrine-bridge": "^3.4.30|^4.3.3", "symfony/framework-bundle": "^3.4.30|^4.3.3", "symfony/service-contracts": "^1.1.1|^2.0"}, "conflict": {"doctrine/orm": "<2.6", "twig/twig": "<1.34|>=2.0,<2.4"}, "require-dev": {"doctrine/coding-standard": "^6.0", "doctrine/orm": "^2.6", "ocramius/proxy-manager": "^2.1", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^7.5", "symfony/phpunit-bridge": "^4.2", "symfony/property-info": "^3.4.30|^4.3.3", "symfony/proxy-manager-bridge": "^3.4|^4|^5", "symfony/twig-bridge": "^3.4|^4.1", "symfony/validator": "^3.4.30|^4.3.3", "symfony/web-profiler-bundle": "^3.4.30|^4.3.3", "symfony/yaml": "^3.4.30|^4.3.3", "twig/twig": "^1.34|^2.12"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.12.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}], "description": "Symfony DoctrineBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "time": "2020-04-23T10:38:48+00:00"}, {"name": "doctrine/doctrine-cache-bundle", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineCacheBundle.git", "reference": "6bee2f9b339847e8a984427353670bad4e7bdccb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineCacheBundle/zipball/6bee2f9b339847e8a984427353670bad4e7bdccb", "reference": "6bee2f9b339847e8a984427353670bad4e7bdccb", "shasum": ""}, "require": {"doctrine/cache": "^1.4.2", "doctrine/inflector": "^1.0", "php": "^7.1", "symfony/doctrine-bridge": "^3.4|^4.0"}, "require-dev": {"instaclick/coding-standard": "~1.1", "instaclick/object-calisthenics-sniffs": "dev-master", "instaclick/symfony2-coding-standard": "dev-remaster", "phpunit/phpunit": "^7.0", "predis/predis": "~0.8", "satooshi/php-coveralls": "^1.0", "squizlabs/php_codesniffer": "~1.5", "symfony/console": "^3.4|^4.0", "symfony/finder": "^3.4|^4.0", "symfony/framework-bundle": "^3.4|^4.0", "symfony/phpunit-bridge": "^3.4|^4.0", "symfony/security-acl": "^2.8", "symfony/validator": "^3.4|^4.0", "symfony/yaml": "^3.4|^4.0"}, "suggest": {"symfony/security-acl": "For using this bundle to cache ACLs"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineCacheBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON><PERSON><EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}], "description": "Symfony Bundle for Doctrine Cache", "homepage": "https://www.doctrine-project.org", "keywords": ["cache", "caching"], "time": "2019-11-29T11:22:01+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "629572819973f13486371cb611386eb17851e85c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/629572819973f13486371cb611386eb17851e85c", "reference": "629572819973f13486371cb611386eb17851e85c", "shasum": ""}, "require": {"php": "^7.1"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "time": "2019-11-10T09:48:07+00:00"}, {"name": "doctrine/inflector", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "4111f6853aea6f28b2b1dcfdde83d12dd3d5e6e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/4111f6853aea6f28b2b1dcfdde83d12dd3d5e6e3", "reference": "4111f6853aea6f28b2b1dcfdde83d12dd3d5e6e3", "shasum": ""}, "require": {"php": "^7.2"}, "require-dev": {"doctrine/coding-standard": "^7.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector", "Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "time": "2020-05-09T15:09:09+00:00"}, {"name": "doctrine/instantiator", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "ae466f726242e637cebdd526a7d991b9433bacf1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/ae466f726242e637cebdd526a7d991b9433bacf1", "reference": "ae466f726242e637cebdd526a7d991b9433bacf1", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"doctrine/coding-standard": "^6.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-shim": "^0.11", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "time": "2019-10-21T16:45:58+00:00"}, {"name": "doctrine/lexer", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "5242d66dbeb21a30dd8a3e66bf7a73b66e05e1f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/5242d66dbeb21a30dd8a3e66bf7a73b66e05e1f6", "reference": "5242d66dbeb21a30dd8a3e66bf7a73b66e05e1f6", "shasum": ""}, "require": {"php": "^7.2"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "time": "2019-10-30T14:39:59+00:00"}, {"name": "doctrine/orm", "version": "v2.7.2", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "dafe298ce5d0b995ebe1746670704c0a35868a6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/dafe298ce5d0b995ebe1746670704c0a35868a6a", "reference": "dafe298ce5d0b995ebe1746670704c0a35868a6a", "shasum": ""}, "require": {"doctrine/annotations": "^1.8", "doctrine/cache": "^1.9.1", "doctrine/collections": "^1.5", "doctrine/common": "^2.11", "doctrine/dbal": "^2.9.3", "doctrine/event-manager": "^1.1", "doctrine/instantiator": "^1.3", "doctrine/persistence": "^1.2", "ext-pdo": "*", "ocramius/package-versions": "^1.2", "php": "^7.1", "symfony/console": "^3.0|^4.0|^5.0"}, "require-dev": {"doctrine/coding-standard": "^5.0", "phpunit/phpunit": "^7.5", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/yaml": "If you want to use YAML Metadata Mapping Driver"}, "bin": ["bin/doctrine"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.7.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\ORM\\": "lib/Doctrine/ORM"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "https://www.doctrine-project.org/projects/orm.html", "keywords": ["database", "orm"], "time": "2020-03-19T06:41:02+00:00"}, {"name": "doctrine/persistence", "version": "1.3.7", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "0af483f91bada1c9ded6c2cfd26ab7d5ab2094e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/0af483f91bada1c9ded6c2cfd26ab7d5ab2094e0", "reference": "0af483f91bada1c9ded6c2cfd26ab7d5ab2094e0", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2", "php": "^7.1"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "time": "2020-03-21T15:13:52+00:00"}, {"name": "doctrine/reflection", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/reflection.git", "reference": "55e71912dfcd824b2fdd16f2d9afe15684cfce79"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/reflection/zipball/55e71912dfcd824b2fdd16f2d9afe15684cfce79", "reference": "55e71912dfcd824b2fdd16f2d9afe15684cfce79", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "ext-tokenizer": "*", "php": "^7.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^5.0", "doctrine/common": "^2.10", "phpstan/phpstan": "^0.11.0", "phpstan/phpstan-phpunit": "^0.11.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Reflection project is a simple library used by the various Doctrine projects which adds some additional functionality on top of the reflection functionality that comes with PHP. It allows you to get the reflection information about classes, methods and properties statically.", "homepage": "https://www.doctrine-project.org/projects/reflection.html", "keywords": ["reflection", "static"], "time": "2020-03-27T11:06:43+00:00"}, {"name": "egulias/email-validator", "version": "2.1.17", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ade6887fd9bd74177769645ab5c474824f8a418a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ade6887fd9bd74177769645ab5c474824f8a418a", "reference": "ade6887fd9bd74177769645ab5c474824f8a418a", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "EmailValidator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "time": "2020-02-13T22:36:52+00:00"}, {"name": "fig/link-util", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/php-fig/link-util.git", "reference": "c038ee75ca13663ddc2d1f185fe6f7533c00832a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link-util/zipball/c038ee75ca13663ddc2d1f185fe6f7533c00832a", "reference": "c038ee75ca13663ddc2d1f185fe6f7533c00832a", "shasum": ""}, "require": {"php": ">=5.5.0", "psr/link": "~1.0@dev"}, "provide": {"psr/link-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "^5.1", "squizlabs/php_codesniffer": "^2.3.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Fig\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common utility implementations for HTTP links", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "time": "2020-04-27T06:40:36+00:00"}, {"name": "friendsofsymfony/jsrouting-bundle", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSJsRoutingBundle.git", "reference": "9deaf916760ce1d64cf46460473260b02751cee5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSJsRoutingBundle/zipball/9deaf916760ce1d64cf46460473260b02751cee5", "reference": "9deaf916760ce1d64cf46460473260b02751cee5", "shasum": ""}, "require": {"php": "^7.1", "symfony/console": "~3.3|^4.0|^5.0", "symfony/framework-bundle": "~3.3|^4.0|^5.0", "symfony/serializer": "~3.3|^4.0|^5.0", "willdurand/jsonp-callback-validator": "~1.0"}, "require-dev": {"symfony/expression-language": "~3.3|^4.0|^5.0", "symfony/phpunit-bridge": "^3.3|^4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"FOS\\JsRoutingBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSJsRoutingBundle/contributors"}], "description": "A pretty nice way to expose your Symfony2 routing to client applications.", "homepage": "http://friendsofsymfony.github.com", "keywords": ["Js Routing", "javascript", "routing"], "time": "2020-05-20T09:38:45+00:00"}, {"name": "friendsofsymfony/oauth-server-bundle", "version": "1.6.2", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSOAuthServerBundle.git", "reference": "fcaa25cc49474bdb0db7894f880976fe76ffed23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSOAuthServerBundle/zipball/fcaa25cc49474bdb0db7894f880976fe76ffed23", "reference": "fcaa25cc49474bdb0db7894f880976fe76ffed23", "shasum": ""}, "require": {"friendsofsymfony/oauth2-php": "~1.1", "paragonie/random_compat": "^1|^2", "php": "^5.5|^7.0", "symfony/dependency-injection": "^2.8|~3.0|^4.0", "symfony/framework-bundle": "~2.8|~3.0|^4.0", "symfony/security-bundle": "~2.8|~3.0|^4.0"}, "require-dev": {"doctrine/doctrine-bundle": "~1.0", "doctrine/mongodb-odm": "~1.0", "doctrine/orm": "~2.2", "phing/phing": "~2.4", "php-mock/php-mock-phpunit": "^1.1", "phpunit/phpunit": "~4.8|~5.0", "propel/propel1": "^1.6.5", "symfony/class-loader": "~2.8|~3.0|^4.0", "symfony/console": "~2.8|~3.0|^4.0", "symfony/form": "~2.8|~3.0|^4.0", "symfony/phpunit-bridge": "~2.8|~3.0|^4.0", "symfony/templating": "~2.8|~3.0|^4.0", "symfony/twig-bundle": "~2.8|~3.0|^4.0", "symfony/yaml": "~2.8|~3.0|^4.0", "willdurand/propel-typehintable-behavior": "^1.0.4"}, "suggest": {"doctrine/doctrine-bundle": "*", "doctrine/mongodb-odm-bundle": "*", "propel/propel-bundle": "If you want to use Propel with Symfony2, then you will have to install the PropelBundle", "symfony/console": "Needed to be able to use commands", "symfony/form": "Needed to be able to use the AuthorizeFormType", "willdurand/propel-typehintable-behavior": "The Typehintable behavior is useful to add type hints on generated methods, to be compliant with interfaces"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"FOS\\OAuthServerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/FriendsOfSymfony/FOSOAuthServerBundle/contributors"}], "description": "Symfony2 OAuth Server Bundle", "homepage": "http://friendsofsymfony.github.com", "keywords": ["o<PERSON>h", "oauth2", "server"], "time": "2019-01-23T15:23:04+00:00"}, {"name": "friendsofsymfony/oauth2-php", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/oauth2-php.git", "reference": "606b8ea1c3c927c272ac1409116332ad5a2ed94c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/oauth2-php/zipball/606b8ea1c3c927c272ac1409116332ad5a2ed94c", "reference": "606b8ea1c3c927c272ac1409116332ad5a2ed94c", "shasum": ""}, "require": {"php": "^5.5.9|^7.0.8|^7.1.3|^7.2.5", "symfony/http-foundation": "~3.0|~4.0|~5.0"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"OAuth2\\": "lib/"}, "exclude-from-classmap": ["/tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/FriendsOfSymfony/oauth2-php/contributors"}], "description": "OAuth2 library", "homepage": "https://github.com/FriendsOfSymfony/oauth2-php", "keywords": ["o<PERSON>h", "oauth2"], "time": "2020-03-03T22:14:46+00:00"}, {"name": "friendsofsymfony/rest-bundle", "version": "2.7.4", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSRestBundle.git", "reference": "3d8501dbdfa48811ef942f5f93c358c80d5ad8eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSRestBundle/zipball/3d8501dbdfa48811ef942f5f93c358c80d5ad8eb", "reference": "3d8501dbdfa48811ef942f5f93c358c80d5ad8eb", "shasum": ""}, "require": {"doctrine/inflector": "^1.0", "php": "^7.1", "psr/log": "^1.0", "symfony/config": "^3.4|^4.3", "symfony/debug": "^3.4|^4.3", "symfony/dependency-injection": "^3.4|^4.3", "symfony/event-dispatcher": "^3.4|^4.3", "symfony/finder": "^3.4|^4.3", "symfony/framework-bundle": "^3.4|^4.3", "symfony/http-foundation": "^3.4|^4.3", "symfony/http-kernel": "^3.4|^4.3", "symfony/routing": "^3.4|^4.3", "symfony/security-core": "^3.4|^4.3", "willdurand/jsonp-callback-validator": "^1.0", "willdurand/negotiation": "^2.0"}, "conflict": {"jms/serializer": "<1.13.0", "jms/serializer-bundle": "<2.0.0", "sensio/framework-extra-bundle": "<3.0.13"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "jms/serializer": "^1.13|^2.0|^3.0", "jms/serializer-bundle": "^2.3.1|^3.0", "phpoption/phpoption": "^1.1", "psr/http-message": "^1.0", "sensio/framework-extra-bundle": "^3.0.13|^4.0|^5.0", "symfony/asset": "^3.4|^4.3", "symfony/browser-kit": "^3.4|^4.3", "symfony/css-selector": "^3.4|^4.3", "symfony/expression-language": "^3.4|^4.3", "symfony/form": "^3.4|^4.3", "symfony/phpunit-bridge": "^4.1.8", "symfony/security-bundle": "^3.4|^4.3", "symfony/serializer": "^3.4|^4.3", "symfony/templating": "^3.4|^4.3", "symfony/twig-bundle": "^3.4|^4.3", "symfony/validator": "^3.4|^4.3", "symfony/web-profiler-bundle": "^3.4|^4.3", "symfony/yaml": "^3.4|^4.3"}, "suggest": {"jms/serializer-bundle": "Add support for advanced serialization capabilities, recommended, requires ^2.0|^3.0", "sensio/framework-extra-bundle": "Add support for the request body converter and the view response listener, requires ^3.0", "symfony/expression-language": "Add support for using the expression language in the routing, requires ^2.7|^3.0", "symfony/serializer": "Add support for basic serialization capabilities and xml decoding, requires ^2.7|^3.0", "symfony/validator": "Add support for validation capabilities in the ParamFetcher, requires ^2.7|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"FOS\\RestBundle\\": ""}, "exclude-from-classmap": ["Resources/", "Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSRestBundle/contributors"}], "description": "This Bundle provides various tools to rapidly develop RESTful API's with Symfony", "homepage": "http://friendsofsymfony.github.com", "keywords": ["rest"], "time": "2020-04-23T17:34:09+00:00"}, {"name": "friendsofsymfony/user-bundle", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSUserBundle.git", "reference": "1049935edd24ec305cc6cfde1875372fa9600446"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSUserBundle/zipball/1049935edd24ec305cc6cfde1875372fa9600446", "reference": "1049935edd24ec305cc6cfde1875372fa9600446", "shasum": ""}, "require": {"paragonie/random_compat": "^1 || ^2", "php": "^5.5.9 || ^7.0", "symfony/form": "^2.8 || ^3.0 || ^4.0", "symfony/framework-bundle": "^2.8 || ^3.0 || ^4.0", "symfony/security-bundle": "^2.8 || ^3.0 || ^4.0", "symfony/templating": "^2.8 || ^3.0 || ^4.0", "symfony/twig-bundle": "^2.8 || ^3.0 || ^4.0", "symfony/validator": "^2.8 || ^3.0 || ^4.0", "twig/twig": "^1.28 || ^2.0"}, "conflict": {"doctrine/doctrine-bundle": "<1.3", "symfony/doctrine-bridge": "<2.7"}, "require-dev": {"doctrine/doctrine-bundle": "^1.3", "friendsofphp/php-cs-fixer": "^2.2", "phpunit/phpunit": "^4.8.35|^5.7.11|^6.5", "swiftmailer/swiftmailer": "^4.3 || ^5.0 || ^6.0", "symfony/console": "^2.8 || ^3.0 || ^4.0", "symfony/phpunit-bridge": "^2.8 || ^3.0 || ^4.0", "symfony/yaml": "^2.8 || ^3.0 || ^4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"FOS\\UserBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSUserBundle/contributors"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Symfony FOSUserBundle", "homepage": "http://friendsofsymfony.github.com", "keywords": ["User management"], "time": "2018-03-08T08:59:27+00:00"}, {"name": "gedmo/doctrine-extensions", "version": "v2.4.41", "source": {"type": "git", "url": "https://github.com/Atlantic18/DoctrineExtensions.git", "reference": "e55a6727052f91834a968937c93b6fb193be8fb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Atlantic18/DoctrineExtensions/zipball/e55a6727052f91834a968937c93b6fb193be8fb6", "reference": "e55a6727052f91834a968937c93b6fb193be8fb6", "shasum": ""}, "require": {"behat/transliterator": "~1.2", "doctrine/common": "~2.4", "php": ">=5.3.2"}, "conflict": {"doctrine/annotations": "<1.2", "doctrine/mongodb-odm": ">=2.0"}, "require-dev": {"doctrine/common": ">=2.5.0", "doctrine/mongodb-odm": ">=1.0.2 <2.0", "doctrine/orm": ">=2.5.0", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5", "symfony/yaml": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"doctrine/mongodb-odm": "to use the extensions with the MongoDB ODM", "doctrine/orm": "to use the extensions with the ORM"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4.x-dev"}}, "autoload": {"psr-4": {"Gedmo\\": "lib/G<PERSON>mo"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Doctrine2 behavioral extensions", "homepage": "http://gediminasm.org/", "keywords": ["Blameable", "behaviors", "doctrine2", "extensions", "gedmo", "loggable", "nestedset", "sluggable", "sortable", "timestampable", "translatable", "tree", "uploadable"], "time": "2020-05-10T22:20:03+00:00"}, {"name": "hoa/compiler", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Compiler.git", "reference": "aa09caf0bf28adae6654ca6ee415ee2f522672de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Compiler/zipball/aa09caf0bf28adae6654ca6ee415ee2f522672de", "reference": "aa09caf0bf28adae6654ca6ee415ee2f522672de", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0", "hoa/file": "~1.0", "hoa/iterator": "~2.0", "hoa/math": "~1.0", "hoa/protocol": "~1.0", "hoa/regex": "~1.0", "hoa/visitor": "~2.0"}, "require-dev": {"hoa/json": "~2.0", "hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Compiler\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Compiler library.", "homepage": "https://hoa-project.net/", "keywords": ["algebraic", "ast", "compiler", "context-free", "coverage", "exhaustive", "grammar", "isotropic", "language", "lexer", "library", "ll1", "llk", "parser", "pp", "random", "regular", "rule", "sampler", "syntax", "token", "trace", "uniform"], "time": "2017-08-08T07:44:07+00:00"}, {"name": "hoa/consistency", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Consistency.git", "reference": "fd7d0adc82410507f332516faf655b6ed22e4c2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Consistency/zipball/fd7d0adc82410507f332516faf655b6ed22e4c2f", "reference": "fd7d0adc82410507f332516faf655b6ed22e4c2f", "shasum": ""}, "require": {"hoa/exception": "~1.0", "php": ">=5.5.0"}, "require-dev": {"hoa/stream": "~1.0", "hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Consistency\\": "."}, "files": ["Prelude.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Consistency library.", "homepage": "https://hoa-project.net/", "keywords": ["autoloader", "callable", "consistency", "entity", "flex", "keyword", "library"], "time": "2017-05-02T12:18:12+00:00"}, {"name": "hoa/event", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Event.git", "reference": "6c0060dced212ffa3af0e34bb46624f990b29c54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Event/zipball/6c0060dced212ffa3af0e34bb46624f990b29c54", "reference": "6c0060dced212ffa3af0e34bb46624f990b29c54", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Event\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Event library.", "homepage": "https://hoa-project.net/", "keywords": ["event", "library", "listener", "observer"], "time": "2017-01-13T15:30:50+00:00"}, {"name": "hoa/exception", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Exception.git", "reference": "091727d46420a3d7468ef0595651488bfc3a458f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Exception/zipball/091727d46420a3d7468ef0595651488bfc3a458f", "reference": "091727d46420a3d7468ef0595651488bfc3a458f", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/event": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Exception\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Exception library.", "homepage": "https://hoa-project.net/", "keywords": ["exception", "library"], "time": "2017-01-16T07:53:27+00:00"}, {"name": "hoa/file", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/File.git", "reference": "35cb979b779bc54918d2f9a4e02ed6c7a1fa67ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/File/zipball/35cb979b779bc54918d2f9a4e02ed6c7a1fa67ca", "reference": "35cb979b779bc54918d2f9a4e02ed6c7a1fa67ca", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/event": "~1.0", "hoa/exception": "~1.0", "hoa/iterator": "~2.0", "hoa/stream": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\File\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\File library.", "homepage": "https://hoa-project.net/", "keywords": ["Socket", "directory", "file", "finder", "library", "link", "temporary"], "time": "2017-07-11T07:42:15+00:00"}, {"name": "hoa/iterator", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Iterator.git", "reference": "d1120ba09cb4ccd049c86d10058ab94af245f0cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Iterator/zipball/d1120ba09cb4ccd049c86d10058ab94af245f0cc", "reference": "d1120ba09cb4ccd049c86d10058ab94af245f0cc", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Iterator\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Iterator library.", "homepage": "https://hoa-project.net/", "keywords": ["iterator", "library"], "time": "2017-01-10T10:34:47+00:00"}, {"name": "hoa/math", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Math.git", "reference": "7150785d30f5d565704912116a462e9f5bc83a0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Math/zipball/7150785d30f5d565704912116a462e9f5bc83a0c", "reference": "7150785d30f5d565704912116a462e9f5bc83a0c", "shasum": ""}, "require": {"hoa/compiler": "~3.0", "hoa/consistency": "~1.0", "hoa/exception": "~1.0", "hoa/iterator": "~2.0", "hoa/protocol": "~1.0", "hoa/zformat": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Math\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Math library.", "homepage": "https://hoa-project.net/", "keywords": ["arrangement", "combination", "combinatorics", "counting", "library", "math", "permutation", "sampler", "set"], "time": "2017-05-16T08:02:17+00:00"}, {"name": "hoa/protocol", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Protocol.git", "reference": "5c2cf972151c45f373230da170ea015deecf19e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Protocol/zipball/5c2cf972151c45f373230da170ea015deecf19e2", "reference": "5c2cf972151c45f373230da170ea015deecf19e2", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Protocol\\": "."}, "files": ["Wrapper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Protocol library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "protocol", "resource", "stream", "wrapper"], "time": "2017-01-14T12:26:10+00:00"}, {"name": "hoa/regex", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Regex.git", "reference": "7e263a61b6fb45c1d03d8e5ef77668518abd5bec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Regex/zipball/7e263a61b6fb45c1d03d8e5ef77668518abd5bec", "reference": "7e263a61b6fb45c1d03d8e5ef77668518abd5bec", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0", "hoa/math": "~1.0", "hoa/protocol": "~1.0", "hoa/ustring": "~4.0", "hoa/visitor": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Regex\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Regex library.", "homepage": "https://hoa-project.net/", "keywords": ["compiler", "library", "regex"], "time": "2017-01-13T16:10:24+00:00"}, {"name": "hoa/stream", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Stream.git", "reference": "3293cfffca2de10525df51436adf88a559151d82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Stream/zipball/3293cfffca2de10525df51436adf88a559151d82", "reference": "3293cfffca2de10525df51436adf88a559151d82", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/event": "~1.0", "hoa/exception": "~1.0", "hoa/protocol": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Stream\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Stream library.", "homepage": "https://hoa-project.net/", "keywords": ["Context", "bucket", "composite", "filter", "in", "library", "out", "protocol", "stream", "wrapper"], "time": "2017-02-21T16:01:06+00:00"}, {"name": "hoa/ustring", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Ustring.git", "reference": "e6326e2739178799b1fe3fdd92029f9517fa17a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Ustring/zipball/e6326e2739178799b1fe3fdd92029f9517fa17a0", "reference": "e6326e2739178799b1fe3fdd92029f9517fa17a0", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "suggest": {"ext-iconv": "ext/iconv must be present (or a third implementation) to use Hoa\\Ustring::transcode().", "ext-intl": "To get a better Hoa\\Ustring::toAscii() and Hoa\\Ustring::compareTo()."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Ustring\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Ustring library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "search", "string", "unicode"], "time": "2017-01-16T07:08:25+00:00"}, {"name": "hoa/visitor", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Visitor.git", "reference": "c18fe1cbac98ae449e0d56e87469103ba08f224a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Visitor/zipball/c18fe1cbac98ae449e0d56e87469103ba08f224a", "reference": "c18fe1cbac98ae449e0d56e87469103ba08f224a", "shasum": ""}, "require": {"hoa/consistency": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Visitor\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Visitor library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "structure", "visit", "visitor"], "time": "2017-01-16T07:02:03+00:00"}, {"name": "hoa/zformat", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Zformat.git", "reference": "522c381a2a075d4b9dbb42eb4592dd09520e4ac2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Zformat/zipball/522c381a2a075d4b9dbb42eb4592dd09520e4ac2", "reference": "522c381a2a075d4b9dbb42eb4592dd09520e4ac2", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Zformat\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Zformat library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "parameter", "zformat"], "time": "2017-01-10T10:39:54+00:00"}, {"name": "jdorn/sql-formatter", "version": "v1.2.17", "source": {"type": "git", "url": "https://github.com/jdorn/sql-formatter.git", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jdorn/sql-formatter/zipball/64990d96e0959dff8e059dfcdc1af130728d92bc", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["lib"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/jdorn/sql-formatter/", "keywords": ["highlight", "sql"], "time": "2014-01-12T16:20:24+00:00"}, {"name": "jms/metadata", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/metadata.git", "reference": "8d8958103485c2cbdd9a9684c3869312ebdaf73a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/metadata/zipball/8d8958103485c2cbdd9a9684c3869312ebdaf73a", "reference": "8d8958103485c2cbdd9a9684c3869312ebdaf73a", "shasum": ""}, "require": {"php": "^7.2"}, "require-dev": {"doctrine/cache": "^1.0", "doctrine/coding-standard": "^4.0", "phpunit/phpunit": "^7.0", "symfony/cache": "^3.1|^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Metadata\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Class/method/property metadata management in PHP", "keywords": ["annotations", "metadata", "xml", "yaml"], "time": "2019-09-17T15:30:40+00:00"}, {"name": "jms/serializer", "version": "3.6.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/serializer.git", "reference": "e82527ceddedf8dd988a4ce900f4b14fedf4d6f2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/serializer/zipball/e82527ceddedf8dd988a4ce900f4b14fedf4d6f2", "reference": "e82527ceddedf8dd988a4ce900f4b14fedf4d6f2", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/instantiator": "^1.0.3", "hoa/compiler": "^**********", "jms/metadata": "^2.0", "php": "^7.2"}, "conflict": {"hoa/consistency": "<**********", "hoa/core": "*", "hoa/iterator": "<**********"}, "require-dev": {"doctrine/coding-standard": "^5.0", "doctrine/orm": "~2.1", "doctrine/phpcr-odm": "^1.3|^2.0", "ext-pdo_sqlite": "*", "jackalope/jackalope-doctrine-dbal": "^1.1.5", "ocramius/proxy-manager": "^1.0|^2.0", "phpunit/phpunit": "^7.5||^8.0||^9.0", "psr/container": "^1.0", "symfony/dependency-injection": "^3.0|^4.0|^5.0", "symfony/expression-language": "^3.0|^4.0|^5.0", "symfony/filesystem": "^3.0|^4.0|^5.0", "symfony/form": "^3.0|^4.0|^5.0", "symfony/translation": "^3.0|^4.0|^5.0", "symfony/validator": "^3.1.9|^4.0|^5.0", "symfony/yaml": "^3.3|^4.0|^5.0", "twig/twig": "~1.34|~2.4"}, "suggest": {"doctrine/cache": "Required if you like to use cache functionality.", "doctrine/collections": "Required if you like to use doctrine collection types as ArrayCollection.", "symfony/yaml": "Required if you'd like to use the YAML metadata format."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}, "autoload": {"psr-4": {"JMS\\Serializer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Library for (de-)serializing data of any complexity; supports XML, JSON, and YAML.", "homepage": "http://jmsyst.com/libs/serializer", "keywords": ["deserialization", "jaxb", "json", "serialization", "xml"], "time": "2020-03-21T20:26:09+00:00"}, {"name": "jms/serializer-bundle", "version": "3.5.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/JMSSerializerBundle.git", "reference": "5793ec59b2243365a625c0fd78415732097c11e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/JMSSerializerBundle/zipball/5793ec59b2243365a625c0fd78415732097c11e8", "reference": "5793ec59b2243365a625c0fd78415732097c11e8", "shasum": ""}, "require": {"jms/serializer": "^2.3|^3.0", "php": "^7.2", "symfony/dependency-injection": "^3.3 || ^4.0 || ^5.0", "symfony/framework-bundle": "^3.0 || ^4.0 || ^5.0"}, "require-dev": {"doctrine/orm": "^2.4", "phpunit/phpunit": "^6.0", "symfony/expression-language": "^3.0 || ^4.0 || ^5.0", "symfony/finder": "^3.0 || ^4.0 || ^5.0", "symfony/form": "^3.0 || ^4.0 || ^5.0", "symfony/stopwatch": "^3.0 || ^4.0 || ^5.0", "symfony/twig-bundle": "*", "symfony/validator": "^3.0 || ^4.0 || ^5.0", "symfony/yaml": "^3.0 || ^4.0 || ^5.0"}, "suggest": {"jms/di-extra-bundle": "Required to get lazy loading (de)serialization visitors, ^1.3", "symfony/finder": "Required for cache warmup, supported versions ^3.0|^4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.5-dev"}}, "autoload": {"psr-4": {"JMS\\SerializerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Allows you to easily serialize, and deserialize data of any complexity", "homepage": "http://jmsyst.com/bundles/JMSSerializerBundle", "keywords": ["deserialization", "json", "serialization", "xml"], "time": "2019-11-29T13:03:07+00:00"}, {"name": "knplabs/doctrine-behaviors", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/KnpLabs/DoctrineBehaviors.git", "reference": "072078422bcada395906ec96b192c6b30a93bebb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/DoctrineBehaviors/zipball/072078422bcada395906ec96b192c6b30a93bebb", "reference": "072078422bcada395906ec96b192c6b30a93bebb", "shasum": ""}, "require": {"behat/transliterator": "~1.0", "doctrine/common": ">=2.2", "php": "~7.0"}, "require-dev": {"doctrine/orm": ">=2.4.5", "ext-pdo_mysql": "*", "ext-pdo_pgsql": "*", "ext-pdo_sqlite": "*", "hexmedia/yaml-linter": "~0.1", "jakub-onderka/php-parallel-lint": "~0.8", "phpunit/phpunit": "~4.8"}, "suggest": {"symfony/framework-bundle": "To be able to use it as a bundle"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"Knp\\DoctrineBehaviors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Knplabs", "homepage": "http://knplabs.com"}], "description": "Doctrine2 behavior traits", "homepage": "http://knplabs.com", "keywords": ["Blameable", "behaviors", "doctrine2", "filterable", "softdeletable", "timestampable", "translatable", "tree"], "time": "2019-12-09T14:23:03+00:00"}, {"name": "knplabs/knp-menu", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpMenu.git", "reference": "254fb64d545f087451cc197a7fa5d47e03ffb038"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpMenu/zipball/254fb64d545f087451cc197a7fa5d47e03ffb038", "reference": "254fb64d545f087451cc197a7fa5d47e03ffb038", "shasum": ""}, "require": {"php": "^7.2"}, "conflict": {"twig/twig": "<1.40 || >=2,<2.9"}, "require-dev": {"phpspec/prophecy": "^1.8", "psr/container": "^1.0", "symfony/http-foundation": "^3.4 || ^4.2|| ^5.0", "symfony/phpunit-bridge": "^3.3 || ^4.2|| ^5.0", "symfony/routing": "^3.4 || ^4.2|| ^5.0", "twig/twig": "^1.40 || ^2.9 || ^3.0"}, "suggest": {"twig/twig": "for the TwigRenderer and the integration with your templates"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"psr-4": {"Knp\\Menu\\": "src/Knp/Menu"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs", "homepage": "https://knplabs.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "The Community", "homepage": "https://github.com/KnpLabs/KnpMenu/contributors"}], "description": "An object oriented menu library", "homepage": "https://knplabs.com", "keywords": ["menu", "tree"], "time": "2020-04-22T14:37:23+00:00"}, {"name": "knplabs/knp-menu-bundle", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpMenuBundle.git", "reference": "2795e236db1d807040762be9a2813ab8c6ed0569"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpMenuBundle/zipball/2795e236db1d807040762be9a2813ab8c6ed0569", "reference": "2795e236db1d807040762be9a2813ab8c6ed0569", "shasum": ""}, "require": {"knplabs/knp-menu": "^3.0", "php": "^7.2", "symfony/framework-bundle": "^3.4 | ^4.2 | ^5.0"}, "require-dev": {"phpspec/prophecy": "^1.8", "symfony/expression-language": "^3.4 | ^4.2 | ^5.0", "symfony/phpunit-bridge": "^3.4 | ^4.2 | ^5.0", "symfony/templating": "^3.4 | ^4.0 | ^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"psr-4": {"Knp\\Bundle\\MenuBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Knplabs", "homepage": "http://knplabs.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://github.com/KnpLabs/KnpMenuBundle/contributors"}], "description": "This bundle provides an integration of the KnpMenu library", "keywords": ["menu"], "time": "2019-12-01T13:07:41+00:00"}, {"name": "kriswallsmith/assetic", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/kriswallsmith/assetic.git", "reference": "e911c437dbdf006a8f62c2f59b15b2d69a5e0aa1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kriswallsmith/assetic/zipball/e911c437dbdf006a8f62c2f59b15b2d69a5e0aa1", "reference": "e911c437dbdf006a8f62c2f59b15b2d69a5e0aa1", "shasum": ""}, "require": {"php": ">=5.3.1", "symfony/process": "~2.1|~3.0"}, "conflict": {"twig/twig": "<1.27"}, "require-dev": {"leafo/lessphp": "^0.3.7", "leafo/scssphp": "~0.1", "meenie/javascript-packer": "^1.1", "mrclay/minify": "<2.3", "natxet/cssmin": "3.0.4", "patchwork/jsqueeze": "~1.0|~2.0", "phpunit/phpunit": "~4.8 || ^5.6", "psr/log": "~1.0", "ptachoire/cssembed": "~1.0", "symfony/phpunit-bridge": "~2.7|~3.0", "twig/twig": "~1.23|~2.0", "yfix/packager": "dev-master"}, "suggest": {"leafo/lessphp": "Assetic provides the integration with the lessphp LESS compiler", "leafo/scssphp": "Assetic provides the integration with the scssphp SCSS compiler", "leafo/scssphp-compass": "Assetic provides the integration with the SCSS compass plugin", "patchwork/jsqueeze": "Assetic provides the integration with the JSqueeze JavaScript compressor", "ptachoire/cssembed": "Assetic provides the integration with phpcssembed to embed data uris", "twig/twig": "Assetic provides the integration with the Twig templating engine"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-0": {"Assetic": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kriswallsmith.net/"}], "description": "Asset Management for PHP", "homepage": "https://github.com/kriswallsmith/assetic", "keywords": ["assets", "compression", "minification"], "time": "2016-11-11T18:43:20+00:00"}, {"name": "monolog/monolog", "version": "1.25.3", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "fa82921994db851a8becaf3787a9e73c5976b6f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/fa82921994db851a8becaf3787a9e73c5976b6f1", "reference": "fa82921994db851a8becaf3787a9e73c5976b6f1", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "jakub-onderka/php-parallel-lint": "0.9", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpunit/phpunit": "~4.5", "phpunit/phpunit-mock-objects": "2.3.0", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2019-12-20T14:15:16+00:00"}, {"name": "nelmio/cors-bundle", "version": "1.5.6", "source": {"type": "git", "url": "https://github.com/nelmio/NelmioCorsBundle.git", "reference": "10a24c10f242440211ed31075e74f81661c690d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nelmio/NelmioCorsBundle/zipball/10a24c10f242440211ed31075e74f81661c690d9", "reference": "10a24c10f242440211ed31075e74f81661c690d9", "shasum": ""}, "require": {"symfony/framework-bundle": "^2.7 || ^3.0 || ^4.0"}, "require-dev": {"matthiasnoback/symfony-dependency-injection-test": "^1.0 || ^2.0", "mockery/mockery": "^0.9 || ^1.0", "symfony/phpunit-bridge": "^2.7 || ^3.0 || ^4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"Nelmio\\CorsBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Nelmio", "homepage": "http://nelm.io"}, {"name": "Symfony Community", "homepage": "https://github.com/nelmio/NelmioCorsBundle/contributors"}], "description": "Adds CORS (Cross-Origin Resource Sharing) headers support in your Symfony2 application", "keywords": ["api", "cors", "crossdomain"], "time": "2019-06-17T08:53:14+00:00"}, {"name": "ocramius/package-versions", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/Ocramius/PackageVersions.git", "reference": "421679846270a5772534828013a93be709fb13df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/421679846270a5772534828013a93be709fb13df", "reference": "421679846270a5772534828013a93be709fb13df", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0", "php": "^7.4.0"}, "require-dev": {"composer/composer": "^1.9.3 || ^2.0@dev", "doctrine/coding-standard": "^7.0.2", "ext-zip": "^1.15.0", "infection/infection": "^0.15.3", "phpunit/phpunit": "^9.0.1", "vimeo/psalm": "^3.9.3"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.99.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "time": "2020-04-06T17:43:35+00:00"}, {"name": "paragonie/random_compat", "version": "v2.0.18", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "0a58ef6e3146256cc3dc7cc393927bcc7d1b72db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/0a58ef6e3146256cc3dc7cc393927bcc7d1b72db", "reference": "0a58ef6e3146256cc3dc7cc393927bcc7d1b72db", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "4.*|5.*"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "autoload": {"files": ["lib/random.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2019-01-03T20:59:08+00:00"}, {"name": "patchwork/jsqueeze", "version": "v2.0.5", "source": {"type": "git", "url": "https://github.com/tchwork/jsqueeze.git", "reference": "693d64850eab2ce6a7c8f7cf547e1ab46e69d542"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tchwork/jsqueeze/zipball/693d64850eab2ce6a7c8f7cf547e1ab46e69d542", "reference": "693d64850eab2ce6a7c8f7cf547e1ab46e69d542", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Patchwork\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["(Apache-2.0 or GPL-2.0)"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Efficient JavaScript minification in PHP", "homepage": "https://github.com/tchwork/jsqueeze", "keywords": ["compression", "javascript", "minification"], "time": "2016-04-19T09:28:22+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/link", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/link.git", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link/zipball/eea8e8662d5cd3ae4517c9b864493f59fca95562", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for HTTP links", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "time": "2016-10-28T16:06:13+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "scssphp/scssphp", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/scssphp/scssphp.git", "reference": "4363ddce8d750f055c436833dd77d83517946532"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/scssphp/scssphp/zipball/4363ddce8d750f055c436833dd77d83517946532", "reference": "4363ddce8d750f055c436833dd77d83517946532", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.5 || ^7.5 || ^8.3", "squizlabs/php_codesniffer": "~3.5", "twbs/bootstrap": "~4.3", "zurb/foundation": "~6.5"}, "bin": ["bin/pscss"], "type": "library", "autoload": {"psr-4": {"ScssPhp\\ScssPhp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/robocoder"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Cerdic"}], "description": "scssphp is a compiler for SCSS written in PHP.", "homepage": "http://scssphp.github.io/scssphp/", "keywords": ["css", "less", "sass", "scss", "stylesheet"], "time": "2020-04-21T15:53:32+00:00"}, {"name": "sensio/distribution-bundle", "version": "v5.0.25", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioDistributionBundle.git", "reference": "80a38234bde8321fb92aa0b8c27978a272bb4baf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioDistributionBundle/zipball/80a38234bde8321fb92aa0b8c27978a272bb4baf", "reference": "80a38234bde8321fb92aa0b8c27978a272bb4baf", "shasum": ""}, "require": {"php": ">=5.3.9", "sensiolabs/security-checker": "~5.0|~6.0", "symfony/class-loader": "~2.3|~3.0", "symfony/config": "~2.3|~3.0", "symfony/dependency-injection": "~2.3|~3.0", "symfony/filesystem": "~2.3|~3.0", "symfony/http-kernel": "~2.3|~3.0", "symfony/process": "~2.3|~3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\DistributionBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Base bundle for Symfony Distributions", "keywords": ["configuration", "distribution"], "time": "2019-06-18T15:43:58+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v5.4.1", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "585f4b3a1c54f24d1a8431c729fc8f5acca20c8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/585f4b3a1c54f24d1a8431c729fc8f5acca20c8a", "reference": "585f4b3a1c54f24d1a8431c729fc8f5acca20c8a", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/persistence": "^1.0", "php": ">=7.1.3", "symfony/config": "^3.4|^4.3", "symfony/dependency-injection": "^3.4|^4.3", "symfony/framework-bundle": "^3.4|^4.3", "symfony/http-kernel": "^3.4|^4.3"}, "require-dev": {"doctrine/doctrine-bundle": "^1.6", "doctrine/orm": "^2.5", "nyholm/psr7": "^1.1", "symfony/browser-kit": "^3.4|^4.3", "symfony/dom-crawler": "^3.4|^4.3", "symfony/expression-language": "^3.4|^4.3", "symfony/finder": "^3.4|^4.3", "symfony/monolog-bridge": "^3.0|^4.0", "symfony/monolog-bundle": "^3.2", "symfony/phpunit-bridge": "^3.4.19|^4.1.8", "symfony/psr-http-message-bridge": "^1.1", "symfony/security-bundle": "^3.4|^4.3", "symfony/twig-bundle": "^3.4|^4.3", "symfony/yaml": "^3.4|^4.3", "twig/twig": "~1.12|~2.0"}, "suggest": {"symfony/expression-language": "", "symfony/psr-http-message-bridge": "To use the PSR-7 converters", "symfony/security-bundle": ""}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.4.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "time": "2019-07-08T08:31:25+00:00"}, {"name": "sensiolabs/security-checker", "version": "v6.0.3", "source": {"type": "git", "url": "https://github.com/sensiolabs/security-checker.git", "reference": "a576c01520d9761901f269c4934ba55448be4a54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/security-checker/zipball/a576c01520d9761901f269c4934ba55448be4a54", "reference": "a576c01520d9761901f269c4934ba55448be4a54", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/console": "^2.8|^3.4|^4.2|^5.0", "symfony/http-client": "^4.3|^5.0", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-ctype": "^1.11"}, "bin": ["security-checker"], "type": "library", "extra": {"branch-alias": {"dev-master": "6.0-dev"}}, "autoload": {"psr-4": {"SensioLabs\\Security\\": "SensioLabs/Security"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A security checker for your composer.lock", "time": "2019-11-01T13:20:14+00:00"}, {"name": "stof/doctrine-extensions-bundle", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/stof/StofDoctrineExtensionsBundle.git", "reference": "46db71ec7ffee9122eca3cdddd4ef8d84bae269c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stof/StofDoctrineExtensionsBundle/zipball/46db71ec7ffee9122eca3cdddd4ef8d84bae269c", "reference": "46db71ec7ffee9122eca3cdddd4ef8d84bae269c", "shasum": ""}, "require": {"gedmo/doctrine-extensions": "^2.3.4", "php": ">=5.3.2", "symfony/framework-bundle": "~2.7|~3.2|~4.0"}, "require-dev": {"symfony/phpunit-bridge": "^4.0", "symfony/security-bundle": "^2.7 || ^3.2 || ^4.0"}, "suggest": {"doctrine/doctrine-bundle": "to use the ORM extensions", "doctrine/mongodb-odm-bundle": "to use the MongoDB ODM extensions"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Stof\\DoctrineExtensionsBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Integration of the gedmo/doctrine-extensions with Symfony2", "homepage": "https://github.com/stof/StofDoctrineExtensionsBundle", "keywords": ["behaviors", "doctrine2", "extensions", "gedmo", "loggable", "nestedset", "sluggable", "sortable", "timestampable", "translatable", "tree"], "time": "2017-12-24T16:06:50+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.2.3", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "149cfdf118b169f7840bbe3ef0d4bc795d1780c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/149cfdf118b169f7840bbe3ef0d4bc795d1780c9", "reference": "149cfdf118b169f7840bbe3ef0d4bc795d1780c9", "shasum": ""}, "require": {"egulias/email-validator": "~2.0", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "~0.9.1", "symfony/phpunit-bridge": "^3.4.19|^4.1.8"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses", "true/punycode": "Needed to support internationalized email addresses, if ext-intl is not installed"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "time": "2019-11-12T09:31:26+00:00"}, {"name": "symfony/assetic-bundle", "version": "v2.8.2", "source": {"type": "git", "url": "https://github.com/symfony/assetic-bundle.git", "reference": "2e0a23a4874838e26de6f025e02fc63328921a4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/assetic-bundle/zipball/2e0a23a4874838e26de6f025e02fc63328921a4c", "reference": "2e0a23a4874838e26de6f025e02fc63328921a4c", "shasum": ""}, "require": {"kriswallsmith/assetic": "~1.4", "php": ">=5.3.0", "symfony/console": "~2.3|~3.0", "symfony/dependency-injection": "~2.3|~3.0", "symfony/framework-bundle": "~2.3|~3.0", "symfony/yaml": "~2.3|~3.0"}, "conflict": {"kriswallsmith/spork": "<=0.2", "twig/twig": "<1.27"}, "require-dev": {"kriswallsmith/spork": "~0.3", "patchwork/jsqueeze": "~1.0", "symfony/class-loader": "~2.3|~3.0", "symfony/css-selector": "~2.3|~3.0", "symfony/dom-crawler": "~2.3|~3.0", "symfony/phpunit-bridge": "~2.7|~3.0", "symfony/twig-bundle": "~2.3|~3.0"}, "suggest": {"kriswallsmith/spork": "to be able to dump assets in parallel", "symfony/twig-bundle": "to use the Twig integration"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\AsseticBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kriswallsmith.net/"}], "description": "Integrates Assetic into Symfony2", "homepage": "https://github.com/symfony/AsseticBundle", "keywords": ["assets", "compression", "minification"], "abandoned": "symfony/webpack-encore-pack", "time": "2017-07-14T07:26:46+00:00"}, {"name": "symfony/http-client", "version": "v5.0.8", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "93b41572fbb3b8dd11d4f6f0434bbbbacd8619ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/93b41572fbb3b8dd11d4f6f0434bbbbacd8619ab", "reference": "93b41572fbb3b8dd11d4f6f0434bbbbacd8619ab", "shasum": ""}, "require": {"php": "^7.2.5", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.8|^2", "symfony/polyfill-php73": "^1.11", "symfony/service-contracts": "^1.0|^2"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "1.1"}, "require-dev": {"guzzlehttp/promises": "^1.3.1", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0", "symfony/process": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpClient component", "homepage": "https://symfony.com", "time": "2020-04-12T16:45:47+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "e3ba2688594d8ef284f40348f7efb72cba4edec4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/e3ba2688594d8ef284f40348f7efb72cba4edec4", "reference": "e3ba2688594d8ef284f40348f7efb72cba4edec4", "shasum": ""}, "require": {"php": "^7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2020-05-09T18:37:03+00:00"}, {"name": "symfony/mime", "version": "v5.0.8", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "5d6c81c39225a750f3f43bee15f03093fb9aaa0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/5d6c81c39225a750f3f43bee15f03093fb9aaa0b", "reference": "5d6c81c39225a750f3f43bee15f03093fb9aaa0b", "shasum": ""}, "require": {"php": "^7.2.5", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10", "symfony/dependency-injection": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A library to manipulate MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "time": "2020-04-17T03:29:44+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "dd80460fcfe1fa2050a7103ad818e9d0686ce6fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/dd80460fcfe1fa2050a7103ad818e9d0686ce6fd", "reference": "dd80460fcfe1fa2050a7103ad818e9d0686ce6fd", "shasum": ""}, "require": {"monolog/monolog": "~1.22 || ~2.0", "php": ">=5.6", "symfony/config": "~3.4 || ~4.0 || ^5.0", "symfony/dependency-injection": "~3.4.10 || ^4.0.10 || ^5.0", "symfony/http-kernel": "~3.4 || ~4.0 || ^5.0", "symfony/monolog-bridge": "~3.4 || ~4.0 || ^5.0"}, "require-dev": {"symfony/console": "~3.4 || ~4.0 || ^5.0", "symfony/phpunit-bridge": "^3.4.19 || ^4.0 || ^5.0", "symfony/yaml": "~3.4 || ~4.0 || ^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "http://symfony.com", "keywords": ["log", "logging"], "time": "2019-11-13T13:11:14+00:00"}, {"name": "symfony/polyfill-apcu", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-apcu.git", "reference": "9ec426b564916afb9bfd15f78708ec322eeb8538"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-apcu/zipball/9ec426b564916afb9bfd15f78708ec322eeb8538", "reference": "9ec426b564916afb9bfd15f78708ec322eeb8538", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Apcu\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting apcu_* functions to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["apcu", "compatibility", "polyfill", "portable", "shim"], "time": "2020-05-12T16:14:59+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "e94c8b1bbe2bc77507a1056cdb06451c75b427f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/e94c8b1bbe2bc77507a1056cdb06451c75b427f9", "reference": "e94c8b1bbe2bc77507a1056cdb06451c75b427f9", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2020-05-12T16:14:59+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "c4de7601eefbf25f9d47190abe07f79fe0a27424"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/c4de7601eefbf25f9d47190abe07f79fe0a27424", "reference": "c4de7601eefbf25f9d47190abe07f79fe0a27424", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "4ef3923e4a86e1b6ef72d42be59dbf7d33a685e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/4ef3923e4a86e1b6ef72d42be59dbf7d33a685e3", "reference": "4ef3923e4a86e1b6ef72d42be59dbf7d33a685e3", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/intl": "~2.3|~3.0|~4.0|~5.0"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "time": "2020-05-12T16:14:59+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "3bff59ea7047e925be6b7f2059d60af31bb46d6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/3bff59ea7047e925be6b7f2059d60af31bb46d6a", "reference": "3bff59ea7047e925be6b7f2059d60af31bb46d6a", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fa79b11539418b02fc5e1897267673ba2c19419c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fa79b11539418b02fc5e1897267673ba2c19419c", "reference": "fa79b11539418b02fc5e1897267673ba2c19419c", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "e3c8c138280cdfe4b81488441555583aa1984e23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/e3c8c138280cdfe4b81488441555583aa1984e23", "reference": "e3c8c138280cdfe4b81488441555583aa1984e23", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-util": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php56\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "82225c2d7d23d7e70515496d249c0152679b468e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/82225c2d7d23d7e70515496d249c0152679b468e", "reference": "82225c2d7d23d7e70515496d249c0152679b468e", "shasum": ""}, "require": {"paragonie/random_compat": "~1.0|~2.0|~9.99", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php70\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "f048e612a3905f34931127360bdd2def19a5e582"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/f048e612a3905f34931127360bdd2def19a5e582", "reference": "f048e612a3905f34931127360bdd2def19a5e582", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "a760d8964ff79ab9bf057613a5808284ec852ccc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/a760d8964ff79ab9bf057613a5808284ec852ccc", "reference": "a760d8964ff79ab9bf057613a5808284ec852ccc", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-util", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-util.git", "reference": "4afb4110fc037752cf0ce9869f9ab8162c4e20d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-util/zipball/4afb4110fc037752cf0ce9869f9ab8162c4e20d7", "reference": "4afb4110fc037752cf0ce9869f9ab8162c4e20d7", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Util\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony utilities for portability of PHP codes", "homepage": "https://symfony.com", "keywords": ["compat", "compatibility", "polyfill", "shim"], "time": "2020-05-12T16:14:59+00:00"}, {"name": "symfony/service-contracts", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "144c5e51266b281231e947b51223ba14acf1a749"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/144c5e51266b281231e947b51223ba14acf1a749", "reference": "144c5e51266b281231e947b51223ba14acf1a749", "shasum": ""}, "require": {"php": "^7.2.5", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-11-18T17:27:11+00:00"}, {"name": "symfony/swiftmailer-bundle", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/symfony/swiftmailer-bundle.git", "reference": "defa9bdfc0191ed70b389cb93c550c6c82cf1745"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/swiftmailer-bundle/zipball/defa9bdfc0191ed70b389cb93c550c6c82cf1745", "reference": "defa9bdfc0191ed70b389cb93c550c6c82cf1745", "shasum": ""}, "require": {"php": ">=7.0.0", "swiftmailer/swiftmailer": "^6.1.3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^3.4|^4.0|^5.0"}, "conflict": {"twig/twig": "<1.41|<2.10"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0", "symfony/framework-bundle": "^3.4|^4.0|^5.0", "symfony/phpunit-bridge": "^3.4.32|^4.3.5|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log": "Allows logging"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SwiftmailerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony SwiftmailerBundle", "homepage": "http://symfony.com", "time": "2019-11-07T21:01:35+00:00"}, {"name": "symfony/symfony", "version": "v3.4.40", "source": {"type": "git", "url": "https://github.com/symfony/symfony.git", "reference": "ad6f8608e92b548e5695f9213a81d14c2ef274b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/symfony/zipball/ad6f8608e92b548e5695f9213a81d14c2ef274b5", "reference": "ad6f8608e92b548e5695f9213a81d14c2ef274b5", "shasum": ""}, "require": {"doctrine/common": "~2.4", "ext-xml": "*", "fig/link-util": "^1.0", "php": "^5.5.9|>=7.0.8", "psr/cache": "~1.0", "psr/container": "^1.0", "psr/link": "^1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "symfony/polyfill-apcu": "~1.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-icu": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php56": "~1.0", "symfony/polyfill-php70": "~1.6", "twig/twig": "^1.41|^2.10"}, "conflict": {"monolog/monolog": ">=2", "phpdocumentor/reflection-docblock": "<3.0||>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0"}, "provide": {"psr/cache-implementation": "1.0", "psr/container-implementation": "1.0", "psr/log-implementation": "1.0", "psr/simple-cache-implementation": "1.0"}, "replace": {"symfony/asset": "self.version", "symfony/browser-kit": "self.version", "symfony/cache": "self.version", "symfony/class-loader": "self.version", "symfony/config": "self.version", "symfony/console": "self.version", "symfony/css-selector": "self.version", "symfony/debug": "self.version", "symfony/debug-bundle": "self.version", "symfony/dependency-injection": "self.version", "symfony/doctrine-bridge": "self.version", "symfony/dom-crawler": "self.version", "symfony/dotenv": "self.version", "symfony/event-dispatcher": "self.version", "symfony/expression-language": "self.version", "symfony/filesystem": "self.version", "symfony/finder": "self.version", "symfony/form": "self.version", "symfony/framework-bundle": "self.version", "symfony/http-foundation": "self.version", "symfony/http-kernel": "self.version", "symfony/inflector": "self.version", "symfony/intl": "self.version", "symfony/ldap": "self.version", "symfony/lock": "self.version", "symfony/monolog-bridge": "self.version", "symfony/options-resolver": "self.version", "symfony/process": "self.version", "symfony/property-access": "self.version", "symfony/property-info": "self.version", "symfony/proxy-manager-bridge": "self.version", "symfony/routing": "self.version", "symfony/security": "self.version", "symfony/security-bundle": "self.version", "symfony/security-core": "self.version", "symfony/security-csrf": "self.version", "symfony/security-guard": "self.version", "symfony/security-http": "self.version", "symfony/serializer": "self.version", "symfony/stopwatch": "self.version", "symfony/templating": "self.version", "symfony/translation": "self.version", "symfony/twig-bridge": "self.version", "symfony/twig-bundle": "self.version", "symfony/validator": "self.version", "symfony/var-dumper": "self.version", "symfony/web-link": "self.version", "symfony/web-profiler-bundle": "self.version", "symfony/web-server-bundle": "self.version", "symfony/workflow": "self.version", "symfony/yaml": "self.version"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/annotations": "~1.0", "doctrine/cache": "~1.6", "doctrine/data-fixtures": "1.0.*", "doctrine/dbal": "~2.4", "doctrine/doctrine-bundle": "~1.4", "doctrine/orm": "~2.4,>=2.4.5", "egulias/email-validator": "~1.2,>=1.2.8|~2.0", "monolog/monolog": "~1.11", "ocramius/proxy-manager": "~0.4|~1.0|~2.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "predis/predis": "~1.0", "symfony/phpunit-bridge": "^3.4.31|^4.3.4|~5.0", "symfony/security-acl": "~2.8|~3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Doctrine\\": "src/Symfony/Bridge/Doctrine/", "Symfony\\Bridge\\Monolog\\": "src/Symfony/Bridge/Monolog/", "Symfony\\Bridge\\ProxyManager\\": "src/Symfony/Bridge/ProxyManager/", "Symfony\\Bridge\\Twig\\": "src/Symfony/Bridge/Twig/", "Symfony\\Bundle\\": "src/Symfony/Bundle/", "Symfony\\Component\\": "src/Symfony/Component/"}, "classmap": ["src/Symfony/Component/Intl/Resources/stubs"], "exclude-from-classmap": ["**/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "The Symfony PHP framework", "homepage": "https://symfony.com", "keywords": ["framework"], "time": "2020-04-28T17:41:57+00:00"}, {"name": "twig/twig", "version": "v2.12.5", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "18772e0190734944277ee97a02a9a6c6555fcd94"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/18772e0190734944277ee97a02a9a6c6555fcd94", "reference": "18772e0190734944277ee97a02a9a6c6555fcd94", "shasum": ""}, "require": {"php": "^7.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.12-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "time": "2020-02-11T15:31:23+00:00"}, {"name": "<PERSON><PERSON><PERSON>/jsonp-callback-validator", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/willdurand/JsonpCallbackValidator.git", "reference": "1a7d388bb521959e612ef50c5c7b1691b097e909"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/JsonpCallbackValidator/zipball/1a7d388bb521959e612ef50c5c7b1691b097e909", "reference": "1a7d388bb521959e612ef50c5c7b1691b097e909", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~3.7"}, "type": "library", "autoload": {"psr-0": {"JsonpCallbackValidator": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.willdurand.fr"}], "description": "JSONP callback validator.", "time": "2014-01-20T22:35:06+00:00"}, {"name": "will<PERSON><PERSON>/negotiation", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/willdurand/Negotiation.git", "reference": "03436ededa67c6e83b9b12defac15384cb399dc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/Negotiation/zipball/03436ededa67c6e83b9b12defac15384cb399dc9", "reference": "03436ededa67c6e83b9b12defac15384cb399dc9", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Negotiation\\": "src/Negotiation"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Content Negotiation tools for PHP provided as a standalone library.", "homepage": "http://williamdurand.fr/Negotiation/", "keywords": ["accept", "content", "format", "header", "negotiation"], "time": "2017-05-14T17:21:12+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.4.5"}, "platform-dev": [], "platform-overrides": {"php": "7.4.5"}}