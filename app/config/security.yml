# To get started with security, check out the documentation:
# http://symfony.com/doc/current/book/security.html
security:

    # http://symfony.com/doc/current/book/security.html#where-do-users-come-from-user-providers
    providers:
        fos_userbundle:
            id: fos_user.user_provider.username
        # in_memory:
        #     memory:
        #         users:
        #             admin: { password: lala, roles: ROLE_ADMIN }
                #     user:  { password: "wbs123#!", roles: [ 'ROLE_USER' ] }
                #     admin: { password: "wbs123#!", roles: [ 'ROLE_ADMIN' ] }

    role_hierarchy:
        ROLE_ADMIN:       ROLE_USER
        ROLE_SUPER_ADMIN: ROLE_ADMIN

    encoders:
        FOS\UserBundle\Model\UserInterface: bcrypt
        # FOS\UserBundle\Model\UserInterface: sha512
        # Symfony\Component\Security\Core\User\User: plaintext

    firewalls:
        # disables authentication for assets and the profiler, adapt it according to your needs
        # dev:
        #     pattern: ^/(_(profiler|wdt)|css|images|js)/
        #     security: false
        api:
            pattern: ^/api/v3                                # All URLs are protected
            fos_oauth: true                            # OAuth2 protected resource
            stateless: true                            # Do no set session cookies
            anonymous: false                           # Anonymous access is not allowed
        oauth_token:                                   # Everyone can access the access token URL.
            # pattern: ^/api/token
            pattern: ^/oauth/v2/token
            security: false
        oauth_authorize:
            pattern:    ^/oauth/v2/auth
            form_login:
                provider: fos_userbundle
                check_path: /oauth/v2/auth/login_check
                # check_path: /login_check
                login_path: /oauth/v2/auth/login
                # login_path: /login
                default_target_path: /admin/domain
            anonymous: true
            # context: test_connect
        main:
            anonymous: true
            pattern: ^/
            logout:
                path:   /logout
                target: /login
            form_login:
                provider: fos_userbundle
                login_path: /login
                check_path: /login_check
                csrf_token_generator: security.csrf.token_manager
                use_referer: true
                default_target_path: /admin/sports
    # access_control:
    #     - { path: ^/api, roles: ROLE_API }
    access_control:
        # REST API ACL
        - { path: ^/api/auth/login, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        # - { path: ^/api/v3, roles: IS_AUTHENTICATED_FULLY }
        - { path: ^/api/v3, roles: IS_AUTHENTICATED_ANONYMOUSLY, requires_channel: https }
        - { path: ^/api/v2, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        # FEED APP ACL
        - { path: ^/login, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/admin, roles: IS_AUTHENTICATED_FULLY }