# This file is a "template" of what your parameters.yml file should look like
# Set parameters here that may be different on each deployment target of the app, e.g. development, staging, production.
# http://symfony.com/doc/current/best_practices/configuration.html#infrastructure-related-configuration
parameters:
    database_host:     127.0.0.1
    database_port:     ~
    database_name:     feedo_db
    database_user:     feedo
    database_password: 123

    mailer_transport:  smtp
    mailer_host:       127.0.0.1
    mailer_user:       <EMAIL>
    mailer_password:   nopassword

    # A secret key that's used to generate certain security-related tokens
    secret:            RandomString

    # The domain name in use
    domain.name:       feed.gr

    # the location of competition images, used for full stats (i.e. over-under, no-goal-goal-goal, most-goals, etc.)
    comp.image.path: 'https://wbsearthcomps-5c24.kxcdn.com/'
