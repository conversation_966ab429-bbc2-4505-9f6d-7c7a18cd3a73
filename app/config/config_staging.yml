imports:
    - { resource: config.yml }

#doctrine:
#    orm:
#        metadata_cache_driver: apc
#        result_cache_driver: apc
#        query_cache_driver: apc

monolog:
    handlers:
        main:
            type: rotating_file
            path: "%kernel.logs_dir%/%kernel.environment%.log"
            level: debug
            max_files: 10
            channels: [!event, !request, !security]
        errors:
            type: rotating_file
            path: "%kernel.logs_dir%/%kernel.environment%_error.log"
            level: error
            max_files: 10
            channels: [!event, !request, !security]
        console:
            type:  console
            channels: [!event, !doctrine, !console, !request, !security]
