imports:
    - { resource: parameters.yml }
    - { resource: security.yml }
    - { resource: services.yml }
    - { resource: doctrine_extensions.yml }

# Put parameters here that don't need to change on each machine where the app is deployed
# http://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    # locale: en
    locale: en

framework:
    #esi:             ~
    #translator:      { fallbacks: ["%locale%"] }
    secret:          "%secret%"
    router:
        resource: "%kernel.root_dir%/config/routing.yml"
        strict_requirements: ~
    form:            ~
    csrf_protection: ~
    validation:      { enable_annotations: true }
    #serializer:      { enable_annotations: true }
    templating:
        engines: ['twig']
    default_locale:  "%locale%"
    trusted_hosts:   ~
    trusted_proxies: ~
    session:
        # http://symfony.com/doc/current/reference/configuration/framework.html#handler-id
        handler_id:  session.handler.native_file
        # save_path:   "%kernel.root_dir%/../var/sessions/%kernel.environment%"
        save_path: '/var/lib/php/sessions'
        # save_path:  '%kernel.root_dir%/sessions'
    fragments:       ~
    http_method_override: true
    assets: ~
    translator: { fallbacks: [en] }
        # path:
        #     - '%kernel.root_dir%/../translations'

# Twig Configuration
twig:
    debug:            "%kernel.debug%"
    strict_variables: "%kernel.debug%"
    form_themes:
        - bootstrap_3_horizontal_layout.html.twig
        # - bootstrap_3_layout.html.twig

# Doctrine Configuration
doctrine:
    dbal:
        driver:   pdo_mysql
        host:     "%database_host%"
        port:     "%database_port%"
        dbname:   "%database_name%"
        user:     "%database_user%"
        password: "%database_password%"
        charset:  utf8mb4
        default_table_options:
            charset: utf8mb4
            collate: utf8mb4_unicode_ci
        logging: false
        profiling: false
        # if using pdo_sqlite as your database driver:
        #   1. add the path in parameters.yml
        #     e.g. database_path: "%kernel.root_dir%/data/data.db3"
        #   2. Uncomment database_path in parameters.yml.dist
        #   3. Uncomment next line:
        #     path:     "%database_path%"

    orm:
        auto_generate_proxy_classes: "%kernel.debug%"
        naming_strategy: doctrine.orm.naming_strategy.underscore
        auto_mapping: true
        mappings:
            translatable:
                type: annotation
                alias: Gedmo
                prefix: Gedmo\Translatable\Entity
                # make sure vendor library location is correct
                dir: "%kernel.root_dir%/../vendor/gedmo/doctrine-extensions/lib/Gedmo/Translatable/Entity/MappedSuperclass"
        # entity_managers:
        #     default:
        #         auto_mapping: true
        #         naming_strategy: doctrine.orm.naming_strategy.underscore
        #         filters:
        #             oneLocale:
        #                 class: A2lix\I18nDoctrineBundle\Doctrine\ORM\Filter\OneLocaleFilter
        #                 enabled: true
        # mappings:
        #     translatable:
        #         type: annotation
        #         alias: Gedmo
        #         prefix: Gedmo\Translatable\Entity
        #         # make sure vendor library location is correct
        #         dir: "%kernel.root_dir%/../vendor/gedmo/doctrine-extensions/lib/Gedmo/Translatable/Entity"

# Swiftmailer Configuration
swiftmailer:
    transport: "%mailer_transport%"
    host:      "%mailer_host%"
    username:  "%mailer_user%"
    password:  "%mailer_password%"
    spool:     { type: memory }

# assetic:
#     filters:
#         scssphp:
#             formatter: 'ScssPhp\ScssPhp\Formatter\Compressed'


stof_doctrine_extensions:
    default_locale: en_US
    orm:
        default:
            tree: true
            timestampable: true
            translatable: false
            # sluggable: true

monolog:
    use_microseconds: false
    handlers:
        main:
            type: rotating_file
            max_files: 10

# a2lix_translation_form:
#     locale_provider: default
#     locales: [en, gr]
#     default_locale: en
#     required_locales: [en, gr]
#     templating: "A2lixTranslationFormBundle::default.html.twig"
#
# a2lix_auto_form:
#     excluded_fields: [id, locale, translatable]

# a2lix_i18n_doctrine:
#     manager_registry: doctrine


# knp_doctrine_behaviors:
#     translatable:   true
#     blameable:      ~
#     tree:           ~
#     geocodable:     ~     # Here null is converted to false
#     loggable:       ~
#     sluggable:      ~
#     soft_deletable: ~

# NelmioCors Configuration
nelmio_cors:
    defaults:
        allow_credentials: false
        allow_origin: ['*']
        allow_headers: ['*']
        allow_methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
        max_age: 3600
        hosts: []
        origin_regex: false
    paths:
        '^/api/':
            allow_origin: ['*']
            allow_headers: ['Content-Type', 'X-Custom-Auth', 'Authorization']
            allow_methods: ['POST', 'PUT', 'GET', 'DELETE', 'OPTIONS']
            max_age: 3600
        # '^/api/v3/':
        #     allow_origin: ['*']
        #     allow_headers: ['X-Custom-Auth', 'Authorization']
        #     allow_methods: ['POST', 'PUT', 'GET', 'DELETE', 'OPTIONS']
        #     max_age: 3600
        # '^/':
        #     origin_regex: true
        #     allow_origin: ['^http://localhost:[0-9]+']
        #     allow_headers: ['X-Custom-Auth']
        #     allow_methods: ['POST', 'PUT', 'GET', 'DELETE']
        #     max_age: 3600
        #     hosts: ['^api\.']

# FOSRest Configuration
fos_rest:
    body_listener: true
    format_listener:
        rules:
            - { path: '^/api', priorities: ['json'], fallback_format: json, prefer_extension: false }
            - { path: '^/', priorities: [ 'html', '*/*'], fallback_format: ~, prefer_extension: true }
    param_fetcher_listener: true
    view:
        view_response_listener: 'force'
        formats:
            json: true
            xml: false
        templating_formats:
            html: true

# FOSUserBundle Configuration
fos_user:
    db_driver: orm
    firewall_name: api
    user_class: AppBundle\Entity\Authentication\User
    from_email:
        address: "%mailer_user%"
        sender_name: "%mailer_user%"

# FOSAuthServerBundle Configuration
fos_oauth_server:
    db_driver: orm
    client_class:        AppBundle\Entity\Authentication\Client
    access_token_class:  AppBundle\Entity\Authentication\AccessToken
    refresh_token_class: AppBundle\Entity\Authentication\RefreshToken
    auth_code_class:     AppBundle\Entity\Authentication\AuthCode
    service:
        user_provider: fos_user.user_provider.username

jms_serializer:
    #enable_short_alias: false

# app/config/config.yml
knp_menu:
    # use "twig: false" to disable the Twig extension and the TwigRenderer
    twig:
        template: KnpMenuBundle::menu.html.twig
    #  if true, enables the helper for PHP templates
    templating: false
    # the renderer to use, list is also available by default
    default_renderer: twig

assetic:
    use_controller: '%kernel.debug%'
    debug:          '%kernel.debug%'
    filters:
        scssphp:
            formatter: 'ScssPhp\ScssPhp\Formatter\Compressed'
        jsqueeze: ~