# Learn more about services, parameters and containers at
# http://symfony.com/doc/current/book/service_container.html
parameters:
#    parameter_name: value

services:
    wbs.feedparser_controller:
        class: AppBundle\Controller\FeedParserController
        arguments: ['@service_container']
        calls:
            - [setContainer, ['@service_container']]
    wbs.ftleagueseasonsport_controller:
        class: AppBundle\Controller\FtLeagueSeasonSportController
        arguments: ['@service_container']
        calls:
            - [setContainer, ['@service_container'], "@ftleagueseason.repository", "@ftleagueseasonsport.repository"]
    wbs.previews_controller:
        class: AppBundle\Controller\Crons\GeneratePostController
        arguments: ['@service_container']
        calls:
            - [setContainer, ['@service_container']]
    wbs.teamstats_controller:
        class: AppBundle\Controller\Crons\TeamStatsController
        arguments: ['@service_container']
        calls:
            - [setContainer, ['@service_container']]
    wbs.ftfixtureodds_controller:
        class: AppBundle\Controller\Crons\FtFixtureOddsController
        arguments: ['@service_container']
        calls:
            - [setContainer, ['@service_container']]
    wbs.ftfixture_controller:
        class: AppBundle\Controller\Crons\FtFixtureController
        arguments: ['@service_container']
        calls:
            - [setContainer, ['@service_container']]
    wbs.migration_controller:
        class: AppBundle\Controller\Migration\MigrationController
        arguments: ['@service_container']
        calls:
            - [setContainer, ['@service_container']]
    wbs.leaguesforstandings_controller:
        class: AppBundle\Controller\Crons\LeaguesForStandingsController
        arguments: ['@service_container']
        calls:
            - [setContainer, ['@service_container']]
    wbs.testo_controller:
        class: AppBundle\Controller\Test\TestoController
        arguments: ['@service_container']
        calls:
            - [setContainer, ['@service_container']]
    wbs.oddtype_controller:
        class: AppBundle\Controller\Crons\SetOddTypesController
        arguments: ['@service_container']
        calls:
            - [setContainer, ['@service_container']]
    wbs.ftfixtureuserodds_controller:
        class: AppBundle\Controller\Crons\FtFixtureUserOddsController
        arguments: ['@service_container']
        calls:
            - [setContainer, ['@service_container']]
    wbs.country.service:
        class: AppBundle\Service\CountryService
        arguments:
            - ['@doctrine.orm.default_entity_manager', '@wbs.parser.logger']
    wbs.globalhelper.service:
        class: AppBundle\Service\GlobalHelperService
        arguments:
            - ['@doctrine.orm.default_entity_manager', '@doctrine.dbal.connection_factory', '@wbs.parser.logger']
    wbs.teamstats.service:
        class: AppBundle\Service\TeamStatsService
        arguments:
            - ['@doctrine.orm.default_entity_manager', '@doctrine.dbal.connection_factory', '@wbs.parser.logger']
    wbs.ftleague.service:
        class: AppBundle\Service\FtLeagueService
        arguments: ['@doctrine.orm.default_entity_manager', '@wbs.parser.logger', "@country.repository"]
    wbs.ftleaguestandings.service:
        class: AppBundle\Service\FtLeagueStandingsService
        arguments: ['@doctrine.orm.default_entity_manager', '@wbs.parser.logger', "@domainsport.repository", '@service_container', '%kernel.environment%', '%kernel.project_dir%']
    wbs.ftleagueseason.service:
        class: AppBundle\Service\FtLeagueSeasonService
        arguments: ['@doctrine.orm.default_entity_manager', '@wbs.parser.logger']
    wbs.ftfixtureodd.service:
        class: AppBundle\Service\FtFixtureOddService
        arguments: ['@doctrine.orm.default_entity_manager', '@wbs.parser.logger']
    wbs.ftfixture.service:
        class: AppBundle\Service\FtFixtureService
        arguments: ['@doctrine.orm.default_entity_manager', '@doctrine.dbal.connection_factory', '@wbs.parser.logger']
    wbs.ftteam.service:
        class: AppBundle\Service\FtTeamService
        arguments: ['@doctrine.orm.default_entity_manager', '@wbs.parser.logger']
    wbs.parser.logger_hadler:
        class: Monolog\Handler\StreamHandler
        arguments: ['%kernel.logs_dir%/%kernel.environment%.parser.log', 200]
    wbs.parser.logger:
        class: Symfony\Bridge\Monolog\Logger
        arguments: [parser]
        calls:
            - [pushHandler, ['@wbs.parser.logger_hadler']]
    application_server.command.create_oauth_client:
        class: AppBundle\Command\CreateOAuthClientCommand
        arguments: ['@fos_oauth_server.client_manager.default']
        tags: [{ name: console.command }]
    country.repository:
        class: Doctrine\ORM\EntityRepository
        factory: ["@doctrine.orm.entity_manager", getRepository]
        arguments:
            - AppBundle\Entity\Country
    domainsport.repository:
        class: Doctrine\ORM\EntityRepository
        factory: ["@doctrine.orm.entity_manager", getRepository]
        arguments:
            - AppBundle\Entity\DomainSport
    ftleagueseason.repository:
        class: Doctrine\ORM\EntityRepository
        factory: ["@doctrine.orm.entity_manager", getRepository]
        arguments:
            - AppBundle\Entity\FtLeagueSeason
    ftleagueseasonsport.repository:
        class: Doctrine\ORM\EntityRepository
        factory: ["@doctrine.orm.entity_manager", getRepository]
        arguments:
            - AppBundle\Entity\FtLeagueSeasonSport
    app.form.ft_competition_schedule_type:
        class: AppBundle\Form\FtCompetitionScheduleType
        arguments: ['@doctrine.orm.entity_manager', '@app.twig.competition_extension']
        tags:
            - { name: form.type }
    app.twig.competition_extension:
        class: AppBundle\Twig\CompetitionExtension
        public: false
        tags:
            - { name: twig.extension }
    # parser.logger:
    #     class: %monolog.logger.class%
    #     arguments: [ nameOfLoggingChannel ]
    #     calls: [ [pushHandler, ['@wbs.parser.handler']] ]
