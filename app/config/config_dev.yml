imports:
    - { resource: config.yml }

framework:
    router:
        resource: "%kernel.root_dir%/config/routing_dev.yml"
        strict_requirements: true
    profiler: { only_exceptions: false }

web_profiler:
    toolbar: true
    intercept_redirects: false

monolog:
    handlers:
        main:
            type: rotating_file
            path: "%kernel.logs_dir%/%kernel.environment%.log"
            level: debug
            max_files: 10
            channels: ["!event", "!request", "!security"]
        errors:
            type: rotating_file
            path: "%kernel.logs_dir%/%kernel.environment%_error.log"
            level: error
            max_files: 10
            channels: ["!event", "!request", "!security"]
        console:
            type: console
            channels: ["!event", "!doctrine", "!console", "!request", "!security"]

#swiftmailer:
#    delivery_address: <EMAIL>
