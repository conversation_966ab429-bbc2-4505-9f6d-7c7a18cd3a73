{% extends 'base.html.twig' %}

{% block body %}
<style>
.card-container.card {
    max-width: 350px;
    padding: 40px 40px;
}

form {
    position: relative;
}

input[type=checkbox] {
    position: absolute;
    bottom: 55px;
    right: 155px;
}

input {
    margin: 5px 0 5px 5px;
}

input#_submit {
    display: block;
    margin: 5px 0 0 0;
    color: #fff;
    background-color: #428bca;
    border-color: #428bca;
    -moz-user-select: none;
    -ms-user-select: none;
    border: 1px solid transparent;
    border-radius: .25rem;
    padding: 10px;
    cursor: pointer;
    text-transform: uppercase;
    font-weight: 400;
    outline: 0;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
}
</style>
<div class="container">
    <div class="card card-container">
        {% block fos_user_content %}{% endblock %}
    </div>
</div>
{% endblock %}