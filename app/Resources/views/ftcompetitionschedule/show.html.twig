{% extends 'base.html.twig' %}

{% block title %}Show Competition Match{% endblock %}

{% block body %}
<h1>Competition Match!</h1>

<table class="table table-stripped">
	<tbody>
		<tr>
			<th>Id</th>
			<td>{{ ftCompetitionSchedule.id }}</td>
		</tr>
		<tr>
			<th>Home Team</th>
			<td>{{ ftHomeCompetitionTeam.name_gr }}</td>
		</tr>
		<tr>
			<th>Away Team</th>
			<td>{{ ftAwayCompetitionTeam.name_gr }}</td>
		</tr>
		<tr>
			<th>Match Type</th>
			<td>{{ ftCompetitionSchedule.matchType ? ftCompetitionSchedule.matchType|replace({'_': ' '})|title : '' }}</td>
		</tr>
		<tr>
			<th>Phase</th>
			<td>{{ get_phase_name(ftCompetitionSchedule.phaseId) }}</td>
		</tr>
		<tr>
			<th>Match Datetime</th>
			<td>{% if ftCompetitionSchedule.matchDatetime %}{{ ftCompetitionSchedule.matchDatetime|date('Y-m-d H:i:s') }}{% endif %}</td>
		</tr>
		<tr>
			<th>Score</th>
			<td>{{ ftCompetitionSchedule.score }}</td>
		</tr>
		<tr>
			<th>TV GR</th>
			<td>{{ ftCompetitionSchedule.tvGr }}</td>
		</tr>
		<tr>
			<th>TV RO</th>
			<td>{{ ftCompetitionSchedule.tvRo }}</td>
		</tr>
		<tr>
			<th>Betarades Link</th>
			<td>{{ ftCompetitionSchedule.linkBetarades is not null ? ftCompetitionSchedule.linkBetarades : '' }}</td>
		</tr>
		<tr>
			<th>Betarades Link Text</th>
			<td>{{ ftCompetitionSchedule.linkTextBetarades is not null ? ftCompetitionSchedule.linkTextBetarades : '' }}</td>
		</tr>
		<tr>
			<th>Bethome Link</th>
			<td>{{ ftCompetitionSchedule.linkBethome is not null ? ftCompetitionSchedule.linkBethome : '' }}</td>
		</tr>
		<tr>
			<th>Bethome Link Text</th>
			<td>{{ ftCompetitionSchedule.linkTextBethome is not null ? ftCompetitionSchedule.linkTextBethome : '' }}</td>
		</tr>
		<tr>
			<th>Pariurix Link</th>
			<td>{{ ftCompetitionSchedule.linkPariurix is not null ? ftCompetitionSchedule.linkPariurix : '' }}</td>
		</tr>
		<tr>
			<th>Pariurix Link Text</th>
			<td>{{ ftCompetitionSchedule.linkTextPariurix is not null ? ftCompetitionSchedule.linkTextPariurix : '' }}</td>
		</tr>
		<tr>
			<th>Home Odds</th>
			<td>{{ ftCompetitionSchedule.homeOdds is not null ? ftCompetitionSchedule.homeOdds : '' }}</td>
		</tr>
		<tr>
			<th>Away Odds</th>
			<td>{{ ftCompetitionSchedule.awayOdds is not null ? ftCompetitionSchedule.awayOdds : '' }}</td>
		</tr>
		<tr>
			<th>Affiliate Link</th>
			<td>{{ ftCompetitionSchedule.affiliateLink is not null ? ftCompetitionSchedule.affiliateLink : '' }}</td>
		</tr>
		<tr>
			<th>Is Hot</th>
			<td>{{ ftCompetitionSchedule.isHot ? 'Yes' : 'No' }}</td>
		</tr>
	</tbody>
</table>

<div class="pb-50">
	<a href="{{ path('ftcompetitionschedule_new') }}" class="btn btn-default btn-primary pull-left mr-20">Create New Competition Match</a>
	<a href="{{ path('ftcompetitionschedule_edit', { 'id': ftCompetitionSchedule.id }) }}" class="btn btn-default btn-info pull-left mr-20">Edit Competition Match</a>
	<a href="{{ path('ftcompetitionschedule_index') }}" class="btn btn-default pull-left">Back to the list</a>
	{{ form_start(delete_form) }}
		<input type="submit" value="Delete" class="btn btn-danger pull-right">
	{{ form_end(delete_form) }}
</div>
{% endblock %}
