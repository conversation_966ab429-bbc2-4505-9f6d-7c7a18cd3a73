{% extends 'base.html.twig' %}

{% block title %}Competition Schedule List{% endblock %}

{% block body %}
    <h1>FtCompetitionSchedule List</h1>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('ftcompetitionschedule_new') }}" class="btn btn-primary pull-left mr-20">Create New Competition Match</a>
            <a href="{{ path('ftcompetitionschedule_rest', { 'locale': 'el', 'site': 'betarades' }) }}" target="_blank" class="btn btn-default btn-info pull-left mr-20">Check Schedules Api</a>
        </div>
    </div>

    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>Id</th>
                <th>Home Team</th>
                <th>Away Team</th>
                <th>Match Type</th>
                <th>Phase</th>
                <th>Match Datetime</th>
                <th>Score</th>
                <th>TV GR</th>
            </tr>
        </thead>
        <tbody>
        {% for ftCompetitionSchedule in ftCompetitionSchedules %}
            <tr>
                <td><a href="{{ path('ftcompetitionschedule_show', { 'id': ftCompetitionSchedule.id }) }}">{{ ftCompetitionSchedule.id }}</a></td>
                <td>{{ ftCompetitionSchedule.h_name }}</td>
                <td>{{ ftCompetitionSchedule.a_name }}</td>
                <td>{{ ftCompetitionSchedule.match_type ? ftCompetitionSchedule.match_type|replace({'_': ' '})|title : '' }}</td>
                <td>{{ get_phase_name(ftCompetitionSchedule.phase_id) }}</td>
                <td>{% if ftCompetitionSchedule.match_datetime %}{{ ftCompetitionSchedule.match_datetime|date('Y-m-d H:i:s') }}{% endif %}</td>
                <td>{{ ftCompetitionSchedule.score is not null ? ftCompetitionSchedule.score : '' }}</td>
                <td>{{ ftCompetitionSchedule.tv_gr is not null ? ftCompetitionSchedule.tv_gr : '' }}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('ftcompetitionschedule_new') }}" class="btn btn-primary pull-left mr-20">Create New Competition Match</a>
            <a href="{{ path('ftcompetitionschedule_rest', { 'locale': 'el', 'site': 'betarades' }) }}" target="_blank" class="btn btn-default btn-info pull-left mr-20">Check Schedules Api</a>
        </div>
    </div>

{% endblock %}
