{% extends 'base.html.twig' %}

{% block title %}Show Domain{% endblock %}

{% block body %}
    <h1>Domain</h1>

    <table class="table table-stripped table-hover">
        <tbody>
            <tr>
                <th>Id</th>
                <td>{{ domain.id }}</td>
            </tr>
            <tr>
                <th>Name</th>
                <td>{{ domain.name }}</td>
            </tr>
            <tr>
                <th>Language</th>
                <td>{{ domain.language.name }}</td>
            </tr>
            <tr>
                <th>Host Port</th>
                <td>{{ domain.hostPort }}</td>
            </tr>
            <tr>
                <th>Dbhost</th>
                <td>{{ domain.dbHost }}</td>
            </tr>
            <tr>
                <th>DbNodehost</th>
                <td>{{ domain.dbNodeHost }}</td>
            </tr>
            <tr>
                <th>Dbport</th>
                <td>{{ domain.dbPort }}</td>
            </tr>
            <tr>
                <th>Dbname</th>
                <td>{{ domain.dbName }}</td>
            </tr>
            <tr>
                <th>Dbprefix</th>
                <td>{{ domain.dbPrefix }}</td>
            </tr>
            <tr>
                <th>Dbusername</th>
                <td>{{ domain.dbUsername }}</td>
            </tr>
            <tr>
                <th>Dbpassword</th>
                <td>{{ domain.dbPassword }}</td>
            </tr>
            <tr>
                <th>Posttype</th>
                <td>{{ domain.postType }}</td>
            </tr>
            <tr>
                <th>Categoryid</th>
                <td>{{ domain.categoryId }}</td>
            </tr>
            <tr>
                <th>Isactive</th>
                <td>{{ domain.isActive ? 'Yes' : 'No' }}</td>
            </tr>
            <tr>
                <th>SyncOnlySports</th>
                <td>{{ domain.SyncOnlySports ? 'Yes' : 'No' }}</td>
            </tr>
            <tr>
                <th>Standings Prefix</th>
                <td>{{ domain.standingsPrefix }}</td>
            </tr>
            <tr>
                <th>Create Posts</th>
                <td>{{ domain.createPosts ? 'Yes' : 'No' }}</td>
            </tr>
            <tr>
                <th>Protocol</th>
                <td>{{ domain.protocol ? 'https' : 'http' }}</td>
            </tr>
            <tr>
                <th>Node Port</th>
                <td>{{ domain.nodePortId }}</td>
            </tr>
            <tr>
                <th>StartAbs</th>
                <td>{{ domain.isStartAbs ? 'Yes' : 'No' }}</td>
            </tr>
            <tr>
                <th>CreatedAt</th>
                <td>{% if domain.createdAt %}{{ domain.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
            <tr>
                <th>UpdatedAt</th>
                <td>{% if domain.updatedAt %}{{ domain.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
        </tbody>
    </table>

    <div class="pb-50">
        <a href="{{ path('domain_edit', { 'id': domain.id }) }}" class="btn btn-default btn-info pull-left mr-20">Edit Domain</a>
        <a href="{{ path('domain_index') }}" class="btn btn-default pull-left">Back to the list</a>
        {{ form_start(delete_form) }}
            <input type="submit" value="Delete" class="btn btn-danger pull-right">
        {{ form_end(delete_form) }}
    </div>
{% endblock %}
