{% extends 'base.html.twig' %}

{% block title %}Domain List{% endblock %}

{% block body %}
    <h1>Domain list</h1>
    <h2>Click the Id to see all the fields</h2>

    <table class="table table-stripped table-hover">
        <thead>
            <tr>
                <th>Id</th>
                <th>Name</th>
                <th>Language</th>
                <th>Host Port</th>
                <th>Dbhost</th>
                <th>DbNodehost</th>
                <th>Dbport</th>
                <th>Dbname</th>
                <th>Dbprefix</th>
                <th>Dbusername</th>
                <th>Dbpassword</th>
                <th>Isactive</th>
                <th>Create Posts</th>
                <th>Protocol</th>
                <th>Node Port</th>
                <th>StartAbs</th>
            </tr>
        </thead>
        <tbody>
        {% for domain in domains %}
            <tr>
                <td><a href="{{ path('domain_show', { 'id': domain.id }) }}">{{ domain.id }}</a></td>
                <td>{{ domain.name }}</td>
                <td>{% if domain.language %}{{ domain.language.name }}{% endif %}</td>
                <td>{{ domain.hostPort }}</td>
                <td>{{ domain.dbHost }}</td>
                <td>{{ domain.dbNodeHost }}</td>
                <td>{{ domain.dbPort }}</td>
                <td>{{ domain.dbName }}</td>
                <td>{{ domain.dbPrefix }}</td>
                <td>{{ domain.dbUsername }}</td>
                <td>{{ domain.dbPassword }}</td>
                <td>{{ domain.isActive ? 'Yes' : 'No' }}</td>
                <td>{{ domain.createPosts ? 'Yes' : 'No' }}</td>
                <td>{{ domain.protocol ? 'https' : 'http' }}</td>
                <td>{{ domain.nodePortId }}</td>
                <td>{{ domain.isStartAbs ? 'Yes' : 'No' }}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('domain_new') }}" class="btn btn-primary pull-left mr-20">Create New Domain</a>
        </div>
    </div>
{% endblock %}
