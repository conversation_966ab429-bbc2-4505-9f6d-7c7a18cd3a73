{% extends 'base.html.twig' %}

{% block title %}FtLeagueStandingsInfo List{% endblock %}

{% block body %}
    <h1>FtLeagueStandingsInfo list</h1>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('ftleaguestandingsinfo_new') }}" class="btn btn-primary">Create New FtLeagueStandingsInfo</a>
        </div>
    </div>

    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>Id</th>
                <th>Sportid</th>
                <th>Season</th>
                <th>Optionid</th>
                <th>Value</th>
                <th>CreatedAt</th>
                <th>UpdatedAt</th>
            </tr>
        </thead>
        <tbody>
        {% for ftLeagueStandingsInfo in ftLeagueStandingsInfos %}
            <tr>
                <td><a href="{{ path('ftleaguestandingsinfo_show', { 'id': ftLeagueStandingsInfo.id }) }}">{{ ftLeagueStandingsInfo.id }}</a></td>
                <td>{{ ftLeagueStandingsInfo.sportId }}</td>
                <td>{{ ftLeagueStandingsInfo.season }}</td>
                <td>{{ ftLeagueStandingsInfo.optionId }}</td>
                <td>{{ ftLeagueStandingsInfo.value }}</td>
                <td>{% if ftLeagueStandingsInfo.createdAt %}{{ ftLeagueStandingsInfo.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
                <td>{% if ftLeagueStandingsInfo.updatedAt %}{{ ftLeagueStandingsInfo.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('ftleaguestandingsinfo_new') }}" class="btn btn-primary">Create New FtLeagueStandingsInfo</a>
        </div>
    </div>
{% endblock %}
