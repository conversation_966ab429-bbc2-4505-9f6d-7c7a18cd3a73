{% extends 'base.html.twig' %}

{% block title %}Show FtLeagueStandingsInfo{% endblock %}

{% block body %}
    <h1>FtLeagueStandingsInfo</h1>

    <table class="table table-stripped">
        <tbody>
            <tr>
                <th>Id</th>
                <td>{{ ftLeagueStandingsInfo.id }}</td>
            </tr>
            <tr>
                <th>Sportid</th>
                <td>{{ ftLeagueStandingsInfo.sportId }}</td>
            </tr>
            <tr>
                <th>Season</th>
                <td>{{ ftLeagueStandingsInfo.season }}</td>
            </tr>
            <tr>
                <th>Optionid</th>
                <td>{{ ftLeagueStandingsInfo.optionId }}</td>
            </tr>
            <tr>
                <th>Value</th>
                <td>{{ ftLeagueStandingsInfo.value }}</td>
            </tr>
            <tr>
                <th>CreatedAt</th>
                <td>{% if ftLeagueStandingsInfo.createdAt %}{{ ftLeagueStandingsInfo.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
            <tr>
                <th>UpdatedAt</th>
                <td>{% if ftLeagueStandingsInfo.updatedAt %}{{ ftLeagueStandingsInfo.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
        </tbody>
    </table>

    <div class="pb-50">
        <a href="{{ path('ftleaguestandingsinfo_edit', { 'id': ftLeagueStandingsInfo.id }) }}" class="btn btn-default btn-info pull-left mr-20">Edit FtLeagueStandingsInfo</a>
        <a href="{{ path('ftleaguestandingsinfo_index') }}" class="btn btn-default pull-left">Back to the list</a>
        {{ form_start(delete_form) }}
            <input type="submit" value="Delete" class="btn btn-danger pull-right">
        {{ form_end(delete_form) }}
    </div>
{% endblock %}
