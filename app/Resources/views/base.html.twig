<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>{% block title %}Welcome!{% endblock %}</title>
    {% block stylesheets %}
      {% stylesheets filter="scssphp" output="css/app.css"
        "assets/scss/main.scss"
        "assets/css/*.css"
      %}
      <link rel="stylesheet" type="text/css" href="{{ asset_url }}" />
    {% endstylesheets %}
    {% endblock %}
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}" />
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
  </head>
  <body class="">
    <div class="loader-container"><div class="loader">Loading&#8230;</div></div>
    <div class="cv-container">
      {% if is_granted("ROLE_ADMIN") %}
        <div id="left">
        {{ knp_menu_render('AppBundle:Builder:mainMenu') }}
        {% if app.user.username == 'fdaskalou' %}
          <a href="{{ path('ftfixtureuserodd_index') }}" style="color:#fff;display:block">Reporter CRUD last 45 days</a>
          <a href="{{ path('ftfixtureuserodd_executecommand') }}" style="color:#fff;display:block">Execute Reporter Bonus Command</a>
        {% endif %}
        </div>
      {% endif %}

      <div id="content">
        <div id="content-inner">
        {% block body %}{% endblock %}
        </div>
      </div>
    </div>
    {% block javascripts %}
      <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
      <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
      <script src="{{ asset('bundles/fosjsrouting/js/router.js') }}"></script>
      <script src="{{ path('fos_js_routing_js', { callback: 'fos.Router.setData' }) }}"></script>
    {% endblock %}
    {% javascripts filter="?jsqueeze" output="js/app.js"
      "assets/js/jquery.collection.js"
      "assets/js/bootstrap-dropdown.js"
      "assets/js/bootstrap-multiselect.js"
    %}
      <script src="{{ asset_url }}"></script>
    {% endjavascripts %}
    </body>
</html>
