{% extends 'base.html.twig' %}

{% block title %}Team Formations List{% endblock %}

{% block body %}
<h1>Team Formation list</h1>

<div class="pb-50">
	<div class="pull-left">
		<a href="{{ path('ftteamformation_new') }}" class="btn btn-primary">New Team Formation</a>
	</div>
</div>

<table class="table table-striped table-hover">
	<thead>
		<tr>
			<th>Id</th>
			<th>Name</th>
			<th>Created At</th>
			<th>Updated At</th>
		</tr>
	</thead>
	<tbody>
	{% for ftTeamFormation in ftTeamFormations %}
		<tr>
			<td><a href="{{ path('ftteamformation_show', { 'id': ftTeamFormation.id }) }}">{{ ftTeamFormation.id }}</a></td>
			<td>{{ ftTeamFormation.name }}</td>
			<td>{% if ftTeamFormation.createdAt %}{{ ftTeamFormation.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
			<td>{% if ftTeamFormation.updatedAt %}{{ ftTeamFormation.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
		</tr>
	{% endfor %}
	</tbody>
</table>

<div class="pb-50">
	<div class="pull-left">
		<a href="{{ path('ftteamformation_new') }}" class="btn btn-primary">New Team Formation</a>
	</div>
</div>
{% endblock %}
