{% extends 'base.html.twig' %}

{% block title %}Show Team Formation{% endblock %}

{% block body %}
<h1>Team Formation</h1>

<table class="table table-stripped">
	<tbody>
		<tr>
			<th>Id</th>
			<td>{{ ftTeamFormation.id }}</td>
		</tr>
		<tr>
			<th>Name</th>
			<td>{{ ftTeamFormation.name }}</td>
		</tr>
		<tr>
			<th>Created At</th>
			<td>{% if ftTeamFormation.createdAt %}{{ ftTeamFormation.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
		</tr>
		<tr>
			<th>Updated At</th>
			<td>{% if ftTeamFormation.updatedAt %}{{ ftTeamFormation.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
		</tr>
	</tbody>
</table>

<div class="pb-50">
	<a href="{{ path('ftteamformation_edit', { 'id': ftTeamFormation.id }) }}" class="btn btn-default btn-info pull-left mr-20">Edit Team Formation</a>
	<a href="{{ path('ftteamformation_index') }}" class="btn btn-default pull-left">Back to the list</a>
	{{ form_start(delete_form) }}
		<input type="submit" value="Delete" class="btn btn-danger pull-right">
	{{ form_end(delete_form) }}
</div>
{% endblock %}
