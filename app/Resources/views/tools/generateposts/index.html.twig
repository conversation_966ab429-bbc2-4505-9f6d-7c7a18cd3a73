{% extends 'base.html.twig' %}
{% block title %}Tools - Generate Posts{% endblock %}
{% block body %}
    <h2>Επέλεξε site</h2>
    {{ form_widget(form.domain) }}
    <input id="getSports" class="btn btn-default" type="button" value="Get Sports for domain" />

    <div id="sports-container"></div>
    <div id="messages" class="mt-20"></div>
    <div id="toolbar"><input id="generate-posts" class="btn btn-info mt-20" type="button" value="Generate Posts" /></div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script type="text/javascript">
    $(document).ready(function(){
        var $body = $('body'),
            $toolbar = $('#toolbar'),
            // $generateSeason = $('#generate-season');
            $generatePosts = $('#generate-posts');
        
        $("#getSports").click(function(){
            $body.addClass('is-loading');

            var domainId = $("#form_domain").val();
            var urlPath = Routing.generate('sport_get_football', { domainId: domainId, withStandings: 'true' });
            $.ajax({
                type: 'GET',
                // url: "{{ path('sport_get_football', { 'domainId': " + domainId + "}) }}",
                url: urlPath,
                success: function(data) {
                    if (data) {
                        var curParent = '';
                        var html = '';

                        html = '<select id="sport">';
                        data.forEach(function(entry) {
                            if (curParent !== entry.parent_sport) {
                                if (curParent !== '') {
                                    html += '</optgroup>';
                                }
                                html += '<optgroup label="' + entry.parent_sport + '">';
                                html += '<option value="' + entry.sport_id + '">' + entry.title + '</option>';
                                curParent = entry.parent_sport;
                            }
                            else {
                                html += '<option value="' + entry.sport_id + '">' + entry.title + '</option>';
//								html += '</optgroup>';
                            }
                        })
                        html += '</select>';

                        $generatePosts.addClass('cv-show');

                        $body.removeClass('is-loading');
                        $('#sports-container').html(html);
                        $('#sport').hide();
                        $('#sport').multiselect({
                            buttonWidth: '100%',
                            enableFiltering: true,
                            onChange: function(option, checked) {
                                $generatePosts.removeAttr('data-lssid');
                            }
                            // enableClickableOptGroups: true,
                        });
                    }
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    $body.removeClass('is-loading');
                    alert(xhr.responseText);
                },
                complete: function(req, res) {
                    $body.removeClass('is-loading');
                }
            });
        });

        document.addEventListener('click', function(event) {
            var targetElement = event.target || event.srcElement;
            if ('generate-posts' === targetElement.id) {
                var sportId = $('#sport option:selected').val(),
                    domainId = $("#form_domain").val();
                $generatePosts.attr('data-lssid', sportId)

                $body.addClass('is-loading');
                try {
                    var urlPath = Routing.generate('tools_generate_posts_create', { domainId: domainId, sportId: sportId } );
                }
                catch(err) {
                    $body.removeClass('is-loading');
                    alert(err.message);
                }

                $.ajax({
                    type: 'GET',
                    url: urlPath,
                    success: function(data) {
                        alert(data);
                        $body.removeClass('is-loading');
                        // console.log(sportIds);
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        $body.removeClass('is-loading');
                        alert(xhr.responseText);
                    },
                    complete: function(req, res) {
                        $body.removeClass('is-loading');
                    }
                });                
                return false;
            }
        });

    });
</script>
{% endblock %}