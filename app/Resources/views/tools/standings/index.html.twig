{% extends 'base.html.twig' %}
{% block title %}Tools - Standings{% endblock %}
{% block body %}
	<h2>Επέλεξε site</h2>
	{{ form_widget(form.domain) }}
	<input id="getSports" class="btn btn-default" type="button" value="Get Sports with Standings" />

	<div id="sports-container"></div>
	<div id="messages" class="mt-20"></div>
	<div id="toolbar"><input id="generate-season" class="btn btn-info mt-20" type="button" value="Generate Season" /><input id="generate-standings" class="btn btn-info mt-20" type="button" value="Generate Standings" /></div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script type="text/javascript">
	$(document).ready(function(){
		var $body 					= $('body'),
			$toolbar 					= $('#toolbar'),
			$generateSeason 	= $('#generate-season');
			$generateStandings= $('#generate-standings');
		
		$("#getSports").click(function(){
			$body.addClass('is-loading');

			var domainId = $("#form_domain").val();
			var urlPath = Routing.generate('sport_get_football', { domainId: domainId, withStandings: 'true' });
			$.ajax({
				type: 'GET',
				// url: "{{ path('sport_get_football', { 'domainId': " + domainId + "}) }}",
				url: urlPath,
				success: function(data) {
					if (data) {
						var curParent = '';
						var html = '';

						html = '<select id="sport">';
						data.forEach(function(entry) {
							if (curParent !== entry.parent_sport) {
								if (curParent !== '') {
									html += '</optgroup>';
								}
								html += '<optgroup label="' + entry.parent_sport + '">';
								html += '<option value="' + entry.sport_id + '">' + entry.title + '</option>';
								curParent = entry.parent_sport;
							}
							else {
								html += '<option value="' + entry.sport_id + '">' + entry.title + '</option>';
//								html += '</optgroup>';
							}
						})
						html += '</select>';

						html += '<input id="checkStandings" class="btn btn-primary mt-20" type="button" value="Check Standings" />';

						$body.removeClass('is-loading');
						$('#sports-container').html(html);
						$('#sport').hide();
						$('#sport').multiselect({
							buttonWidth: '100%',
							enableFiltering: true,
							onChange: function(option, checked) {
								$generateStandings.removeAttr('data-lssid');
							}
							// enableClickableOptGroups: true,
						});
					}
				},
				error: function(xhr, ajaxOptions, thrownError) {
					$body.removeClass('is-loading');
					alert(xhr.responseText);
				},
				complete: function(req, res) {
					$body.removeClass('is-loading');
				}
			});
		});

		document.addEventListener('click', function(event) {
			var targetElement = event.target || event.srcElement;
			if ('checkStandings' === targetElement.id) {
				var sportId = $('#sport option:selected').val();

				if (!sportId) {
					alert('Πρέπει να διαλέξεις πρωτάθλημα!');
				}
				else {
					var html = '';

					// get current season for selected sport
					$body.addClass('is-loading');
					var urlPath = Routing.generate('sport_get_league_season_info', { 'sportId': sportId });
					
					$.ajax({
						type: 'GET',
						url: urlPath,
						success: function(data) {
							var standingsUrl = "http://xml.globalscore.com/xml.php?un=betarades&pass=sdfT@5cKd0x4&sport=s&tp=leagueTables&lid=" + data.league_id + "&ssn=" + data.season;

							html += "<p class='alert alert-info'>Η τρέχουσα σεζόν στο σύστημα μας για το πρωτάθλημα <strong>" + data.league_id + "</strong> είναι <strong>" + data.season + "</strong></p>";
							html += "<p class='alert alert-info'>Δες στον Δεδούλη τη <a href='" + standingsUrl+ "' target='none' title='Βαθμολογία'>Βαθμολογία</a></p>";
							html += "<p class='alert alert-warning'>Εάν πιστεύεις ότι η σεζόν που έχουμε δεν συμφωνεί με την πραγματική σεζόν, εκτέλεσε την εργασία δημιουργίας των νέων σεζόν από Δεδούλη</p>";

							$('#messages').html(html);
							$generateSeason.addClass('cv-show');
							$generateStandings.addClass('cv-show');
							$generateStandings.attr('data-lssid', data.id)
						},
						error: function(xhr, ajaxOptions, thrownError) {
							$body.removeClass('is-loading');
							alert(xhr.responseText);
						},
						complete: function(req, res) {
							$body.removeClass('is-loading');
						}
					});
				}
			} // if checkStandings

			if ('generate-standings' === targetElement.id) {
				// var sportId = $('#sport option:selected').val();
				var sportId = $generateStandings.attr('data-lssid');

				if (!sportId) {
					alert('Πρέπει να εκτελέσεις την Check Standings για το πρωτάθλημα που επέλεξες!');
				}
				else {
					var html = '';

					// get current season for selected sport
					$body.addClass('is-loading');
					var urlPath = Routing.generate('sport_generatestandings', { 'sportId': sportId });
					
					$.ajax({
						type: 'GET',
						url: urlPath,
						success: function(data) {
							console.log(data);
							html = "<p class='alert alert-success'>" + data + "</p>";
							$('#messages').html(html);
							$generateSeason.addClass('cv-hide');
						},
						error: function(xhr, ajaxOptions, thrownError) {
							$body.removeClass('is-loading');
							alert(xhr.responseText);
						},
						complete: function(req, res) {
							$body.removeClass('is-loading');
						}
					});
				}
			} // if generate-standings
		});

		/**
		* Generate Season Click
		*/
		$('#generate-season').on('click', function(event) {
			$body.addClass('is-loading');
			try {
				var urlPath = Routing.generate('checker_generateseason');
			}
			catch(err) {
				$body.removeClass('is-loading');
				alert(err.message);
			}

			$.ajax({
				type: 'GET',
				url: urlPath,
				success: function(data) {
					alert(data);
					$body.removeClass('is-loading');
					// console.log(sportIds);
				},
				error: function(xhr, ajaxOptions, thrownError) {
					$body.removeClass('is-loading');
					alert(xhr.responseText);
				},
				complete: function(req, res) {
					$body.removeClass('is-loading');
				}
			});
		});
	});
</script>
{% endblock %}