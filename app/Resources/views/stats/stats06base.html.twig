<div class="{{ divClass }}">
  <table>
    <thead>
      <tr class="subheader">
        <th class="triple_no">{{ 'TIMEFRAME' | trans }}</th>
        <th><em>{{ homeTeamName }}</em></th>
        <th><em>{{ awayTeamName }}</em></th>
      </tr>
    </thead>
    <tbody>
      {% for single in data %}
      <tr>
        <td>{{ single.label }}</td>
        <td>
          <div class="bar">
            <span style="width: {{ single.goals1p }}%;"></span>
          </div>
          <div class="legend">{{ single.goals1 }}</div>
        </td>
        <td>
          <div class="bar">
            <span style="width: {{ single.goals2p }}%;"></span>
          </div>
          <div class="legend">{{ single.goals2 }}</div>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
