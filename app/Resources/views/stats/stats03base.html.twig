<div class="{{ divClass }}">
  <table>
    <thead>
      <tr class="header">
        <th>&nbsp;</th>
        <th colspan="3">{{ 'TOTAL' | trans }}</th>
        <th colspan="3">{{ 'HOME' | trans }}</th>
        <th colspan="3">{{ 'AWAY' | trans }}</th>
      </tr>
      <tr class="subheader">
        <th>{{ 'TEAM' | trans }}</th>
        <th>{{ 'PLAYED' | trans }}</th>
        <th>OVER</th>
        <th>UNDER</th>
        <th>{{ 'PLAYED' | trans }}</th>
        <th>OVER</th>
        <th>UNDER</th>
        <th>{{ 'PLAYED' | trans }}</th>
        <th>OVER</th>
        <th>UNDER</th>
      </tr>
    </thead>
    <tbody>
      {% for single in data %}
      <tr>
        <td class="team label">
          <img src="{{ logoUrl }}{{ single.teamId }}.png">
          <em>{{ single.teamName }}</em>
        </td>
        <td>{{ single.totalOverUnder }}</td>
        <td>{{ single.totalOver }}</td>
        <td>{{ single.totalUnder }}</td>
        <td>{{ single.homeOverUnder }}</td>
        <td>{{ single.homeOver }}</td>
        <td>{{ single.homeUnder }}</td>
        <td>{{ single.awayOverUnder }}</td>
        <td>{{ single.awayOver }}</td>
        <td>{{ single.awayUnder }}</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
