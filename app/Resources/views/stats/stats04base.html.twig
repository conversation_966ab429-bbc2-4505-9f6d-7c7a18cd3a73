<div class="{{ divClass }}">
  <table>
    <thead>
      <tr class="subheader">
        <th>{{ 'TEAM' | trans }}</th>
        <th>{{ 'MP.' | trans }}</th>
        <th>GG</th>
        <th>NG</th>
        <th>0-1</th>
        <th>2-3</th>
        <th>4-6</th>
        <th>7+</th>
        <th>1 {{ 'HT' | trans }}</th>
        <th>Χ {{ 'HT' | trans }}</th>
        <th>2 {{ 'HT' | trans }}</th>
      </tr>
    </thead>
    <tbody>
      {% for single in data %}
      <tr>
        <td class="team label">
          <img src="{{ logoUrl }}{{ single.teamId }}.png">
          <em>{{ single.teamName }}</em> {{ (ha) ? (("0" == single.orderBy) ? "<span>( " ~ ('home' | trans ) ~ " )</span>" : "<span>( " ~ ('away' | trans) ~ " )</span>") | raw : '' }} 
        </td>
        <td>{{ single.played }}</td>
        <td>{{ single.goalGoal }}</td>
        <td>{{ single.noGoal }}</td>
        <td>{{ single.goals01 }}</td>
        <td>{{ single.goals23 }}</td>
        <td>{{ single.goals46 }}</td>
        <td>{{ single.goals7 }}</td>
        <td>{{ single.winsHt }}</td>
        <td>{{ single.drawsHt }}</td>
        <td>{{ single.losesHt }}</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
