<div class="info_box table-stats table-tabs-container stripped bordered aa-table" id="stats09">
  <a href="/vathmologia-{{ sportSlug }}"><h3 class="headline headline--arrow headline--navy headline--nospace headline__arrow headline--darkblue headline__arrow-nospace">{{ 'LEAGUE TABLE' | trans }} {{ sportTitle }}</h3></a>

  <table>
    <thead>
      <tr class="subheader">
        <th>{{ 'INDEX' | trans }}</th>
        <th>{{ 'TEAM' | trans }}</th>
        <th>{{ 'Pts' | trans }}</th>
        <th>{{ 'MP' | trans }}</th>
        <th>{{ 'W' | trans }}</th>
        <th>{{ 'D' | trans }}</th>
        <th>{{ 'L' | trans }}</th>
        <th>{{ 'GOALS' | trans }}</th>
        <th>{{ 'FORM' | trans }} &amp;<br/>OVER/UNDER</th>
      </tr>
    </thead>
    <tbody>
      {% set legends = {} %}
      {% for single in data %}
        {% if (null != single.legend) and ('' != single.legend) %}
          {% if (single.legendValue not in legends|keys) %}
            {% set legends = legends|merge({ (single.legendValue): single.legend }) %}
          {% endif %}
        {% endif %}
      <tr>
        <td {{ (null != single.legend) ? ' class=legend-' ~ single.legend : ''}}>{{ single.rank }}</td>
        <td class="label team">
          <img src="{{ logoUrl }}{{ single.teamId }}.png">
          <em>{{ single.teamName }}</em>
        </td>
        <td class="points">{{ single.points }}</td>
        <td>{{ single.played }}</td>
        <td>{{ single.wins }}</td>
        <td>{{ single.draws }}</td>
        <td>{{ single.loses }}</td>
        <td class="goals">{{ single.goals }}</td>
        {% set singleMatchForm = single.matchForm|split(',') %}
        {% set singleOverUnderForm = single.overUnderForm|split(',') %}
        <td class="form">
          <div>{% for matchForm in singleMatchForm %}<span class="f {{ matchForm }}">{{ matchForm|replace({'w': 'W' | trans, 'd': 'D' | trans, 'l': 'L' | trans}) }}</span>{% endfor %}</div>
          <div>{% for overUnderForm in singleOverUnderForm %}<span class="f {{ overUnderForm }}">{{ overUnderForm|replace({'o': 'O', 'u': 'U'}) }}</span>{% endfor %}</div>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  {% if legends %}
  <div class="league__standings__legends">
  {% for key, single in legends %}
    <div class="legend"><span class="legend-{{ single }}"></span>{{ key }}</div>
  {% endfor %}
  </div>
  {% endif %}
</div>
