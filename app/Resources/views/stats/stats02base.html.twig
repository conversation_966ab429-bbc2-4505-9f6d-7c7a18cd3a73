<table>
  <tbody>
    {% if (slice) %}
      {% if calcType == "single" %}
        {% set data1 = team1.h10|slice(0, 6) %}
        {% set data2 = team2.a10|slice(0, 6) %}
      {% elseif calcType == "all" %}
        {% set data1 = team1.ha10|slice(0, 6) %}
        {% set data2 = team2.ah10|slice(0, 6) %}
      {% endif %}
    {% else %}
      {% if calcType == "single" %}
        {% set data1 = team1.h10 %}
        {% set data2 = team2.a10 %}
      {% elseif calcType == "all" %}
        {% set data1 = team1.ha10 %}
        {% set data2 = team2.ah10 %}
      {% endif %}
    {% endif %}

    <tr class="header">
      <th colspan="2">
        <img src="{{ logoUrl }}{{ teamId1 }}.png"><em>{{ teamName1 }}</em> <span>{{ type ? 'HOME' | trans : '' }}</span>
      </th>
      <th>{{ 'HT' | trans }}</th>
      <th>{{ 'FIN' | trans }}</th>
      <th>&nbsp;</th>
    </tr>

    {% for teamD1 in data1 %}
    <tr>
      <td class="date">{{ teamD1.matchDateTime }}</td>
      <td>{{ teamD1.matchName|replace({ (teamName1): '<b>' ~ teamName1 ~ '</b>'})|raw }}</td>
      <td class="sc hf">{{ teamD1.htScore }}</td>
      <td class="sc">{{ teamD1.ftScore }}</td>
      <td class="form"><span class="f {{ teamD1.matchForm }}">{{ teamD1.matchForm|replace({'w': 'W' | trans, 'd': 'D' | trans, 'l': 'L' | trans}) }}</span><span class="f {{ teamD1.overUnder }}">{{ teamD1.overUnder|replace({'o': 'O', 'u': 'U'}) }}</span></td>
    </tr>
    {% endfor %}

    <tr class="header">
      <th colspan="2">
        <img src="{{ logoUrl }}{{ teamId2 }}.png"><em>{{ teamName2 }}</em> <span>{{ type ? 'AWAY' | trans : '' }}</span>
      </th>
      <th>{{ 'HT' | trans }}</th>
      <th>{{ 'FIN' | trans }}</th>
      <th>&nbsp;</th>
    </tr>

    {% for teamD2 in data2 %}
    <tr>
      <td class="date">{{ teamD2.matchDateTime }}</td>
      <td>{{ teamD2.matchName|replace({ (teamName2): '<b>' ~ teamName2 ~ '</b>'})|raw }}</td>
      <td class="sc hf">{{ teamD2.htScore }}</td>
      <td class="sc">{{ teamD2.ftScore }}</td>
      <td class="form"><span class="f {{ teamD2.matchForm }}">{{ teamD2.matchForm|replace({'w': 'W' | trans, 'd': 'D' | trans, 'l': 'L' | trans}) }}</span><span class="f {{ teamD2.overUnder }}">{{ teamD2.overUnder|replace({'o': 'O', 'u': 'U'}) }}</span></td>
    </tr>
    {% endfor %}

  </tbody>
</table>
