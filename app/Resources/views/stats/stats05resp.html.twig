 <div class="info_box table-stats table-tabs-container bordered is-portrait stripped" id="stats05">
   <h3 class="headline headline--arrow headline--navy headline--nospace headline__arrow headline--darkblue headline__arrow-nospace">{{ 'HALF-TIMES' | trans }}/{{ 'FULL-TIMES' | trans }}</h3>

   <div class="tabs">
     <ul>
       <li data-tab="ha" class="active">{{ 'HOME' | trans }}/{{ 'AWAY' | trans }}</li>
     </ul>
   </div>

   <div class="tabs-content">
     <div class="ha active">
       <table>
         <thead>
           <tr class="header">
             <th>&nbsp;</th>
             <th class="team">
               <em>{{ homeTeamName }}</em> <span>({{ 'home' | trans }})</span>
             </th>
             <th class="team">
               <em>{{ awayTeamName }}</em> <span>({{ 'away' | trans }})</span>
             </th>
           </tr>
         </thead>
         <tbody>
           <tr>
             <td>{{ 'MP.' | trans }}</td>
             <td>{{ data.ha[0].played }}</td>
             <td>{{ data.ha[1].played }}</td>
           </tr>
           <tr>
             <td>1/1</td>
             <td>{{ data.ha[0].winWin }}</td>
             <td>{{ data.ha[1].winWin }}</td>
           </tr>
           <tr>
             <td>X/1</td>
             <td>{{ data.ha[0].drawWin }}</td>
             <td>{{ data.ha[1].drawWin }}</td>
           </tr>
           <tr>
             <td>2/1</td>
             <td>{{ data.ha[0].loseWin }}</td>
             <td>{{ data.ha[1].loseWin }}</td>
           </tr>
           <tr>
             <td>1/X</td>
             <td>{{ data.ha[0].winDraw }}</td>
             <td>{{ data.ha[1].winDraw }}</td>
           </tr>
           <tr>
             <td>X/X</td>
             <td>{{ data.ha[0].drawDraw }}</td>
             <td>{{ data.ha[1].drawDraw }}</td>
           </tr>
           <tr>
             <td>2/X</td>
             <td>{{ data.ha[0].loseDraw }}</td>
             <td>{{ data.ha[1].loseDraw }}</td>
           </tr>
           <tr>
             <td>1/2</td>
             <td>{{ data.ha[0].winLose }}</td>
             <td>{{ data.ha[1].winLose }}</td>
           </tr>
           <tr>
             <td>X/2</td>
             <td>{{ data.ha[0].drawLose }}</td>
             <td>{{ data.ha[1].drawLose }}</td>
           </tr>
           <tr>
             <td>2/2</td>
             <td>{{ data.ha[0].loseLose }}</td>
             <td>{{ data.ha[1].loseLose }}</td>
           </tr>
         </tbody>
       </table>
     </div>

   </div>
 </div><!-- table-tabs-container -->
