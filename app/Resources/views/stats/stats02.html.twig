<div class="info_box table-stats table-tabs-container stripped bordered aa-table" id="stats02">
  <h3 class="headline headline--arrow headline--navy headline--nospace headline__arrow headline--darkblue headline__arrow-nospace">{{ 'LATEST MATCHES' | trans }}</h3>

  <div class="tabs">
    <ul>
      <li data-tab="ha" class="active">{{ 'HOME' | trans }}/{{ 'AWAY' | trans }}</li>
      <li data-tab="all">{{ 'TOTAL' | trans }}</li>
    </ul>
    <ul>
      <li data-tab="n6" class="active">6</li>
      <li data-tab="n10">10</li>
    </ul>
  </div>

  <div class="tabs-content">
    <div class="han6 active">
      {% include '::stats/stats02base.html.twig' with {'type': true, 'team1': team1Data, 'team2': team2Data, 'teamId1': homeTeam.id, 'teamName1': homeTeam.name, 'teamId2': awayTeam.id, 'teamName2':awayTeam.name, 'slice': true, 'calcType': 'single'} %}
    </div>
    <div class="alln6">
      {% include '::stats/stats02base.html.twig' with {'type': false, 'team1': team1Data, 'team2': team2Data, 'teamId1': homeTeam.id, 'teamName1': homeTeam.name, 'teamId2': awayTeam.id, 'teamName2':awayTeam.name, 'slice': true, 'calcType': 'all'} %}
    </div>
    <div class="han10">
      {% include '::stats/stats02base.html.twig' with {'type': true, 'team1': team1Data, 'team2': team2Data, 'teamId1': homeTeam.id, 'teamName1': homeTeam.name, 'teamId2': awayTeam.id, 'teamName2':awayTeam.name, 'slice': false, 'calcType': 'single'} %}
    </div>
    <div class="alln10">
      {% include '::stats/stats02base.html.twig' with {'type': false, 'team1': team1Data, 'team2': team2Data, 'teamId1': homeTeam.id, 'teamName1': homeTeam.name, 'teamId2': awayTeam.id, 'teamName2':awayTeam.name, 'slice': false, 'calcType': 'all'} %}
    </div>
  </div>

</div><!-- table-tabs-container -->
