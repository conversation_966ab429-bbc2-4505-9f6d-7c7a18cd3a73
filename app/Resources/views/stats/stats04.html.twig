<div class="info_box table-stats table-tabs-container bordered {{ isPortrait ? 'is-portrait stripped' : '' }}" id="stats04">
  <h3 class="headline headline--arrow headline--navy headline--nospace headline__arrow headline--darkblue headline__arrow-nospace">{{ 'GOALS AND HALF TIMES' | trans | raw }}</h3>

  <div class="tabs">
    <ul>
      <li data-tab="ha" class="active">{{ 'HOME' | trans }}/{{ 'AWAY' | trans }}</li>
      <li data-tab="t">{{ 'TOTAL' | trans }}</li>
    </ul>
  </div>

  <div class="tabs-content">
    {% set templateName = isPortrait ? '::stats/stats04baseresp.html.twig' : '::stats/stats04base.html.twig' %}
    {% include templateName with {'data': data.ha, 'ha': true, 'divClass': 'ha active'} %}
    {% include templateName with {'data': data.t, 'ha': false, 'divClass': 't'} %}
  </div>
</div><!-- table-tabs-container -->
