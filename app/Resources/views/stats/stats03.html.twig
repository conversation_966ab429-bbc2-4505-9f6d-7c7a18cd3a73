<div class="info_box table-stats table-tabs-container bordered {{ isPortrait ? 'is-portrait' : '' }}" id="stats03">
  <h3 class="headline headline--arrow headline--navy headline--nospace headline__arrow headline--darkblue headline__arrow-nospace">OVER/UNDER</h3>

  <div class="tabs">
    <ul>
      <li data-tab="ou25" class="active">2.5</li>
      <li data-tab="ou15">1.5</li>
      <li data-tab="ou35">3.5</li>
    </ul>
    <ul>
      <li data-tab="f" class="active">{{ 'FT' | trans }}</li>
      <li data-tab="fh">{{ '1st H' | trans }}</li>
      <li data-tab="sh">{{ '2nd H' | trans }}</li>
    </ul>
  </div>

  <div class="tabs-content">
    {% set templateName = isPortrait ? '::stats/stats03baseresp.html.twig' : '::stats/stats03base.html.twig' %}
    {% include templateName with {'data': data.ou25f, 'divClass': 'ou25f active'} %}
    {% include templateName with {'data': data.ou15f, 'divClass': 'ou15f'} %}
    {% include templateName with {'data': data.ou35f, 'divClass': 'ou35f'} %}
    {% include templateName with {'data': data.ou25fh, 'divClass': 'ou25fh'} %}
    {% include templateName with {'data': data.ou15fh, 'divClass': 'ou15fh'} %}
    {% include templateName with {'data': data.ou35fh, 'divClass': 'ou35fh'} %}
    {% include templateName with {'data': data.ou25sh, 'divClass': 'ou25sh'} %}
    {% include templateName with {'data': data.ou15sh, 'divClass': 'ou15sh'} %}
    {% include templateName with {'data': data.ou35sh, 'divClass': 'ou35sh'} %}
  </div>
</div><!-- table-tabs-container -->
