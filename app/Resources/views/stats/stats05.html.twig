<div class="info_box table-stats table-tabs-container bordered" id="stats05">
  <h3 class="headline headline--arrow headline--navy headline--nospace headline__arrow headline--darkblue headline__arrow-nospace">{{ 'HALF-TIMES' | trans }}/{{ 'FULL-TIMES' | trans }}</h3>
  
  <div class="tabs">
    <ul>
      <li data-tab="ha" class="active">{{ 'HOME' | trans }}/{{ 'AWAY' | trans }}</li>
    </ul>
  </div>

  <div class="tabs-content">
    <div class="ha active">
      <table>
        <thead>
          <tr class="subheader">
            <th>{{ 'TEAM' | trans }}</th>
            <th>{{ 'MP.' | trans }}</th>
            <th>1/1</th>
            <th>X/1</th>
            <th>2/1</th>
            <th>1/X</th>
            <th>X/X</th>
            <th>2/X</th>
            <th>1/2</th>
            <th>X/2</th>
            <th>2/2</th>
          </tr>
        </thead>
        <tbody>
          {% for single in data.ha %}
          <tr>
            <td class="team label">
              <img src="{{ logoUrl }}{{ single.teamId }}.png">
               <em>{{ single.teamName }}</em> {{ (("0" == single.orderBy) ? "<span>( " ~ ('home' | trans ) ~ " )</span>" : "<span>( " ~ ('away' | trans) ~ " )</span>") | raw }} 
            </td>
            <td>{{ single.played }}</td>
            <td>{{ single.winWin }}</td>
            <td>{{ single.drawWin }}</td>
            <td>{{ single.loseWin }}</td>
            <td>{{ single.winDraw }}</td>
            <td>{{ single.drawDraw }}</td>
            <td>{{ single.loseDraw }}</td>
            <td>{{ single.winLose }}</td>
            <td>{{ single.drawLose }}</td>
            <td>{{ single.loseLose }}</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

  </div>
</div><!-- table-tabs-container -->
