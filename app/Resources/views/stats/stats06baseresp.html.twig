<div class="{{ divClass }}">
  <table>
    <thead>
      <tr class="subheader">
        <th class="triple_no">{{ 'TIMEFRAME' | trans }}</th>
        <th>{{ homeTeamName }}</th>
        <th>{{ awayTeamName }}</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>{{ homeTeamName }}</td>
      </tr>
      {% for single in data %}
      <tr>
        <td>{{ single.label }}</td>
        <td>
          <div class="bar">
            <span style="width: {{ single.goals1p }}%;"></span>
          </div>
          <div class="legend">{{ single.goals1 }}</div>
        </td>
        <td>
          <div class="bar">
            <span style="width: {{ single.goals2p }}%;"></span>
          </div>
          <div class="legend">{{ single.goals2 }}</div>
        </td>
      </tr>
      {% endfor %}
      <tr>
        <td>{{ awayTeamName }}</td>
      </tr>
      {% for single in data %}
      <tr>
        <td>{{ single.label }}</td>
        <td>
          <div class="bar">
            <span style="width: {{ single.goals1p }}%;"></span>
          </div>
          <div class="legend">{{ single.goals1 }}</div>
        </td>
        <td>
          <div class="bar">
            <span style="width: {{ single.goals2p }}%;"></span>
          </div>
          <div class="legend">{{ single.goals2 }}</div>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
