<div class="info_box table-stats table-tabs-container stripped bordered" id="stats01">
  <table>
    <tbody>
      <!-- first table -->
      <tr class="header">
        <th colspan="4" class="team">
          <img src="{{ logoUrl }}{{ team1Data.teamId }}.png">
          {{ team1Data.teamName }}
        </th>
      </tr>
      <tr class="subheader">
        <th>&nbsp;</th>
        <th>{{ 'TOTAL' | trans }}</th>
        <th>{{ 'HOME' | trans }}</th>
        <th>{{ 'AWAY' | trans }}</th>
      </tr>
      <tr>
        <td>
          {{ 'Standings Position' | trans }}
        </td>
        <td>{{ team1Data.rank }}</td><td colspan="2">&nbsp;</td>
      </tr>
      <tr>
        <td>
          {{ 'Points' | trans }}
        </td>
        <td>{{ team1Data.tPoints }}</td><td>{{ team1Data.hPoints }}</td><td>{{ team1Data.aPoints }}</td>
      </tr>
      <tr>
        <td>
          {{ 'Played' | trans }}
        </td>
        <td>{{ team1Data.tPlayed }}</td><td>{{ team1Data.hPlayed }}</td><td>{{ team1Data.aPlayed }}</td>
      </tr>
      <tr>
        <td>
          {{ 'Wins' | trans }}
        </td>
        <td>{{ team1Data.tWins }}</td><td>{{ team1Data.hWins }}</td><td>{{ team1Data.aWins }}</td>
      </tr>
      <tr>
        <td>
          {{ 'Draws' | trans }}
        </td>
        <td>{{ team1Data.tDraws }}</td><td>{{ team1Data.hDraws }}</td><td>{{ team1Data.aDraws }}</td>
      </tr>
      <tr>
        <td>
          {{ 'Loses' | trans }}
        </td>
        <td>{{ team1Data.tLoses }}</td><td>{{ team1Data.hLoses }}</td><td>{{ team1Data.aLoses }}</td>
      </tr>
      <tr>
        <td>
          {{ 'Goals For' | trans }}
        </td>
        <td>{{ team1Data.tGoalsFor }}</td><td>{{ team1Data.hGoalsFor }}</td><td>{{ team1Data.aGoalsFor }}</td>
      </tr>
      <tr>
        <td>
          {{ 'Goals Against' | trans }}
        </td>
        <td>{{ team1Data.tGoalsAgainst }}</td><td>{{ team1Data.hGoalsAgainst }}</td><td>{{ team1Data.aGoalsAgainst }}</td>
      </tr>
      <tr>
        <td>
          {{ 'M.V. goals for / match' | trans }}
        </td>
        <td>{{ team1Data.tGoalsForMV }}</td><td>{{ team1Data.hGoalsForMV }}</td><td>{{ team1Data.aGoalsForMV }}</td>
      </tr>
      <tr>
        <td>
          {{ 'M.V. goals against / match' | trans }}
        </td>
        <td>{{ team1Data.tGoalsAgainstMV }}</td><td>{{ team1Data.hGoalsAgainstMV }}</td><td>{{ team1Data.aGoalsAgainstMV }}</td>
      </tr>
      <!-- second table -->
      <tr class="header">
        <th colspan="4" class="team">
          <img src="{{ logoUrl }}{{ team2Data.teamId }}.png">
          {{ team2Data.teamName }}
        </th>
      </tr>
      <tr class="subheader">
        <th>&nbsp;</th>
        <th>{{ 'TOTAL' | trans }}</th>
        <th>{{ 'HOME' | trans }}</th>
        <th>{{ 'AWAY' | trans }}</th>
      </tr>
      <tr>
        <td>
          {{ 'Standings Position' | trans }}
        </td>
        <td>{{ team2Data.rank }}</td><td colspan="2">&nbsp;</td>
      </tr>
      <tr>
        <td>
          {{ 'Points' | trans }}
        </td>
        <td>{{ team2Data.tPoints }}</td><td>{{ team2Data.hPoints }}</td><td>{{ team2Data.aPoints }}</td>
      </tr>
      <tr>
        <td>
          {{ 'Played' | trans }}
        </td>
        <td>{{ team2Data.tPlayed }}</td><td>{{ team2Data.hPlayed }}</td><td>{{ team2Data.aPlayed }}</td>
      </tr>
      <tr>
        <td>
          {{ 'Wins' | trans }}
        </td>
        <td>{{ team2Data.tWins }}</td><td>{{ team2Data.hWins }}</td><td>{{ team2Data.aWins }}</td>
      </tr>
      <tr>
        <td>
          {{ 'Draws' | trans }}
        </td>
        <td>{{ team2Data.tDraws }}</td><td>{{ team2Data.hDraws }}</td><td>{{ team2Data.aDraws }}</td>
      </tr>
      <tr>
        <td>
          {{ 'Loses' | trans }}
        </td>
        <td>{{ team2Data.tLoses }}</td><td>{{ team2Data.hLoses }}</td><td>{{ team2Data.aLoses }}</td>
      </tr>
      <tr>
        <td>
          {{ 'Goals For' | trans }}
        </td>
        <td>{{ team2Data.tGoalsFor }}</td><td>{{ team2Data.hGoalsFor }}</td><td>{{ team2Data.aGoalsFor }}</td>
      </tr>
      <tr>
        <td>
          {{ 'Goals Against' | trans }}
        </td>
        <td>{{ team2Data.tGoalsAgainst }}</td><td>{{ team2Data.hGoalsAgainst }}</td><td>{{ team2Data.aGoalsAgainst }}</td>
      </tr>
      <tr>
        <td>
          {{ 'M.V. goals for / match' | trans }}
        </td>
        <td>{{ team2Data.tGoalsForMV }}</td><td>{{ team2Data.hGoalsForMV }}</td><td>{{ team2Data.aGoalsForMV }}</td>
      </tr>
      <tr>
        <td>
          {{ 'M.V. goals against / match' | trans }}
        </td>
        <td>{{ team2Data.tGoalsAgainstMV }}</td><td>{{ team2Data.hGoalsAgainstMV }}</td><td>{{ team2Data.aGoalsAgainstMV }}</td>
      </tr>
    </tbody>
  </table>
</div>
