<div class="info_box table-stats table-tabs-container stripped bordered {{ isPortrait ? 'is-portrait' : '' }}" id="stats08">
  <h3 class="headline headline--arrow headline--navy headline--nospace headline__arrow headline--darkblue headline__arrow-nospace">{{ 'STREAKS' | trans }}</h3>

  <table>
    <thead>
      <tr class="header">
        <th>&nbsp;</th>
        <th colspan="2">
          {% if not isPortrait %}
          <img src="{{ logoUrl }}{{ homeTeam.id }}.png">
          {% endif %}
          <em>{{ homeTeam.name }}</em>
        </th>
        <th colspan="2">
          {% if not isPortrait %}
          <img src="{{ logoUrl }}{{ awayTeam.id }}.png">
          {% endif %}
          <em>{{ awayTeam.name }}</em>
        </th>
      </tr>
      <tr class="subheader">
        <th>&nbsp;</th>
        <th>{{ 'HOME' | trans }}</th>
        <th>{{ 'IN TOTAL' | trans }}</th>
        <th>{{ 'AWAY' | trans }}</th>
        <th>{{ 'IN TOTAL' | trans }}</th>
      </tr>
    </thead>
    <tbody>
      {% for single in data %}
      <tr>
        <td>{{ single.label }}</td>
        <td>{{ single.home }}</td>
        <td>{{ single.htotal }}</td>
        <td>{{ single.away }}</td>
        <td>{{ single.atotal }}</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
