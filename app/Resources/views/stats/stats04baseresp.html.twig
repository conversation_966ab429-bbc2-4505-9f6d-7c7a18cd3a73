<div class="{{ divClass }}">
  <table>
    <thead>
      <tr class="header">
        <th>&nbsp;</th>
        <th class="team">
          <em>{{ homeTeamName }}</em> {{ (ha) ? ("<span>( " ~ ('home' | trans) ~ " )</span>") | raw : "" }}
        </th>
        <th class="team">
          <em>{{ awayTeamName }}</em> {{ (ha) ? ("<span>( " ~ ('away' | trans) ~ " )</span>") | raw : "" }}
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>{{ 'MP.' | trans }}</td>
        <td>{{ data[0].played }}</td>
        <td>{{ data[1].played }}</td>
      </tr>
      <tr>
        <td>GG</td>
        <td>{{ data[0].goalGoal }}</td>
        <td>{{ data[1].goalGoal }}</td>
      </tr>
      <tr>
        <td>NG</td>
        <td>{{ data[0].noGoal }}</td>
        <td>{{ data[1].noGoal }}</td>
      </tr>
      <tr>
        <td>0-1</td>
        <td>{{ data[0].goals01 }}</td>
        <td>{{ data[1].goals01 }}</td>
      </tr>
      <tr>
        <td>2-3</td>
        <td>{{ data[0].goals23 }}</td>
        <td>{{ data[1].goals23 }}</td>
      </tr>
      <tr>
        <td>4-6</td>
        <td>{{ data[0].goals46 }}</td>
        <td>{{ data[1].goals46 }}</td>
      </tr>
      <tr>
        <td>7+</td>
        <td>{{ data[0].goals7 }}</td>
        <td>{{ data[1].goals7 }}</td>
      </tr>
      <tr>
        <td>1 {{ 'HT' | trans }}</td>
        <td>{{ data[0].winsHt }}</td>
        <td>{{ data[1].winsHt }}</td>
      </tr>
      <tr>
        <td>Χ {{ 'HT' | trans }}</td>
        <td>{{ data[0].drawsHt }}</td>
        <td>{{ data[1].drawsHt }}</td>
      </tr>
      <tr>
        <td>2 {{ 'HT' | trans }}</td>
        <td>{{ data[0].losesHt }}</td>
        <td>{{ data[1].losesHt }}</td>
      </tr>
    </tbody>
  </table>
</div>
