{% extends 'base.html.twig' %}

{% block title %}Show FtLeagueStandingsOption{% endblock %}

{% block body %}
    <h1>FtLeagueStandingsOption</h1>

    <table class="table table-stripped">
        <tbody>
            <tr>
                <th>Id</th>
                <td>{{ ftLeagueStandingsOption.id }}</td>
            </tr>
            <tr>
                <th>NameEn</th>
                <td>{{ ftLeagueStandingsOption.nameEn }}</td>
            </tr>
            <tr>
                <th>NameEl</th>
                <td>{{ ftLeagueStandingsOption.nameEl }}</td>
            </tr>
            <tr>
                <th>CreatedAt</th>
                <td>{% if ftLeagueStandingsOption.createdAt %}{{ ftLeagueStandingsOption.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
            <tr>
                <th>UpdatedAt</th>
                <td>{% if ftLeagueStandingsOption.updatedAt %}{{ ftLeagueStandingsOption.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
        </tbody>
    </table>

    <div class="pb-50">
        <a href="{{ path('ftleaguestandingsoption_edit', { 'id': ftLeagueStandingsOption.id }) }}" class="btn btn-default btn-info pull-left mr-20">Edit FtLeagueStandingsOption</a>
        <a href="{{ path('ftleaguestandingsoption_index') }}" class="btn btn-default pull-left">Back to the list</a>
        {{ form_start(delete_form) }}
            <input type="submit" value="Delete" class="btn btn-danger pull-right">
        {{ form_end(delete_form) }}
    </div>
{% endblock %}
