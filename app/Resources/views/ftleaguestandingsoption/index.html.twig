{% extends 'base.html.twig' %}

{% block title %}FtLeagueStandingsOption List{% endblock %}

{% block body %}
    <h1>FtLeagueStandingsOption list</h1>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('ftleaguestandingsoption_new') }}" class="btn btn-primary">Create New FtLeagueStandingsOption</a>
        </div>
    </div>

    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>Id</th>
                <th>NameEn</th>
                <th>NameEl</th>
                <th>CreatedAt</th>
                <th>UpdatedAt</th>
            </tr>
        </thead>
        <tbody>
        {% for ftLeagueStandingsOption in ftLeagueStandingsOptions %}
            <tr>
                <td><a href="{{ path('ftleaguestandingsoption_show', { 'id': ftLeagueStandingsOption.id }) }}">{{ ftLeagueStandingsOption.id }}</a></td>
                <td>{{ ftLeagueStandingsOption.nameEn }}</td>
                <td>{{ ftLeagueStandingsOption.nameEl }}</td>
                <td>{% if ftLeagueStandingsOption.createdAt %}{{ ftLeagueStandingsOption.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
                <td>{% if ftLeagueStandingsOption.updatedAt %}{{ ftLeagueStandingsOption.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('ftleaguestandingsoption_new') }}" class="btn btn-primary">Create New FtLeagueStandingsOption</a>
        </div>
    </div>
{% endblock %}
