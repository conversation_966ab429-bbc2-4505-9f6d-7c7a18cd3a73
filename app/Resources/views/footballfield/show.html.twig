{% extends 'base.html.twig' %}

{% block title %}Show Football Field{% endblock %}

{% block body %}
    <h1>Football Field</h1>

    <table class="table table-stripped">
        <tbody>
            <tr>
                <th>Id</th>
                <td>{{ footballField.id }}</td>
            </tr>
            <tr>
                <th>Name</th>
                <td>{{ footballField.name }}</td>
            </tr>
            <tr>
                <th>Weather Api Id</th>
                <td>{{ footballField.weatherApiId }}</td>
            </tr>
            <tr>
                <th>Created At</th>
                <td>{% if footballField.createdAt %}{{ footballField.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
            <tr>
                <th>Updated At</th>
                <td>{% if footballField.updatedAt %}{{ footballField.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
        </tbody>
    </table>

    <div class="pb-50">
        <a href="{{ path('football_field_edit', { 'id': footballField.id }) }}"class="btn btn-default btn-info pull-left mr-20">Edit Football Field</a>
        <a href="{{ path('football_field_index') }}" class="btn btn-default pull-left">Back to the list</a>
        {{ form_start(delete_form) }}
            <input type="submit" value="Delete" class="btn btn-danger pull-right">
        {{ form_end(delete_form) }}
    </div>
{% endblock %}
