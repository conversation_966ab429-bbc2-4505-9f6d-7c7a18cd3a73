{% extends 'base.html.twig' %}

{% block title %}Football Fields List{% endblock %}

{% block body %}
<h1>Football Field list</h1>

<div class="pb-50">
	<div class="pull-left">
		<a href="{{ path('football_field_new') }}" class="btn btn-primary">New Football Field</a>
	</div>
</div>

<table class="table table-striped table-hover">
	<thead>
		<tr>
			<th>Id</th>
			<th>Name</th>
			<th>Weather API Id</th>
			<th>Created At</th>
			<th>Updated At</th>
		</tr>
	</thead>
	<tbody>
	{% for footballField in footballFields %}
		<tr>
			<td><a href="{{ path('football_field_show', { 'id': footballField.id }) }}">{{ footballField.id }}</a></td>
			<td>{{ footballField.name }}</td>
			<td>{{ footballField.weatherApiId }}</td>
			<td>{% if footballField.createdAt %}{{ footballField.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
			<td>{% if footballField.updatedAt %}{{ footballField.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
		</tr>
	{% endfor %}
	</tbody>
</table>

<div class="pb-50">
	<div class="pull-left">
		<a href="{{ path('football_field_new') }}" class="btn btn-primary">New Football Field</a>
	</div>
</div>
{% endblock %}
