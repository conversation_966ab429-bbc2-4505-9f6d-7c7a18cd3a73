<table class="halfView info__table">
	<caption class="headline headline--arrow headline--navy headline--nospace headline__arrow headline--darkblue headline__arrow-nospace">{{ title }}</caption>
	<thead>
	</thead>
	<tbody>
		{% for single in data|slice(0, 15) %}
		<tr>
			<td>{% if imagePath is defined and single.image_name is defined %}<img src="{{ imagePath ~ single.image_name }}.svg" alt="{{ 'Previews for' | trans }} {{ single.title }}">{% endif %}</td>
			<td class="key-namegr"><a href="{{ domainUrl ~ single.slug }}/" title="{{ 'Previews for' | trans }} {{ single.title }}">{{ team ? single.team_name : single.title }}</a></td>
			<td>{{ attribute(single, field) }}{{ total ? ' (' ~ attribute(single, totalField) ~')' : totalSymbol }}</td>
		</tr>
		{% endfor %}
	</tbody>
</table>