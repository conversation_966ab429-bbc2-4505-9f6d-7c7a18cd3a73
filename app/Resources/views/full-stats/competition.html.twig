<div id="standings-half_table-container">
<table class="full info__table" id="competition">
	<caption class="headline headline--arrow headline--navy headline--nospace headline__arrow headline--darkblue headline__arrow-nospace">{{ 'Competition Statistics' | trans }}</caption>
	<thead>
		<th></th>
		<th>{{ 'COMPETITION' | trans }}</th>
		<th>1</th>
		<th>Χ</th>
		<th>2</th>
		<th>Μ.Ο.</th>
		<th>N/G</th>
		<th>G/G</th>
		<th>UNDER</th>
		<th>OVER</th>
	</thead>
	<tbody>
		{% for single in data %}
		<tr>
			<td>
				{% if imagePath is defined and single.image_name is defined %}
				<img src="{{ imagePath ~ single.image_name }}.svg" alt="Αναλύσεις {{ single.title }}">
				{% endif %}
			</td>
			<td class="key-namegr"><a href="https://www.betarades.gr/{{ single.slug }}/" title="Αναλύσεις {{ single.title }}">{{ single.title }}</a></td>
			<td>{{ single.wins_perc }}%</td>
			<td>{{ single.draws_perc }}%</td>
			<td>{{ single.loses_perc }}%</td>
			<td>{{ single.goals_mv }}</td>
			<td>{{ single.ng_perc }}%</td>
			<td>{{ single.gg_perc }}%</td>
			<td>{{ single.under_perc }}%</td>
			<td>{{ single.over_perc }}%</td>
		</tr>
		{% endfor %}
	</tbody>
</table>
<span>{{ 'Calculated for competitions with at least 30 matches played' | trans }}</span>
</div>