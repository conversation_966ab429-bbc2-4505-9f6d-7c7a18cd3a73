<table class="full info__table">
	<caption class="headline headline--arrow headline--navy headline--nospace headline__arrow headline--darkblue headline__arrow-nospace">{{ title }}</caption>
	<thead>
		<th></th>
		<th>{{ 'COMP.' | trans }}</th>
		<th>{{ 'TEAM' | trans }}</th>
		<th>{{ label1 }}</th>
		<th>{{ label2 }}</th>
		<th>&#37;</th>
	</thead>
	<tbody>
		{% for single in data|slice(0, 15) %}
		<tr>
			<td>{{ loop.index }}</td>
			<td>{% if imagePath is defined and single.image_name is defined %}<img src="{{ imagePath ~ single.image_name }}.svg" alt="{{ 'Previews for' | trans }} {{ single.title }}">{% endif %}</td>
			<td class="key-namegr"><a href="{{ domainUrl ~ single.slug }}/" title="{{ 'Previews for' | trans }} {{ single.title }}">{{ single.team_name }}</a></td>
			<td>{{ attribute(single, field) }}</td>
			<td>{{ attribute(single, totalField) }}</td>
			<td>{{ attribute(single, percField) }}%</td>
		</tr>
		{% endfor %}
	</tbody>
</table>