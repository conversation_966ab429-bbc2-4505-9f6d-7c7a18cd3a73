<div id="standings-half_table-container">
{% set labelWins %}{{ 'WINS' | trans }}{% endset %}
{% set labelDraws %}{{ 'DRAWS' | trans }}{% endset %}
{% set labelLoses %}{{ 'LOSES' | trans }}{% endset %}
{% set labelPlayed %}{{ 'PLAYED' | trans }}{% endset %}
{# Team Wins #}
{% set tempTitle %}{{ 'Most Wins' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.wins, 'label1': labelWins, 'label2': labelPlayed, 'field': 'wins', 'percField': 'wins_perc', 'totalField': 'total'} %}

{# Team Draws #}
{% set tempTitle %}{{ 'Most Draws' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.draws, 'label1': labelDraws, 'label2': labelPlayed, 'field': 'draws', 'percField': 'draws_perc', 'totalField': 'total'} %}

{# Team Loses #}
{% set tempTitle %}{{ 'Most Loses' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.loses, 'label1': labelLoses, 'label2': labelPlayed, 'field': 'loses', 'percField': 'loses_perc', 'totalField': 'total'} %}

{# Team Wins Home #}
{% set tempTitle %}{{ 'Most Wins (Home)' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.hWins, 'label1': labelWins, 'label2': labelPlayed, 'field': 'h_wins', 'percField': 'h_wins_perc', 'totalField': 'h_played'} %}

{# Team Draws Home #}
{% set tempTitle %}{{ 'Most Draws (Home)' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.hDraws, 'label1': labelDraws, 'label2': labelPlayed, 'field': 'h_draws', 'percField': 'h_draws_perc', 'totalField': 'h_played'} %}

{# Team Loses Home #}
{% set tempTitle %}{{ 'Most Loses (Home)' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.hLoses, 'label1': labelLoses, 'label2': labelPlayed, 'field': 'h_loses', 'percField': 'h_loses_perc', 'totalField': 'h_played'} %}

{# Team Wins Away #}
{% set tempTitle %}{{ 'Most Wins (Away)' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.aWins, 'label1': labelWins, 'label2': labelPlayed, 'field': 'a_wins', 'percField': 'a_wins_perc', 'totalField': 'a_played'} %}

{# Team Draws Away #}
{% set tempTitle %}{{ 'Most Draws (Away)' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.aDraws, 'label1': labelDraws, 'label2': labelPlayed, 'field': 'a_draws', 'percField': 'a_draws_perc', 'totalField': 'a_played'} %}

{# Team Loses Away #}
{% set tempTitle %}{{ 'Most Loses (Away)' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.aLoses, 'label1': labelLoses, 'label2': labelPlayed, 'field': 'a_loses', 'percField': 'a_loses_perc', 'totalField': 'a_played'} %}
<span>{{ 'Calculated for teams with at least 10 matches played' | trans }}</span>
</div>