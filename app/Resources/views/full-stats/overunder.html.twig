<div id="standings-half_table-container">
{# Sport Over Under #}
{% set tempTitle %}{{ 'Competitions with most Under' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': data.under25, 'field': 'under_25_perc', 'total': false, 'team': false, 'totalSymbol': '%'} %}
{% set tempTitle %}{{ 'Competitions with most Over' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': data.over25, 'field': 'over_25_perc', 'total': false, 'team': false, 'totalSymbol': '%'} %}

{# Team Over Under #}
{% set tempTitle %}{{ 'Teams with most Under' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.under25, 'field': 'under_25', 'total': true, 'team': true, 'totalField': 'total'} %}
{% set tempTitle %}{{ 'Teams with most Over' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.over25, 'field': 'over_25', 'total': true, 'team': true, 'totalField': 'total'} %}

{# Team Under Home/Away Streak #}
{% set tempTitle %}{{ 'Home Under Streak' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.hUnder25Streak, 'field': 'h_under_25_streak', 'total': true, 'team': true, 'totalField': 'h_played'} %}
{% set tempTitle %}{{ 'Away Under Streak' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.aUnder25Streak, 'field': 'a_under_25_streak', 'total': true, 'team': true, 'totalField': 'a_played'} %}

{# Team Over Home/Away Streak #}
{% set tempTitle %}{{ 'Home Over Streak' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.hOver25Streak, 'field': 'h_over_25_streak', 'total': true, 'team': true, 'totalField': 'h_played'} %}
{% set tempTitle %}{{ 'Away Over Streak' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.aOver25Streak, 'field': 'a_over_25_streak', 'total': true, 'team': true, 'totalField': 'a_played'} %}

{# Team Under/Over Total Streak #}
{% set tempTitle %}{{ 'Home/Away Under Streak' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.tUnder25Streak, 'field': 'under_25_streak', 'total': true, 'team': true, 'totalField': 'total'} %}
{% set tempTitle %}{{ 'Home/Away Over Streak' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.tOver25Streak, 'field': 'over_25_streak', 'total': true, 'team': true, 'totalField': 'total'} %}
<span>{{ 'Calculated for competitions with at least 30 matches played' | trans }}</span>
<span>{{ 'Calculated for teams with at least 10 matches played' | trans }}</span>
</div>