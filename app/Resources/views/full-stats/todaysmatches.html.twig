<div id="standings-half_table-container">
<table class="full info__table" id="todays-matches">
	<caption class="headline headline--arrow headline--navy headline--nospace headline__arrow headline--darkblue headline__arrow-nospace">Με μια ματιά</caption>
	<tbody>
		{% for single in data %}
			<tr class="subheader">
				<td class="key-namegr">
					<a href="https://www.betarades.gr/{{ single.competition.slug }}/" title="Αναλύσεις {{ single.competition.name }}">
						{% if imagePath is defined and single.competition.image is defined %}
						<img src="{{ imagePath ~ single.competition.image }}.svg" alt="Αναλύσεις {{ single.competition.name }}">
						{% endif %}
						{{ single.competition.region }} - {{ single.competition.name }}
					</a>
				</td>
				<td>Β.</td>
				<td>ΑΓ.</td>
				<td>Ν</td>
				<td>Ι</td>
				<td>Η</td>
				<td>U2.5</td>
				<td>O2.5</td>
				<td>0-1</td>
				<td>2-3</td>
				<td>4-6</td>
				<td>7+</td>
				<td>N/G</td>
				<td>G/G</td>
			</tr>
			{% for match in single.matches %}
				<tr>
					<td class="key-namegr">{% if match.preview_slug is defined and match.preview_slug is not null %}<a href="https://www.betarades.gr/{{ match.preview_slug }}/" title="Διαβάστε την ανάλυση για {{ match.home.name }} - {{ match.away.name }}">{{ match.home.name }}</a>{% else %}{{ match.home.name }}{% endif %}</td>
					<td class="points">{{ match.home.stats.overall_pts }}</td>
					<td>{{ match.home.stats.total_played }}</td>
					<td>{{ match.home.stats.total_wins }}</td>
					<td>{{ match.home.stats.total_draws }}</td>
					<td>{{ match.home.stats.total_loses }}</td>
					<td>{{ match.home.stats.total_under }}</td>
					<td>{{ match.home.stats.total_over }}</td>
					<td>{{ match.home.stats.total_goals_01 }}</td>
					<td>{{ match.home.stats.total_goals_23 }}</td>
					<td>{{ match.home.stats.total_goals_46 }}</td>
					<td>{{ match.home.stats.total_goals_7 }}</td>
					<td>{{ match.home.stats.total_no_goal }}</td>
					<td>{{ match.home.stats.total_goal_goal }}</td>
				</tr>
				<tr class="separator">
					<td class="key-namegr">{% if match.preview_slug is defined and match.preview_slug is not null %}<a href="https://www.betarades.gr/{{ match.preview_slug }}/" title="Διαβάστε την ανάλυση για {{ match.home.name }} - {{ match.away.name }}">{{ match.away.name }}</a>{% else %}{{ match.away.name }}{% endif %}</td>
					<td class="points">{{ match.away.stats.overall_pts }}</td>
					<td>{{ match.away.stats.total_played }}</td>
					<td>{{ match.away.stats.total_wins }}</td>
					<td>{{ match.away.stats.total_draws }}</td>
					<td>{{ match.away.stats.total_loses }}</td>
					<td>{{ match.away.stats.total_under }}</td>
					<td>{{ match.away.stats.total_over }}</td>
					<td>{{ match.away.stats.total_goals_01 }}</td>
					<td>{{ match.away.stats.total_goals_23 }}</td>
					<td>{{ match.away.stats.total_goals_46 }}</td>
					<td>{{ match.away.stats.total_goals_7 }}</td>
					<td>{{ match.away.stats.total_no_goal }}</td>
					<td>{{ match.away.stats.total_goal_goal }}</td>
				</tr>
			{% endfor %}
		{% endfor %}
	</tbody>
</table>
</div>