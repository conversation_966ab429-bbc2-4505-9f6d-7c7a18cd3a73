<div id="standings-half_table-container">
{# Sport NoGoal GoalGoal #}
{% set tempTitle %}{{ 'Competitions with most N/G' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': data.noGoal, 'field': 'no_goal_perc', 'total': false, 'team': false, 'totalSymbol': '%'} %}
{% set tempTitle %}{{ 'Competitions with most G/G' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': data.goalGoal, 'field': 'goal_goal_perc', 'total': false, 'team': false, 'totalSymbol': '%'} %}

{# Team NG GG #}
{% set tempTitle %}{{ 'Teams with most N/G' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.noGoal, 'field': 'no_goal', 'total': true, 'team': true, 'totalField': 'total'} %}
{% set tempTitle %}{{ 'Teams with most G/G' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.goalGoal, 'field': 'goal_goal', 'total': true, 'team': true, 'totalField': 'total'} %}

{# Team NG Home/Away Streak #}
{% set tempTitle %}{{ 'Home N/G Streak' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.hNoGoalStreak, 'field': 'h_no_goal_streak', 'total': true, 'team': true, 'totalField': 'h_played'} %}
{% set tempTitle %}{{ 'Away N/G Streak' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.aNoGoalStreak, 'field': 'a_no_goal_streak', 'total': true, 'team': true, 'totalField': 'a_played'} %}

{# Team GG Home/Away Streak #}
{% set tempTitle %}{{ 'Home G/G Streak' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.hGoalGoalStreak, 'field': 'h_goal_goal_streak', 'total': true, 'team': true, 'totalField': 'h_played'} %}
{% set tempTitle %}{{ 'Away G/G Streak' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.aGoalGoalStreak, 'field': 'a_goal_goal_streak', 'total': true, 'team': true, 'totalField': 'a_played'} %}

{# Team NG/GG Total Streak #}
{% set tempTitle %}{{ 'Home/Away N/G Streak' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.tNoGoalStreak, 'field': 'no_goal_streak', 'total': true, 'team': true, 'totalField': 'total'} %}
{% set tempTitle %}{{ 'Home/Away G/G Streak' | trans }}{% endset %}
{% include '::full-stats/basehalf.html.twig' with {'title': tempTitle, 'data': teamData.tGoalGoalStreak, 'field': 'goal_goal_streak', 'total': true, 'team': true, 'totalField': 'total'} %}
<span>{{ 'Calculated for competitions with at least 30 matches played' | trans }}</span>
<span>{{ 'Calculated for teams with at least 10 matches played' | trans }}</span>
</div>