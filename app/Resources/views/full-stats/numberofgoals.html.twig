<div id="standings-half_table-container">
{% set labelTimes %}{{ 'TIMES' | trans }}{% endset %}
{% set labelPlayed %}{{ 'PLAYED' | trans }}{% endset %}
{# Team Goals 0-1 #}
{% set tempTitle %}{{ '0-1 Goal' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.goals01, 'label1': labelTimes, 'label2': labelPlayed, 'field': 'goals_01', 'percField': 'goals_01_perc', 'totalField': 'total'} %}

{# Team Goals 2-3 #}
{% set tempTitle %}{{ '2-3 Goal' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.goals23, 'label1': labelTimes, 'label2': labelPlayed, 'field': 'goals_23', 'percField': 'goals_23_perc', 'totalField': 'total'} %}

{# Team Goals 4-6 #}
{% set tempTitle %}{{ '4-6 Goal' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.goals46, 'label1': labelTimes, 'label2': labelPlayed, 'field': 'goals_46', 'percField': 'goals_46_perc', 'totalField': 'total'} %}

{# Team Goals 7+ #}
{% set tempTitle %}{{ '7+ Goal' | trans }}{% endset %}
{% include '::full-stats/basefull.html.twig' with {'title': tempTitle, 'data': teamData.goals7, 'label1': labelTimes, 'label2': labelPlayed, 'field': 'goals_7', 'percField': 'goals_7_perc', 'totalField': 'total'} %}
<span>{{ 'Calculated for teams with at least 10 matches played' | trans }}</span>
</div>