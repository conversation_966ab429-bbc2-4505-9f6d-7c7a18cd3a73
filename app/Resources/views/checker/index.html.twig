{% extends 'base.html.twig' %}

{% block title %}Tools - Checker{% endblock %}

{% block body %}
	<h2>Επέλεξε site</h2>
	{{ form_widget(form.domain) }}
	<input id="getSports" class="btn btn-default" type="button" value="Get Sports" />

	<div id="sports-container"></div>
	<div id="toolbar"><input id="generate-season" class="btn btn-info" type="button" value="Generate Season" /></div>
	<div id="messages"></div>
	<div id="fixtures-container"></div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script type="text/javascript">
	$(document).ready(function(){
		var $body 					= $('body'),
			$toolbar 					= $('#toolbar'),
			$generateSeason 	= $('#generate-season');

		var daysToCheck = 0;
		$("#getSports").click(function(){
			$body.addClass('is-loading');

			var domainId = $("#form_domain").val();
			var urlPath = Routing.generate('sport_get_football', { domainId: domainId });
			$.ajax({
				type: 'GET',
				// url: "{{ path('sport_get_football', { 'domainId': " + domainId + "}) }}",
				url: urlPath,
				success: function(data) {
					if (data) {
						var curParent = '';
						var html = '';

						html = '<select id="sport" multiple="multiple">';
						data.forEach(function(entry) {
							if (curParent !== entry.parent_sport) {
								if (curParent !== '') {
									html += '</optgroup>';
								}
								html += '<optgroup label="' + entry.parent_sport + '">';
								html += '<option value="' + entry.sport_id + '">' + entry.title + '</option>';
								curParent = entry.parent_sport;
							}
							else {
								html += '<option value="' + entry.sport_id + '">' + entry.title + '</option>';
//								html += '</optgroup>';
							}
						})
						html += '</select>';

						html += '\
							<div id="daySelector" class="radio">\
								<label class="radio-inline"><input type="radio" name="daysToCheck" value="1">Σήμερα</label>\
								<label class="radio-inline"><input type="radio" name="daysToCheck" value="2">Αύριο</label>\
								<label class="radio-inline"><input type="radio" name="daysToCheck" value="3">Μεθαύριο</label>\
							</div>\
						';

						html += '<input id="getMatches" class="btn btn-default" type="button" value="Get Matches" />';

						$body.removeClass('is-loading');
						$('#sports-container').html(html);
						$('#sport').hide();
						$('#sport').multiselect({
							buttonWidth: '100%',
							enableFiltering: true,
							enableClickableOptGroups: true,
/*							onChange: function(option, checked) {
								alert(option.length + ' options ' + (checked ? 'selected' : 'deselected' ));
							} */
						});
					}
				},
				error: function(xhr, ajaxOptions, thrownError) {
					$body.removeClass('is-loading');
					alert(xhr.responseText);
				},
				complete: function(req, res) {
					$body.removeClass('is-loading');
				}
			});
	});

	/**
	 * Generate Season Click
	 */
	$('#generate-season').on('click', function(event) {
		$body.addClass('is-loading');
		try {
			var urlPath = Routing.generate('checker_generateseason');
		}
		catch(err) {
			$body.removeClass('is-loading');
			alert(err.message);
		}

		$.ajax({
			type: 'GET',
			url: urlPath,
			success: function(data) {
				alert(data);
				$body.removeClass('is-loading');
				// console.log(sportIds);
			},
			error: function(xhr, ajaxOptions, thrownError) {
				$body.removeClass('is-loading');
				alert(xhr.responseText);
			},
			complete: function(req, res) {
				$body.removeClass('is-loading');
			}
		});
	});

	// event listener for dynamically created DOM elements
	document.addEventListener('change', function(event) {
		var targetElement = event.target || event.srcElement;
/*		if ('sport' === targetElement.id) {
				console.log(targetElement.value);
		}
		else */ 
		if ('daySelector' === targetElement.parentElement.parentElement.id) {
			daysToCheck = targetElement.value;
			// console.log("radio button clicked:: " + targetElement.value);
		}
	});

	document.addEventListener('click', function(event) {
		var targetElement = event.target || event.srcElement;
		if ('getMatches' === targetElement.id){
			var sportIds = getSelectedSportIds();

			// validate user choices
			if (!daysToCheck || !sportIds) {
				alert('Πρέπει να δηλώσεις και πρωτάθλημα και για πότε θέλεις να ψάξεις αγώνες!');
			}
			else {
				$body.addClass('is-loading');
				var domainId = $("#form_domain").val();
				var urlPath = Routing.generate('sport_get_fixtures', { 'domainId' : domainId, 'sportIds': sportIds, 'days': daysToCheck });
				$.ajax({
					type: 'GET',
					// url: "{{ path('sport_get_football', { 'domainId': " + domainId + "}) }}",
					url: urlPath,
					success: function(data) {
						var html = '';
						var leagueSeason = [];
						var missingSports = '';
						var wrongLeagueSeason = '';
						var showGenerateSeason = false;
						
						if (data.fixtures && 0 < data.fixtures.length) {

							// print all validation messages on screen
							if (data.validity) {
								for (var k in data.validity) {
									leagueSeason.push(data.validity[k].league + '__' + data.validity[k].season);
									html += data.validity[k].message;									
								}
/*								data.validity.forEach(function(entry) {
									leagueSeason.push(entry.league + '__' + entry.season);
									html += entry.message;
								}) */
							}

							html += "<table class='table table-striped table-hover'>";
							html += "\
							<thead class='thead-inverse'>\
								<th>FIXTURE ID</th>\
								<th>DATETIME</th>\
								<th>HOME</th>\
								<th>AWAY</th>\
								<th>LEAGUE</th>\
								<th>SEASON</th>\
								<th>SPORT</th>\
								<th>INFO</th>\
							</thead>\
							<tbody>\
							";
							data.fixtures.forEach(function(entry) {

								if (-1 === $.inArray(entry.league + '__' + entry.season, leagueSeason)) {
									//  must run wbs:generate:season and check again
									wrongLeagueSeason = 'Η σεζόν και η λίγκα πιθανώς δεν συμφωνούν με τα στοιχεία από Δεδούλη. ' + data.validity[entry.sportId].league + '__' + data.validity[entry.sportId].season;
									showGenerateSeason = true;
								}
								else {
									wrongLeagueSeason = '';
								}

								// remove sportIds with fixtures, to create a message with sportIds with no fixtures found within the date range of search
								if (-1 != $.inArray(entry.sportId, sportIds)) {
									var sportIndex = sportIds.indexOf(entry.sportId);
									if (sportIndex > -1) {
										sportIds.splice(sportIndex, 1);
									}
								}

								html += "\
									<tr " + ('' !== wrongLeagueSeason ? ' class="error-msg" ' : '' ) + "'>\
										<td>" + entry.fixtureId + "</td>\
										<td>" + entry.matchDateTime.slice(0, -3) + "</td>\
										<td>" + entry.homeTeamName + "</td>\
										<td>" + entry.awayTeamName + "</td>\
										<td>" + entry.league + "</td>\
										<td>" + entry.season + "</td>\
										<td>" + entry.sportId + "</td>\
										<td>" + wrongLeagueSeason + "</td>\
									</tr>\
								";
							})
							html += "</tbody></table>";
						}
						else {
							html += "<p class='notice'>Δεν βρέθηκαν αγώνες, με τα στοιχεία που έδωσες</p>";
						}

						$body.removeClass('is-loading');
						// console.log(sportIds);
						if (sportIds) {
							sportIds.forEach(function(entry) {
								$('#sport-'+entry).append('<p>Δεν βρέθηκαν αγώνες για ' + data.validity[entry].sport + '</p>');
								missingSports += '<p>Δεν βρέθηκαν αγώνες για ' + data.validity[entry].sport + '</p>';
							});
							$('#messages').html(missingSports);
						}

						if (showGenerateSeason) {
							$generateSeason.addClass('cv-show');
						}
						else {
							$generateSeason.removeClass('cv-show');
						}
						$('#fixtures-container').html(html);
					},
					error: function(xhr, ajaxOptions, thrownError) {
						$body.removeClass('is-loading');
						alert(xhr.responseText);
					},
					complete: function(req, res) {
						$body.removeClass('is-loading');
					}
				});
			}
		}
	});

	function getSelectedSportIds() {
		var selected = [];
		$('#sport option:selected').each(function() {
//				selected.push([$(this).val(), $(this).data('order')]);
// console.log('value is ::: ' + $(this).val());
			// selected.push([$(this).val()]);
			selected.push($(this).val());
		});

		// console.log(selected);
/*
		selected.sort(function(a, b) {
				return a[1] - b[1];
		});

		var text = '';
		for (var i = 0; i < selected.length; i++) {
				text += selected[i][0] + ',';
		}
		text = text.substring(0, text.length - 2);
*/
	return selected;
		// return text;
	}
});
</script>
{% endblock %}