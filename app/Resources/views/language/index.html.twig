{% extends 'base.html.twig' %}

{% block title %}Languages List{% endblock %}

{% block body %}
    <h1>Language list</h1>

    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>Id</th>
                <th>Name</th>
                <th>Locale</th>
                <th>Created At</th>
                <th>Updated At</th>
            </tr>
        </thead>
        <tbody>
        {% for language in languages %}
            <tr>
                <td><a href="{{ path('language_show', { 'id': language.id }) }}">{{ language.id }}</a></td>
                <td>{{ language.name }}</td>
                <td>{{ language.locale }}</td>
                <td>{% if language.createdAt %}{{ language.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
                <td>{% if language.updatedAt %}{{ language.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('language_new') }}" class="btn btn-primary pull-left mr-20">Create New Language</a>
        </div>
    </div>

{% endblock %}
