{% extends 'base.html.twig' %}

{% block title %}Show Language{% endblock %}

{% block body %}
    <h1>Language</h1>

    <table class="table table-stripped">
        <tbody>
            <tr>
                <th>Id</th>
                <td>{{ language.id }}</td>
            </tr>
            <tr>
                <th>Name</th>
                <td>{{ language.name }}</td>
            </tr>
            <tr>
                <th>Locale</th>
                <td>{{ language.locale }}</td>
            </tr>
            <tr>
                <th>CreatedAt</th>
                <td>{% if language.createdAt %}{{ language.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
            <tr>
                <th>UpdatedAt</th>
                <td>{% if language.updatedAt %}{{ language.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
        </tbody>
    </table>

    <div class="pb-50">
        <a href="{{ path('language_edit', { 'id': language.id }) }}" class="btn btn-default btn-info pull-left mr-20">Edit Language</a>
        <a href="{{ path('language_index') }}" class="btn btn-default pull-left">Back to the list</a>
        {{ form_start(delete_form) }}
                <input type="submit" value="Delete" class="btn btn-danger pull-right">
        {{ form_end(delete_form) }}
    </div>
{% endblock %}
