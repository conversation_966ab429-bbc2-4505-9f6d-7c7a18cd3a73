{% extends 'base.html.twig' %}

{% block title %}Show Odd Type{% endblock %}

{% block body %}
<h1>Odd Type</h1>

<table class="table table-stripped">
	<tbody>
		<tr>
			<th>Id</th>
			<td>{{ oddType.id }}</td>
		</tr>
		<tr>
			<th>Name</th>
			<td>{{ oddType.name }}</td>
		</tr>
		<tr>
			<th>Rule Success</th>
			<td>{{ oddType.ruleSuccess }}</td>
		</tr>
		<tr>
			<th>Rule Void</th>
			<td>{{ oddType.ruleVoid }}</td>
		</tr>
		<tr>
			<th>Created At</th>
			<td>{% if oddType.createdAt %}{{ oddType.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
		</tr>
		<tr>
			<th>Updated At</th>
			<td>{% if oddType.updatedAt %}{{ oddType.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
		</tr>
	</tbody>
</table>

<div class="pb-50">
	<a href="{{ path('oddtype_edit', { 'id': oddType.id }) }}" class="btn btn-default btn-info pull-left mr-20">Edit Odd Type</a>
	<a href="{{ path('oddtype_index') }}" class="btn btn-default pull-left">Back to the list</a>
	{{ form_start(delete_form) }}
		<input type="submit" value="Delete" class="btn btn-danger pull-right">
	{{ form_end(delete_form) }}
</div>
{% endblock %}
