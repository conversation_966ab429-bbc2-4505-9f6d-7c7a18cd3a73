{% extends 'base.html.twig' %}

{% block title %}Odd Types List{% endblock %}

{% block body %}
<h1>Odd Types list</h1>

{% include '::oddtype/legend.html.twig' %}

<div class="pb-50">
	<div class="pull-left">
		<a href="{{ path('oddtype_new') }}" class="btn btn-primary">New Odd Type</a>
	</div>
</div>

<table class="table table-striped table-hover">
	<thead>
		<tr>
			<th>Id</th>
			<th>Name</th>
			<th>Rule Success</th>
			<th>Rule Void</th>
			<th>Created At</th>
			<th>Updated At</th>
		</tr>
	</thead>
	<tbody>
	{% for oddType in oddTypes %}
		<tr>
			<td><a href="{{ path('oddtype_show', { 'id': oddType.id }) }}">{{ oddType.id }}</a></td>
			<td>{{ oddType.name }}</td>
			<td>{{ oddType.ruleSuccess }}</td>
			<td>{{ oddType.ruleVoid }}</td>
			<td>{% if oddType.createdAt %}{{ oddType.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
			<td>{% if oddType.updatedAt %}{{ oddType.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
		</tr>
	{% endfor %}
	</tbody>
</table>

<div class="pb-50">
	<div class="pull-left">
		<a href="{{ path('oddtype_new') }}" class="btn btn-primary">New Odd Type</a>
	</div>
</div>
{% endblock %}
