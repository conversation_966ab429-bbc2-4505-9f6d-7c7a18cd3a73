{% extends 'base.html.twig' %}

{% block title %}Tools - Find City{% endblock %}

{% block body %}
<h1>City finder</h1>

<div id="find-city" class="container">
        <div class="countries">
            <label for="search-country-name">Ψάξε για Χώρα στα Ελληνικά χωρίς οξείες</label><input type="text" id="search-country-name" value="" class="form-control" autocomplete=off />
            <div id="countries-result"></div>
        </div>

        <div class="cities">
            <span class="selected-country-name"></span>
            <label id="lbl__search-city-name" for="search-city-name">Ψάξε για Πόλη στα αγγλικά</label><input type="text" id="search-city-name" value="" class="form-control" autocomplete=off />
            <div id="cities-result"></div>
        </div>
</div>

{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {% javascripts filter="?jsqueeze"
        '@AppBundle/Resources/public/assets/js/citiesHandler.js'
    %}
    <script src="{{ asset_url }}" type="text/javascript"></script>
    {% endjavascripts %}
{% endblock %}