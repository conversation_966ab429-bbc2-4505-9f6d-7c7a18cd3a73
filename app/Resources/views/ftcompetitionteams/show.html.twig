{% extends 'base.html.twig' %}

{% block title %}Show Competition Team{% endblock %}

{% block body %}
<h1>Competition Team</h1>

<table class="table table-stripped">
	<tbody>
		<tr>
			<th>Id</th>
			<td>{{ ftCompetitionTeams.id }}</td>
		</tr>
		<tr>
			<th>Name (EN)</th>
			<td>{{ ftCompetitionTeams.nameEn }}</td>
		</tr>
		<tr>
			<th>Name (GR)</th>
			<td>{{ ftCompetitionTeams.nameGr }}</td>
		</tr>
		<tr>
			<th>Name (RO)</th>
			<td>{{ ftCompetitionTeams.nameRo }}</td>
		</tr>
		<tr>
			<th>Flag</th>
			<td>{% if ftCompetitionTeams.flag %}<a href="{{ ftCompetitionTeams.flag }}" target="_blank">{{ ftCompetitionTeams.flag }}</a>{% endif %}</td>
		</tr>
		<tr>
			<th>Team URL (GR)</th>
			<td>{% if ftCompetitionTeams.teamUrlGr %}<a href="{{ ftCompetitionTeams.teamUrlGr }}" target="_blank">{{ ftCompetitionTeams.teamUrlGr }}</a>{% endif %}</td>
		</tr>
		<tr>
			<th>Points</th>
			<td>{{ ftCompetitionTeams.pts }}</td>
		</tr>
		<tr>
			<th>Games Played</th>
			<td>{{ ftCompetitionTeams.gp }}</td>
		</tr>
		<tr>
			<th>Goals</th>
			<td>{{ ftCompetitionTeams.goals }}</td>
		</tr>
		<tr>
			<th>Team Index</th>
			<td>{{ ftCompetitionTeams.teamIndex is not null ? ftCompetitionTeams.teamIndex : '' }}</td>
		</tr>
	</tbody>
</table>

<div class="pb-50">
	<a href="{{ path('ftcompetitionteams_new') }}" class="btn btn-default btn-primary pull-left mr-20">Create New Competition Team</a>
	<a href="{{ path('ftcompetitionteams_edit', { 'id': ftCompetitionTeams.id }) }}" class="btn btn-default btn-info pull-left mr-20">Edit Competition Team</a>
	<a href="{{ path('ftcompetitionteams_index') }}" class="btn btn-default pull-left">Back to the list</a>
	{{ form_start(delete_form) }}
		<input type="submit" value="Delete" class="btn btn-danger pull-right">
	{{ form_end(delete_form) }}
</div>
{% endblock %}
