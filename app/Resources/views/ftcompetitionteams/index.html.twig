{% extends 'base.html.twig' %}

{% block title %}Competition Teams List{% endblock %}

{% block body %}
    <h1>FtCompetitionTeams List</h1>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('ftcompetitionteams_new') }}" class="btn btn-primary pull-left mr-20">Create New Competition Team</a>
        </div>
    </div>

    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>Id</th>
                <th>Team Name</th>
                <th>Flag URL</th>
                <th>Team URL Gr</th>
                <th>Points</th>
                <th>Games Played</th>
                <th>Goals</th>
                <th>Group id</th>
                <th>Team id</th>
                <th>Team Index</th>
            </tr>
        </thead>
        <tbody>
        {% for ftCompetitionTeam in ftCompetitionTeams %}
            <tr>
                <td><a href="{{ path('ftcompetitionteams_show', { 'id': ftCompetitionTeam.id }) }}">{{ ftCompetitionTeam.id }}</a></td>
                <td>{{ ftCompetitionTeam.nameGr }}</td>
                <td>{% if ftCompetitionTeam.flag %}<a href="{{ ftCompetitionTeam.flag }}" target="_blank">{{ ftCompetitionTeam.flag }}</a>{% endif %}</td>
                <td>{% if ftCompetitionTeam.teamUrlGr %}<a href="{{ ftCompetitionTeam.teamUrlGr }}" target="_blank">{{ ftCompetitionTeam.teamUrlGr }}</a>{% endif %}</td>
                <td>{{ ftCompetitionTeam.pts }}</td>
                <td>{{ ftCompetitionTeam.gp }}</td>
                <td>{{ ftCompetitionTeam.goals }}</td>
                <td>{{ ftCompetitionTeam.leagueSeasonId }}</td>
                <td>{{ ftCompetitionTeam.teamId }}</td>
                <td> {{ ftCompetitionTeam.teamIndex }}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('ftcompetitionteams_new') }}" class="btn btn-primary pull-left mr-20">Create New Competition Team</a>
        </div>
    </div>

{% endblock %}
