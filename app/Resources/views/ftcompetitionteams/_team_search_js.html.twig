{# templates/ftcompetitionteams/_team_search_js.html.twig #}
<script>
    $(document).ready(function () {
        const $search = $('#appbundle_ftcompetitionteams_teamSearch');
        const $hidden = $('#appbundle_ftcompetitionteams_teamId');

        $search.select2({
            ajax: {
                url: '{{ path("team_search") }}',
                dataType: 'json',
                delay: 250,
                data: params => ({ q: params.term }),
                processResults: data => ({ results: data.results }),
                cache: true
            },
            width: '100%',
            minimumInputLength: 1,
            placeholder: $search.attr('placeholder'),
            allowClear: true,
            templateResult: item => {
                if (!item.id) {
                    return item.text;
                }
                return `${item.text} — ${item.id}`;
            },
            templateSelection: item => {
                if (!item.id) {
                    return item.text;
                }
                return `${item.text} — ${item.id}`;
            }
        });


        {% if ftCompetitionTeams.teamId is not null %}
        const id = {{ ftCompetitionTeams.teamId }};
        $.ajax({
            url: '{{ path("team_search") }}',
            data: { q: '' },
            dataType: 'json'
        }).then(data => {
            const item = data.results.find(r => r.id == id);
            if (item) {
                const option = new Option(item.text, item.id, true, true);
                $search.append(option).trigger('change.select2');
                $hidden.val(item.id);
            }
        });
        {% endif %}

        $search.on('select2:select', e => {
            const { id, text } = e.params.data;
            $hidden.val(id);
            $search.empty();
            const opt = new Option(text, id, true, true);
            $search.append(opt).trigger('change.select2');
        });

        $search.on('select2:clear', () => {
            $hidden.val('');
            $search.empty().trigger('change.select2');
        });
    });
</script>
