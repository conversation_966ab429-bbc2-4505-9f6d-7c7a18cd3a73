{% extends 'base.html.twig' %}

{% block body %}
    <h1>Feed list</h1>

    <div id="js-feed-wrapper"></div>
{#
    <ul>
        <li>
            <a href="{{ path('feed_new') }}">Create a new entry</a>
        </li>
    </ul>
#}
{% endblock %}

{% block javascripts %}
  {{ parent() }}
  <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/react/15.3.2/react.min.js"></script>
  <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/react/15.3.2/react-dom.min.js"></script>
  <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/babel-core/5.8.38/browser.min.js"></script>
  <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.14.0/axios.min.js"></script>

  <script type="text/babel" src="{{ asset('js/react/feed.react.js') }}"></script>
  <script type="text/babel">
    var feedsUrl = '{{ path('feed_all') }}';

    ReactDOM.render(
      <FeedSection url={feedsUrl} />, document.getElementById('js-feed-wrapper')
    )
  </script>
{% endblock %}
