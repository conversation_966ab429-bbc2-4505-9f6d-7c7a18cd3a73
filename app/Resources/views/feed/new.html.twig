{% extends 'base.html.twig' %}

{% block body %}
    <h1>Feed creation</h1>

    {{ form_start(form) }}
        {{ form_row(form.username) }}
        {{ form_row(form.password) }}
        {{ form_row(form.language) }}
        {{ form_row(form.active) }}

        <h3>Feed XMLs</h3>
        <ul class="feedXmls" data-prototype="{{ form_widget(form.feedXmls.vars.prototype)|e('html_attr') }}">
            {# iterate over each existing tag and render its only field: name #}
            {% for feedXml in form.feedXmls %}
                <li>{{ form_row(feedXml.name) }}</li>
            {% endfor %}
        </ul>

        <input type="submit" value="Create" />
    {{ form_end(form, {'render_rest': false}) }}

    <ul>
        <li>
            <a href="{{ path('feed_index') }}">Back to the list</a>
        </li>
    </ul>
{% endblock %}

{% block javascripts %}
<script src="//code.jquery.com/jquery-1.10.2.js"></script>
<script src="{{ asset('js/feedXmlHandler.js') }}"></script>
{% endblock %}
