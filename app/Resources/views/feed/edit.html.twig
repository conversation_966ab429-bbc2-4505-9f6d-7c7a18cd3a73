{% extends 'base.html.twig' %}

{% block body %}
    <h1>Feed edit</h1>

    {{ form_start(edit_form) }}
        {{ form_row(edit_form.username) }}
        {{ form_row(edit_form.password) }}
        {{ form_row(edit_form.language) }}
        {{ form_row(edit_form.active) }}

        <h3>Feed XMLs</h3>
        <ul class="feedXmls" data-prototype="{{ form_widget(edit_form.feedXmls.vars.prototype)|e('html_attr') }}">
            {# iterate over each existing tag and render its only field: name #}
            {% for feedXml in edit_form.feedXmls %}
                <li>{{ form_row(feedXml.url, { 'attr': {'class': 'larger'}}) }}</li>
                <li>{{ form_row(feedXml.name) }}</li>
                <li>{{ form_row(feedXml.description) }}</li>
            {% endfor %}
        </ul>

        <input type="submit" value="Edit" />
    {{ form_end(edit_form) }}

    <ul>
        <li>
            <a href="{{ path('feed_index') }}">Back to the list</a>
        </li>
        <li>
            {{ form_start(delete_form) }}
                <input type="submit" value="Delete">
            {{ form_end(delete_form) }}
        </li>
    </ul>
{% endblock %}

{% block javascripts %}
<script src="//code.jquery.com/jquery-1.10.2.js"></script>
<script src="{{ asset('js/feedXmlHandler.js') }}"></script>
{% endblock %}
