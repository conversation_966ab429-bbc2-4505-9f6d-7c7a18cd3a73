{% extends 'base.html.twig' %}

{% block body %}
    <h1>Feed</h1>

    <table>
        <tbody>
            <tr>
                <th>Id</th>
                <td>{{ feed.id }}</td>
            </tr>
            <tr>
                <th>Username</th>
                <td>{{ feed.username }}</td>
            </tr>
            <tr>
                <th>Password</th>
                <td>{{ feed.password }}</td>
            </tr>
            <tr>
                <th>Language</th>
                <td>{{ feed.language }}</td>
            </tr>
            <tr>
                <th>Active</th>
                <td>{% if feed.active %}Yes{% else %}No{% endif %}</td>
            </tr>
        </tbody>
    </table>

    <ul>
        <li>
            <a href="{{ path('feed_index') }}">Back to the list</a>
        </li>
        <li>
            <a href="{{ path('feed_edit', { 'id': feed.id }) }}">Edit</a>
        </li>
        <li>
            {{ form_start(delete_form) }}
                <input type="submit" value="Delete">
            {{ form_end(delete_form) }}
        </li>
    </ul>
{% endblock %}
