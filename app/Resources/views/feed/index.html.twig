{% extends 'base.html.twig' %}

{% block body %}
    <h1>Teams list</h1>

    <table class="table">
        <thead>
            <tr>
                <th>Id</th>
                <th>Username</th>
                <th>Password</th>
                <th>Language</th>
                <th>Active</th>
            </tr>
        </thead>
        <tbody>
        {% for feed in feeds %}
            <tr>
                <td><a href="{{ path('feed_show', { 'id': feed.id }) }}">{{ feed.id }}</a></td>
                <td>{{ feed.username }}</td>
                <td>{{ feed.password }}</td>
                <td>{{ (feed.language == 1) ? 'Ελληνικά' : 'Αγγλικά' }}</td>
                <td>{{ (feed.active == 1) ? 'Ενεργό' : 'Ανενεργό' }}</td>
                <td>
                    <ul>
                        <li>
                            <a href="{{ path('feed_show', { 'id': feed.id }) }}">show</a>
                        </li>
                        <li>
                            <a href="{{ path('feed_edit', { 'id': feed.id }) }}">edit</a>
                        </li>
                    </ul>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <ul>
        <li>
            <a href="{{ path('feed_new') }}">Create a new entry</a>
        </li>
    </ul>
{% endblock %}
