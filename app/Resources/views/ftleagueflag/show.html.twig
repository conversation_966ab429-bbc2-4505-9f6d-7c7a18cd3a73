{% extends 'base.html.twig' %}

{% block title %}Show League Flag{% endblock %}

{% block body %}
    <h1>League Flag</h1>

    <table class="table table-stripped">
        <tbody>
            <tr>
                <th>Id</th>
                <td>{{ ftLeagueFlag.id }}</td>
            </tr>
            <tr>
                <th>League Id</th>
                <td>{{ ftLeagueFlag.leagueId }}</td>
            </tr>
            <tr>
                <th>Code</th>
                <td>{{ ftLeagueFlag.code }}</td>
            </tr>
            <tr>
                <th>Competition Number En</th>
                <td>{{ ftLeagueFlag.competitionNumberEn }}</td>
            </tr>
            <tr>
                <th>Competition Number Gr</th>
                <td>{{ ftLeagueFlag.competitionNumberGr }}</td>
            </tr>
            <tr>
                <th>Image Name</th>
                <td>{{ ftLeagueFlag.imageName }}</td>
            </tr>
            <tr>
                <th>Sport Id</th>
                <td>{{ ftLeagueFlag.sport }}</td>
            </tr>
        </tbody>
    </table>

    <div class="pb-50">
        <a href="{{ path('ftleagueflag_new') }}" class="btn btn-default btn-primary pull-left mr-20">Create New League Flag</a>
        <a href="{{ path('ftleagueflag_edit', { 'id': ftLeagueFlag.id }) }}" class="btn btn-default btn-info pull-left mr-20">Edit League Flag</a>
        <a href="{{ path('ftleagueflag_index') }}" class="btn btn-default pull-left">Back to the list</a>
        {{ form_start(delete_form) }}
        <input type="submit" value="Delete" class="btn btn-danger pull-right">
        {{ form_end(delete_form) }}
    </div>
{% endblock %}
