{% extends 'base.html.twig' %}

{% block title %}League Flags List{% endblock %}

{% block body %}
    <h1>League Flags List</h1>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('ftleagueflag_new') }}" class="btn btn-primary pull-left mr-20">Create New League Flag</a>
            <a href="{{ path('ftleagueflags_rest') }}" target="_blank" class="btn btn-default btn-info pull-left mr-20">Check Flags Api</a>
        </div>
    </div>

    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>Id</th>
                <th>League Id</th>
                <th>Code</th>
                <th>Competition Number En</th>
                <th>Competition Number Gr</th>
                <th>Image Name</th>
                <th>Sport Id</th>
            </tr>
        </thead>
        <tbody>
        {% for ftLeagueFlag in ftLeagueFlags %}
            <tr>
                <td><a href="{{ path('ftleagueflag_show', { 'id': ftLeagueFlag.id }) }}">{{ ftLeagueFlag.id }}</a></td>
                <td>{{ ftLeagueFlag.leagueId }}</td>
                <td>{{ ftLeagueFlag.code }}</td>
                <td>{{ ftLeagueFlag.competitionNumberEn }}</td>
                <td>{{ ftLeagueFlag.competitionNumberGr }}</td>
                <td>{{ ftLeagueFlag.imageName }}</td>
                <td>{{ ftLeagueFlag.sport is not null ? ftLeagueFlag.sport.id : '' }}
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('ftleagueflag_new') }}" class="btn btn-primary pull-left mr-20">Create New League Flag</a>
            <a href="{{ path('ftleagueflags_rest') }}" target="_blank" class="btn btn-default btn-info pull-left mr-20">Check Flags Api</a>
        </div>
    </div>

{% endblock %}
