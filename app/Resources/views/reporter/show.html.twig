{% extends 'base.html.twig' %}

{% block title %}Show Ft Fixture User Odd{% endblock %}

{% block body %}
    <h1>Ft Fixture User Odd</h1>

    <table class="table table-stripped">
        <tbody>
        <tr>
            <th>Id</th>
            <td>{{ ftFixtureUserOdd.id }}</td>
        </tr>
        <tr>
            <th>Fixture Id</th>
            <td>{{ ftFixtureUserOdd.fixtureId }}</td>
        </tr>
        <tr>
            <th>Match Datetime</th>
            <td>{{ ftFixtureUserOdd.matchDatetime|date('Y-m-d H:i:s') }}</td>
        </tr>
        <tr>
            <th>Odd Type Id</th>
            <td>{{ ftFixtureUserOdd.oddTypeId }}</td>
        </tr>
        <tr>
            <th>Odd Value</th>
            <td>{{ ftFixtureUserOdd.oddValue }}</td>
        </tr>
        <tr>
            <th>Odd Result</th>
            <td>{{ ftFixtureUserOdd.oddResult }}</td>
        </tr>
        <tr>
            <th>Author Id</th>
            <td>{{ ftFixtureUserOdd.authorId }}</td>
        </tr>
        <tr>
            <th>Author Name</th>
            <td>{{ ftFixtureUserOdd.authorName }}</td>
        </tr>
        <tr>
            <th>Sport Id</th>
            <td>{{ ftFixtureUserOdd.sportId }}</td>
        </tr>
        <tr>
            <th>Created At</th>
            <td>{{ ftFixtureUserOdd.createdAt|date('Y-m-d H:i:s') }}</td>
        </tr>
        </tbody>
    </table>

    <div class="pb-50">
        <a href="{{ path('ftfixtureuserodd_new') }}" class="btn btn-default btn-primary pull-left mr-20">Create New Ft Fixture User Odd</a>
        <a href="{{ path('ftfixtureuserodd_edit', { 'id': ftFixtureUserOdd.id }) }}" class="btn btn-default btn-info pull-left mr-20">Edit Ft Fixture User Odd</a>
        <a href="{{ path('ftfixtureuserodd_index') }}" class="btn btn-default pull-left">Back to the list</a>
        {{ form_start(delete_form) }}
        <input type="submit" value="Delete" class="btn btn-danger pull-right">
        {{ form_end(delete_form) }}
    </div>
{% endblock %}
