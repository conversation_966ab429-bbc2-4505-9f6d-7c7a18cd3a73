{% extends 'base.html.twig' %}

{% block title %}Reporter Edit{% endblock %}

{% block body %}
    <h1>Reporter Edit last 45 days</h1>
    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('ftfixtureuserodd_new') }}" class="btn btn-primary pull-left mr-20">Create New Ft Fixture User Odd</a>
        </div>
    </div>

    <table class="table table-striped table-hover">
        <thead>
        <tr>
            <th>Id</th>
            <th>Fixture Id</th>
            <th>Match Datetime</th>
            <th>Odd Type Id</th>
            <th>Odd Value</th>
            <th>Odd Result</th>
            <th>Author Name</th>
        </tr>
        </thead>
        <tbody>
        {% for ftFixtureUserOdd in FtFixtureUserOdds %}
            <tr>
                <td><a href="{{ path('ftfixtureuserodd_show', { 'id': ftFixtureUserOdd.id }) }}">{{ ftFixtureUserOdd.id }}</a></td>
                <td>{{ ftFixtureUserOdd.fixture_id }}</td>
                <td>{{ ftFixtureUserOdd.match_datetime }}</td>
                <td>{{ ftFixtureUserOdd.odd_type_id }}</td>
                <td>{{ ftFixtureUserOdd.odd_value }}</td>
                <td>{{ ftFixtureUserOdd.odd_result }}</td>
                <td>{{ ftFixtureUserOdd.author_name }}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <div class="pb-50">
        <div class="pull-left">
            <a href="{{ path('ftfixtureuserodd_new') }}" class="btn btn-primary pull-left mr-20">Create New Ft Fixture User Odd</a>
        </div>
    </div>

{% endblock %}
