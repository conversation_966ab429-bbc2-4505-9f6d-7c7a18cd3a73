{% extends 'base.html.twig' %}

{% block title %}Sports List{% endblock %}

{% block body %}
<h1>Sport list</h1>
<div id="error-message"></div>
<div id='sport-list-container'>
  <ul class="list-group">
  {% if sport is not defined %}
      <li class="list-group-item zero-sports" id="0">
          <a href="{{ path('sport_new') }}" class="action new">Νέο Σπορ</a>
          <div class="form-render"></div>
      </li>
  {% endif %}
  {% for sport in sports %}
      {% if sport.parent == null %}
          <li class="list-group-item" id="{{ sport.id }}">
              <a href="{{ sport.id }}" class="action index sport-name">{% include "::sport/helper.html.twig" %}</a> <a href="{{ path('sport_new', { 'id': sport.id }) }}" class="action new">new</a> <a href="{{ path('sport_edit', { 'id': sport.id }) }}" class="action edit">edit</a>
              <!-- <input type="text" class="search-field input"/> -->
              <div class="form-render"></div>
              {% if sport.children|length > 0 %}
                  <ul class="list-group">
                    {% for sport in sport.children %}
                          <li class="list-group-item" id="{{ sport.id }}">
                              <a href="{{ sport.id }}" class="action index sport-name">{% include "::sport/helper.html.twig" %}</a> <a href="{{ path('sport_new', { 'id': sport.id }) }}" class="action new">new</a> <a href="{{ path('sport_edit', { 'id': sport.id }) }}" class="action edit">edit</a>
                              <!-- <input type="text" class="search-field input"/> -->
                              <div class="form-render"></div>
                              {% if sport.children|length > 0 %}
                                  <ul class="list-group">
                                    {% for sport in sport.children %}
                                          <li class="list-group-item" id="{{ sport.id }}">
                                              <a href="{{ sport.id }}" class="action index sport-name">{% include "::sport/helper.html.twig" %}</a> <a href="{{ path('sport_new', { 'id': sport.id }) }}" class="action new">new</a> <a href="{{ path('sport_edit', { 'id': sport.id }) }}" class="action edit">edit</a>
                                              <div class="form-render"></div>
                                              {% if sport.children|length > 0 %}
                                                  <ul class="list-group">
                                                    {% for sport in sport.children %}
                                                          <li class="list-group-item" id="{{ sport.id }}">
                                                              <a href="{{ sport.id }}" class="action index sport-name">{% include "::sport/helper.html.twig" %}</a> <a href="{{ path('sport_new', { 'id': sport.id }) }}" class="action new">new</a> <a href="{{ path('sport_edit', { 'id': sport.id }) }}" class="action edit">edit</a>
                                                              <div class="form-render"></div>
                                                          </li>
                                                      {% endfor %}
                                                  </ul>
                                              {% endif %}
                                          </li>
                                      {% endfor %}
                                  </ul>
                              {% endif %}
                          </li>
                      {% endfor %}
                  </ul>
              {% endif %}
          </li>
      {% endif %}
  {% endfor %}
  </ul>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
$(document).ready(function() {
  initAjaxForm();
});
function initAjaxForm()
{
  $('#sport-list-container').on('click', '.action', function(e) {
    e.preventDefault();
    // var elem = $(this).parent().find(".form-render");
    var $parent = $(this).parent("li");
    var elem = $parent.find(".form-render").first();

    //if not form render
    if( ! elem.length ){
      elem = $('<div class="form-render" />');
      $parent.append(elem);
    }

    // console.log($(this).attr('method'));
    $.ajax({
        type: 'GET',
        url: $(this).attr('href'),
        data: $(this).serialize()
    })
    .done(function (data) {
      elem.html(data);
      elem.toggle(true);
      // console.log('done my man');
//          $(this).parent('li').children('.form-render').html(data);

      if (typeof data.message !== 'undefined') {
          alert(data.message);
      }
    })
    .fail(function (jqXHR, textStatus, errorThrown) {
        if (typeof jqXHR.responseJSON !== 'undefined') {
            if (jqXHR.responseJSON.hasOwnProperty('form')) {
                $('.form-body').html(jqXHR.responseJSON.form);
            }

            $('.form-error').html(jqXHR.responseJSON.message);

        } else {
            alert(errorThrown);
        }
    });
  });

  // live search
  $('#sport-list-container').on('keyup', '.search-field', function(e) {
    var $this = $(this);
    var $parentLi = $(this).parent();

    var keyword = $this.val().toLowerCase();
    // console.log(keyword);

    // Find child ul
    var $ul = $parentLi.find('>ul');
    if( ! $ul.length ) return;


    var $li = $ul.find('>li');

    // If value is empty show all
    if( keyword === '' || keyword === null || keyword === 'undefined') {
      $li.show();
    }

    // search text inside li's
    $li.each(function(i){
      var $that = $(this);
      // that: li
      // this search field
      if( $that.text().toLowerCase().indexOf(keyword) === -1 ) {
        // Hide of show
        $that.hide();
      } else{
        $that.show();
      }
    });

  });

  $('.form-render').on('click', '.submit-form', function(e) {
    e.preventDefault();
    // $.each( $('form').serializeArray(), function(i, field) {
    //   values[field.name] = field.value;
    // });

    var $mainForm = $(this).closest('.form-render').parent();
    var $form = $(this).parent();
    var values = {};
    $.each( $form.serializeArray(), function(i, field) {
      values[field.name] = field.value;
    });

    $.ajax({
      type: $form.attr('method'),
      url: $form.attr('action'),
      data: values,
      success: function(data) {
        if (data['success'] == true) {
          var $li = $('#'+data.values.parent);
          var $ul = $li.find('>ul');

          if (data['edit'] == true) {
            var $sportNameAnchor = $mainForm.find('.sport-name');
            // set edited name for sport
            $sportNameAnchor.text(data.values.name);
            // get edited li
            var $newLi = $sportNameAnchor.parent();
            // clear form-render div
            $sportNameAnchor.parent().find('>.form-render').html('');
          }
          else {
            var indexPath = data.values.id;
            var newPath = Routing.generate('sport_new', { id: data.values.id });
            var editPath = Routing.generate('sport_edit', { id: data.values.id });

            var $newLi = $("<li />").addClass('list-group-item').attr('id', data.values.id);
            var $newName = $('<a />').addClass('action').text(data.values.name).attr('href', data.values.id);
            var $newAddBtn = $('<a />').addClass('action new').text('new');
            var $newEditBtn = $('<a />').addClass('action edit').text('edit');
            // Get it all together
            $newLi
              .append($newName)
              .append($newAddBtn)
              .append($newEditBtn);

            if ( !$ul.length ) {
              $ul = $('<ul></ul>').addClass('list-group');
              $li.append($ul);
            }
            // $ul.append(newRecordHtml);
            $ul.append($newLi);
          }

          window.scrollTo(0, $newLi.offset().top);

          $form.parent().remove();
          $('#error-message').html('');
        }
        else {
          $('#error-message').html(data.error);
        }
        // console.log(JSON.stringify(data));
      }
    });
  })

  $('.form-render').on('click', '.close', function() {
    var $elem = $(this).parents('.form-container');
    $elem.remove();
  })
}
</script>
{% endblock %}
