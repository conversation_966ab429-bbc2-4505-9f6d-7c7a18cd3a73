{% block body %}
<div class="form-container">
    <div class="form-error"></div>
    <form method="POST" class="ajax-form" action="{{path('sport_edit', { 'id': sport.id })}}">
        <div class="form-body">
            {% include 'sport/form.html.twig' with {'form': form} %}
        </div>

        <button type="submit" class="btn btn-primary submit-form">Submit</button>
        {{form_rest(form)}}
        {{form_end(form)}}

        <span class="close">Close</span>
    </form>
</div>
{% endblock %}

{% block javascripts %}
{# { parent() } #}

<script type="text/javascript">
    $('.translations').collection({
      allow_up: false,
      allow_down: false,
    });

    $('.domain-sports').collection({
      allow_up: false,
      allow_down: false,
    });

    $('.domain-sport-slugs').collection({
      allow_up: false,
      allow_down: false,
    });
</script>
{% endblock %}
