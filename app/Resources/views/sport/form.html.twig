<div class="row">
    <div class="col-lg-4 col-md-4">
        <div class="col-md-12">{{ form_errors(form) }}</div>
        <div class="bg-danger">{#{ form_errors(form.name) }#}</div>
        <div class="form-group input-group">
            {% form_theme form 'jquery.collection.html.twig' %}
            {{ form_widget(form.parent) }}
            {{ form_widget(form.imageName) }}
            {% if form.domainSportSlugs is defined %}
            {{ form_widget(form.domainSportSlugs) }}
            {% endif %}
            {% if form.ftLeagueSport is defined %}
            {{ form_widget(form.ftLeagueSport) }}
            {% endif %}
            {% if form.dummyCompetition is defined %}
            {{ form_widget(form.dummyCompetition) }}
            {% endif %}
            {% if form.hideInStats is defined %}
            {{ form_widget(form.hideInStats) }}
            {% endif %}
            {{ form_widget(form.translations, { 'label' : 'Μεταφράσεις' }) }}
            {% if form.domainSports is defined %}
            {{ form_widget(form.domainSports) }}
            {% endif %}
            {% if form.singleStandings is defined %}
            {{ form_widget(form.singleStandings) }}
            {% endif %}
            {% if form.standingsPriority is defined %}
            {{ form_widget(form.standingsPriority) }}
            {% endif %}
        </div>
    </div>
</div>
