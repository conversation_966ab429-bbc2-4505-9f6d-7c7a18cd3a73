{% extends 'base.html.twig' %}

{% block body %}
    <h1>FtTeam</h1>

    <table>
        <tbody>
            <tr>
                <th>Id</th>
                <td>{{ ftTeam.id }}</td>
            </tr>
            <tr>
                <th>Teamid</th>
                <td>{{ ftTeam.teamId }}</td>
            </tr>
            <tr>
                <th>Isnational</th>
                <td>{% if ftTeam.isNational %}Yes{% else %}No{% endif %}</td>
            </tr>
            <tr>
                <th>NameEn</th>
                <td>{{ ftTeam.nameEn }}</td>
            </tr>
            <tr>
                <th>NameGr</th>
                <td>{{ ftTeam.nameGr }}</td>
            </tr>
            <tr>
                <th>CreatedAt</th>
                <td>{% if ftTeam.createdAt %}{{ ftTeam.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
            <tr>
                <th>UpdatedAt</th>
                <td>{% if ftTeam.updatedAt %}{{ ftTeam.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
            </tr>
        </tbody>
    </table>

    <ul>
        <li>
            <a href="{{ path('ftteam_index') }}">Back to the list</a>
        </li>
        <li>
            <a href="{{ path('ftteam_edit', { 'id': ftTeam.id }) }}">Edit</a>
        </li>
        <li>
            {{ form_start(delete_form) }}
                <input type="submit" value="Delete">
            {{ form_end(delete_form) }}
        </li>
    </ul>
{% endblock %}
