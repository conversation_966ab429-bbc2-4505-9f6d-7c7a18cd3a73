{% extends 'base.html.twig' %}

{% block body %}
    <h1>FtTeam list</h1>

    <table>
        <thead>
            <tr>
                <th>Id</th>
                <th>Teamid</th>
                <th>Isnational</th>
                <th>NameEn</th>
                <th>NameGr</th>
                <th>CreatedAt</th>
                <th>UpdatedAt</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        {% for ftTeam in ftTeams %}
            <tr>
                <td><a href="{{ path('ftteam_show', { 'id': ftTeam.id }) }}">{{ ftTeam.id }}</a></td>
                <td>{{ ftTeam.teamId }}</td>
                <td>{% if ftTeam.isNational %}Yes{% else %}No{% endif %}</td>
                <td>{{ ftTeam.nameEn }}</td>
                <td>{{ ftTeam.nameGr }}</td>
                <td>{% if ftTeam.createdAt %}{{ ftTeam.createdAt|date('Y-m-d H:i:s') }}{% endif %}</td>
                <td>{% if ftTeam.updatedAt %}{{ ftTeam.updatedAt|date('Y-m-d H:i:s') }}{% endif %}</td>
                <td>
                    <ul>
                        <li>
                            <a href="{{ path('ftteam_show', { 'id': ftTeam.id }) }}">show</a>
                        </li>
                        <li>
                            <a href="{{ path('ftteam_edit', { 'id': ftTeam.id }) }}">edit</a>
                        </li>
                    </ul>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <ul>
        <li>
            <a href="{{ path('ftteam_new') }}">Create a new entry</a>
        </li>
    </ul>
{% endblock %}
