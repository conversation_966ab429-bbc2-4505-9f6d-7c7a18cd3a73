FROM php:7.4-fpm

WORKDIR /var/www/symfony

RUN apt-get update && apt-get install -y \
    git \
    unzip \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libonig-dev \
    libzip-dev \
    zip \
    curl

RUN apt-get clean && rm -rf /var/lib/apt/lists/*

RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip

# Configure PHP
COPY docker/php/www.conf /usr/local/etc/php-fpm.d/www.conf
COPY docker/php/custom.ini /usr/local/etc/php/conf.d/custom.ini

# Create and set permissions for the sessions directory
RUN mkdir -p /var/lib/php/sessions \
    && chmod 1777 /var/lib/php/sessions

# Create and set permissions for the tmp directory
RUN mkdir -p /tmp \
    && chmod 1777 /tmp

# Install Composer
COPY --from=composer:2.1 /usr/bin/composer /usr/bin/composer

# Copy the existing application directory contents
COPY . /var/www/symfony

# Give necessary permissions to the app directory
RUN chown -R www-data:www-data /var/www/symfony \
    && chmod -R 755 /var/www/symfony

EXPOSE 9000
CMD ["php-fpm"]
