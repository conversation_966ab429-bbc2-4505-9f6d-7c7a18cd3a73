server {
    listen 80;
    server_name symfony.local;

    root /var/www/symfony/web;  # Changed from /public to /web for Symfony 3
    index app.php;  # Changed from index.php to app.php

    location / {
        try_files $uri /app.php$is_args$args;  # Changed from index.php to app.php
    }

    location ~ ^/app\.php(/|$) {
        fastcgi_pass php:9000;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        internal;
    }

    location ~ \.php$ {
        return 404;
    }

    error_log /var/log/nginx/symfony_error.log;
    access_log /var/log/nginx/symfony_access.log;
}
