{"name": "christos/feedo", "description": "This is the main feed server", "license": "proprietary", "type": "project", "autoload": {"psr-4": {"": "src/"}, "classmap": ["app/AppKernel.php", "app/AppCache.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "require": {"php": ">=7.4.5", "symfony/symfony": "^3.4", "doctrine/orm": "^2.7", "knplabs/doctrine-behaviors": "^1.6", "knplabs/knp-menu-bundle": "^3.0", "nelmio/cors-bundle": "^1.5", "stof/doctrine-extensions-bundle": "^1.3", "patchwork/jsqueeze": "^2.0", "jms/serializer-bundle": "^3.5", "symfony/monolog-bundle": "^3.5", "symfony/swiftmailer-bundle": "^3.3", "doctrine/doctrine-bundle": "^1.12", "sensio/framework-extra-bundle": "^5.4", "friendsofsymfony/jsrouting-bundle": "^2.5", "friendsofsymfony/rest-bundle": "^2.7", "friendsofsymfony/user-bundle": "^2.1", "friendsofsymfony/oauth-server-bundle": "^1.6", "symfony/assetic-bundle": "^2.8", "sensio/distribution-bundle": "^5.0", "scssphp/scssphp": "^1.1", "symfony/var-dumper": "^3.4", "gedmo/doctrine-extensions": "^2.4"}, "require-dev": {}, "scripts": {"symfony-scripts": ["Incenteev\\ParameterHandler\\ScriptHandler::buildParameters", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::buildBootstrap", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::clearCache", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::installAssets", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::installRequirementsFile", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::prepareDeploymentTarget"], "post-install-cmd": ["@symfony-scripts"], "post-update-cmd": ["@symfony-scripts"]}, "config": {"platform": {"php": "7.4.5"}, "allow-plugins": {"ocramius/package-versions": false}}, "extra": {"symfony-app-dir": "app", "symfony-bin-dir": "bin", "symfony-var-dir": "var", "symfony-web-dir": "web", "symfony-tests-dir": "tests", "symfony-assets-install": "relative", "incenteev-parameters": {"file": "app/config/parameters.yml"}}}