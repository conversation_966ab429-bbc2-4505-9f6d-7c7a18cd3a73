<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
// use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Doctrine\Common\Collections\ArrayCollection;

use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\FOSRestController;

/**
 * FtLeague controller.
 *
 * @Route("/api/ft-leagues")
 */
class FtLeagueController extends FOSRestController
{
  /**
   * Lists all FtLeague entities.
   *
   * @Method("GET")
   * @Rest\Get("/")
   */
  public function indexAction()
  {
    $em = $this->getDoctrine()->getManager();

    $queryMain = "SELECT f.* FROM ft_league f ";
    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->execute();
    $ftLeagues = $stmt->fetchAll();

    try {
      $view = $this->view([$ftLeagues], Response::HTTP_OK);
      $em->clear();
      return $view;
    }
    catch (\Exception $e) {
      return $e->getMessage();
    }
  }
}
