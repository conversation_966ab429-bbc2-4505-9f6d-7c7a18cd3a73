<?php

namespace AppBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;

/**
 * Team Football Field controller.
 *
 * @Route("/admin/team-football-field")
 */
class TeamFootballFieldController extends Controller
{
    /**
     * Team Football Field Index
     *
     * @Route("/list", name="team_football_field_index")
     * @Method("GET")
     */
	public function indexAction()
	{
        $em = $this->getDoctrine()->getManager();

        // $teamFootballFields = $em->getRepository('AppBundle:TeamFootballField')->findAll();
        $teamFootballFields = [];

        return $this->render('teamfootballfield/index.html.twig', array(
            'teamFootballFields' => $teamFootballFields,
        ));
    }
}
