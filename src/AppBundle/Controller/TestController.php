<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Doctrine\Common\Collections\ArrayCollection;

use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\FOSRestController;
use JMS\Serializer\SerializerBuilder;
use JMS\Serializer\Naming\IdenticalPropertyNamingStrategy;

use Doctrine\ORM\Query\ResultSetMapping;

use AppBundle\Entity\Sport;
use AppBundle\Entity\SportTranslation;
use AppBundle\Entity\SportGedmoTranslation;
use AppBundle\Form\SportType;
use AppBundle\Entity\FtLeagueSport;
use AppBundle\Entity\DomainSport;

use Symfony\Component\Cache\Adapter\FilesystemAdapter;


/**
 * Test controller.
 *
 * @Route("/test")
 */
class TestController extends FOSRestController
{
  /**
   * Lists all Sport entities.
   *
   * @Method("GET")
   * @Rest\Get("/")
   */
  public function indexAction()
  {
    $todaysLeagues = $this->get('kernel')->getProjectDir() . '/var/cache/' . $this->container->get('kernel')->getEnvironment() . '/todays_leagues';
    $cache = new FilesystemAdapter('', 0, $todaysLeagues);
    $leagues = $cache->getItem('standings.todays_leagues')->get();

    die('here');

    $em = $this->getDoctrine()->getManager();

    $sport = $em->getRepository('AppBundle:Sport')->findOneById(14);
    $sport->setLft(1);
    $sport->setRgt(1);
    $sport->setLvl(1);

    $em->persist($sport);
    // $newSport->mergeNewTranslations();
    $em->flush();

    return new Response('done');

    $newSport = new Sport();
    $newSport->setIsSport(false);
    $newSport->setIsRegion(false);
    $newSport->setIsCompetition(false);
    $newSport->setIsAdditional(true);

    $newSport->setParent($em->getRepository('AppBundle:Sport')->findOneById(7));
    $newSport->setLft(1);
    $newSport->setRgt(1);
    $newSport->setLvl(1);

    $newSport->setSlug($this->container->get("wbs.globalhelper.service")->sluggify('Superleague Play Off B'));

    $newSport->addTranslation(new SportGedmoTranslation($newSport, 'en', 'normal', 'Superleague Play Off B', 'football Superleague Play Off B'));
    $newSport->addTranslation(new SportGedmoTranslation($newSport, 'el', 'normal', "Superleague Play Off B", 'ποδόσφαιρο Superleague Play Off Β\' Όμιλος'));

    $ftLeagueSport = new FtLeagueSport();
    $ftLeagueSport->setSport($newSport);
    $ftLeagueSport->setLId('GRE1.AB');

    $newSport->setFtLeagueSport($ftLeagueSport);

    $em->persist($newSport);
    // $newSport->mergeNewTranslations();
    $em->flush();

    return new Response('done');

/*
    $sportRepo = $em->getRepository('AppBundle:Sport');
    $sportTree = $sportRepo->childrenHierarchy();
    $result = $this->addNames($sportTree);

    $view = $this->view([$result], Response::HTTP_OK);
    $em->clear();
    return $view;
*/
  }

  /**
   * Import from betarades.gr
   *
   * @Method("GET")
   * @Rest\Get("/import")
   */
  public function importAction()
  {
    $em = $this->getDoctrine()->getManager();
    $domain = $em->getRepository('AppBundle:Domain')->findOneByName('www.betarades.gr');
    $helper = $this->container->get("wbs.globalhelper.service");

    // get domain connection
    if ($domain) {
      $connection = $helper->getDomainConnection($domain);
    }

    // get sports first, then do reccursion for regions and competitions
    try {
      // $sports = $this->getValues($connection, true, false, false);
      $sports = $this->getSports($connection, $domain->getDbPrefix());
      $this->createSportsRegionsCompetitions($connection, $domain, null, $sports, true, false, false);
    }
    catch (\Exception $e) {
      return new Response($e->getMessage());
    }

    // foreach ($regions as $single) {
    //   $sport = new Sport();
    //   if ($single['parent'] === 0) {
    //     $sport->setIsSport(false);
    //     $sport->setIsRegion(true);
    //   }
    // }
    //
    // foreach ($competitions as $single) {
    //   $sport = new Sport();
    //   if ($single['parent'] === 0) {
    //     $sport->setIsSport(false);
    //     $sport->setIsRegion(false);
    //     $sport->setIsCompetition(true);
    //   }
    // }

    $em->clear();
    return new Response('import finished');
  }

  private function getSports($connection, $dbPrefix) {
    $sqlSports = "
    SELECT a.term_id, a.name, a.slug, b.parent FROM ".$dbPrefix."terms a INNER JOIN ".$dbPrefix."term_taxonomy b ON a.term_id = b.term_id
    WHERE taxonomy = 'sport' AND parent = 0
    ";

    // 86

    $stmt = $connection->prepare($sqlSports);
    $stmt->execute();
    $sports = $stmt->fetchAll();

    return $sports;
  }

  private function getBetacademyAllSports($connection) {
    $sqlSports = "SELECT a.term_id, a.name, a.sport_id FROM temp_betacademy a";

    $stmt = $connection->prepare($sqlSports);
    $stmt->execute();
    $sports = $stmt->fetchAll();

    return $sports;
  }

  private function getSportChildren() {

  }

  private function getValues($connection, $parent, $isRegion, $isCompetition) {
    if ($isRegion) {
      $sqlRegions = "
      SELECT a.term_id, a.name, a.slug, b.parent FROM bet_terms a INNER JOIN bet_term_taxonomy b ON a.term_id = b.term_id
      WHERE b.parent = $parent
      ";

      $stmt = $connection->prepare($sqlRegions);
      $stmt->execute();
      $regions = $stmt->fetchAll();

      // return array('data' => $regions, 'type' => 'region');
      return $regions;
    }
    else if ($isCompetition) {
      $sqlCompetitions = "
      SELECT a.term_id, a.name, a.slug, b.parent FROM bet_terms a INNER JOIN bet_term_taxonomy b ON a.term_id = b.term_id
      WHERE b.parent = $parent
      ";

      $stmt = $connection->prepare($sqlCompetitions);
      $stmt->execute();
      $competitions = $stmt->fetchAll();

      // return array('data' => $competitions, 'type' => 'region');
      return $competitions;
    }
  }

  private function getMeta($connection, $termId, $name) {
    if ($name === 'hasStandings') {
      $sql = "
      SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_has_standings'
      ";
    }

    if ($name === 'hasPreviews') {
      $sql = "
      SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_do_preview'
      ";
    }

    if ($name === 'isCompetition') {
      $sql = "
      SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_is_competition'
      ";
    }

    if ($name === 'leagueId') {
      $sql = "
      SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_league_id'
      ";
    }

    if ($name === 'tipsTitle') {
      $sql = "
      SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_tips_yoast_title'
      ";
    }

    if ($name === 'tipsDescription') {
      $sql = "
      SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_tips_yoast_description'
      ";
    }

    if ($name === 'standingsTitle') {
      $sql = "
      SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_standings_yoast_title'
      ";
    }

    if ($name === 'standingsDescription') {
      $sql = "
      SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_standings_yoast_description'
      ";
    }

    if ($name === 'tipsDateFrom') {
      $sql = "
      SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_tips_date_from'
      ";
    }

    if ($name === 'tipsDateTo') {
      $sql = "
      SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_tips_date_to'
      ";
    }

    if ($name === 'yoast') {
      $sql = "
      SELECT option_value FROM bet_options WHERE option_name = 'wpseo_taxonomy_meta'
      ";
    }

    if ($name === 'mixed') {
      $sql = "
      SELECT
      (SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_priority') sport_priority,
      (SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_league_priority') sport_league_priority,
      (SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_show_in_standings') show_in_standings,
      (SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_standings_order_by') standings_order_by,
      (SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_show_in_previews') show_in_previews,
      (SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_previews_order_by') previews_order_by,
      (SELECT option_value FROM bet_options WHERE option_name = 'sport_".$termId."_sport_show_in_champions_bar') show_in_champions_bar"
      ;

      $stmt = $connection->prepare($sql);
      $stmt->execute();
      $result = $stmt->fetchAll();

      return $result;
    }

    $stmt = $connection->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetchColumn();

    return $result;
  }

  private function createSportsRegionsCompetitions($connection, $domain, $parent = null, $values, $isSport, $isRegion, $isCompetition) {
    $em = $this->getDoctrine()->getManager();

    foreach ($values as $single) {
      // if ($isCompetition) {
      //   // add domainSport
      //   $domainSport = new DomainSport();
      //   $domainSport->setDomain($domain);
      //   $domainSport->setHasStandings($hasStandings);
      //   $domainSport->setHasPosts($hasPosts);
      //   $domainSport->setShowAsRegion($showAsRegion);
      //   $sport->addDomainSport($domainSport);
      //
      //   // add League Id
      //   $leagueId = $this->getMeta($connection, $single['term_id'], 'leagueId');
      //   $ftLeagueSport = new FtLeagueSport();
      //   $ftLeagueSport->setLId($leagueId);
      //   $sport->setFtLeagueSport($ftLeagueSport);
      // }

      if ($isSport) {
        $sport = new Sport();
        $sport->setLft(1);
        $sport->setRgt(1);
        $sport->setLvl(1);

        // add Translations (normal, tips, standings)
        $translation = new SportTranslation();
        $translation->setLocale('el');
        $translation->setType('normal');
        $translation->setTitle($single['name']);
        $translation->setDescription($single['name']);
        $translation->setSport($sport);
        $sport->addTranslation($translation);

        $hasStandings = $this->getMeta($connection, $single['term_id'], 'hasStandings');

        if ($hasStandings === 1) {
          // standings title/description translation
          $translation = new SportTranslation();
          $translation->setLocale('el');
          $translation->setType('standings');
          $translation->setTitle($this->getMeta($connection, $single['term_id'], 'standingsTitle'));
          $translation->setDescription($this->getMeta($connection, $single['term_id'], 'standingsDescription'));
          $translation->setSport($sport);
          $sport->addTranslation($translation);
        }

        // tips title/description translation
        $translation = new SportTranslation();
        $translation->setLocale('el');
        $translation->setType('tips');
        $translation->setTitle($this->getMeta($connection, $single['term_id'], 'tipsTitle'));
        $translation->setDescription($this->getMeta($connection, $single['term_id'], 'tipsDescription'));
        $translation->setSport($sport);
        $sport->addTranslation($translation);

        $hasPosts     = $this->getMeta($connection, $single['term_id'], 'hasPreviews');
        $showAsRegion = $this->getMeta($connection, $single['term_id'], 'isCompetition');

        // tips date from/to
        $tipsDateFrom = $this->getMeta($connection, $single['term_id'], 'tipsDateFrom');
        $tipsDateTo = $this->getMeta($connection, $single['term_id'], 'tipsDateTo');
        $sport->setTipsDateFrom(new \DateTime($tipsDateFrom));
        $sport->setTipsDateTo(new \DateTime($tipsDateTo));

        $sport->setIsSport(true);
        $sport->setIsRegion(false);
        $sport->setIsCompetition(false);
        $sport->setIsAdditional(false);
        $sport->setSlug($single['slug']);

        $em->persist($sport);
        $regions = $this->getValues($connection, $single['term_id'], true, false);
        $this->createSportsRegionsCompetitions($connection, $domain, $sport, $regions, false, true, false);

      }
      else if ($isRegion) {
        $sport = new Sport();
        $sport->setLft(1);
        $sport->setRgt(1);
        $sport->setLvl(1);

        // add Translations (normal, tips, standings)
        $translation = new SportTranslation();
        $translation->setLocale('el');
        $translation->setType('normal');
        $translation->setTitle($single['name']);
        $translation->setDescription($single['name']);
        $translation->setSport($sport);
        $sport->addTranslation($translation);

        $hasStandings = $this->getMeta($connection, $single['term_id'], 'hasStandings');

        if ($hasStandings === 1) {
          // standings title/description translation
          $translation = new SportTranslation();
          $translation->setLocale('el');
          $translation->setType('standings');
          $translation->setTitle($this->getMeta($connection, $single['term_id'], 'standingsTitle'));
          $translation->setDescription($this->getMeta($connection, $single['term_id'], 'standingsDescription'));
          $translation->setSport($sport);
          $sport->addTranslation($translation);
        }

        // tips title/description translation
        $translation = new SportTranslation();
        $translation->setLocale('el');
        $translation->setType('tips');
        $translation->setTitle($this->getMeta($connection, $single['term_id'], 'tipsTitle'));
        $translation->setDescription($this->getMeta($connection, $single['term_id'], 'tipsDescription'));
        $translation->setSport($sport);
        $sport->addTranslation($translation);

        $hasPosts     = $this->getMeta($connection, $single['term_id'], 'hasPreviews');
        $showAsRegion = $this->getMeta($connection, $single['term_id'], 'isCompetition');

        // tips date from/to
        $tipsDateFrom = $this->getMeta($connection, $single['term_id'], 'tipsDateFrom');
        $tipsDateTo = $this->getMeta($connection, $single['term_id'], 'tipsDateTo');
        $sport->setTipsDateFrom(new \DateTime($tipsDateFrom));
        $sport->setTipsDateTo(new \DateTime($tipsDateTo));

        $sport->setIsSport(false);
        $sport->setIsRegion(true);
        $sport->setIsCompetition(false);
        $sport->setIsAdditional(false);
        $sport->setParent($parent);
        // $sport->setRoot($parent);
        $sport->setSlug($single['slug']);

        $em->persist($sport);
        $competitions = $this->getValues($connection, $single['term_id'], false, true);
        $this->createSportsRegionsCompetitions($connection, $domain, $sport, $competitions, false, false, true);

        // $em->persist($sport);
      }
      else if ($isCompetition) {
        $sport = new Sport();
        $sport->setLft(1);
        $sport->setRgt(1);
        $sport->setLvl(1);

        // add Translations (normal, tips, standings)
        $translation = new SportTranslation();
        $translation->setLocale('el');
        $translation->setType('normal');
        $translation->setTitle($single['name']);
        $translation->setDescription($single['name']);
        $translation->setSport($sport);
        $sport->addTranslation($translation);

        $hasStandings = $this->getMeta($connection, $single['term_id'], 'hasStandings');

        if ($hasStandings === '1') {
          // standings title/description translation
          $translation = new SportTranslation();
          $translation->setLocale('el');
          $translation->setType('standings');
          $translation->setTitle($this->getMeta($connection, $single['term_id'], 'standingsTitle'));
          $translation->setDescription($this->getMeta($connection, $single['term_id'], 'standingsDescription'));
          $translation->setSport($sport);
          $sport->addTranslation($translation);
        }

        // tips title/description translation
        $translation = new SportTranslation();
        $translation->setLocale('el');
        $translation->setType('tips');
        $translation->setTitle($this->getMeta($connection, $single['term_id'], 'tipsTitle'));
        $translation->setDescription($this->getMeta($connection, $single['term_id'], 'tipsDescription'));
        $translation->setSport($sport);
        $sport->addTranslation($translation);

        $hasPosts     = $this->getMeta($connection, $single['term_id'], 'hasPreviews');
        $showAsRegion = $this->getMeta($connection, $single['term_id'], 'isCompetition');

        // tips date from/to
        $tipsDateFrom = $this->getMeta($connection, $single['term_id'], 'tipsDateFrom');
        $tipsDateTo = $this->getMeta($connection, $single['term_id'], 'tipsDateTo');
        $sport->setTipsDateFrom(new \DateTime($tipsDateFrom));
        $sport->setTipsDateTo(new \DateTime($tipsDateTo));

        // if (($hasPosts === '1' || $hasStandings === '1') && ($single['slug'] != 'live-betting')) {
        if ($hasPosts === '1' || $hasStandings === '1') {
          // add domain sport
          $domainSport = new DomainSport();
          $domainSport->setDomain($domain);
          $domainSport->setHasStandings($hasStandings);
          $domainSport->setHasPosts($hasPosts);
          $domainSport->setShowAsRegion($showAsRegion);
          $domainSport->setSport($sport);
          $sport->addDomainSport($domainSport);

          // add League Id
          $leagueId = $this->getMeta($connection, $single['term_id'], 'leagueId');
          $ftLeagueSport = new FtLeagueSport();
          $ftLeagueSport->setLId($leagueId);
          $ftLeagueSport->setSport($sport);
          $sport->setFtLeagueSport($ftLeagueSport);
        }

        $sport->setIsSport(false);
        $sport->setIsRegion(false);
        $sport->setIsCompetition(true);
        $sport->setIsAdditional(false);
        $sport->setParent($parent);
        $sport->setSlug($single['slug']);

        $em->persist($sport);
      }
    }

    $em->flush();

    return new Response('finished');
  }

  /**
   * Import from betarades.gr
   *
   * @Method("GET")
   * @Rest\Get("/betacademy")
   */
  public function betacademyImportAction()
  {
    $em = $this->getDoctrine()->getManager();
    // $domain = $em->getRepository('AppBundle:Domain')->findOneByName('betacademy.com');

    $wp_db_host     = 'localhost';
    $wp_db_port     = 3306;
    $wp_db_name     = 'b37academ1_db';
    $wp_db_username = 'wbsremote';
    $wp_db_password = 'wbs123#!';

    if (!$wp_db_port) {
      $wp_db_port = 3306;
    }

    $wp_db_host2     = 'localhost';
    $wp_db_port2     = 3306;
    $wp_db_name2     = 'feed0_db';
    $wp_db_username2 = 'feed0us3r';
    $wp_db_password2 = 'KFHRBrPLRJt8rfrJ';

    if (!$wp_db_port) {
      $wp_db_port = 3306;
    }

    $connectionFactory = $this->get('doctrine.dbal.connection_factory');
    // get connections for all domains
    $connection = $connectionFactory->createConnection(
      array('pdo' => new \PDO("mysql:host=$wp_db_host;dbname=$wp_db_name;port=$wp_db_port;charset=UTF8", $wp_db_username,$wp_db_password))
    );

    $connection2 = $connectionFactory->createConnection(
      array('pdo' => new \PDO("mysql:host=$wp_db_host2;dbname=$wp_db_name2;port=$wp_db_port2;charset=UTF8", $wp_db_username2,$wp_db_password2))
    );


    // get sports first, then do reccursion for regions and competitions
    try {
      // $sports = $this->getValues($connection, $domain->getDbPrefix(), true, false, false);
      $sports = $this->getBetacademyAllSports($connection2);
      $this->betacademyAddNamesAndRelations($connection2, $sports);
    //  $this->createSportsRegionsCompetitions($connection, $domain, null, $sports, true, false, false);
    }
    catch (\Exception $e) {
      return new Response($e->getMessage());
    }

    $em->clear();
    return new Response('import finished');
  }

  /**
   * Import from betarades.gr
   *
   * @Method("GET")
   * @Rest\Get("/betarades")
   */
  public function matchBetaradesAction()
  {
    $em = $this->getDoctrine()->getManager();
    $domain = $em->getRepository('AppBundle:Domain')->findOneByName('www.betarades.gr');

    $wp_db_host     = $domain->getDbHost();
    $wp_db_port     = $domain->getDbPort();
    $wp_db_name     = $domain->getDbName();
    $wp_db_username = $domain->getDbUsername();
    $wp_db_password = $domain->getDbPassword();

    if (!$wp_db_port) {
      $wp_db_port = 3306;
    }

    $wp_db_host2     = 'localhost';
    $wp_db_port2     = 3306;
    $wp_db_name2     = 'feed0_db';
    $wp_db_username2 = 'feed0us3r';
    $wp_db_password2 = 'KFHRBrPLRJt8rfrJ';

    $connectionFactory = $this->get('doctrine.dbal.connection_factory');
    // get connections for all domains
    $connection = $connectionFactory->createConnection(
      array('pdo' => new \PDO("mysql:host=$wp_db_host;dbname=$wp_db_name;port=$wp_db_port;charset=UTF8", $wp_db_username,$wp_db_password))
    );

    $connection2 = $connectionFactory->createConnection(
      array('pdo' => new \PDO("mysql:host=$wp_db_host2;dbname=$wp_db_name2;port=$wp_db_port2;charset=UTF8", $wp_db_username2,$wp_db_password2))
    );

    try {
      $sports = $this->getSports($connection, 'bet_');
      $this->matchSportsRegionsCompetitions($connection, $connection2, $domain, null, $sports, true, false, false);
    }
    catch (\Exception $e) {
      return new Response($e->getMessage());
    }

    $em->clear();
    return new Response('matching done');
  }

  private function matchSportsRegionsCompetitions($connection, $connection2, $domain = null, $parent = null, $values, $isSport, $isRegion, $isCompetition) {
    $em = $this->getDoctrine()->getManager();
    $rsm = new ResultSetMapping();

    foreach ($values as $single) {
      $sport = $em->getRepository('AppBundle:Sport')->findOneBy(array('slug' => $single['slug']));

      if ($sport) {
        try {
          $sql = "INSERT INTO domain_sport_terms (domain_id, sport_id, term_id) VALUES (1, ".$sport->getId().", ".(int) $single['term_id'].")";
          $stmt = $connection2->prepare($sql);
          $stmt->execute();

        }
        catch (\Exception $e) {
          echo '<pre>';
          var_dump($e->getMessage());
          echo '</pre>';
          die();
        }

        if ($isSport) {
          // $single['term_id'];
          $regions = $this->getValues($connection, $single['term_id'], true, false);
          $this->matchSportsRegionsCompetitions($connection, $connection2, $domain, $sport, $regions, false, true, false);
        }
        else if ($isRegion) {
          $competitions = $this->getValues($connection, $single['term_id'], true, false);
          $this->matchSportsRegionsCompetitions($connection, $connection2, $domain, $sport, $competitions, false, true, false);
        }
        else if ($isCompetition) {
          // silent else
        }
      }
    }

    // $em->flush();
    // $em->clear();
    return true;
  }

  private function betacademyAddNamesAndRelations($connection2, $sports) {
    $em = $this->getDoctrine()->getManager();
    if ($sports) {
      foreach ($sports as $single) {
        try {
          $sport = $em->getRepository('AppBundle:Sport')->findOneById($single['sport_id']);

          if ($sport) {
            $lala = '[FOUND] SLUG: '.$sport->getSlug().' SPORTID: '.$single['sport_id'].' TERMID: '.$single['term_id'].' NAME: '.$single['name'];
          }

          $translation = new SportTranslation();
          $translation->setLocale('en');
          $translation->setType('normal');
          $translation->setTitle($single['name']);
          $translation->setDescription($single['name']);
          $translation->setSport($sport);
          $sport->addTranslation($translation);

          $sql = "INSERT INTO domain_sport_terms (domain_id, sport_id, term_id) VALUES (2, ".$single['sport_id'].", ".$single['term_id'].")";
          $stmt = $connection2->prepare($sql);
          $stmt->execute();

          $em->persist($sport);
        }
        catch (\Exception $e) {
          echo '<pre>';
          var_dump($e->getMessage());
          echo '</pre>';
          die();
        }
      }
    }
    $em->flush();
    $em->clear();
    return true;
  }

  /**
   * Import from betarades.gr
   *
   * @Method("GET")
   * @Rest\Get("/betarades-update")
   */
  public function updateBetaradesSportAction()
  {
    $em     = $this->getDoctrine()->getManager();
    $helper = $this->container->get("wbs.globalhelper.service");

    $domain = $em->getRepository('AppBundle:Domain')->findOneByName('www.betarades.gr');
    // $connection2 = $helper->getDomainConnection($tempDomain);
    if ($domain) {
      $connection = $helper->getDomainConnection($domain);

      // get all Sport Ids
      $sportIds = $em->getRepository('AppBundle:Sport')->getAllSportIds();

      if ($sportIds) {
        foreach ($sportIds as $sportId) {
          // check if sport id exists in domain
          $termId = $this->getDomainSportTerm($domain->getId(), $sportId);

          // get Meta for termId (sport_show_in_standings, sport_show_in_previews, sport_show_in_champions_bar, sport_priority, sport_league_priority, sport_standings_order_by, sport_previews_order_by)

          $metaData = $this->getMeta($connection, $termId, 'mixed')[0];
          $sportPriority        = $metaData['sport_priority'] ?: 0;
          $sportLeaguePriority  = $metaData['sport_league_priority'] ?: 0;
          $standingsPriority    = $metaData['standings_order_by'] ?: 0;
          $previewsPriority     = $metaData['previews_order_by'] ?: 0;
          $showInStandings      = $metaData['show_in_standings'] ?: 0;
          $showInPreviews       = $metaData['show_in_previews'] ?: 0;
          $showInChampionsBar   = $metaData['show_in_champions_bar'] ?: 0;

          // if ($sportPriority != '' && $sportPriority != null && $sportLeaguePriority != '') {
          //   // $sportName = $em->getRepository('AppBundle:Sport')->findOneById($sportId)->getSlug();
          //   $tempSport = $em->getRepository('AppBundle:Sport')->findOneById($sportId);
          //   if ($tempSport->getIsSport() == 1 || $tempSport->getIsRegion() == 1) {
          //     $priority = $sportPriority;
          //   }
          //   else {
          //     $priority = $sportLeaguePriority;
          //   }
          // }

          $tempSport = $em->getRepository('AppBundle:Sport')->findOneById($sportId);
          if ($tempSport->getIsSport() == 1 || $tempSport->getIsRegion() == 1) {
            $priority = $sportPriority;
          }
          else {
            $priority = $sportLeaguePriority;
          }

          $priorityChampionsBar = $priority;

          // update domain wbs_sports with meta values
          try {
            $sql = "UPDATE ".$domain->getDbPrefix()."wbs_sports SET priority = $priority, priority_standings = $standingsPriority, priority_previews = $previewsPriority, show_in_standings = $showInStandings, show_in_previews = $showInPreviews, show_in_championsbar = $showInChampionsBar, priority_championsbar = $priorityChampionsBar WHERE id = $sportId";
            $stmt = $connection->prepare($sql);
            $stmt->execute();
          }
          catch (\Exception $e) {
            return new Response($e->getMessage());
          }
          // $stmt->execute();
        }
      }
    }

    $em->clear();
    return new Response('all done');
  }

  private function getDomainSportTerm($domainId, $sportId) {
    $em = $this->getDoctrine()->getManager();
    $result = array();

    $queryMain = "
    SELECT term_id FROM domain_sport_terms dst WHERE dst.domain_id = :domainId AND dst.sport_id = :sportId
    ";

    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->bindValue(":domainId", $domainId);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->execute();
    $termId = $stmt->fetchColumn();

    if ($termId) {
      $result = $termId;
    }

    $em->clear();
    return $result;
  }

  /**
   * Import from betarades.gr
   *
   * @Method("GET")
   * @Rest\Get("/import-yoast-previews")
   */
  public function importYoastAction()
  {
    $em = $this->getDoctrine()->getManager();
    $domain = $em->getRepository('AppBundle:Domain')->findOneByName('www.betarades.gr');
    $helper = $this->container->get("wbs.globalhelper.service");

    // get domain connection
    if ($domain) {
      $connection = $helper->getDomainConnection($domain);
    }

    // get sports first, then do reccursion for regions and competitions
    try {
      $sports = $this->getSports($connection, $domain->getDbPrefix());
      $yoastMeta = $this->getMeta($connection, null, 'yoast');
      $this->createYoastMeta($connection, $domain, null, unserialize($yoastMeta)['sport'], $sports, true, false, false);
    }
    catch (\Exception $e) {
      return new Response($e->getMessage());
    }

    $em->clear();
    return new Response('import finished');
  }

  private function createYoastMeta($connection, $domain, $parent = null, $yoastMeta, $sports, $isSport, $isRegion, $isCompetition) {
    $em = $this->getDoctrine()->getManager();

    foreach ($sports as $single) {
      $sport        = $em->getRepository('AppBundle:Sport')->findOneBy(array('slug' => $single['slug']));
      $title        = null;
      $description  = null;

      if (array_key_exists($single['term_id'], $yoastMeta)) {
        if (isset($yoastMeta[$single['term_id']]['wpseo_title'])) {
          $title = $yoastMeta[$single['term_id']]['wpseo_title'];
        }

        if (isset($yoastMeta[$single['term_id']]['wpseo_desc'])) {
          $description = $yoastMeta[$single['term_id']]['wpseo_desc'];
        }

        if ($title) {
          if (!$description) {
            $description = $title;
          }

          $translation = new SportTranslation();
          $translation->setLocale('el');
          $translation->setType('previews');
          $translation->setTitle($title);
          $translation->setDescription($description);
          $translation->setSport($sport);
          $sport->addTranslation($translation);

          $em->persist($translation);
        }
      }

      if ($isSport) {
        $regions = $this->getValues($connection, $single['term_id'], true, false);
        $this->createYoastMeta($connection, $domain, $sport, $yoastMeta, $regions, false, true, false);
      }
      else if ($isRegion) {
        $competitions = $this->getValues($connection, $single['term_id'], false, true);
        $this->createYoastMeta($connection, $domain, $sport, $yoastMeta, $competitions, false, false, true);
      }
      else if ($isCompetition) {
      }
    }

    $em->flush();
  }

  /**
   * Update betarades.gr sport with image_name and competition_number
   *
   * @Method("GET")
   * @Rest\Get("/update-sport")
   */
  public function updateSportAction()
  {
    $em = $this->getDoctrine()->getManager();
    $helper = $this->container->get("wbs.globalhelper.service");
    $tempDomain = $em->getRepository('AppBundle:Domain')->findOneByName('www.betarades.gr');
    $connection2 = $helper->getDomainConnection($tempDomain);

    $ftLeagueFlags = $em->getRepository('AppBundle:FtLeagueFlag')->getSportDataToSync();

    if ($ftLeagueFlags) {
      foreach ($ftLeagueFlags as $single) {
        $sqlUpdate = "UPDATE bet_wbs_sports SET image_name = '".$single->getImageName()."', competition_number = '".$single->getCompetitionNumberGr()."' WHERE id = ".$single->getSport()->getId();

        $stmt = $connection2->prepare($sqlUpdate);
        $stmt->execute();
      }
    }
    $em->clear();
    return new Response('done');

  }
}
