<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use AppBundle\Entity\FtTeam;
use AppBundle\Form\FtTeamType;

/**
 * FtTeam controller.
 *
 * @Route("/ftteam")
 */
class FtTeamController extends Controller
{
    /**
     * Lists all FtTeam entities.
     *
     * @Route("/", name="ftteam_index")
     * @Method("GET")
     */
    public function indexAction()
    {
        $em = $this->getDoctrine()->getManager();

        $ftTeams = $em->getRepository('AppBundle:FtTeam')->findAll();

        return $this->render('ftteam/index.html.twig', array(
            'ftTeams' => $ftTeams,
        ));
    }

    /**
     * Creates a new FtTeam entity.
     *
     * @Route("/new", name="ftteam_new")
     * @Method({"GET", "POST"})
     */
    public function newAction(Request $request)
    {
        $ftTeam = new FtTeam();
        $form = $this->createForm('AppBundle\Form\FtTeamType', $ftTeam);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($ftTeam);
            $em->flush();

            return $this->redirectToRoute('ftteam_show', array('id' => $ftTeam->getId()));
        }

        return $this->render('ftteam/new.html.twig', array(
            'ftTeam' => $ftTeam,
            'form' => $form->createView(),
        ));
    }

    /**
     * Finds and displays a FtTeam entity.
     *
     * @Route("/{id}", name="ftteam_show")
     * @Method("GET")
     */
    public function showAction(FtTeam $ftTeam)
    {
        $deleteForm = $this->createDeleteForm($ftTeam);

        return $this->render('ftteam/show.html.twig', array(
            'ftTeam' => $ftTeam,
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Displays a form to edit an existing FtTeam entity.
     *
     * @Route("/{id}/edit", name="ftteam_edit")
     * @Method({"GET", "POST"})
     */
    public function editAction(Request $request, FtTeam $ftTeam)
    {
        $deleteForm = $this->createDeleteForm($ftTeam);
        $editForm = $this->createForm('AppBundle\Form\FtTeamType', $ftTeam);
        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($ftTeam);
            $em->flush();

            return $this->redirectToRoute('ftteam_edit', array('id' => $ftTeam->getId()));
        }

        return $this->render('ftteam/edit.html.twig', array(
            'ftTeam' => $ftTeam,
            'edit_form' => $editForm->createView(),
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Deletes a FtTeam entity.
     *
     * @Route("/{id}", name="ftteam_delete")
     * @Method("DELETE")
     */
    public function deleteAction(Request $request, FtTeam $ftTeam)
    {
        $form = $this->createDeleteForm($ftTeam);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->remove($ftTeam);
            $em->flush();
        }

        return $this->redirectToRoute('ftteam_index');
    }

    /**
     * Creates a form to delete a FtTeam entity.
     *
     * @param FtTeam $ftTeam The FtTeam entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createDeleteForm(FtTeam $ftTeam)
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('ftteam_delete', array('id' => $ftTeam->getId())))
            ->setMethod('DELETE')
            ->getForm()
        ;
    }
}
