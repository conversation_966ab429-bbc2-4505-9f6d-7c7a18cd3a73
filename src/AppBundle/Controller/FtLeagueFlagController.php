<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use AppBundle\Entity\FtLeagueFlag;
use AppBundle\Form\FtLeagueFlagType;

/**
 * FtLeagueFlag controller.
 *
 * @Route("/ftleagueflag")
 */
class FtLeagueFlagController extends Controller
{
    /**
     * Lists all FtLeagueFlag entities.
     *
     * @Route("/", name="ftleagueflag_index")
     * @Method("GET")
     */
    public function indexAction()
    {
        $em = $this->getDoctrine()->getManager();

        $ftLeagueFlags = $em->getRepository('AppBundle:FtLeagueFlag')->findAll();

        return $this->render('ftleagueflag/index.html.twig', array(
            'ftLeagueFlags' => $ftLeagueFlags,
        ));
    }

    /**
     * Creates a new FtLeagueFlag entity.
     *
     * @Route("/new", name="ftleagueflag_new")
     * @Method({"GET", "POST"})
     */
    public function newAction(Request $request)
    {
        $ftLeagueFlag = new FtLeagueFlag();
        $form = $this->createForm('AppBundle\Form\FtLeagueFlagType', $ftLeagueFlag);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($ftLeagueFlag);
            $em->flush();

            return $this->redirectToRoute('ftleagueflag_show', array('id' => $ftLeagueFlag->getId()));
        }

        return $this->render('ftleagueflag/new.html.twig', array(
            'ftLeagueFlag' => $ftLeagueFlag,
            'form' => $form->createView(),
        ));
    }

    /**
     * Finds and displays a FtLeagueFlag entity.
     *
     * @Route("/{id}", name="ftleagueflag_show")
     * @Method("GET")
     */
    public function showAction(FtLeagueFlag $ftLeagueFlag)
    {
        $deleteForm = $this->createDeleteForm($ftLeagueFlag);

        return $this->render('ftleagueflag/show.html.twig', array(
            'ftLeagueFlag' => $ftLeagueFlag,
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Displays a form to edit an existing FtLeagueFlag entity.
     *
     * @Route("/{id}/edit", name="ftleagueflag_edit")
     * @Method({"GET", "POST"})
     */
    public function editAction(Request $request, FtLeagueFlag $ftLeagueFlag)
    {
        $deleteForm = $this->createDeleteForm($ftLeagueFlag);
        $editForm = $this->createForm('AppBundle\Form\FtLeagueFlagType', $ftLeagueFlag);
        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($ftLeagueFlag);
            $em->flush();

            return $this->redirectToRoute('ftleagueflag_edit', array('id' => $ftLeagueFlag->getId()));
        }

        return $this->render('ftleagueflag/edit.html.twig', array(
            'ftLeagueFlag' => $ftLeagueFlag,
            'edit_form' => $editForm->createView(),
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Deletes a FtLeagueFlag entity.
     *
     * @Route("/{id}", name="ftleagueflag_delete")
     * @Method("DELETE")
     */
    public function deleteAction(Request $request, FtLeagueFlag $ftLeagueFlag)
    {
        $form = $this->createDeleteForm($ftLeagueFlag);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->remove($ftLeagueFlag);
            $em->flush();
        }

        return $this->redirectToRoute('ftleagueflag_index');
    }

    /**
     * Creates a form to delete a FtLeagueFlag entity.
     *
     * @param FtLeagueFlag $ftLeagueFlag The FtLeagueFlag entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createDeleteForm(FtLeagueFlag $ftLeagueFlag)
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('ftleagueflag_delete', array('id' => $ftLeagueFlag->getId())))
            ->setMethod('DELETE')
            ->getForm()
        ;
    }
}
