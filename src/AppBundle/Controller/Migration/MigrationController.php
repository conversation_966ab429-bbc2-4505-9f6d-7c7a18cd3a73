<?php

namespace AppBundle\Controller\Migration;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\FOSRestController;

/**
 * Migration controller.
 *
 * @Route("/migration")
 */
class MigrationController extends FOSRestController
{

  /**
   * Migrate data from betarades to new tables previews/tips
   *
   * @Method("GET")
   * @Rest\Get("/previews")
   */
  public function migrateAction($offset, $limit) {
    $em = $this->getDoctrine()->getManager();
    $helper = $this->container->get("wbs.globalhelper.service");

    // TODO right now previews are created only for betarades.gr
    // but it should be
    // $domains = $domains = $em->getRepository('AppBundle:Domain')->findBy(array('isActive' => true));
    // get active domains here and create the connections
    $domainName = $this->container->hasParameter('domain.name') ? $this->container->getParameter('domain.name') : null;
    $betaradesDomain  = $em->getRepository('AppBundle:Domain')->findOneByName($domainName);

    $connection       = $helper->getDomainConnection($betaradesDomain);

    $posts = $this->getAllPostPreviews($connection, $betaradesDomain->getDbPrefix(), $limit, $offset);

    // $postMeta = $this->getPostMeta($connection, $betaradesDomain->getDbPrefix(), 129858);
    // $postMeta = $this->getPostMeta($connection, 125885);

    $i = 0;
    if ($posts) {
      foreach ($posts as $post) {
        // if ($post['ID'] != 129858) continue;

        // get post meta
        $postMeta = $this->getPostMeta($connection, $betaradesDomain->getDbPrefix(), $post['ID']);
        $termId   = $this->getPostTermId($connection, $betaradesDomain->getDbPrefix(), $post);

        $sportId = null;
        if ($termId) {
          $sportId = $this->getSportIdByTermId($betaradesDomain->getId(), $termId);

          $isFootball = $em->getRepository('AppBundle:Sport')->getIsFootball($sportId);
        }

        if ($postMeta) {
          if ($sportId) {
            // se periptwsh pou den uparxei SportId giati den exw enhmerwsei swsta to susthma mas, agnohse thn eggrafh kai dei3thn
            try {
              // get fixture league, explode right of . and add it to bet_wbs_previews.group field
              $group = null;
              $fixtureId = $postMeta['fixture_id'];
              if ($fixtureId) {
                $fixture = $em->getRepository('AppBundle:FtFixture')->findOneBy(array('fId' => $fixtureId));

                if ($fixture) {
                  $pos = strpos($fixture->getLeague(), '.');

                  if ($pos) {
                    $temp = substr($fixture->getLeague(), $pos + 1);
                    $group = $temp ?: null;
                  } // if ($pos)
                } // if ($fixture)
              } // if ($fixtureId)

              $previewId = $this->insertPreview($connection, $betaradesDomain->getDbPrefix(), $post, $postMeta, $sportId, $isFootball, $group);

              // for inserted preview add relationship if fixture_id exists
              if ($fixtureId) {
                $domainFtFixtureRel = new \AppBundle\Entity\DomainFtFixtureRelationship();
                $domainFtFixtureRel->setDomainId($betaradesDomain->getId());
                $domainFtFixtureRel->setFixtureId($fixtureId);
                $domainFtFixtureRel->setPreviewId($previewId);

                $em->persist($domainFtFixtureRel);
                $em->flush();
              }
              $i++;
            } // try
            catch (\Exception $e) {
              echo '<pre>';
              var_dump($e->getMessage());
              echo '</pre>';
            } // catch
          } // if ($sportId)
          else {
            echo '<pre>';
            var_dump('[NO sport id relation for post: '.$post['ID'].' and term: '.$termId.'] ');
            echo '</pre>';
          }
        }

      }
    }

    $em->clear();
    return new Response('migration done with '.$i.' records inserted.');
  }

  /**
   * Migrate data from betarades to new tables previews/tips
   *
   * @Method("GET")
   * @Rest\Get("/tips")
   */
  public function migrateTipsAction($offset, $limit) {
    $em = $this->getDoctrine()->getManager();
    $helper = $this->container->get("wbs.globalhelper.service");

    // TODO right now previews are created only for betarades.gr
    // but it should be
    // $domains = $domains = $em->getRepository('AppBundle:Domain')->findBy(array('isActive' => true));
    // get active domains here and create the connections
    $domainName = $this->container->hasParameter('domain.name') ? $this->container->getParameter('domain.name') : null;
    $betaradesDomain  = $em->getRepository('AppBundle:Domain')->findOneByName($domainName);

    $connection       = $helper->getDomainConnection($betaradesDomain);

    // do tips 3000 posts a time (local DB)
    // $limit  = 5000;
    // $offset = 16600;
    // $offset = 0;
    $i      = 0;

    $tips = $this->getAllPostTips($connection, $betaradesDomain->getDbPrefix(), $limit, $offset);

    if ($tips) {
      foreach ($tips as $tip) {
        // if ($post['ID'] != 129858) continue;

        // get post meta
        $postMeta = $this->getPostTipsMeta($connection, $betaradesDomain->getDbPrefix(), $tip['ID']);
        $termId   = $this->getPostTermId($connection, $betaradesDomain->getDbPrefix(), $tip);

        $sportId = null;
        if ($termId) {
          $sportId = $this->getSportIdByTermId($betaradesDomain->getId(), $termId);
        }

        if ($postMeta) {
          if ($sportId) {
            // se periptwsh pou den uparxei SportId giati den exw enhmerwsei swsta to susthma mas, agnohse thn eggrafh kai dei3thn
            try {
              $this->insertTip($connection, $betaradesDomain->getDbPrefix(), $postMeta, $sportId);
              $i++;
            } catch (\Exception $e) {
              echo '<pre>';
              var_dump($e->getMessage());
              echo '</pre>';
            }
          }
          else {
            echo '<pre>';
            var_dump('[NO sport id relation for tip: '.$tip['ID'].' and term: '.$termId.'] ');
            echo '</pre>';
          }
        }
      }
    }

    $em->clear();
    return new Response('migration done with '.$i.' tips inserted.');
  }

  /**
   * Migrate Odds data set new column preview_id from post_id
   *
   * @Method("GET")
   * @Rest\Get("/odds")
   */
  public function migrateOddsAction($offset, $limit) {
    $em = $this->getDoctrine()->getManager();
    $helper = $this->container->get("wbs.globalhelper.service");

    // TODO right now previews are created only for betarades.gr
    // but it should be
    // $domains = $domains = $em->getRepository('AppBundle:Domain')->findBy(array('isActive' => true));
    // get active domains here and create the connections
    $domainName = $this->container->hasParameter('domain.name') ? $this->container->getParameter('domain.name') : null;
    $betaradesDomain  = $em->getRepository('AppBundle:Domain')->findOneByName($domainName);

    $connection       = $helper->getDomainConnection($betaradesDomain);

    // do odds 3000 posts a time (local DB)
    $i      = 0;

    // $postPreviewRel = $this->getPostPreviewRel($connection, $betaradesDomain->getDbPrefix(), $limit, $offset);
    $odds = $this->getAllOdds($connection, $betaradesDomain->getDbPrefix(), $limit, $offset);

    if ($odds) {
      foreach ($odds as $odd) {
        // get preview_id for post_id
        $previewId = $this->getPreviewIdFromPostId($connection, $betaradesDomain->getDbPrefix(), $odd['post_id']);

        if ($previewId) {
          try {
            $this->updateOdd($connection, $betaradesDomain->getDbPrefix(), $odd['post_id'], $odd['book_id'], $previewId);
            $i++;
          }
          catch (\Exception $e) {
            echo '<pre>';
            var_dump($e->getMessage());
            echo '</pre>';
          }
        }
      }
    }

    $em->clear();
    return new Response('migration done with '.$i.' odds updated.');
  }

  private function getAllPostPreviews($conn, $dbPrefix, $limit = 100, $offset) {
    $sql = "
      SELECT ID, post_name FROM ".$dbPrefix."posts WHERE post_type = 'previews' AND post_status <> 'auto-draft' AND post_status <> 'draft' AND post_status <> 'trash' ORDER BY ID LIMIT $limit OFFSET $offset
    ";

    $stmt = $conn->prepare($sql);
    $stmt->execute();

    return $stmt->fetchAll();
  }

  private function getAllPostTips($conn, $dbPrefix, $limit = 100, $offset) {
    $sql = "
      SELECT ID FROM ".$dbPrefix."posts WHERE post_type = 'bettingtips' AND post_status <> 'auto-draft' AND post_status <> 'draft' AND post_status <> 'trash' ORDER BY ID LIMIT $limit OFFSET $offset
    ";

    $stmt = $conn->prepare($sql);
    $stmt->execute();

    return $stmt->fetchAll();
  }

  private function getPostMeta($conn, $dbPrefix, $postId) {
    $final = array();
    $stats = array();
    $sql = "
      SELECT meta_id, meta_key, meta_value FROM ".$dbPrefix."postmeta WHERE post_id = $postId
    ";

    $stmt = $conn->prepare($sql);
    $stmt->execute();

    $result = $stmt->fetchAll();

    $final['stats']               = null;
    $final['fixture_id']          = null;
    $final['leagueName']          = null;
    $final['status']              = null;
    $final['homeTeamId']          = 0;
    $final['homeName']            = null;
    $final['awayTeamId']          = 0;
    $final['awayName']            = null;
    $final['match_date_time']     = null;
    $final['match_season']        = null;
    $final['preview_stadium']     = null;
    $final['preview_history']     = null;
    $final['preview_weather']     = null;
    $final['preview_referee']     = null;
    $final['preview_televised']   = null;
    $final['preview_absent_home'] = null;
    $final['preview_absent_away'] = null;
    $final['preview_lineup_home'] = null;
    $final['preview_lineup_away'] = null;
    $final['ft_score']            = null;
    $final['ht_score']            = null;
    $final['events']              = null;

    if ($result) {
      foreach ($result as $single) {
        if ($single['meta_key'] === 'acf_match_stats') {
          if ($single['meta_value']) $statsData = json_decode($single['meta_value']);

          $stats['homeStats']       = isset($statsData->homeStats) ? $statsData->homeStats : null;
          $stats['awayStats']       = isset($statsData->awayStats) ? $statsData->awayStats : null;
          $stats['homeMatches']     = isset($statsData->homeMatches) ? $statsData->homeMatches : null;
          $stats['awayMatches']     = isset($statsData->awayMatches) ? $statsData->awayMatches : null;
          $stats['homeMatchesAll']  = isset($statsData->homeMatchesAll) ? $statsData->homeMatchesAll : null;
          $stats['awayMatchesAll']  = isset($statsData->awayMatchesAll) ? $statsData->awayMatchesAll : null;
          $final['stats']       = json_encode($stats, JSON_UNESCAPED_UNICODE);
          $final['fixture_id']  = isset($statsData->main->id) ? $statsData->main->id : null;
          $final['leagueName']  = isset($statsData->main->leagueName) ? $statsData->main->leagueName : null;
          $final['status']      = isset($statsData->main->status) ? $statsData->main->status : null;
          $final['homeTeamId']  = isset($statsData->main->homeTeamId) ? $statsData->main->homeTeamId : 0;
          $final['homeName']    = isset($statsData->main->homeName) ? $statsData->main->homeName : null;
          $final['awayTeamId']  = isset($statsData->main->awayTeamId) ? $statsData->main->awayTeamId : 0;
          $final['awayName']    = isset($statsData->main->awayName) ? $statsData->main->awayName : null;
        }

        if ($single['meta_key'] === 'acf_match_date_time') $final['match_date_time'] = $single['meta_value'];

        if ($single['meta_key'] === 'acf_match_season') $final['match_season'] = $single['meta_value'];

        if ($single['meta_key'] === 'preview_stadium') $final['preview_stadium'] = $single['meta_value'];

        if ($single['meta_key'] === 'preview_history') $final['preview_history'] = $single['meta_value'];
        if ($single['meta_key'] === 'preview_weather') $final['preview_weather'] = $single['meta_value'];
        if ($single['meta_key'] === 'preview_referee') $final['preview_referee'] = $single['meta_value'];
        if ($single['meta_key'] === 'preview_televised') $final['preview_televised'] = $single['meta_value'];
        if ($single['meta_key'] === 'preview_absent_home') $final['preview_absent_home'] = $single['meta_value'];
        if ($single['meta_key'] === 'preview_absent_away') $final['preview_absent_away'] = $single['meta_value'];
        if ($single['meta_key'] === 'preview_lineup_home') $final['preview_lineup_home'] = $single['meta_value'];
        if ($single['meta_key'] === 'preview_lineup_away') $final['preview_lineup_away'] = $single['meta_value'];

        if ($single['meta_key'] === 'acf_match_result') {
          $matchResult = json_decode($single['meta_value']);

          $final['ft_score']  = $matchResult->fullTimeScore;
          $final['ht_score']  = $matchResult->halfTimeScore;
          $final['events']    = $matchResult->event;
        }
      }
    }

    return $final;
  }

  private function getPostTipsMeta($conn, $dbPrefix, $postId) {
    $final = array();
    $stats = array();
    $sql = "
      SELECT meta_id, meta_key, meta_value FROM ".$dbPrefix."postmeta WHERE post_id = $postId
    ";

    $stmt = $conn->prepare($sql);
    $stmt->execute();

    $result = $stmt->fetchAll();

    $final['match_name']      = null;
    $final['match_date_time'] = null;
    $final['opap_code']       = null;
    $final['pick']            = null;
    $final['odd']             = 0.00;
    $final['stake']           = 0;
    $final['book_id']         = null;
    $final['result']          = null;
    $final['status']          = null;
    $final['preview_id']      = 0;

    if ($result) {
      foreach ($result as $single) {
        if ($single['meta_key'] === 'tips_matchname') $final['match_name'] = $single['meta_value'];
        if ($single['meta_key'] === 'tips_datetime') $final['match_date_time'] = $single['meta_value'];
        if ($single['meta_key'] === 'tips_opap_code') $final['opap_code'] = $single['meta_value'];
        if ($single['meta_key'] === 'tips_pick') $final['pick'] = $single['meta_value'];
        if ($single['meta_key'] === 'tips_odd') $final['odd'] = $single['meta_value'];
        if ($single['meta_key'] === 'tips_stake') $final['stake'] = $single['meta_value'];
        if ($single['meta_key'] === 'tips_score') $final['result'] = $single['meta_value'];
        if ($single['meta_key'] === 'tips_result') {
          switch ($single['meta_value']) {
            case '1win' :
              $final['status'] = 'W';
              break;
            case '2void' :
              $final['status'] = 'V';
              break;
            case '3lose' :
              $final['status'] = 'L';
              break;
            case '0notfinished' :
              $final['status'] = 'N';
              break;
          }
        }
        if ($single['meta_key'] === 'tips_preview') $final['preview_id'] = $single['meta_value'];

        if ($single['meta_key'] === 'tips_bookmaker') {
          $bookmaker = unserialize($single['meta_value']);
          if (isset($bookmaker[0])) $final['book_id'] = $bookmaker[0];
        }

        if ($single['meta_key'] === 'tips_preview') {
          if ($single['meta_value']) {
            $previewId = $this->getPreviewIdFromPostId($conn, $dbPrefix, $single['meta_value']);
            if ($previewId) $final['preview_id'] = $previewId;
          }
        }

      }
    }

    return $final;
  }

  private function getPreviewIdFromPostId($conn, $dbPrefix, $postId) {
    $sql = "SELECT id FROM ".$dbPrefix."wbs_previews WHERE post_id = $postId";

    $stmt = $conn->prepare($sql);
    $stmt->execute();

    return $stmt->fetchColumn();
  }

  private function insertPreview($conn, $dbPrefix, $post, $postMeta, $sportId, $isFootball = 0, $group = null) {
    $sqlInsert = "
    INSERT INTO ".$dbPrefix."wbs_previews (sport_id, post_id, slug, fixture_id, match_date_time, league_match_name, season, stats, status, h_team_id, h_team_name, a_team_id, a_team_name, ht_score, ft_score, events, h_team_lineup, h_team_absenses, a_team_lineup, a_team_absenses, referee, stadium, weather, broadcast, team_history, is_football, `group`) VALUES (
      ".$sportId.",
      ".$post['ID'].",
      '".$post['post_name']."',
      ".($postMeta['fixture_id'] ?: 'null').",
      ".($postMeta['match_date_time'] ? "'".$postMeta['match_date_time']."'" : "'0000-00-00 00:00:00'").",
      ".($postMeta['leagueName'] ? "'".addslashes($postMeta['leagueName'])."'" : 'null').",
      ".($postMeta['match_season'] ? "'".$postMeta['match_season']."'" : 'null').",
      ".($postMeta['stats'] ? "'".addslashes($postMeta['stats'])."'" : 'null').",
      ".($postMeta['status'] ? "'".$postMeta['status']."'" : 'null').",
      ".($postMeta['homeTeamId'] ? "'".$postMeta['homeTeamId']."'" : '0').",
      ".($postMeta['homeName'] ? "'".addslashes($postMeta['homeName'])."'" : 'null').",
      ".($postMeta['awayTeamId'] ? "'".$postMeta['awayTeamId']."'" : '0').",
      ".($postMeta['awayName'] ? "'".addslashes($postMeta['awayName'])."'" : 'null').",
      ".($postMeta['ht_score'] ? "'".$postMeta['ht_score']."'" : 'null').",
      ".($postMeta['ft_score'] ? "'".$postMeta['ft_score']."'" : 'null').",
      ".($postMeta['events'] ? "'".addslashes($postMeta['events'])."'" : 'null').",
      ".($postMeta['preview_lineup_home'] ? "'".addslashes($postMeta['preview_lineup_home'])."'" : 'null').",
      ".($postMeta['preview_absent_home'] ? "'".addslashes($postMeta['preview_absent_home'])."'" : 'null').",
      ".($postMeta['preview_lineup_away'] ? "'".addslashes($postMeta['preview_lineup_away'])."'" : 'null').",
      ".($postMeta['preview_absent_away'] ? "'".addslashes($postMeta['preview_absent_away'])."'" : 'null').",
      ".($postMeta['preview_referee'] ? "'".addslashes($postMeta['preview_referee'])."'" : 'null').",
      ".($postMeta['preview_stadium'] ? "'".addslashes($postMeta['preview_stadium'])."'" : 'null').",
      ".($postMeta['preview_weather'] ? "'".addslashes($postMeta['preview_weather'])."'" : 'null').",
      ".($postMeta['preview_televised'] ? "'".addslashes($postMeta['preview_televised'])."'" : 'null').",
      ".($postMeta['preview_history'] ? "'".addslashes($postMeta['preview_history'])."'" : 'null').",
      ".$isFootball.",
      ".($group ? "'".$group."'" : 'null')."
      )
    ";

    $stmt = $conn->prepare($sqlInsert);
    $stmt->execute();

    return $conn->lastInsertId();
  }

  private function insertTip($conn, $dbPrefix, $postMeta, $sportId) {
    date_default_timezone_set( 'Europe/Athens' );

    if (!$postMeta['status']) return;
    if (!$postMeta['match_name']) return;
    if (!$postMeta['book_id']) return;
    $sqlInsert = "
    INSERT INTO ".$dbPrefix."wbs_tips (sport_id, preview_id, match_name, match_date_time, opap_code, pick, stake, odd, book_id, result, status) VALUES (
      ".$sportId.",
      ".($postMeta['preview_id'] ?: '0').",
      ".($postMeta['match_name'] ? "'".addslashes($postMeta['match_name'])."'" : 'null').",
      ".($postMeta['match_date_time'] ? "'".date("Y-m-d H:i:s", $postMeta['match_date_time'])."'" : "'0000-00-00 00:00:00'").",
      ".($postMeta['opap_code'] ? "'".addslashes($postMeta['opap_code'])."'" : 'null').",
      ".($postMeta['pick'] ? "'".addslashes($postMeta['pick'])."'" : 'null').",
      ".($postMeta['stake'] ? "'".$postMeta['stake']."'" : '0').",
      ".($postMeta['odd'] ? "'".$postMeta['odd']."'" : '0.00').",
      ".($postMeta['book_id'] ? "'".$postMeta['book_id']."'" : 'null').",
      ".($postMeta['result'] ? "'".addslashes($postMeta['result'])."'" : '0').",
      ".($postMeta['status'] ? "'".addslashes($postMeta['status'])."'" : "'Ν'")."
      )
    ";

    $stmt = $conn->prepare($sqlInsert);
    $stmt->execute();
    return;
  }

  private function getPostTermId($conn, $dbPrefix, $post) {
    $sql = "
      SELECT term_id
      FROM
        ".$dbPrefix."term_relationships rel
          INNER JOIN ".$dbPrefix."term_taxonomy tex ON tex.term_taxonomy_id = rel.term_taxonomy_id WHERE object_id = ".$post['ID'];

    $stmt = $conn->prepare($sql);
    $stmt->execute();

    return $stmt->fetchColumn();
  }

  private function getSportIdByTermId($domainId, $termId) {
    $em = $this->getDoctrine()->getManager();

    $sql = "
      SELECT sport_id FROM domain_sport_terms WHERE term_id = $termId AND domain_id = $domainId
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->execute();

    $em->clear();
    return $stmt->fetchColumn();
  }

  private function getAllOdds($conn, $dbPrefix, $limit = 100, $offset) {
    $sql = "
      SELECT post_id, book_id FROM ".$dbPrefix."odds LIMIT $limit OFFSET $offset
    ";

    $stmt = $conn->prepare($sql);
    $stmt->execute();

    return $stmt->fetchAll();
  }

  private function updateOdd($conn, $dbPrefix, $postId, $bookId, $previewId) {
    $sqlUpdate = "
      UPDATE ".$dbPrefix."odds SET preview_id = $previewId WHERE post_id = $postId AND book_id = $bookId
    ";

    $stmt = $conn->prepare($sqlUpdate);
    $stmt->execute();

    return;
  }
}
