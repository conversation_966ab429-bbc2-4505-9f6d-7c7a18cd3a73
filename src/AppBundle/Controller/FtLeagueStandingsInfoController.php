<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use AppBundle\Entity\FtLeagueStandingsInfo;
use AppBundle\Form\FtLeagueStandingsInfoType;

/**
 * FtLeagueStandingsInfo controller.
 *
 * @Route("/admin/ftleaguestandingsinfo")
 */
class FtLeagueStandingsInfoController extends Controller
{
    /**
     * Lists all FtLeagueStandingsInfo entities.
     *
     * @Route("/", name="ftleaguestandingsinfo_index")
     * @Method("GET")
     */
    public function indexAction()
    {
        $em = $this->getDoctrine()->getManager();

        $ftLeagueStandingsInfos = $em->getRepository('AppBundle:FtLeagueStandingsInfo')->findAll();

        return $this->render('ftleaguestandingsinfo/index.html.twig', array(
            'ftLeagueStandingsInfos' => $ftLeagueStandingsInfos,
        ));
    }

    /**
     * Creates a new FtLeagueStandingsInfo entity.
     *
     * @Route("/new", name="ftleaguestandingsinfo_new")
     * @Method({"GET", "POST"})
     */
    public function newAction(Request $request)
    {
        $ftLeagueStandingsInfo = new FtLeagueStandingsInfo();
        $form = $this->createForm('AppBundle\Form\FtLeagueStandingsInfoType', $ftLeagueStandingsInfo);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($ftLeagueStandingsInfo);
            $em->flush();

            return $this->redirectToRoute('ftleaguestandingsinfo_show', array('id' => $ftLeagueStandingsInfo->getId()));
        }

        return $this->render('ftleaguestandingsinfo/new.html.twig', array(
            'ftLeagueStandingsInfo' => $ftLeagueStandingsInfo,
            'form' => $form->createView(),
        ));
    }

    /**
     * Finds and displays a FtLeagueStandingsInfo entity.
     *
     * @Route("/{id}", name="ftleaguestandingsinfo_show")
     * @Method("GET")
     */
    public function showAction(FtLeagueStandingsInfo $ftLeagueStandingsInfo)
    {
        $deleteForm = $this->createDeleteForm($ftLeagueStandingsInfo);

        return $this->render('ftleaguestandingsinfo/show.html.twig', array(
            'ftLeagueStandingsInfo' => $ftLeagueStandingsInfo,
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Displays a form to edit an existing FtLeagueStandingsInfo entity.
     *
     * @Route("/{id}/edit", name="ftleaguestandingsinfo_edit")
     * @Method({"GET", "POST"})
     */
    public function editAction(Request $request, FtLeagueStandingsInfo $ftLeagueStandingsInfo)
    {
        $deleteForm = $this->createDeleteForm($ftLeagueStandingsInfo);
        $editForm = $this->createForm('AppBundle\Form\FtLeagueStandingsInfoType', $ftLeagueStandingsInfo);
        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($ftLeagueStandingsInfo);
            $em->flush();

            return $this->redirectToRoute('ftleaguestandingsinfo_edit', array('id' => $ftLeagueStandingsInfo->getId()));
        }

        return $this->render('ftleaguestandingsinfo/edit.html.twig', array(
            'ftLeagueStandingsInfo' => $ftLeagueStandingsInfo,
            'edit_form' => $editForm->createView(),
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Deletes a FtLeagueStandingsInfo entity.
     *
     * @Route("/{id}", name="ftleaguestandingsinfo_delete")
     * @Method("DELETE")
     */
    public function deleteAction(Request $request, FtLeagueStandingsInfo $ftLeagueStandingsInfo)
    {
        $form = $this->createDeleteForm($ftLeagueStandingsInfo);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->remove($ftLeagueStandingsInfo);
            $em->flush();
        }

        return $this->redirectToRoute('ftleaguestandingsinfo_index');
    }

    /**
     * Creates a form to delete a FtLeagueStandingsInfo entity.
     *
     * @param FtLeagueStandingsInfo $ftLeagueStandingsInfo The FtLeagueStandingsInfo entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createDeleteForm(FtLeagueStandingsInfo $ftLeagueStandingsInfo)
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('ftleaguestandingsinfo_delete', array('id' => $ftLeagueStandingsInfo->getId())))
            ->setMethod('DELETE')
            ->getForm()
        ;
    }
}
