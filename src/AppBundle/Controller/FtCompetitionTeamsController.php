<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use AppBundle\Entity\FtCompetitionTeams;
use AppBundle\Form\FtCompetitionTeamsType;

/**
 * FtCompetitionTeams controller.
 *
 * @Route("/admin/ftcompetitionteams")
 */
class FtCompetitionTeamsController extends Controller
{
  /**
   * Lists all FtCompetitionTeams entities.
   *
   * @Route("/", name="ftcompetitionteams_index")
   * @Method("GET")
   */
  public function indexAction()
  {

    $em = $this->getDoctrine()->getManager();
    $ftCompetitionTeams = $em->getRepository('AppBundle:FtCompetitionTeams')->findBy([], ['teamIndex' => 'ASC']);
    usort($ftCompetitionTeams, function($a, $b) {
      if ($a->getTeamIndex() === null && $b->getTeamIndex() !== null) {
        return 1;
      }
      if ($a->getTeamIndex() !== null && $b->getTeamIndex() === null) {
        return -1;
      }
      $teamIndexComparison = $a->getTeamIndex() <=> $b->getTeamIndex();
      if ($teamIndexComparison !== 0) {
        return $teamIndexComparison;
      }

      // If teamIndex is the same, compare pts (descending)
      return $b->getPts() <=> $a->getPts();
    });

    return $this->render('ftcompetitionteams/index.html.twig', array(
      'ftCompetitionTeams' => $ftCompetitionTeams,
    ));
  }

  /**
   * Creates a new FtCompetitionTeams entity.
   *
   * @Route("/new", name="ftcompetitionteams_new")
   * @Method({"GET", "POST"})
   */
  public function newAction(Request $request)
  {
    $ftCompetitionTeams = new FtCompetitionTeams();
    $seasonChoices = $this->getDoctrine()
        ->getRepository(FtCompetitionTeams::class)
        ->getLeagueSeasonChoices();

    $form = $this->createForm(
        FtCompetitionTeamsType::class,
        $ftCompetitionTeams,
        [
            'season_choices' => $seasonChoices
        ]
    );
    $form->handleRequest($request);

    if ($form->isSubmitted() && $form->isValid()) {
      $em = $this->getDoctrine()->getManager();
      $em->persist($ftCompetitionTeams);
      $em->flush();

      return $this->redirectToRoute('ftcompetitionteams_show', [
          'id' => $ftCompetitionTeams->getId(),
      ]);
    }

    return $this->render('ftcompetitionteams/new.html.twig', [
        'teams' => $ftCompetitionTeams,
        'form'  => $form->createView(),
    ]);
  }

  /**
   * Finds and displays a FtCompetitionTeams entity.
   *
   * @Route("/{id}", name="ftcompetitionteams_show")
   * @Method("GET")
   */
  public function showAction(FtCompetitionTeams $ftCompetitionTeams)
  {
    $deleteForm = $this->createDeleteForm($ftCompetitionTeams);

    return $this->render('ftcompetitionteams/show.html.twig', array(
      'ftCompetitionTeams' => $ftCompetitionTeams,
      'delete_form' => $deleteForm->createView(),
    ));
  }

  /**
   * Displays a form to edit an existing FtCompetitionTeams entity.
   *
   * @Route("/{id}/edit", name="ftcompetitionteams_edit")
   * @Method({"GET", "POST"})
   */
  public function editAction(Request $request, FtCompetitionTeams $ftCompetitionTeams)
  {
    $deleteForm = $this->createDeleteForm($ftCompetitionTeams);
    $seasonChoices = $this->getDoctrine()
        ->getRepository(FtCompetitionTeams::class)
        ->getLeagueSeasonChoices();

    $editForm = $this->createForm(
        FtCompetitionTeamsType::class,
        $ftCompetitionTeams,
        [
            'season_choices' => $seasonChoices
        ]
    );

    $editForm->handleRequest($request);

    if ($editForm->isSubmitted() && $editForm->isValid()) {
      $this->getDoctrine()->getManager()->flush();
      return $this->redirectToRoute('ftcompetitionteams_show', array('id' => $ftCompetitionTeams->getId()));
    }

    return $this->render('ftcompetitionteams/edit.html.twig', array(
      'ftCompetitionTeams' => $ftCompetitionTeams,
      'edit_form' => $editForm->createView(),
      'delete_form' => $deleteForm->createView(),
    ));
  }

  /**
   * Deletes a FtCompetitionTeams entity.
   *
   * @Route("/{id}", name="ftcompetitionteams_delete")
   * @Method("DELETE")
   */
  public function deleteAction(Request $request, FtCompetitionTeams $ftCompetitionTeams)
  {
    $form = $this->createDeleteForm($ftCompetitionTeams);
    $form->handleRequest($request);

    if ($form->isSubmitted() && $form->isValid()) {
      $em = $this->getDoctrine()->getManager();
      $em->remove($ftCompetitionTeams);
      $em->flush();
    }

    return $this->redirectToRoute('ftcompetitionteams_index');
  }

  /**
   * Creates a form to delete a FtCompetitionTeams entity.
   *
   * @param FtCompetitionTeams $ftCompetitionTeams The FtCompetitionTeams entity
   *
   * @return \Symfony\Component\Form\Form The form
   */
  private function createDeleteForm(FtCompetitionTeams $ftCompetitionTeams)
  {
    return $this->createFormBuilder()
      ->setAction($this->generateUrl('ftcompetitionteams_delete', array('id' => $ftCompetitionTeams->getId())))
      ->setMethod('DELETE')
      ->getForm();
  }
    /**
     * @Route("/admin/team-search", name="team_search")
     * @Method("GET")
     */
    public function teamSearchAction(Request $request)
    {
        $term = $request->query->get('q');

        $teams = $this->getDoctrine()
            ->getRepository('AppBundle:FtTeam')
            ->createQueryBuilder('t')
            ->where('LOWER(t.nameEn) LIKE :term')
            ->setParameter('term', '%' . strtolower($term) . '%')
            ->getQuery()
            ->getResult();

        $results = [];

        foreach ($teams as $team) {
            $results[] = [
                'id' => $team->getTeamId(),         // or getTeamIndex() if you're using that
                'text' => $team->getNameEn(),   // This is what shows in the dropdown
            ];
        }

        return new JsonResponse(['results' => $results]);
    }

}
