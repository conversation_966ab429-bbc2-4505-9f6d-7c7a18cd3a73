<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\BufferedOutput;

use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\FOSRestController;
use JMS\Serializer\SerializerBuilder;
use JMS\Serializer\Naming\IdenticalPropertyNamingStrategy;

// use Symfony\Component\Serializer\Serializer;
// use Symfony\Component\Serializer\Encoder\XmlEncoder;
// use Symfony\Component\Serializer\Encoder\JsonEncoder;
// use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

use AppBundle\Entity\Sport;
use AppBundle\Entity\SportTranslation;
use AppBundle\Entity\FtLeagueSport;
use AppBundle\Entity\DomainSport;
use AppBundle\Form\SportType;
use AppBundle\Entity\DomainSportSlug;

use Symfony\Component\Security\Core\Exception\AccessDeniedException;

/**
 * Sport controller.
 *
 * @Route("/admin/sports")
 */
class SportController extends FOSRestController
{
  const GET_ALL = true;
  const GET_INDEX = false;
  const NODES_ALL = true;
  const NODES_SINGLE = false;

  /**
   * Lists all Sport entities.
   *
   * @Method("GET")
   * @Rest\Get("/")
   */
  public function indexAction()
  {
    if (false === $this->get('security.authorization_checker')->isGranted('IS_AUTHENTICATED_FULLY')) {
      throw new AccessDeniedException();
    }

    $em = $this->getDoctrine()->getManager();

    $sportRepo = $em->getRepository('AppBundle:Sport');
    $sports = $sportRepo->findAll();

    $em->clear();
    return $this->render('sport/index.html.twig', array(
      'sports'        => $sports
    ));

    // $sportRepo = $em->getRepository('AppBundle:Sport');
    // $sportTree = $sportRepo->childrenHierarchy();
    // $result = $this->addRelations($sportTree, self::GET_INDEX, self::NODES_ALL);
    //
    // $view = $this->view($result, Response::HTTP_OK);
    // $em->clear();
    // return $view;
  }

  /**
   * Creates a new Sport entity.
   *
   * @Route("/new", name="sport_new", options={"expose": true})
   * @Method({"GET"})
   */
  public function newAction(Request $request)
  {
    $em = $this->getDoctrine()->getManager();
    $sportId = $request->query->get('id');

    $args = array();
    $sport = new Sport();
    if ($sportId && $sportId > 0) {
      // to use when saving the new child/node
      $parentSport = $em->getRepository('AppBundle:Sport')->findOneById($sportId);
      $sport->setParent($parentSport);

      // if adding a new competition or additional sport let the Form Type know
      // in order to show appropriate extra fields
      // if ($parentSport->getIsRegion() || $parentSport->getIsCompetition()) {
      if ($parentSport->getLvl() === 1 || $parentSport->getLvl() === 2) {
        $args = array(
          'is_competition' => true
        );
      }
    }

    $form = $this->createForm(SportType::class, $sport, $args);
    $form->handleRequest($request);

    $em->clear();
    return $this->render('sport/ajaxnew.html.twig', array(
        'sport' => $sport,
        'form' => $form->createView(),
    ));
  }

  /**
   * Save new Sport entity.
   *
   * @Route("/new", name="sport_create")
   * @Method({"POST"})
   */
  public function createAction(Request $request)
  {
    // $encoders = array(new XmlEncoder(), new JsonEncoder());
    // $normalizers = array(new ObjectNormalizer());
    //
    // $serializer = new Serializer($normalizers, $encoders);

    $em = $this->getDoctrine()->getManager();
    $locale = $this->container->getParameter('locale');

    $newSport = new Sport();

    $args = array();
    $form = $this->createForm(SportType::class, $newSport, $args);
    $form->handleRequest($request);

    if ($parentSportId = $newSport->getParent()) {
      $parentSport = $em->getRepository('AppBundle:Sport')->findOneById($parentSportId);

      $newSport->setIsSport(false);
      $newSport->setIsRegion($parentSport->getIsSport());
      // DymmyCompetition::checked means the competition is used for routing purposes only i.e. Mexico A with additionals Apertura/Clausura/Finals
      // so, is_competition must be 0
      // $dummyCompetition = isset($request->request->get('sport')['dummyCompetition']) ? (($request->request->get('sport')['dummyCompetition'] == true) ? false : true) : false;

      // if parent is region
      $isCompetition = false;
      if ($parentSport->getLvl() === 1 || $parentSport->getLvl() === 2) {
        $dummyCompetition = isset($request->request->get('sport')['dummyCompetition']) ? $request->request->get('sport')['dummyCompetition'] : 0;
        $isCompetition = isset($dummyCompetition) ? ($dummyCompetition == 1 ? 0 : 1) : 1;
        // $isCompetition = !isset($request->request->get('sport')['dummyCompetition'])
      }
      // $newSport->setIsCompetition($dummyCompetition);
      $newSport->setIsCompetition($isCompetition);
      // $newSport->setIsCompetition($parentSport->getIsRegion());

      $isAdditional = ($parentSport->getLvl() == 2) ? true : false;

      $hasAdditionals = ($parentSport->getLvl() == 2) ? true : false;
      $parentSport->setHasAdditionals($hasAdditionals);

      // $newSport->setIsAdditional($parentSport->getIsCompetition());
      $newSport->setIsAdditional($isAdditional);
    }
    else {
      $parentSport = null;
      $newSport->setIsSport(true);
      $newSport->setIsRegion(false);
      $newSport->setIsCompetition(false);
      $newSport->setIsAdditional(false);
    }

    $newSport->setHasAdditionals(false);
    if ($parentSport) {
      $newSport->setParent($parentSport);
    }

    $newSport->setLft(1);
    $newSport->setRgt(1);
    $newSport->setLvl(1);

    try {
      $sportData = $request->request->get('sport');

      // if (isset($sportData['singleStandings']) && )
      $newSport->setSingleStandings(!isset($sportData['singleStandings']) ? 0 : $sportData['singleStandings']);
      $newSport->setHideInStats(!isset($sportData['hideInStats']) ? 0 : $sportData['hideInStats']);
      $newSport->setStandingsPriority(!isset($sportData['standingsPriority']) ? 0 : $sportData['standingsPriority']);

      // add Translations
      if ($translations = $newSport->getTranslations()) {
        foreach($translations as $translation) {
          // $newSport->addTranslation($translation);
          $translation->setSport($newSport);
          // $translation->setLocale($translation->getLocale());
        }
      }

      // add League Sport
      if ($ftLeagueSportData = isset($sportData['ftLeagueSport']) ? $sportData['ftLeagueSport']['lId'] ? $sportData['ftLeagueSport'] : null : null) {
        $ftLeagueSport = new FtLeagueSport();
        $ftLeagueSport->setLId($ftLeagueSportData['lId']);
        $ftLeagueSport->setSport($newSport);
        $newSport->setFtLeagueSport($ftLeagueSport);
      }

      // add DomainSports
      if ($domainSportsData = isset($sportData['domainSports']) ? $sportData['domainSports'] : null) {
      // if ($domainSports = $newSport->getDomainSports()) {
        foreach ($domainSportsData as $domainSportData) {
          //$lala = $serializer->deserialize(json_encode($domainSport), 'AppBundle\Entity\DomainSport', 'json');
          $domain = $em->getRepository('AppBundle:Domain')->findOneById($domainSportData['domain']);
          $domainSport = new DomainSport();
          $domainSport->setDomain($domain);
          $domainSport->setHasStandings(isset($domainSportData['hasStandings']) ? $domainSportData['hasStandings'] : 0);
          $domainSport->setHasPosts(isset($domainSportData['hasPosts']) ? $domainSportData['hasPosts'] : 0);
          $domainSport->setShowAsRegion(isset($domainSportData['showAsRegion']) ? $domainSportData['showAsRegion'] : 0);
          $domainSport->setSport($newSport);
          $newSport->addDomainSport($domainSport);
          // $domainSport->setSport($newSport);
        }
      }

      // add DomainSportSlugs
      if ($domainSportSlugsData = isset($sportData['domainSportSlugs']) ? $sportData['domainSportSlugs'] : null) {
        foreach ($domainSportSlugsData as $domainSportSlugData) {
          $domain = $em->getRepository('AppBundle:Domain')->findOneById($domainSportSlugData['domain']);
          $domainSportSlug = new DomainSportSlug();
          $domainSportSlug->setDomain($domain);
          $domainSportSlug->setSport($newSport);
          $domainSportSlug->setSlug($domainSportSlugData['slug']);
          $newSport->addDomainSportSlug($domainSportSlug);
        }
      }

// $defaultTrans = $em->getRepository('AppBundle:SportTranslation')->findOneByTitle($newSport->getId());
      $helper = $this->container->get("wbs.globalhelper.service");
      // $translatedTitle = $helper->getTranslationTitle($sportData['translations'], $locale);
      $translatedTitle = $helper->getTranslationTitle($translations, $locale);

      // slug is required
      // slug should be set by the form field, if it's empty, make slug from title Name attribute, else return an error
      // if (!isset($sportData['slug']) || '' === $sportData['slug']) {
      //   if ($translatedTitle) {
      //     $newSport->setSlug($helper->sluggify($translatedTitle));
      //   }
      //   else {
      //     $result['success']  = false;
      //     $result['error']    = 'Δεν έχετε δηλώσει slug ή Τίτλο για το Όνομα του Sport';
      //   }
      // }

      $em->persist($newSport);
      if ($parentSport) {
        $em->persist($parentSport);
      }
      $em->flush();

      $jsonSport = array(
        'id'      => $newSport->getId(),
        'name'    => $translatedTitle,
        'parent'  => ($newSport->getParent()) ? $newSport->getParent()->getId() : null,
        // 'slug'    => $newSport->getSlug()
      );

      $result['success']  = true;
      $result['edit']     = false;
      $result['values']   = $jsonSport;
    }
    catch (\Exception $e) {
      $result['success']  = false;
      $result['error']    = $e->getMessage();
    }

    return new JsonResponse($result);
  }

  /**
   * Finds and displays a Sport entity.
   *
   * @Route("/{id}", name="sport_get", options={"expose": true})
   * @Method("GET")
   */
  public function getAction(Sport $sport)
  {
    $deleteForm = $this->createDeleteForm($sport);

    return $this->render('sport/show.html.twig', array(
      'sport' => $sport,
      'delete_form' => $deleteForm->createView(),
    ));
  }

  /**
   * Displays a form to edit an existing Sport entity.
   *
   * @Route("/{id}/edit", name="sport_edit", options={"expose": true})
   * @Method({"GET", "POST"})
   */
  public function editAction(Request $request, Sport $sport)
  {
    $em = $this->getDoctrine()->getManager();
    $helper = $this->container->get("wbs.globalhelper.service");

    $args = array();
    // if adding a new competition or additional sport let the Form Type know
    // in order to show appropriate fields
    if ($sport->getIsCompetition() || $sport->getIsAdditional()) {
      $args = array(
        'is_competition' => true
      );
    }

    $editForm = $this->createForm(SportType::class, $sport, $args);
    $editForm->handleRequest($request);

    $deleteForm = $this->createDeleteForm($sport);
    $deleteForm = $this->createForm('AppBundle\Form\SportType', $sport);
    $deleteForm->handleRequest($request);

    $originalTranslations     = new ArrayCollection();
    $originalDomainSports     = new ArrayCollection();
    $originalDomainSportSlugs = new ArrayCollection();

    foreach ($sport->getTranslations() as $translation) {
      $originalTranslations->add($translation);
    }
    foreach ($sport->getDomainSports() as $domainSport) {
      $originalDomainSports->add($domainSport);
    }
    foreach ($sport->getDomainSportSlugs() as $domainSportSlug) {
      $originalDomainSportSlugs->add($domainSportSlug);
    }

    $originalFtLeagueSport = $sport->getFtLeagueSport();

    if ($editForm->isSubmitted() && $editForm->isValid()) {
      try {
        $em = $this->getDoctrine()->getManager();

        if (!$originalFtLeagueSport) {
          if (null !== $sport->getFtLeagueSport()) {
            if (null !== $sport->getFtLeagueSport()->getLId()) {
              $sport->getFtLeagueSport()->setSport($sport);
            }
          }
        }
        else {
          if (!$sport->getFtLeagueSport()->getLId()) {
            // $sport->removeFtLeagueSport();
            $ftLeagueSport = $em->getRepository('AppBundle:FtLeagueSport')->findOneById($originalFtLeagueSport->getId());
            $em->remove($ftLeagueSport);
            $em->flush();
            // $sport->removeFtLeagueSport($ftLeagueSport);
            $sport->setFtLeagueSport(null);
          }
          else {
            $sport->getFtLeagueSport()->setSport($sport);
          }
        }

        foreach ($originalDomainSports as $domainSport) {
          if (false === $sport->getDomainSports()->contains($domainSport)) {
            $domainSport->getSport()->removeElement($sport);
            $em->remove($domainSport);
          }
          else {
            $domainSport->setSport($sport);
            $em->persist($domainSport);
          }
        }
        $domainSports = $sport->getDomainSports();

        foreach ($originalDomainSportSlugs as $domainSportSlug) {
          if (false === $sport->getDomainSportSlugs()->contains($domainSportSlug)) {
            $domainSportSlug->getSport()->removeElement($sport);
            $em->remove($domainSportSlug);
          }
          else {
            $domainSportSlug->setSport($sport);
            $em->persist($domainSportSlug);
          }
        }
        $domainSportSlugs = $sport->getDomainSportSlugs();

        foreach ($originalTranslations as $translation) {
          if (false === $sport->getTranslations()->contains($translation)) {
            $translation->getSport()->removeElement($sport);
            $em->remove($translation);
          }
          else {
            $translation->setSport($sport);
            $em->persist($translation);
          }
        }
        $translations = $sport->getTranslations();

        $locale           = $this->container->getParameter('locale');
        $helper           = $this->container->get("wbs.globalhelper.service");
        $translatedTitle  = $helper->getTranslationTitle($sport->getTranslations(), $locale);

        $jsonSport = array(
          'id'      => $sport->getId(),
          'name'    => $translatedTitle,
          'parent'  => ($sport->getParent()) ? $sport->getParent()->getId() : null
          // 'slug'    => $sport->getSlug()
        );

        $result['success']  = true;
        $result['edit']     = false;
        $result['values']   = $jsonSport;

        $em->persist($sport);
        $em->flush();
      }
      catch (\Exception $e) {
        $result['success']  = false;
        $result['error']    = $e->getMessage();
      }

      $em->clear();
      return new JsonResponse($result);

      // return $this->redirectToRoute('sport_edit', array('id' => $sport->getId()));
    }

    return $this->render('sport/edit.html.twig', array(
      'sport' => $sport,
      'form' => $editForm->createView(),
      'delete_form' => $deleteForm->createView(),
    ));
  }

  /**
   * Deletes a Sport entity.
   *
   * @Route("/{id}", name="sport_delete", options={"expose": true})
   * @Method("DELETE")
   */
  public function deleteAction(Request $request, Sport $sport)
  {
    $form = $this->createDeleteForm($sport);
    $form->handleRequest($request);

    if ($form->isSubmitted() && $form->isValid()) {
      $em = $this->getDoctrine()->getManager();
      $em->remove($sport);
      $em->flush();
    }

    return $this->redirectToRoute('sport_index');
  }

  /**
   * Creates a form to delete a Sport entity.
   *
   * @param Sport $sport The Sport entity
   *
   * @return \Symfony\Component\Form\Form The form
   */
  private function createDeleteForm(Sport $sport)
  {
    return $this->createFormBuilder()
      ->setAction($this->generateUrl('sport_delete', array('id' => $sport->getId())))
      ->setMethod('DELETE')
      ->getForm()
    ;
  }

  private function addRelations(&$sportTree, $allRelations, $allNodes) {
    $em = $this->getDoctrine()->getManager();

    foreach($sportTree as &$single) {
      $sportTrans = $em->getRepository('AppBundle:Sport')->findTranslation($single['id']);
      $ftLeagueSport = $em->getRepository('AppBundle:FtLeagueSport')->findOneBySport($single['id']);
      $currentSportSeason = $em->getRepository('AppBundle:FtLeagueSeasonSport')->getLatestSportSeason($single['id']);

      if ($allRelations) {
        // add leagueId
        if (($single['isCompetition'] || $single['isAdditional'])) {
          if ($ftLeagueSport) {
            $single['ftLeagueSport'] = array(
              'id' => $ftLeagueSport->getId(),
              'l_id' => $ftLeagueSport->getLId(),
              'createdAt' => $ftLeagueSport->getCreatedAt()->format('Y-m-d H:i:s')
            );
          }
          else {
            $single['ftLeagueSport'] = "null";
          }

          // add current Sport Season
          if ($currentSportSeason) {
            $single['sportSeason'] = $currentSportSeason;
          }
          else {
            $single['sportSeason'] = "null";
          }
        }
      }

      // add Translations
      if ($sportTrans) {
        $tempArr = array();
        foreach ($sportTrans as $sportTran) {
          $tempArr[$sportTran['locale'].'__'.$sportTran['type']] = array(
            'id' => intval($sportTran['id']),
            'sportId' => intval($sportTran['sport_id']),
            'type' => $sportTran['type'],
            'locale' => $sportTran['locale'],
            'title' => $sportTran['title'],
            'description' => $sportTran['description'],
          );
        }
        $single['__names'] = $tempArr;
      }
      else {
        $single['__names'] = "null";
      }

      if ($allNodes) {
        // recursion for all children
        if (isset($single['__children']) || isset($single['children'])) {
          isset($single['__children']) ?: $single['__children'] = $single['children'];

          $this->addRelations($single['__children'], $allRelations, $allNodes);
        }
      }
    }

    $em->clear();
    return $sportTree;
  }

  /**
   * Lists all Sport entities.
   *
   * @Method("GET")
   ****  @Rest\Get("/v2/all-football/{domainId}", name="sport_get_football", options={"expose": true})
   * @Route("/v2/all-football/{domainId}/{withStandings}", name="sport_get_football", options={"expose": true})
   */
  public function getAllFootballAction(Request $request, $domainId, $withStandings = null)
  {
    $em = $this->getDoctrine()->getManager();
    if (!$domainId) return null;

    $sports = $em->getRepository('AppBundle:Sport')->getFootballCompetitions($domainId, $withStandings);

    $em->clear();

    return new JsonResponse($sports);
  }

  /**
   * Get fixtures for sports and date. Also validate sport data
   *
   * @Method("GET")
   * @Route("/v2/sports/{domainId}/{sportIds}/{days}", name="sport_get_fixtures", options={"expose": true})
   */
  public function getSportFixtures(Request $request, $domainId, $sportIds, $days) {
    $em   = $this->getDoctrine()->getManager();
    $msg  = '';

    // validate sports
    $sports = explode(',', $sportIds);
    $validityArr  = array();
    $sportsArr    = array();
    if ($sports) {
      foreach ($sports as $sport) {
        $sportName      = ($em->getRepository('AppBundle:Sport')->findTranslationByTypeAndLocale((int) $sport)) ? : '(δεν βρέθηκε τίτλος)';
        $domainName     = $em->getRepository('AppBundle:Domain')->findOneById($domainId);
        $domainSport    = $em->getRepository('AppBundle:DomainSport')->findOneBy(array('domain' => $domainId, 'sport' => $sport));
        $ftLeagueSport  = $em->getRepository('AppBundle:FtLeagueSport')->findOneBy(array('sport' => $sport));
        $leagueId       = (isset($ftLeagueSport) && null !== $ftLeagueSport->getLId()) ? $ftLeagueSport->getLId() : null;
        $sportSeason    = $em->getRepository('AppBundle:FtLeagueSeasonSport')->findBy(array('sport' => $sport), array('season' => 'DESC'), 1);
        $sportsArr[] = array('id' => $sport, 'league' => $leagueId, 'season' => isset($sportSeason[0]) ? $sportSeason[0]->getSeason(): null);

        if ($domainSport) {
          $tmpMesg = '';
          $tmpMesg .= ($domainSport->getHasStandings()) ? '' : '<p class="notice">To sport '.((isset($sportName[0])) ? $sportName[0]['title'] : sport).' δεν έχει δηλωθεί για Βαθμολογίες</p>';
          $tmpMesg .= ($domainSport->getHasPosts()) ? '' : '<p class="notice">To sport '.((isset($sportName[0])) ? $sportName[0]['title'] : sport).' δεν έχει δηλωθεί για Αναλύσεις</p>';
          $tmpMesg .= ($leagueId) ? '' : '<p class="notice">To sport '.((isset($sportName[0])) ? $sportName[0]['title'] : sport).' δεν έχει δηλωμένο League Id του Δεδούλη</p>';

          $msg .= ('' !== $tmpMesg) ? '<div id="sport-'.$sport.'">'.$tmpMesg.'</div>': '';

          $validityArr[$sport] = array(
            'hasStandings'  => $domainSport->getHasStandings(),
            'hasPosts'      => $domainSport->getHasPosts(),
            'sport'         => (isset($sportName[0])) ? $sportName[0]['title'] : '',
            'domain'        => $domainName->getName(),
            'league'        => $leagueId,
            'season'        => (isset($sportSeason[0])) ? $sportSeason[0]->getSeason() : '',
            'message'       => $msg,
          );
        }
        else {
          $msg .= '<div id="sport-'.$sport.'">';
          $msg .= '<p class="error">Το sport με id: '.$sport.' και όνομα: '.$sportName.' δεν είναι συνδεδεμένο με το domain '.$domainName->getName().'</p>';
          $msg .= '</div>';

          $validityArr[$sport] = array(
            'hasStandings'  => null,
            'hasPosts'      => null,
            'sport'         => (isset($sportName[0])) ? $sportName[0]['title'] : '',
            'domain'        => $domainName,
            'league'        => $leagueId,
            'season'        => $sportSeason->getSeason(),
            'message'       => $msg,
          );
        }
      }
    }
    
    $data = null;

    $feeds = $em->getRepository('AppBundle:Feed')->findAll();

    if ($feeds) {
      foreach ($feeds as $feed) {
        $data = $this->get("wbs.ftfixture.service")->batchInsert($feed->getUsername(), $feed->getPassword(), false, false, null, true, $sportIds, $days);
      }
    }

    // if ($data) {
    //   foreach ($data as $single) {
    //   }
    // }
    // die();

    return new JsonResponse(array('validity' => $validityArr , 'fixtures' => $data, 'reqSports' => $sportsArr));
  }

  /**
   * Get Sport standings values for standings tool
   *
   * @Method("GET")
   * @Route("/v2/sport-league-season-info/{sportId}", name="sport_get_league_season_info", options={"expose": true})
   */
  public function getSportLeagueSeasonInfoAction(Request $request, $sportId)
  {
    $em = $this->getDoctrine()->getManager();
    if (!$sportId) return null;

    $sportInfo = $em->getRepository('AppBundle:Sport')->getSportLeagueSeasonInfo($sportId);

    $em->clear();

    return new JsonResponse($sportInfo);
  }

	/**
		* Generate Stanindgs for sport command
		*
		* @Route("/generate-standings/{sportId}", name="sport_generatestandings", options={"expose": true})
		* @Method("GET")
		*/
	public function generateStandingsAction(Request $request, $sportId) {
		$kernel = $this->get('kernel');
		$application = new Application($kernel);
		$application->setAutoExit(false);

    // options is treatead as an array, so pass single sportId as array
		$input = new ArrayInput(array(
			'command'	=> 'wbs:feed:parse',
      'name'    => 'standings',
      '--options' => array((int) $sportId),
		));

		$output = new BufferedOutput();
		$application->run($input, $output);

		$content = $output->fetch();

		return new Response($content);
	}
}
