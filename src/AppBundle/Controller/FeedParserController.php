<?php

namespace AppBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Response;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;

/**
 * Feed Parser controller.
 *
 * @Route("/feed-parser")
 */
class FeedParserController extends Controller
{
  const LANGUAGE_ALL = true;
  const LANGUAGE_NONE = false;
  const FIXTURES_ALL = true;

  public function parseFeedAction($feedName, $options, $silent, $sportIds, $days) {
    $em = $this->getDoctrine()->getManager();
    $result = null;

    try {
      if ($feedName === 'fixtures') {
        $result = $this->parseXml('wbs.ftfixture.service', self::LANGUAGE_NONE, null, $options, $silent, $sportIds, $days);
      }
      else if ($feedName === 'all-fixtures') {
        $result = $this->parseXml('wbs.ftfixture.service', self::LANGUAGE_NONE, self::FIXTURES_ALL, $options);
      }
      else if ($feedName === 'old-fixtures') {
        $result = $this->parseXml('wbs.ftfixture.service', self::LANGUAGE_NONE, self::FIXTURES_ALL, $options);
      }
      else if ($feedName === 'leagues') {
        $result = $this->parseXml('wbs.ftleague.service', self::LANGUAGE_ALL, null, $options);
      }
      else if ($feedName === 'standings') {
        $result = $this->parseXml('wbs.ftleaguestandings.service', self::LANGUAGE_NONE, null, $options);
      }
      else if ($feedName === 'teams') {
        $result = $this->parseXml('wbs.ftteam.service', self::LANGUAGE_ALL, null, $options);
      }
      else if ($feedName === 'league-seasons') {
        $result = $this->parseXml('wbs.ftleagueseason.service', self::LANGUAGE_NONE, null, $options);
      }
      else if ($feedName === 'odds') {
        $result = $this->parseXml('wbs.ftfixtureodd.service', self::LANGUAGE_NONE, null, $options);
      }
      else if ($feedName === 'countries') {
        $result = $this->parseXml('wbs.country.service', self::LANGUAGE_ALL, null, $options);
      }
    }
    catch (\Exception $e) {
      return new Response('[ERROR]: '.$e->getMessage());
    }

    // echo "Memory usage after: " . (memory_get_usage() / 1024) . " KB" . PHP_EOL;
    // $time += microtime(true);
    // echo PHP_EOL.' total time:: ' . sprintf('%f', $time);

    return new Response($result);
  }

  private function parseXml($serviceName, $hasLanguage, $allFixtures = null, $options, $silent = null, $sportIds = null, $days = null) {
    $em = $this->getDoctrine()->getManager();
    $translator = $this->get('translator');

    $feeds = $em->getRepository('AppBundle:Feed')->findAll();

    if ($feeds) {
      foreach ($feeds as $feed) {
        // $lan = $this->get('wbs.helper.global')->getDefaultLanguageCode();
        $lan = ['gr', 'en'];

        if ($lan) {
          if ($hasLanguage) {
            foreach ($lan as $single) {
              $lala = $this->get("$serviceName")->batchInsert($feed->getUsername(), $feed->getPassword(), $single, $allFixtures, $translator, $options);
            }
          }
          else {
            $lala = $this->get("$serviceName")->batchInsert($feed->getUsername(), $feed->getPassword(), $hasLanguage, $allFixtures, $translator, $options, $silent, $sportIds, $days);
          }
        }
      }
    }

    return $lala;
  }

  /*
   * Parse Countries. Use feed xml and add/update data in Country
   */
  // private function parseCountries() {
  //   $em = $this->getDoctrine()->getManager();
  //
  //   $feeds = $em->getRepository('AppBundle:Feed')->findAll();
  //
  //   if ($feeds) {
  //     foreach ($feeds as $feed) {
  //       $lan = $this->get('wbs.helper.global')->getDefaultLanguageCode();
  //
  //       if ($lan) {
  //         foreach ($lan as $single) {
  //           $this->get('wbs.country.service')->batchInsert($feed->getUsername(), $feed->getPassword(), $single);
  //         }
  //       }
  //     }
  //   }
  // }

  /*
   * Parse Leagues. Use feed xml and add/update data in League
   */
  // private function parseLeagues() {
  //   $em = $this->getDoctrine()->getManager();
  //
  //   $feeds = $em->getRepository('AppBundle:Feed')->findAll();
  //
  //   if ($feeds) {
  //     foreach ($feeds as $feed) {
  //       $lan = $this->get('wbs.helper.global')->getDefaultLanguageCode();
  //
  //       if ($lan) {
  //         foreach ($lan as $single) {
  //           $this->get('wbs.league.service')->batchInsert($feed->getUsername(), $feed->getPassword(), $single);
  //         }
  //       }
  //     }
  //   }
  // }
}
