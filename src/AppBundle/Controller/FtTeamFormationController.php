<?php

namespace AppBundle\Controller;

use AppBundle\Entity\FtTeamFormation;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;

/**
 * Ftteamformation controller.
 *
 * @Route("admin/teamformation")
 */
class FtTeamFormationController extends Controller
{
    /**
     * Lists all ftTeamFormation entities.
     *
     * @Route("/", name="ftteamformation_index")
     * @Method("GET")
     */
    public function indexAction()
    {
        $em = $this->getDoctrine()->getManager();

        $ftTeamFormations = $em->getRepository('AppBundle:FtTeamFormation')->findAll();

        return $this->render('ftteamformation/index.html.twig', array(
            'ftTeamFormations' => $ftTeamFormations,
        ));
    }

    /**
     * Creates a new ftTeamFormation entity.
     *
     * @Route("/new", name="ftteamformation_new")
     * @Method({"GET", "POST"})
     */
    public function newAction(Request $request)
    {
        $ftTeamFormation = new Ftteamformation();
        $form = $this->createForm('AppBundle\Form\FtTeamFormationType', $ftTeamFormation);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($ftTeamFormation);
            $em->flush();
            $teamFormationsDir = $this->get('kernel')->getProjectDir() . '/var/cache/' . $this->container->get('kernel')->getEnvironment() . '/team_formations';
            $cache      = new FilesystemAdapter('', 0, $teamFormationsDir);
            $myResult = $cache->deleteItem('team.formations');

            return $this->redirectToRoute('ftteamformation_show', array('id' => $ftTeamFormation->getId()));
        }

        return $this->render('ftteamformation/new.html.twig', array(
            'ftTeamFormation' => $ftTeamFormation,
            'form' => $form->createView(),
        ));
    }

    /**
     * Finds and displays a ftTeamFormation entity.
     *
     * @Route("/{id}", name="ftteamformation_show")
     * @Method("GET")
     */
    public function showAction(FtTeamFormation $ftTeamFormation)
    {
        $deleteForm = $this->createDeleteForm($ftTeamFormation);

        return $this->render('ftteamformation/show.html.twig', array(
            'ftTeamFormation' => $ftTeamFormation,
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Displays a form to edit an existing ftTeamFormation entity.
     *
     * @Route("/{id}/edit", name="ftteamformation_edit")
     * @Method({"GET", "POST"})
     */
    public function editAction(Request $request, FtTeamFormation $ftTeamFormation)
    {
        $deleteForm = $this->createDeleteForm($ftTeamFormation);
        $editForm = $this->createForm('AppBundle\Form\FtTeamFormationType', $ftTeamFormation);
        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $this->getDoctrine()->getManager()->flush();
            $teamFormationsDir = $this->get('kernel')->getProjectDir() . '/var/cache/' . $this->container->get('kernel')->getEnvironment() . '/team_formations';
            $cache      = new FilesystemAdapter('', 0, $teamFormationsDir);
            $myResult = $cache->deleteItem('team.formations');

            return $this->redirectToRoute('ftteamformation_edit', array('id' => $ftTeamFormation->getId()));
        }

        return $this->render('ftteamformation/edit.html.twig', array(
            'ftTeamFormation' => $ftTeamFormation,
            'edit_form' => $editForm->createView(),
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Deletes a ftTeamFormation entity.
     *
     * @Route("/{id}", name="ftteamformation_delete")
     * @Method("DELETE")
     */
    public function deleteAction(Request $request, FtTeamFormation $ftTeamFormation)
    {
        $form = $this->createDeleteForm($ftTeamFormation);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->remove($ftTeamFormation);
            $em->flush();
        }

        return $this->redirectToRoute('ftteamformation_index');
    }

    /**
     * Creates a form to delete a ftTeamFormation entity.
     *
     * @param FtTeamFormation $ftTeamFormation The ftTeamFormation entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createDeleteForm(FtTeamFormation $ftTeamFormation)
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('ftteamformation_delete', array('id' => $ftTeamFormation->getId())))
            ->setMethod('DELETE')
            ->getForm()
        ;
    }
}
