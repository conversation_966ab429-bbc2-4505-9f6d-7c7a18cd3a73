<?php

namespace AppBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Response;
// use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;

use AppBundle\Entity\FtLeagueSeasonSport;

/**
 * Feed Parser controller.
 *
 */
class FtLeagueSeasonSportController extends Controller
{

  public function generateSeason() {
    $em = $this->getDoctrine()->getManager();
    $msg = array();

    $sports = $em->getRepository('AppBundle:Sport')->getAllCompetitionIds();

    if ($sports) {
      foreach ($sports as $sport) {

        $ftLeagueSport = $em->getRepository('AppBundle:FtLeagueSport')->getCompetitionLeagueId($sport['id']);

        if ($ftLeagueSport) {

          $currentSeason = $em->getRepository('AppBundle:FtLeagueSeason')->getLatestLeagueSeason($ftLeagueSport);

          if ($currentSeason) {
            $sportObj   = $em->getRepository('AppBundle:Sport')->findOneById($sport['id']);
            $sportTitle = $em->getRepository('AppBundle:Sport')->findTranslationByTypeAndLocale($sport['id']);

            if (!$em->getRepository('AppBundle:FtLeagueSeasonSport')->findBy(array('sport' => $sportObj, 'season' => $currentSeason))) {
              // new FtLeagueSeasonSport
              $this->addLeagueSeasonSport($sportObj, $currentSeason);
              $msg[] = array('job' => 'Added league season for sport', 'sport' => $sportTitle, 'season' => $currentSeason);
            }
          } // if $currentSeason

          if (isset($sport['additional']['id']) && $sport['additional']['id']) {
            foreach ($sport['additional']['id'] as $additional) {
              $additionalLeague = $em->getRepository('AppBundle:FtLeagueSport')->getCompetitionLeagueId($additional['id']);
              $additionalSeason = $em->getRepository('AppBundle:FtLeagueSeason')->getLatestLeagueSeason($additionalLeague);
              $sportObj         = $em->getRepository('AppBundle:Sport')->findOneById($additional['id']);
              $sportTitle       = $em->getRepository('AppBundle:Sport')->findTranslationByTypeAndLocale($additional['id']);

              if ($additionalSeason) {
                // if ftleagueseasonport doesn't exist, and if additional sport season === parent sport season
                if (!$em->getRepository('AppBundle:FtLeagueSeasonSport')->findBy(array('sport' => $sportObj, 'season' => $additionalSeason)) && ($additionalSeason === $currentSeason)) {
                  // new FtLeagueSeasonSport
                  $this->addLeagueSeasonSport($sportObj, $additionalSeason);
                  $msg[] = array('job' => 'Added league season for Additional sport', 'sport' => $sportTitle, 'season' => $additionalSeason);
                }
              } // if $additionalSeason
            } // foreach additional
          } // hasAdditionals
        } // if $ftLeagueSport
      } // foreach $sports
    } // if $sports

    $em->clear();

    return new Response(json_encode($msg));
  }

  private function addLeagueSeasonSport($sport, $season) {
    $em = $this->getDoctrine()->getManager();
    $ftLeagueSeasonSport = new FtLeagueSeasonSport();
    $ftLeagueSeasonSport->setSport($sport);
    $ftLeagueSeasonSport->setSeason($season);
    $em->persist($ftLeagueSeasonSport);
    $em->flush();
    $em->clear();

    return true;
  }
}
