<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use AppBundle\Entity\FtCompetitionSchedule;

/**
 * FtCompetitionSchedule controller.
 *
 * @Route("/admin/ftcompetitionschedule")
 */
class FtCompetitionScheduleController extends Controller
{
  /**
   * Lists all FtCompetitionSchedule entities.
   *
   * @Route("/", name="ftcompetitionschedule_index")
   * @Method("GET")
   */
  public function indexAction(Request $request)
  {
    $em = $this->getDoctrine()->getManager();
    $ftCompetitionSchedules = $em->getRepository('AppBundle:FtCompetitionSchedule')->findAllCompetitionMatches();

    return $this->render('ftcompetitionschedule/index.html.twig', array(
      'ftCompetitionSchedules' => $ftCompetitionSchedules,
    ));
  }

  /**
   * Creates a new FtCompetitionSchedule entity.
   *
   * @Route("/new", name="ftcompetitionschedule_new")
   * @Method({"GET", "POST"})
   */
  public function newAction(Request $request)
  {
    $em = $this->getDoctrine()->getManager();
    $ftCompetitionSchedule = new FtCompetitionSchedule();

    $ftCompetitionTeams = $em->getRepository('AppBundle:FtCompetitionTeams')->findAllTeams();
    $competitionTeamsFinal = [];
    foreach ($ftCompetitionTeams as $ftCompetitionTeam) {
      $competitionTeamsFinal[$ftCompetitionTeam['name_gr']] = $ftCompetitionTeam['id'];
    }
    $form = $this->createForm('AppBundle\Form\FtCompetitionScheduleType', $ftCompetitionSchedule, ['more_data' => $competitionTeamsFinal ]);
    $form->handleRequest($request);

    if ($form->isSubmitted() && $form->isValid()) {
      $em = $this->getDoctrine()->getManager();
      $em->persist($ftCompetitionSchedule);
      $em->flush();

      return $this->redirectToRoute('ftcompetitionschedule_show', array('id' => $ftCompetitionSchedule->getId()));
    }

    return $this->render('ftcompetitionschedule/new.html.twig', array(
      'teams' => $ftCompetitionSchedule,
      'form' => $form->createView(),
    ));
  }

  /**
   * Finds and displays a FtCompetitionSchedule entity.
   *
   * @Route("/{id}/", name="ftcompetitionschedule_show")
   * @Method("GET")
   */
  public function showAction(FtCompetitionSchedule $ftCompetitionSchedule)
  {
    $em = $this->getDoctrine()->getManager();
    $deleteForm = $this->createDeleteForm($ftCompetitionSchedule);
    $ftHomeCompetitionTeam = $em->getRepository('AppBundle:FtCompetitionTeams')->findOneTeam($ftCompetitionSchedule->getHTeam());
    $ftAwayCompetitionTeam = $em->getRepository('AppBundle:FtCompetitionTeams')->findOneTeam($ftCompetitionSchedule->getATeam());

    return $this->render('ftcompetitionschedule/show.html.twig', array(
      'ftCompetitionSchedule' => $ftCompetitionSchedule,
      'ftHomeCompetitionTeam' => $ftHomeCompetitionTeam[0],
      'ftAwayCompetitionTeam' => $ftAwayCompetitionTeam[0],
      'delete_form' => $deleteForm->createView(),
    ));
  }

  /**
   * Displays a form to edit an existing FtCompetitionSchedule entity.
   *
   * @Route("/{id}/edit", name="ftcompetitionschedule_edit")
   * @Method({"GET", "POST"})
   */
  public function editAction(Request $request, FtCompetitionSchedule $ftCompetitionSchedule)
  {
    $deleteForm = $this->createDeleteForm($ftCompetitionSchedule);
    $editForm = $this->createForm('AppBundle\Form\FtCompetitionScheduleType', $ftCompetitionSchedule);
    $editForm->handleRequest($request);

    if ($editForm->isSubmitted() && $editForm->isValid()) {
      $this->getDoctrine()->getManager()->flush();
      return $this->redirectToRoute('ftcompetitionschedule_show', array('id' => $ftCompetitionSchedule->getId()));
    }

    return $this->render('ftcompetitionschedule/edit.html.twig', array(
      'ftCompetitionSchedule' => $ftCompetitionSchedule,
      'edit_form' => $editForm->createView(),
      'delete_form' => $deleteForm->createView(),
    ));
  }

  /**
   * Deletes a FtCompetitionSchedule entity.
   *
   * @Route("/{id}", name="ftcompetitionschedule_delete")
   * @Method("DELETE")
   */
  public function deleteAction(Request $request, FtCompetitionSchedule $ftCompetitionSchedule)
  {
    $form = $this->createDeleteForm($ftCompetitionSchedule);
    $form->handleRequest($request);

    if ($form->isSubmitted() && $form->isValid()) {
      $em = $this->getDoctrine()->getManager();
      $em->remove($ftCompetitionSchedule);
      $em->flush();
    }

    return $this->redirectToRoute('ftcompetitionschedule_index');
  }

  /**
   * Creates a form to delete a FtCompetitionSchedule entity.
   *
   * @param FtCompetitionSchedule $ftCompetitionSchedule The FtCompetitionSchedule entity
   *
   * @return \Symfony\Component\Form\Form The form
   */
  private function createDeleteForm(FtCompetitionSchedule $ftCompetitionSchedule)
  {
    return $this->createFormBuilder()
      ->setAction($this->generateUrl('ftcompetitionschedule_delete', array('id' => $ftCompetitionSchedule->getId())))
      ->setMethod('DELETE')
      ->getForm();
  }
}
