<?php

namespace AppBundle\Controller;

use AppBundle\Entity\ReporterBonus;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use AppBundle\Entity\FtFixtureUserOdd;

/**
 * FtFixtureUserOdd controller.
 *
 * @Route("/admin/ftfixtureuserodd")
 */
class FtFixtureUserOddController extends Controller
{
	/**
	 * Lists FtFixtureUserOdd from last 45 days entities.
	 *
	 * @Route("/", name="ftfixtureuserodd_index")
	 * @Method("GET")
	 */
	public function indexAction()
	{
		$em = $this->getDoctrine()->getManager();
		$ftFtFixtureUserOdds = $em->getRepository('AppBundle:FtFixtureUserOdd')->findReporterMatchesLast45Days();

		return $this->render('reporter/index.html.twig', array(
			'FtFixtureUserOdds' => $ftFtFixtureUserOdds
		));
	}

    /**
     * Execute Reporter Controller Command
     *
     * @Route("/reporter-bonus-command", name="ftfixtureuserodd_executecommand")
     * @Method({"GET", "POST"})
     */
    public function commandAction(Request $request)
    {
        $formBuilder = $this->createFormBuilder()
            ->add('month', NumberType::class, array('required' => true))
            ->add('year', NumberType::class, array('required' => true));

        $form = $formBuilder->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $month = $data['month'];
            $year = $data['year'];

            $em = $this->getDoctrine()->getManager();

            $monthStart = date("$year-$month-01 00:00:00");
            $monthEnd 	= date('Y-m-t 23:59:59', mktime(0, 0, 0, $month + 1, 0, $year));

            $reporterBonus = $em->getRepository('AppBundle:FtFixtureUserOdd')->getMonthlyUserOdds($monthStart, $monthEnd);

            if ($reporterBonus) {
                foreach ($reporterBonus as $bonus) {
                    $authorBonus = $em->getRepository('AppBundle:ReporterBonus')->findOneBy(array('monthNo' => $bonus['month_no'], 'yearNo' => $bonus['year_no'], 'authorId' => $bonus['author_id']));

                    if (!$authorBonus) $authorBonus = new ReporterBonus();

                    $authorBonus->setMonthNo($bonus['month_no']);
                    $authorBonus->setYearNo($bonus['year_no']);
                    $authorBonus->setPoints($bonus['points']);
                    $authorBonus->setAuthorId($bonus['author_id']);
                    $authorBonus->setAuthorName($bonus['author_name']);
                    $authorBonus->setBetCount($bonus['bet_count']);
                    $authorBonus->setBetProfit($bonus['bet_profit']);

                    $em->persist($authorBonus);
                    $em->flush();
                }
            }

            $em->clear();

            return $this->redirectToRoute('ftfixtureuserodd_executecommand');
        }

        return $this->render('tools/reporter-bonus/index.html.twig', array(
            'form' => $form->createView(),
        ));
    }

	/**
	 * Creates a new FtFixtureUserOdd entity.
	 *
	 * @Route("/new", name="ftfixtureuserodd_new")
	 * @Method({"GET", "POST"})
	 */
	public function newAction(Request $request)
	{
		$ftFixtureUserOdd = new FtFixtureUserOdd();

		$form = $this->createForm('AppBundle\Form\FtFixtureUserOddType', $ftFixtureUserOdd);
		$form->handleRequest($request);

		if ($form->isSubmitted() && $form->isValid()) {
			$em = $this->getDoctrine()->getManager();
			$em->persist($ftFixtureUserOdd);
			$em->flush();

			return $this->redirectToRoute('ftfixtureuserodd_show', array('id' => $ftFixtureUserOdd->getId()));
		}

		return $this->render('reporter/new.html.twig', array(
			'teams' => $ftFixtureUserOdd,
			'form' => $form->createView(),
		));
	}

	/**
	 * Finds and displays a FtFixtureUserOdd entity.
	 *
	 * @Route("/{id}", name="ftfixtureuserodd_show")
	 * @Method("GET")
	 */
	public function showAction(FtFixtureUserOdd $ftFixtureUserOdd)
	{
		$deleteForm = $this->createDeleteForm($ftFixtureUserOdd);

		return $this->render('reporter/show.html.twig', array(
			'ftFixtureUserOdd' => $ftFixtureUserOdd,
			'delete_form' => $deleteForm->createView(),
		));
	}

	/**
	 * Displays a form to edit an existing FtFixtureUserOdd entity.
	 *
	 * @Route("/{id}/edit", name="ftfixtureuserodd_edit")
	 * @Method({"GET", "POST"})
	 */
	public function editAction(Request $request, FtFixtureUserOdd $ftFixtureUserOdd)
	{
		$deleteForm = $this->createDeleteForm($ftFixtureUserOdd);
		$editForm = $this->createForm('AppBundle\Form\FtFixtureUserOddType', $ftFixtureUserOdd);
		$editForm->handleRequest($request);

		if ($editForm->isSubmitted() && $editForm->isValid()) {
			$this->getDoctrine()->getManager()->flush();
			return $this->redirectToRoute('ftfixtureuserodd_show', array('id' => $ftFixtureUserOdd->getId()));
		}

		return $this->render('reporter/edit.html.twig', array(
			'ftFixtureUserOdd' => $ftFixtureUserOdd,
			'edit_form' => $editForm->createView(),
			'delete_form' => $deleteForm->createView(),
		));
	}

	/**
	 * Deletes a FtFixtureUserOdd entity.
	 *
	 * @Route("/{id}", name="ftfixtureuserodd_delete")
	 * @Method("DELETE")
	 */
	public function deleteAction(Request $request, FtFixtureUserOdd $ftFixtureUserOdd)
	{
		$form = $this->createDeleteForm($ftFixtureUserOdd);
		$form->handleRequest($request);

		if ($form->isSubmitted() && $form->isValid()) {
			$em = $this->getDoctrine()->getManager();
			$em->remove($ftFixtureUserOdd);
			$em->flush();
		}

		return $this->redirectToRoute('ftfixtureuserodd_index');
	}

	/**
	 * Creates a form to delete a FtFixtureUserOdd entity.
	 *
	 * @param FtFixtureUserOdd $ftFixtureUserOdd The FtFixtureUserOdd entity
	 *
	 * @return \Symfony\Component\Form\Form The form
	 */
	private function createDeleteForm(FtFixtureUserOdd $ftFixtureUserOdd)
	{
		return $this->createFormBuilder()
            ->setAction($this->generateUrl('ftfixtureuserodd_delete', array('id' => $ftFixtureUserOdd->getId())))
            ->setMethod('DELETE')
            ->getForm();
	}
}
