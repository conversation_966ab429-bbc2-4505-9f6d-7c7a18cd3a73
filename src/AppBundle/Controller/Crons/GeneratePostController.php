<?php

namespace AppBundle\Controller\Crons;

use Symfony\Component\HttpFoundation\Response;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;

/**
 * GeneratePost controller.
 *
 * @Route("/cron")
 */
class GeneratePostController extends Controller
{

    /**
     * Create domain posts
     *
     * @Method("GET")
     * @Rest\Get("/posts")
     */
    public function generatePostAction($sports = null, $domain = null) {
        try {
            $em                 = $this->getDoctrine()->getManager();
            $logger             = $this->get('logger');
            $helper             = $this->container->get("wbs.globalhelper.service");
            $localeFallback     = $this->container->getParameter('locale');
            $domains            = array();
            $domainConnections  = array();

            //$time = -microtime(true);

            // TODO right now previews are created only for betarades.gr
            // but it should be
            // $domains = $em->getRepository('AppBundle:Domain')->findBy(array('isActive' => true));
            // if (!$domain) {
            //   $domains = $em->getRepository('AppBundle:Domain')->findBy(array('isActive' => true));
            // }
            // else {
            //   $domains[] = $em->getRepository('AppBundle:Domain')->findOneById((int) $domain);
            // }

            if ($domain) {
                $domains[(int) $domain] = $em->getRepository('AppBundle:Domain')->findOneById((int) $domain);
                $domainConnections[(int) $domain] = $helper->getDomainConnection($domains[(int) $domain]);
            }
            else {
                // getAllActiveDomains
                $activeDomains = $em->getRepository('AppBundle:Domain')->findBy(array('isActive' => true));
                if ($activeDomains) {
                    foreach ($activeDomains as $activeDomain) {
                        $domains[$activeDomain->getId()]        = $activeDomain;
                        $domainConnections[$activeDomain->getId()] = $helper->getDomainConnection($activeDomain);
                    }
                }
            }

            // temp fix is to get the domain name from the parameters.yml configuration
            // get active domains here and create the connections
            // $domainName = $this->container->hasParameter('domain.name') ? $this->container->getParameter('domain.name') : null;
            // $tempDomain = $em->getRepository('AppBundle:Domain')->findOneByName($domainName);

            // $connection2 = $helper->getDomainConnection($tempDomain);

            // fast
            $leagues = $em->getRepository('AppBundle:Sport')->getActiveDomainCompetitions();
            // CAUTION
            // get sport Ids to use inside query IN CLAUSE is much faster than looping through leagues
            // and getting matches query for each league
            $leaguesInClause = $helper->getSportsInClause($leagues);

            if (!$leaguesInClause) {
                echo '<pre>';
                var_dump('no leagues for active domains found');
                echo '</pre>';
                die();
            }

            $date = strtotime("+5 day");
            $dateFrom = date('Y-m-d 06:00:00', time());
            $dateTo = date('Y-m-d 05:59:59', $date);

            // getLeagueMatches is run for greek language only.
            // I should be getting the fixtures without a language setting, and add league_match_name, h_team_name, a_team_name using domain locale!!!
            $leagueMatches = $em->getRepository('AppBundle:FtFixture')->getLeagueMatches($leaguesInClause, $dateFrom, $dateTo);

            // store temporary leagueNames, teamNames so as not to query again and again for the same objects
            $leagueNames  = array();
            $teamNames    = array();

            if ($leagueMatches) {
                foreach ($leagueMatches as $leagueMatch) {
                    if ($sports) {
                        if (array_search((int) $leagueMatch['sport_id'], $sports) === false) {
                        continue;
                        }
                    }

                    // if job is running for a specific domain
                    if ($domain) {
                        // if domain is not connected to current leagueMatch sport, continue
                        // check if leagueMatch sport_id exists in $leagues (activeDomainCompetitions), if parameter $domain not in domains continue
                        if (isset($leagues[$leagueMatch['sport_id']]) && isset($leagues[$leagueMatch['sport_id']]['domains'])) {
                            if (array_search($domain, $leagues[(int) $leagueMatch['sport_id']]['domains']) === false) {
                                continue;
                            }
                        }
                        else {
                            continue; 
                        }

                        $this->prepareForInsert($em, $leagueNames, $teamNames, $domains[$domain], $localeFallback, $leagueMatch, $domainConnections, $helper);

                    } // if $domain
                    else {
                        // job should be running for all active domains
                        // array of domains connected to fixture sport id
                        if ($curDomains = (isset($leagues[$leagueMatch['sport_id']]['domains'])) ? $leagues[$leagueMatch['sport_id']]['domains'] : null) {
                            if ($curDomains) {
                                foreach ($curDomains as $curDomain) {
                                    $this->prepareForInsert($em, $leagueNames, $teamNames, $domains[$curDomain], $localeFallback, $leagueMatch, $domainConnections, $helper);
                                }
                            }
                        }
                    }
                }
            }

            // close all open mysql connections
            foreach ($domainConnections as $domainConnection) {
                $domainConnection->close();
            }
            
            //$time += microtime(true);
            //echo PHP_EOL.' total time:: ' . sprintf('%f', $time);

            $em->clear();
            return new Response('Generate post finished!');
        }
        catch (\Exception $e) {
            return new Response($e->getMessage());
        }
    }

    private function insertPost($helper, $conn, $dbPrefix, $createPosts, $data) {
        // die('must fix insertPost insert post sql');
        try {
            $conn->beginTransaction();

            // init
            $postId   = 0;
            $postSlug = null;

            // if posts should be created for domain
            if ($createPosts) {
                $postTitle = $data['h_team_name']." - ".$data['a_team_name'];
                $postSlug = $helper->sluggify($data['h_team_name'].'-'.$data['a_team_name'].'-'.$data['f_id']);

                $tempDt = new \DateTime();
                $postDate = $tempDt->format('Y-m-d H:i:s');

                $sqlInsert = "
                INSERT INTO ".$dbPrefix."posts (post_content, post_title, post_excerpt, comment_status, ping_status, post_name, post_type, post_date, post_date_gmt) VALUES ('', '$postTitle', '', 'closed', 'closed', '$postSlug', 'previews', '$postDate', '$postDate');
                ";

                $stmt = $conn->prepare($sqlInsert);
                $stmt->execute();

                $postId = $conn->lastInsertId();
            } // if ($createPosts)

                $sqlInsert = "
            INSERT INTO " . $dbPrefix . "wbs_previews (sport_id, fixture_id, post_id, slug, match_date_time, league_match_name, season, week, status, h_team_id, h_team_name, a_team_id, a_team_name, ht_score, ft_score, et_score, pt_score, events, is_football, stadium, weather_api_id, versus_history, h_team_latest, a_team_latest, hide_in_cb, `group`) VALUES (
                " . $data['sport_id'] . ",
                " . $data['f_id'] . ",
                " . $postId . ",
                " . ($postSlug ? "'" . $postSlug . "'" : 'null') . ",
                '" . $data['match_datetime'] . "',
                '" . $data['league_match_name'] . "',
                '" . $data['season'] . "',
                " . $data['week'] . ",
                '" . $data['status'] . "',
                " . $data['h_team'] . ",
                '" . $data['h_team_name'] . "',
                " . $data['a_team'] . ",
                '" . $data['a_team_name'] . "',
                '" . $data['ht_score'] . "',
                '" . $data['ft_score'] . "',
                '" . $data['et_score'] . "',
                '" . $data['pt_score'] . "',
                '" . $data['events'] . "',
                true,
                '" . $data['stadium'] . "',
                " . $data['weather_api_id'] . ",
                '" . $data['versus_history'] . "',
                '" . $data['h_team_latest'] . "',
                '" . $data['a_team_latest'] . "',
                '" . $data['hide_in_cb'] . "',
                '" . $data['league_group'] . "'
                )
            ";

                $stmt = $conn->prepare($sqlInsert);
                $stmt->execute();

                $previewId = $conn->lastInsertId();
                $conn->commit();
                return $previewId;
            }

        catch (Exception $e) {
            $logger->critical($e->getMessage());
            $conn->rollback();
        }

        return null;
    }

    private function updatePost($conn, $dbPrefix, $data, $wpPreview) {
        $sqlUpdate = "
        UPDATE ".$dbPrefix."wbs_previews SET
        sport_id = ".$data['sport_id'].",
        match_date_time = '".$data['match_datetime']."',
        league_match_name = '".$data['league_match_name']."',
        status = '".$data['status']."',
        h_team_id = ".$data['h_team'].",
        h_team_name = '".$data['h_team_name']."',
        a_team_id = ".$data['a_team'].",
        a_team_name = '".$data['a_team_name']."',
        ht_score = '".$data['ht_score']."',
        ft_score = '".$data['ft_score']."',
        et_score = '".$data['et_score']."',
        pt_score = '".$data['pt_score']."',
        events = '".$data['events']."',
        weather_api_id = ".$data['weather_api_id'].",
        versus_history = '".$data['versus_history']."',
        h_team_latest = '".$data['h_team_latest']."',
        a_team_latest = '".$data['a_team_latest']."',
        `group` = '".$data['league_group']."',
        updated_at = '".date('Y-m-d H:i:s', time())."'
        WHERE id = ".$wpPreview->getPreviewId()."
        ";

        $stmt = $conn->prepare($sqlUpdate);
        $stmt->execute();

        return true;
    }

    private function updatePostResult($conn, $dbPrefix, $data, $previewId) {
        $sqlUpdate = "
        UPDATE ".$dbPrefix."wbs_previews SET
        status = '".$data['status']."',
        ht_score = '".$data['ht_score']."',
        ft_score = '".$data['ft_score']."',
        et_score = '".$data['et_score']."',
        pt_score = '".$data['pt_score']."',
        events = '".$data['events']."',
        updated_at = '".date('Y-m-d H:i:s', time())."'
        WHERE id = ".$previewId."
        ";

        $stmt = $conn->prepare($sqlUpdate);
        $stmt->execute();

        return true;
    }

    /**
     * Update domain previews with finished matches
     *
     * @Method("GET")
     * @Rest\Get("/finished")
     */
    public function updateFinishedPostAction() {
        $em     = $this->getDoctrine()->getManager();
        $logger = $this->get('logger');
        $helper = $this->container->get("wbs.globalhelper.service");
        $calc   = $this->container->get("wbs.teamstats.service");
        $translator = $this->get('translator');
        $sportsClearCache = array();
        // TODO REMOVE WHEN MUNDIAL IS FINISHED
        $mundialSportIds = array(712, 713, 714, 715, 716, 717, 718, 719, 720);

        // $domains = get active domains;
        $domains      = $em->getRepository('AppBundle:Domain')->findBy(array('isActive' => true));
        $connections  = array();
        $domainsArr   = array();

        if ($domains) {
	        foreach ($domains as $domain) {
	            $connections[$domain->getId()] = $helper->getDomainConnection($domain);
	            $domainsArr[$domain->getId()] = $domain;
	        }
        }

        $finishedDomainMatches = $em->getRepository('AppBundle:FtFixture')->getFinishedMatchesWithPreviews();

        if ($finishedDomainMatches) {
            foreach ($finishedDomainMatches as $single) {

                //if (in_array($single['status'], array('F', 'FE', 'FP'))) {
                    //$logger->info('[CVYZ] updating game '.$single['fixture_id'].' - '.$single['sport_id'].' - '.$single['season']);

                    // if game finished is a mundial game, then clear cache for team mundial template (BETARADES ONLY)
                    if (in_array((int) $single['sport_id'], $mundialSportIds)) {
                        $betarades_domain = $em->getRepository('AppBundle:Domain')->findOneById(2);
                        $protocol = (true === $betarades_domain->getProtocol() ? 'https' : 'http');
                        $nodePort = $betarades_domain->getNodePortId();
                        $hostName = $betarades_domain->getDbNodeHost();
            
                        $domainSportIds = implode('-', $domainSports[$domainId]);
                        $url = $protocol."://".$hostName.":".$nodePort."/delete-mundial-cache/".$single['h_team'].'-'.$single['a_team'];
                        $helper->curlRequest($url, false, null);
                    }

                    if ($single['domains']) {
                        $hasStandings = 0;
                        foreach ($single['domains'] as $domainId => $domainValues) {
                            // update DOMAIN preview
                            $this->updatePostResult( $connections[ $domainId ], $domainsArr[ $domainId ]->getDbPrefix(), $single, $domainValues['preview_id'] );

                            // gather list of sportIds and seasons to clear their DOMAIN memcache
                            if ( 1 === (int) $single['is_additional'] ) {
                                $sportsClearCache[ (int) $single['parent_id'] ]['sport_id'] = (int) $single['parent_id'];
                                $sportsClearCache[ (int) $single['parent_id'] ]['domain'][] = $domainId;
                            } else {
                                $sportsClearCache[ (int) $single['sport_id'] ]['sport_id'] = (int) $single['sport_id'];
                                $sportsClearCache[ (int) $single['sport_id'] ]['domain'][] = $domainId;
                            }


                            if ( ! $hasStandings ) {
                                $hasStandings = (int) $domainValues['has_standings'];
                            }

                            // remove preview domain fixture relationship
                            $dfr = $em->getRepository( 'AppBundle:DomainFtFixtureRelationship' )->findOneById( $domainValues['id'] );
                            if ( $dfr ) {
                                $em->remove( $dfr );
                                $em->flush();
                            }
                        }
                    }
                    
                    // do standings only if has_standings and sport_id + season exist
                    if (1 === (int) $hasStandings && $single['sport_id'] && $single['season']) {
                        //$logger->info('[CVYZ] team standings '.$single['h_team'].' - '.$single['league_id'].' - '.$single['ft1'].' - '.$single['ft2']);
                        // calculate team standings
                        $homeStandings = $this->calculateTeamStandings($single['h_team'], $single['sport_id'], $single['league_id'], $single['season'], $single['ft1'], $single['ft2'], true);
                        $awayStandings = $this->calculateTeamStandings($single['a_team'], $single['sport_id'], $single['league_id'], $single['season'], $single['ft1'], $single['ft2'], false);

                        $this->updateTeamStandings($single['h_team'], $homeStandings);
                        $this->updateTeamStandings($single['a_team'], $awayStandings);

                        // after updating team standings for both teams re-calculate all team ranks for sportId, season
                        $this->updateTeamStandingsRank($single['sport_id'], $single['league_id'], $single['season']);

                        // gather list of sportIds and seasons to clear their DOMAIN memcache
                        // $sportsClearCache[] = array('sportId' => $single['sport_id'], 'season' => $single['season']);

                        // gather list of sportIds and seasons to clear their DOMAIN memcache
                        // $sportsClearCache[] = $single['sport_id'];
                    }

                    // continue;

                    // calculate team stats HOME/AWAY only if league season sport is set for sport_id
                    if ($single['sport_id']) {
                        $seasonSport = $em->getRepository('AppBundle:FtLeagueSeasonSport')->getLatestSportSeason($single['sport_id']);
                        if ($seasonSport) {
                            $seasonSportId = $seasonSport['id'];

                            // calculate team stats for home team
                            $homeTeamStats = $em->getRepository('AppBundle:TeamStats')->findOneBy(array('leagueSeasonId' => $seasonSportId, 'teamId' => $single['h_team']));
                            try {
                                if (!$homeTeamStats) {
                                    $homeTeamStats = new \AppBundle\Entity\TeamStats();
                                    $homeTeamStats->setLeagueSeasonId($seasonSportId);
                                    $homeTeamStats->setTeamId($single['h_team']);

                                    $em->persist($homeTeamStats);
                                    $em->flush();
                                }

                                $calc->calculateTeamStats($single['h_team'], $seasonSportId, $homeTeamStats, $single['fixture_id']);
                                //$calc->fixStreakTeamStats($single['h_team'], $single['sport_id'], $single['season'], $seasonSportId);
                            }
                            catch (\Exception $e) {
                                $logger->critical($e->getMessage());
                            }

                            // calculate team stats for away team
                            $awayTeamStats = $em->getRepository('AppBundle:TeamStats')->findOneBy(array('leagueSeasonId' => $seasonSportId, 'teamId' => $single['a_team']));
                            try {
                                if (!$awayTeamStats) {
                                    $awayTeamStats = new \AppBundle\Entity\TeamStats();
                                    $awayTeamStats->setLeagueSeasonId($seasonSportId);
                                    $awayTeamStats->setTeamId($single['a_team']);

                                    $em->persist($awayTeamStats);
                                    $em->flush();
                                }

                                $calc->calculateTeamStats($single['a_team'], $seasonSportId, $awayTeamStats, $single['fixture_id']);
                                //$calc->fixStreakTeamStats($single['a_team'], $single['sport_id'], $single['season'], $seasonSportId);
                            }
                            catch (\Exception $e) {
                                $logger->critical($e->getMessage());
                            }
                        }
                    }
                //} // if in_array status
            } // foreach $finishedDomainMatches
        } // if $finishedDomainMatches
        if ($sportsClearCache) {
            $projectDir = $this->get('kernel')->getProjectDir();
            $environment = $this->container->get('kernel')->getEnvironment();
            $helper->clearStandingsMemcache($em, $sportsClearCache, $translator, $projectDir, $environment);
        }

        $em->clear();
        return new Response('update of recently finished matches done');
    }

    private function calculateTeamStandings($teamId, $sportId, $leagueId, $season, $ft1, $ft2, $isHome) {
        $result = array();

        $overall_p = 1;

        if ($ft1 > $ft2) {
        if ($isHome) {
            $overall_pts  = 3;
            $overall_w    = 1;
            $overall_d    = 0;
            $overall_l    = 0;
            $overall_gf   = $ft1;
            $overall_ga   = $ft2;
            $home_p       = 1;
            $home_w       = 1;
            $home_d       = 0;
            $home_l       = 0;
            $home_gf      = $ft1;
            $home_ga      = $ft2;
            $away_p       = 0;
            $away_w       = 0;
            $away_d       = 0;
            $away_l       = 0;
            $away_gf      = 0;
            $away_ga      = 0;
        }
        else {
            $overall_pts  = 0;
            $overall_w    = 0;
            $overall_d    = 0;
            $overall_l    = 1;
            $overall_gf   = $ft2;
            $overall_ga   = $ft1;
            $home_p       = 0;
            $home_w       = 0;
            $home_d       = 0;
            $home_l       = 0;
            $home_gf      = 0;
            $home_ga      = 0;
            $away_p       = 1;
            $away_w       = 0;
            $away_d       = 0;
            $away_l       = 1;
            $away_gf      = $ft2;
            $away_ga      = $ft1;
        }
        }
        else if ($ft1 === $ft2) {
        if ($isHome) {
            $overall_pts  = 1;
            $overall_w    = 0;
            $overall_d    = 1;
            $overall_l    = 0;
            $overall_gf   = $ft1;
            $overall_ga   = $ft2;
            $home_p       = 1;
            $home_w       = 0;
            $home_d       = 1;
            $home_l       = 0;
            $home_gf      = $ft1;
            $home_ga      = $ft2;
            $away_p       = 0;
            $away_w       = 0;
            $away_d       = 0;
            $away_l       = 0;
            $away_gf      = 0;
            $away_ga      = 0;
        }
        else {
            $overall_pts  = 1;
            $overall_w    = 0;
            $overall_d    = 1;
            $overall_l    = 0;
            $overall_gf   = $ft2;
            $overall_ga   = $ft1;
            $home_p       = 0;
            $home_w       = 0;
            $home_d       = 0;
            $home_l       = 0;
            $home_gf      = 0;
            $home_ga      = 0;
            $away_p       = 1;
            $away_w       = 0;
            $away_d       = 1;
            $away_l       = 0;
            $away_gf      = $ft2;
            $away_ga      = $ft1;
        }
        }
        else if ($ft1 < $ft2) {
        if ($isHome) {
            $overall_pts  = 0;
            $overall_w    = 0;
            $overall_d    = 0;
            $overall_l    = 1;
            $overall_gf   = $ft1;
            $overall_ga   = $ft2;
            $home_p       = 1;
            $home_w       = 0;
            $home_d       = 0;
            $home_l       = 1;
            $home_gf      = $ft1;
            $home_ga      = $ft2;
            $away_p       = 0;
            $away_w       = 0;
            $away_d       = 0;
            $away_l       = 0;
            $away_gf      = 0;
            $away_ga      = 0;
        }
        else {
            $overall_pts  = 3;
            $overall_w    = 1;
            $overall_d    = 0;
            $overall_l    = 0;
            $overall_gf   = $ft2;
            $overall_ga   = $ft1;
            $home_p       = 0;
            $home_w       = 0;
            $home_d       = 0;
            $home_l       = 0;
            $home_gf      = 0;
            $home_ga      = 0;
            $away_p       = 1;
            $away_w       = 1;
            $away_d       = 0;
            $away_l       = 0;
            $away_gf      = $ft2;
            $away_ga      = $ft1;
        }
        }

        $result['sport_id']     = (int) $sportId;
        $result['league_id']    = strval($leagueId);
        $result['season']       = strval($season);
        $result['team_id']      = (int) $teamId;
        $result['rank']         = 1;
        $result['cnf']          = '';
        $result['overall_pts']  = $overall_pts;
        $result['overall_p']    = $overall_p;
        $result['overall_w']    = $overall_w;
        $result['overall_d']    = $overall_d;
        $result['overall_l']    = $overall_l;
        $result['overall_gf']   = $overall_gf;
        $result['overall_ga']   = $overall_ga;
        $result['home_p']       = $home_p;
        $result['home_w']       = $home_w;
        $result['home_d']       = $home_d;
        $result['home_l']       = $home_l;
        $result['home_gf']      = $home_gf;
        $result['home_ga']      = $home_ga;
        $result['away_p']       = $away_p;
        $result['away_w']       = $away_w;
        $result['away_d']       = $away_d;
        $result['away_l']       = $away_l;
        $result['away_gf']      = $away_gf;
        $result['away_ga']      = $away_ga;

        return $result;
    }

    /*
    * ASSUMING there already exists a standings table for sport/leage/season even with 0 values
    */
    private function updateTeamStandings($teamId, $values) {
        $em     = $this->getDoctrine()->getManager();
        $logger = $this->get('logger');
        $helper = $this->container->get("wbs.globalhelper.service");

        $teamStandings = $em->getRepository('AppBundle:FtLeagueStandings')->findOneBy(array('sportId' => $values['sport_id'], 'lId' => $values['league_id'], 'season' => $values['season'], 'teamId' => $teamId));

        if ($teamStandings) {
            // $dateTime = $helper->getCurrentTimestampDate();
            $dateTime = new \DateTime();

            $teamStandings->setOverallP($teamStandings->getOverallP() + $values['overall_p']);
            $teamStandings->setOverallW($teamStandings->getOverallW() + $values['overall_w']);
            $teamStandings->setOverallD($teamStandings->getOverallD() + $values['overall_d']);
            $teamStandings->setOverallL($teamStandings->getOverallL() + $values['overall_l']);
            $teamStandings->setOverallGF($teamStandings->getOverallGF() + $values['overall_gf']);
            $teamStandings->setOverallGA($teamStandings->getOverallGA() + $values['overall_ga']);
            $teamStandings->setOverallPts($teamStandings->getOverallPts() + $values['overall_pts']);
            $teamStandings->setHomeP($teamStandings->getHomeP() + $values['home_p']);
            $teamStandings->setHomeW($teamStandings->getHomeW() + $values['home_w']);
            $teamStandings->setHomeD($teamStandings->getHomeD() + $values['home_d']);
            $teamStandings->setHomeL($teamStandings->getHomeL() + $values['home_l']);
            $teamStandings->setHomeGF($teamStandings->getHomeGF() + $values['home_gf']);
            $teamStandings->setHomeGA($teamStandings->getHomeGA() + $values['home_ga']);
            $teamStandings->setAwayP($teamStandings->getAwayP() + $values['away_p']);
            $teamStandings->setAwayW($teamStandings->getAwayW() + $values['away_w']);
            $teamStandings->setAwayD($teamStandings->getAwayD() + $values['away_d']);
            $teamStandings->setAwayL($teamStandings->getAwayL() + $values['away_l']);
            $teamStandings->setAwayGF($teamStandings->getAwayGF() + $values['away_gf']);
            $teamStandings->setAwayGA($teamStandings->getAwayGA() + $values['away_ga']);
            $teamStandings->setUpdatedAt($dateTime);

            try {
                $em->persist($teamStandings);
                $em->flush();

                $em->clear();
            }
            catch (\Exception $e) {
                $em->clear();
                $logger->critical($e->getMessage());
                return false;
            }
        }

        return true;
    }

    private function updateTeamStandingsRank($sportId, $leagueId, $season) {
        $em = $this->getDoctrine()->getManager();

        $newRank = "
        SELECT (@cnt := @cnt + 1) AS rowNumber, id FROM ft_league_standings CROSS JOIN (SELECT @cnt := 0) AS dummy WHERE sport_id = $sportId AND season = '$season' AND l_id = '$leagueId' ORDER BY overall_pts DESC;
        ";

        $stmt = $em->getConnection()->prepare($newRank);
        $stmt->execute();
        $result = $stmt->fetchAll();

        if ($result) {
            foreach ($result as $single) {
                $updateRank = "UPDATE ft_league_standings SET `rank` = " . $single['rowNumber'] . " WHERE id = " . $single['id'];

                try {
                    $stmt = $em->getConnection()->prepare($updateRank);
                    $stmt->execute();
                    // $logger = $this->parser;
                    // $logger->info("Updated $hasRecords LEAGUE records for $lan");
                }
                catch (\Exception $e) {
                    $logger = $this->parser;
                    $logger->critical($e->getMessage());
                    return false;
                }
            }
        }

        $em->clear();
        return true;
    }

    public function clearDomainStandingsCacheAction($sportOptions = null, $domainId = null)
    {
        $em               = $this->getDoctrine()->getManager();
        $helper           = $this->container->get("wbs.globalhelper.service");
        $translator       = $this->get('translator');
        $sportsClearCache = array();
        $projectDir = $this->get('kernel')->getProjectDir();
        $environment = $this->container->get('kernel')->getEnvironment();

        if (!$domainId) return false;
        if (!$sportOptions) return false;

        $domain = $em->getRepository('AppBundle:Domain')->findOneById($domainId);

        $sports = $em->getRepository('AppBundle:Sport')->findById($sportOptions);
        foreach ($sports as $sport) {
            if (1 === (int) $sport->getIsAdditional()) {
                $sportsClearCache[(int) $sport->getParentId()]['sport_id'] = (int) $sport->getParentId();
                $sportsClearCache[(int) $sport->getParentId()]['domain'][] = $domainId;
            } else {
                $sportsClearCache[(int) $sport->getId()]['sport_id'] = (int) $sport->getId();
                $sportsClearCache[(int) $sport->getId()]['domain'][] = $domainId;
            }
        }

        if ($domain && $sportOptions) {
            $helper->clearStandingsMemcache($em, $sportsClearCache, $translator, $projectDir, $environment);
        }

        return new Response('Finished');
    }

    private function prepareForInsert($em, &$leagueNames, &$teamNames, $domain, $localeFallback, $leagueMatch, $domainConnections, $helper) {
        $sportId        = $leagueMatch['sport_id'];
        $hTeam          = $leagueMatch['h_team'];
        $aTeam          = $leagueMatch['a_team'];
        $season         = $leagueMatch['season'];
        $domainId       = $domain->getId();
        $domainLocale   = $domain->getLanguage()->getLocale();
        $domainDbPrefix = $domain->getDbPrefix();
        $createPosts    = $domain->getCreatePosts();
        $hideInChampionsBar = false;

        $teamFootballField = $em->getRepository('AppBundle:TeamFootballField')->getDefaultFootballFieldByTeamId($hTeam);

        // LEAGUE NAMES
        // query leagueName if not already set in temporary leagueNames[]
        // key is: sportId_locale
        // value is: league name for sport in locale
        if (!isset($leagueNames[$sportId.'_'.$domainLocale])) {
	        $leagueName = $em->getRepository('AppBundle:FtLeague')->getLeagueNameByLocale($sportId, $domainLocale, $localeFallback);
	        $leagueNames[$sportId.'_'.$domainLocale] = $leagueName;
        }

        // TEAM NAMES
        if (!isset($teamNames[$hTeam.'_'.$domainLocale])) {
	        $hTeamName = $em->getRepository('AppBundle:FtTeam')->getTeamNameByLocale($hTeam, $domainLocale, $localeFallback);
	        $teamNames[$hTeam.'_'.$domainLocale] = $hTeamName;
        }

        if (!isset($teamNames[$aTeam.'_'.$domainLocale])) {
	        $aTeamName = $em->getRepository('AppBundle:FtTeam')->getTeamNameByLocale($aTeam, $domainLocale, $localeFallback);
	        $teamNames[$aTeam.'_'.$domainLocale] = $aTeamName;
        }

        if (371 === $sportId) {
            $hideInChampionsBar = 1;
        }

        $leagueMatch['league_match_name'] = $leagueNames[$sportId.'_'.$domainLocale];
        $leagueMatch['h_team_name']       = $teamNames[$hTeam.'_'.$domainLocale];
        $leagueMatch['a_team_name']       = $teamNames[$aTeam.'_'.$domainLocale];
        $leagueMatch['stadium']           = ($domainLocale == 'el' && $teamFootballField) ? addcslashes($teamFootballField->getName(), "'") : '';
        $leagueMatch['weather_api_id']    = $teamFootballField ? $teamFootballField->getWeatherApiId() : 0;
        $leagueMatch['hide_in_cb']        = $hideInChampionsBar;
        $leagueMatch['hide_in_cb']        = $leagueMatch['hide_in_cb'] == '' ? 0 : $leagueMatch['hide_in_cb'];

        $versusResult = [];
        if (!empty($season)) {
            $versusHistory = $em->getRepository('AppBundle:FtFixture')->getTeamsHistoryLastTenSeasonsFinishedMatches($hTeam, $aTeam, $season);
            //$versusHistory2 = $em->getRepository('AppBundle:FtFixture')->getVersusHistory($hTeam, $aTeam);

            $leagueMatch['h_team_latest'] = addcslashes(serialize($em->getRepository('AppBundle:FtFixture')->getTeamLastestFiveMatchesForAllCompetitions($hTeam)), "'");
            $leagueMatch['a_team_latest'] = addcslashes(serialize($em->getRepository('AppBundle:FtFixture')->getTeamLastestFiveMatchesForAllCompetitions($aTeam)), "'");

            if ($versusHistory) {
                $wins = 0;
                $draws = 0;
                $loses = 0;
                $tempMatchDateTime = '';
                foreach ($versusHistory as $versusFtFixture) {
                    if ($tempMatchDateTime != $versusFtFixture['match_datetime']) {
                        if ($versusFtFixture['won'] == 1) {
                            $wins++;
                        } elseif ($versusFtFixture['draw'] == 1) {
                            $draws++;
                        } elseif ($versusFtFixture['lost'] == 1) {
                            $loses++;
                        }

                        $tempMatchDateTime = $versusFtFixture['match_datetime'];

                        $competitionName = !empty($versusFtFixture['sport_id'])
                            ? $em->getRepository('AppBundle:Sport')->findTranslationByTypeAndLocale($versusFtFixture['sport_id'])
                            : '';
                        $matchDateTime = strtotime($versusFtFixture['match_datetime']);
                        $matchDateTimeString = date('Y-m-d H:i', $matchDateTime);
                        $versusResult['matches'][$matchDateTimeString]['events'] = $versusFtFixture['events'];
                        $versusResult['matches'][$matchDateTimeString]['competition'] = isset($competitionName[0]) ? $competitionName[0]['title'] . ' - ' . $versusFtFixture['season'] : $versusFtFixture['season'];
                        $versusResult['matches'][$matchDateTimeString]['matchtitle'] = $versusFtFixture['h_name'] . ' - ' . $versusFtFixture['a_name'];

                        $versusResult['matches'][$matchDateTimeString]['result']['fulltime'] = $versusFtFixture['ft1'] . '-' . $versusFtFixture['ft2'];
                        $versusResult['matches'][$matchDateTimeString]['result']['halftime'] = $versusFtFixture['ht1'] . '-' . $versusFtFixture['ht2'];
                        switch ($versusFtFixture['status']) {
                            case 'FE':
                                $versusResult['matches'][$matchDateTimeString]['result']['extratime'] = $versusFtFixture['et1'] . '-' . $versusFtFixture['et2'];
                                break;
                            case 'FP':
                                $versusResult['matches'][$matchDateTimeString]['result']['extratime'] = $versusFtFixture['et1'] . '-' . $versusFtFixture['et2'];
                                $versusResult['matches'][$matchDateTimeString]['result']['penalties'] = $versusFtFixture['pt1'] . '-' . $versusFtFixture['pt2'];
                                break;
                        }
                    }
                }

                $versusResult['history'] = $wins . '-' . $draws . '-' . $loses;
            }
        }

//        $versusResult2 = [];
//        if ($versusHistory2) {
//            $wins = 0;
//            $draws = 0;
//            $loses = 0;
//            foreach ($versusHistory2 as $versusFtFixture) {
//
//                $wins = ($versusFtFixture->getFt1() > $versusFtFixture->getFt2()) ? $wins + 1 : $wins;
//                $draws = ($versusFtFixture->getFt1() == $versusFtFixture->getFt2()) ? $draws + 1 : $draws;
//                $loses = ($versusFtFixture->getFt1() < $versusFtFixture->getFt2()) ? $loses + 1 : $loses;
//
//                $competitionName = $em->getRepository('AppBundle:Sport')->findTranslationByTypeAndLocale($versusFtFixture->getSportId());
//                $matchDateTimeString = $versusFtFixture->getMatchDatetime()->format('Y-m-d H:i');
//                $versusResult2['matches'][$matchDateTimeString]['events'] = $versusFtFixture->getEvents();
//                $versusResult2['matches'][$matchDateTimeString]['competition'] = isset($competitionName[0]) ? $competitionName[0]['title'] . ' - ' . $versusFtFixture->getSeason() : $versusFtFixture->getSeason();
//
//                $versusResult2['matches'][$matchDateTimeString]['result']['fulltime'] = $versusFtFixture->getFt1() . '-' . $versusFtFixture->getFt2();
//                $versusResult2['matches'][$matchDateTimeString]['result']['halftime'] = $versusFtFixture->getHt1() . '-' . $versusFtFixture->getHt2();
//                switch ($versusFtFixture->getStatus()) {
//                    case 'FE':
//                        $versusResult2['matches'][$matchDateTimeString]['result']['extratime'] = $versusFtFixture->getEt1() . '-' . $versusFtFixture->getEt2();
//                        break;
//                    case 'FP':
//                        $versusResult2['matches'][$matchDateTimeString]['result']['extratime'] = $versusFtFixture->getEt1() . '-' . $versusFtFixture->getEt2();
//                        $versusResult2['matches'][$matchDateTimeString]['result']['penalties'] = $versusFtFixture->getPt1() . '-' . $versusFtFixture->getPt2();
//                        break;
//                }
//            }
//
//            $versusResult2['history'] = $wins . '-' . $draws . '-' . $loses;
//        }
        
        $leagueMatch['versus_history'] = addcslashes(serialize($versusResult), "'");
        //$leagueMatch['versus_history2'] = addcslashes(serialize($versusResult2), "'");

        $wpPreview = $em->getRepository('AppBundle:DomainFtFixture')->findOneBy(array('domainId' => $domainId, 'fixtureId' => $leagueMatch['f_id']));
        if ($wpPreview) {
            $wpPreviewTemp = $em->getRepository('AppBundle:DomainFtFixtureRelationship')->findOneBy(array('domainId' => $domainId, 'fixtureId' => $leagueMatch['f_id']));  
            if ($wpPreviewTemp) {
                // do update
                $this->updatePost(
                $domainConnections[$domainId],
                $domainDbPrefix, 
                $leagueMatch, 
                $wpPreviewTemp
                );
            }
        } else {
			// TODO this should be run foreach domain in $leagues
			$previewId = $this->insertPost(
                $helper,
                $domainConnections[$domainId],
                $domainDbPrefix,
                $createPosts,
                $leagueMatch
            );

            // if inserted
            if ($previewId) {
				// add permanent domain relationship (historic)
				$domainFtFixture = new \AppBundle\Entity\DomainFtFixture();
				$domainFtFixture->setPreviewId($previewId);
				$domainFtFixture->setFixtureId($leagueMatch['f_id']);
				$domainFtFixture->setDomainId($domainId);
				$domainFtFixture->setHomeTeamId($leagueMatch['h_team']);
				$domainFtFixture->setAwayTeamId($leagueMatch['a_team']);
				$em->persist($domainFtFixture);

				// add temp preview domain relationship (for fast recently finished updates)
				// add preview domain relationship
				$domainFtFixtureRel = new \AppBundle\Entity\DomainFtFixtureRelationship();
				$domainFtFixtureRel->setDomainId($domainId);
				$domainFtFixtureRel->setFixtureId($leagueMatch['f_id']);
				$domainFtFixtureRel->setPreviewId($previewId);

				$em->persist($domainFtFixtureRel);
				$em->flush();
            }
        } // if $wpPreview
    }
}
