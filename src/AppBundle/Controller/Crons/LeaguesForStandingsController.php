<?php

namespace AppBundle\Controller\Crons;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use FOS\RestBundle\Controller\Annotations as Rest;

use Symfony\Component\Cache\Adapter\FilesystemAdapter;

/**
 * GeneratePost controller.
 *
 * @Route("/cron")
 */
class LeaguesForStandingsController extends Controller
{
  /**
   * Get today's league + season for standings feed parse
   *
   * @Method("GET")
   * @Rest\Get("/leagues-for-standings")
   */
  public function getTodaysLeaguesForStandingsAction() {
    $result = array();

    $todaysLeagues = $this->get('kernel')->getProjectDir() . '/var/cache/' . $this->container->get('kernel')->getEnvironment() . '/todays_leagues';
    $cache = new FilesystemAdapter('', 0, $todaysLeagues);

    $myResult = $cache->deleteItem('standings.todays_leagues');

    $leaguesCache = $cache->getItem('standings.todays_leagues');

    if (!$leaguesCache->isHit()) {
      $em = $this->getDoctrine()->getManager();

      $date = strtotime('+1 day');
      $dateFrom = date('Y-m-d 06:00:00', time());
      $dateTo   = date('Y-m-d 05:59:59', $date);

      $sql = "
        SELECT sport_id, (SELECT l_id FROM ft_league_sport ls WHERE ls.sport_id = tbl.sport_id) league_id, season, domain_id FROM (
          SELECT DISTINCT ds.sport_id, (SELECT lss.season FROM ft_league_season_sport lss WHERE lss.sport_id = ds.sport_id ORDER BY lss.season DESC LIMIT 1) season, d.id domain_id
          FROM
            domain_sport ds
              LEFT JOIN domain d ON ds.domain_id = d.id
          WHERE
              ds.has_posts = 1
          AND ds.has_standings = 1
          AND d.is_active = 1
          AND NOT EXISTS (SELECT sport_id FROM ft_fixture f WHERE f.sport_id = ds.sport_id
          AND f.match_datetime >= STR_TO_DATE(:dateFrom, '%Y-%m-%d %H:%i:%s')
          AND f.match_datetime <= STR_TO_DATE(:dateTo, '%Y-%m-%d %H:%i:%s'))
          ORDER BY ds.sport_id
        ) tbl
        WHERE season IS NOT NULL
        ORDER BY sport_id, domain_id
      ";

      $stmt = $em->getConnection()->prepare($sql);
      $stmt->bindValue(":dateFrom", $dateFrom);
      $stmt->bindValue(":dateTo", $dateTo);
      $stmt->execute();
      $todaysLeagues = $stmt->fetchAll();

      if ($todaysLeagues) {
        foreach ($todaysLeagues as $key => $value) {
          if ($value['sport_id'] && $value['league_id'] && $value['season']) {
            $result[$value['sport_id']]['league_id'] = $value['league_id'];
            $result[$value['sport_id']]['sport_id'] = $value['sport_id'];
            $result[$value['sport_id']]['season'] = $value['season'];
            $result[$value['sport_id']]['domain'][] = $value['domain_id'];
  
            // $result[$key]['sport_id']   = $value['sport_id'];
            // $result[$key]['league_id']  = $value['league_id'];
            // $result[$key]['season']     = $value['season'];
            // $result[$key]['domain'][]     = $value['domain_id'];
          }
        }
      }

      // echo '<pre>';
      // print_r($result);
      // echo '</pre>';

      $leaguesCache->set($result);
      $cache->save($leaguesCache);

      $em->clear();
    }

    return $leaguesCache->get();
  }
}
