<?php

namespace AppBundle\Controller\Crons;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use FOS\RestBundle\Controller\Annotations as Rest;

use Symfony\Component\Cache\Adapter\FilesystemAdapter;
/**
 * SetOddTypes controller.
 *
 * @Route("/cron")
 */
class SetOddTypesController extends Controller
{

  /**
   * Set Odd Types Cache via betarades express
   *
   * @Method("GET")
   * @Rest\Get("/odd-types-cache")
   */
  public function setOddTypesCacheAction($domainId = null) {
		// this should work for betarades domain only
    $em     = $this->getDoctrine()->getManager();
    // $logger = $this->get('logger');
    $helper = $this->container->get("wbs.globalhelper.service");
    $oddTypesMostUsedCacheDir = $this->get('kernel')->getProjectDir() . '/var/cache/' . $this->container->get('kernel')->getEnvironment() . '/odd_types_most_used';
    $cache      = new FilesystemAdapter('', 0, $oddTypesMostUsedCacheDir);
    $result = array();

		$query = $em->createQueryBuilder()
			->select('ot.id, ot.name')
			->from('AppBundle:OddType', 'ot')
			->orderBy('ot.name', 'ASC')
			->getQuery();

    $oddTypes = $query->getResult();
    
    $mostUsed = $cache->getItem('oddtypes.most_used');

    if ($mostUsed->isHit()) {
      $result['most_used'] = $mostUsed->get();
    }

    array_unshift($oddTypes, array('id' => 255, 'name' => 'NO BET'));
    $result['all'] = $oddTypes;

    $domain = $em->getRepository('AppBundle:Domain')->findOneById($domainId);
    
    if ($domain) {
      $protocol = (true === $domain->getProtocol() ? 'https' : 'http');
      $nodePort = $domain->getNodePortId();
      $hostName = $domain->getDbNodeHost();
    }

    $url = $protocol."://".$hostName.":".$nodePort."/set-odd-types/";
    $helper->curlRequest($url, true, json_encode($result));

    $em->clear();
    return new Response('Odd types cache set ' . $domain);
	}

  /**
   * Set Odd Types Most Used cache
   *
   * @Method("GET")
   * @Rest\Get("/odd-types-most-used")
   */
	public function setOddTypesMostUsedAction() {
    $em     = $this->getDoctrine()->getManager();
    // $logger = $this->get('logger');
    $helper = $this->container->get("wbs.globalhelper.service");
    $oddTypesMostUsedCacheDir = $this->get('kernel')->getProjectDir() . '/var/cache/' . $this->container->get('kernel')->getEnvironment() . '/odd_types_most_used';
    $cache      = new FilesystemAdapter('', 0, $oddTypesMostUsedCacheDir);
    
    $dateFrom = date('Y-m-d 03:00:00', strtotime('-15 days'));
    $dateTo = date('Y-m-d 02:59:59');

    $oddTypesMostUsedCache = $cache->getItem('oddtypes.most_used');
    $oddTypesMostUsed = $em->getRepository('AppBundle:OddType')->getOddTypesMostUsed($dateFrom, $dateTo);
    if ($oddTypesMostUsed) {
      $oddTypesMostUsedCache->set($oddTypesMostUsed);
      $cache->save($oddTypesMostUsedCache);
    }

		return new Response('Odd Types Most Used set');
	}
}