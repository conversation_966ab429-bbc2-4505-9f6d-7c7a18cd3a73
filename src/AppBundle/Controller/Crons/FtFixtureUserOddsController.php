<?php

namespace AppBundle\Controller\Crons;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use FOS\RestBundle\Controller\Annotations as Rest;

use AppBundle\Entity\FtFixtureUserOdd;
use AppBundle\Entity\ReporterBonus;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;

/**
 * SetOddTypes controller.
 *
 * @Route("/cron")
 */
class FtFixtureUserOddsController extends Controller
{

  /**
   * Get Daily User Odds from betarades express
   *
   * @Method("GET")
   * @Rest\Get("/get-user-odds")
   */
  public function getFtFixtureUserOddsAction($domainId = null) {
		// this should work for betarades domain only
    $em     	= $this->getDoctrine()->getManager();
    // $logger = $this->get('logger');
		$helper 	= $this->container->get("wbs.globalhelper.service");
		$language = new ExpressionLanguage();

		$domain = $em->getRepository('AppBundle:Domain')->findOneById($domainId);

		if ($domain) {
			$protocol = (true === $domain->getProtocol() ? 'https' : 'http');
			$nodePort = $domain->getNodePortId();
			$hostName = $domain->getDbNodeHost();
		}

		$url = $protocol."://".$hostName.":".$nodePort."/get-preview-daily-user-odds/";
		$dailyUserOdds = $helper->curlRequest($url, false, null);

		if ($dailyUserOdds) {
			$dailyUserOdds = json_decode($dailyUserOdds);

			foreach ($dailyUserOdds as $dailyUserOdd) {
				$ftFixtureUserOdd = new FtFixtureUserOdd();
				$ftFixtureUserOdd->setFixtureId($dailyUserOdd->fixture_id);
				$ftFixtureUserOdd->setMatchDateTime(\DateTime::createFromFormat('Y-m-d H:i:s', $dailyUserOdd->match_date_time));
				$ftFixtureUserOdd->setSportId($dailyUserOdd->sport_id);
				$ftFixtureUserOdd->setOddTypeId($dailyUserOdd->odd_type_id);
				$ftFixtureUserOdd->setOddValue($dailyUserOdd->odd_value);
				$ftFixtureUserOdd->setAuthorId($dailyUserOdd->author_id);
				$ftFixtureUserOdd->setAuthorName($dailyUserOdd->author_name);

				$voidResult = false;
				// find finished fixture ONLY, 'F', 'FE', 'FP')
				$ftFixture = $em->getRepository('AppBundle:FtFixture')->findOneBy(array('fId' => $dailyUserOdd->fixture_id));

				// if match abandoned or postponed then REMOVE User Odd
				if (in_array($ftFixture->getStatus(), ['PP', 'AB'])) {
					$em->remove($ftFixtureUserOdd);
					// $oddResult = null;
					// $ftFixtureUserOdd->setOddResult($oddResult);
				} // game postponed or abandoned
				else if (in_array($ftFixture->getStatus(), ['F', 'FE', 'FP'])) {
					$oddType = $em->getRepository('AppBundle:OddType')->findOneById($dailyUserOdd->odd_type_id);

					if ($oddType) {
						$ht1 = $ftFixture->getHt1();
						$ht2 = $ftFixture->getHt2();
						$ft1 = $ftFixture->getFt1();
						$ft2 = $ftFixture->getFt2();

						$ruleResult = $oddType->getRuleSuccess();

						$successResult = str_replace('ft1', $ft1, $ruleResult);
						$successResult = str_replace('ft2', $ft2, $successResult);
						$successResult = str_replace('ht1', $ht1, $successResult);
						$successResult = str_replace('ht2', $ht2, $successResult);
						$successResult = $language->evaluate($successResult);

						if ($oddType->getRuleVoid()) {
							$ruleVoidResult = $oddType->getRuleVoid();

							$voidResult = str_replace('ft1', $ft1, $ruleVoidResult);
							$voidResult = str_replace('ft2', $ft2, $voidResult);
							$voidResult = str_replace('ht1', $ht1, $voidResult);
							$voidResult = str_replace('ht2', $ht2, $voidResult);
							$voidResult = $language->evaluate($voidResult);
						}

						$oddResult = (true == $successResult) 
							? 0 
							: (($oddType->getRuleVoid() && true === $voidResult) ? 1 : 2);

						$ftFixtureUserOdd->setOddResult($oddResult);
					}
					$em->persist($ftFixtureUserOdd);
				} // game finished
				else {
					// if game has not finished but not PP/AB save it
					// game not finished yet by Dedoulis, keep it intact
					$em->persist($ftFixtureUserOdd);
				}
			} // foreach ($dailyUserOdds as $dailyUserOdd)

			$em->flush();
		}

    $em->clear();
    return new Response('User daily odds saved for ' . $domain);
	}

  /**
   * Set Odd Types Most Used cache
   *
   * @Method("GET")
   * @Rest\Get("/odd-types-most-used")
   */
	public function setOddTypesMostUsedAction() {
		// This is not used
    $em     = $this->getDoctrine()->getManager();
    // $logger = $this->get('logger');
		$helper = $this->container->get("wbs.globalhelper.service");

		$oddTypesMostUsedCacheDir = $this->get('kernel')->getProjectDir() . '/var/cache/' . $this->container->get('kernel')->getEnvironment() . '/odd_types_most_used';
    $cache = new FilesystemAdapter('', 0, $oddTypesMostUsedCacheDir);

		// calculate most used in the last 15 days
		$result = '';
		// save to cache
		$cache->set('oddtypes.most_used', $result);

		return new Response('Odd Types Most Used set');
	}

	/**
   * Set Reporter Monthly Bonuses
   *
   * @Method("GET")
   * @Rest\Get("/reporter-bonus/{month}/{year}")
	 */
	public function setReporterBonusAction($month, $year) {
		$em     = $this->getDoctrine()->getManager();

		if (!$month) {
			$month = date('m');
		}

		if (!$year) {
			$year = date('Y');
		}
		// $monthStart = date('Y-m-01 03:00:00', strtotime(date("Y-m-d H:i:s")));
		// $monthEnd 	= date('Y-m-t 02:59:59', strtotime(date("Y-m-d H:i:s")));

		$monthStart = date("$year-$month-01 00:00:00");
		$monthEnd 	= date('Y-m-t 23:59:59', mktime(0, 0, 0, $month + 1, 0, $year));

		$reporterBonus = $em->getRepository('AppBundle:FtFixtureUserOdd')->getMonthlyUserOdds($monthStart, $monthEnd);

		if ($reporterBonus) {
			foreach ($reporterBonus as $bonus) {
				$authorBonus = $em->getRepository('AppBundle:ReporterBonus')->findOneBy(array('monthNo' => $bonus['month_no'], 'yearNo' => $bonus['year_no'], 'authorId' => $bonus['author_id']));

				if (!$authorBonus) $authorBonus = new ReporterBonus();

				$authorBonus->setMonthNo($bonus['month_no']);
				$authorBonus->setYearNo($bonus['year_no']);
				$authorBonus->setPoints($bonus['points']);
				$authorBonus->setAuthorId($bonus['author_id']);
				$authorBonus->setAuthorName($bonus['author_name']);
				$authorBonus->setBetCount($bonus['bet_count']);
				$authorBonus->setBetProfit($bonus['bet_profit']);

				$em->persist($authorBonus);
				$em->flush();
			}
		}

		$em->clear();

		return new Response('setting up reporter monthly bonuses');
	}

	/**
   * Fix User Odds
   *
   * @Method("GET")
   * @Rest\Get("/fix-user-odds")
   */
	public function fixFtFixtureUserOddsAction() {
    $em     	= $this->getDoctrine()->getManager();
    // $logger = $this->get('logger');
		$helper 	= $this->container->get("wbs.globalhelper.service");
		$language = new ExpressionLanguage();

		// $start 	= date( 'Y-m-d 00:00:00', strtotime( '-1 day' ) );
		// $end 		= date( 'Y-m-d 23:59:59', strtotime( '0 day' ) );

		$start	= \DateTime::createFromFormat('Y-m-d H:i:s', date('Y-m-d', strtotime('-4 day')) . ' 03:00:00');
		$end		= \DateTime::createFromFormat('Y-m-d H:i:s', date('Y-m-d', strtotime('0 day')) . ' 02:59:59');

		$criteria = new \Doctrine\Common\Collections\Criteria();
		$criteria->where($criteria->expr()->eq('oddResult', null));
		$criteria->andWhere($criteria->expr()->neq('oddTypeId', 255));
		$criteria->andWhere(
			$criteria->expr()->andX(
				$criteria->expr()->gte('matchDatetime', $start),
				$criteria->expr()->lte('matchDatetime', $end)
			)
		);
		
		$ftFixtureUserOdds = $em->getRepository('AppBundle:FtFixtureUserOdd')->matching($criteria);

		if ($ftFixtureUserOdds) {

			foreach ($ftFixtureUserOdds as $ftFixtureUserOdd) {
				$voidResult = false;
				// find finished fixture ONLY, 'F', 'FE', 'FP')
				$ftFixture = $em->getRepository('AppBundle:FtFixture')->findOneBy(array('fId' => $ftFixtureUserOdd->getFixtureId()));

				// if match abandoned or postponed then REMOVE User Odd
				if (in_array($ftFixture->getStatus(), ['PP', 'AB'])) {
					$em->remove($ftFixtureUserOdd);
					// $oddResult = null;
					// $ftFixtureUserOdd->setOddResult($oddResult);
				}
				else if (in_array($ftFixture->getStatus(), ['F', 'FE', 'FP'])) {
					$oddType = $em->getRepository('AppBundle:OddType')->findOneById($ftFixtureUserOdd->getOddTypeId());
					
					if ($oddType) {
						$ht1 = $ftFixture->getHt1();
						$ht2 = $ftFixture->getHt2();
						$ft1 = $ftFixture->getFt1();
						$ft2 = $ftFixture->getFt2();
	
						$ruleResult = $oddType->getRuleSuccess();
	
						$successResult = str_replace('ft1', $ft1, $ruleResult);
						$successResult = str_replace('ft2', $ft2, $successResult);
						$successResult = str_replace('ht1', $ht1, $successResult);
						$successResult = str_replace('ht2', $ht2, $successResult);
						$successResult = $language->evaluate($successResult);
	
						if ($oddType->getRuleVoid()) {
							$ruleVoidResult = $oddType->getRuleVoid();
	
							$voidResult = str_replace('ft1', $ft1, $ruleVoidResult);
							$voidResult = str_replace('ft2', $ft2, $voidResult);
							$voidResult = str_replace('ht1', $ht1, $voidResult);
							$voidResult = str_replace('ht2', $ht2, $voidResult);
							$voidResult = $language->evaluate($voidResult);
						}

						$oddResult = (true == $successResult) 
							? 0 
							: (($oddType->getRuleVoid() && true === $voidResult) ? 1 : 2);

						$ftFixtureUserOdd->setOddResult($oddResult);
					}
					$em->persist($ftFixtureUserOdd);
				} // game finished
				else {
					// game not finished yet by Dedoulis, keep it intact
					$em->persist($ftFixtureUserOdd);
				}
			}

			$em->flush();
		}

		$em->clear();
    	return new Response('User daily odds fixed');
	}

	/**
   	 * Fix User Odds
   	 *
   	 * @Method("GET")
   	 * @Rest\Get("/fix-user-odds-doubles")
   	 */
        public function fixFtFixtureUserOddsDoublesAction()
        {
			$em             = $this->getDoctrine()->getManager();
			$helper         = $this->container->get("wbs.globalhelper.service");
			$language       = new ExpressionLanguage();

			$start  = \DateTime::createFromFormat('Y-m-d H:i:s', date('Y-m-d', strtotime('-30 day')) . ' 03:00:00');
			$end    = \DateTime::createFromFormat('Y-m-d H:i:s', date('Y-m-d', strtotime('0 day')) . ' 02:59:59');

			$criteria = new \Doctrine\Common\Collections\Criteria();
			$criteria->where($criteria->expr()->eq('oddResult', null));
			$criteria->andWhere($criteria->expr()->neq('oddTypeId', 255));
			$criteria->andWhere(
				$criteria->expr()->andX(
					$criteria->expr()->gte('matchDatetime', $start),
					$criteria->expr()->lte('matchDatetime', $end)
				)
			);

			$ftFixtureUserOdds = $em->getRepository('AppBundle:FtFixtureUserOdd')->matching($criteria);

			if ($ftFixtureUserOdds) {

				$domain = $em->getRepository('AppBundle:Domain')->findOneById(2);
				$connection = $helper->getDomainConnection($domain);
				$dbPrefix = $domain->getDbPrefix();

				foreach ($ftFixtureUserOdds as $ftFixtureUserOdd) {
					$voidResult = false;
					// find finished fixture ONLY, 'F', 'FE', 'FP')
					$ftFixture = $em->getRepository('AppBundle:FtFixture')->findOneBy(array('fId' => $ftFixtureUserOdd->getFixtureId()));

					// if match abandoned or postponed then REMOVE User Odd
					if (in_array($ftFixture->getStatus(), ['PP', 'AB'])) {
						$em->remove($ftFixtureUserOdd);
						// $oddResult = null;
						// $ftFixtureUserOdd->setOddResult($oddResult);
					}
					else if (in_array($ftFixture->getStatus(), ['F', 'FE', 'FP'])) {

						$oddType = $em->getRepository('AppBundle:OddType')->findOneById($ftFixtureUserOdd->getOddTypeId());

						if ($oddType) {
							$ht1 = $ftFixture->getHt1();
							$ht2 = $ftFixture->getHt2();
							$ft1 = $ftFixture->getFt1();
							$ft2 = $ftFixture->getFt2();

							$ruleResult = $oddType->getRuleSuccess();

							$successResult = str_replace('ft1', $ft1, $ruleResult);
							$successResult = str_replace('ft2', $ft2, $successResult);
							$successResult = str_replace('ht1', $ht1, $successResult);
							$successResult = str_replace('ht2', $ht2, $successResult);
							$successResult = $language->evaluate($successResult);

							if ($oddType->getRuleVoid()) {
								$ruleVoidResult = $oddType->getRuleVoid();

								$voidResult = str_replace('ft1', $ft1, $ruleVoidResult);
								$voidResult = str_replace('ft2', $ft2, $voidResult);
								$voidResult = str_replace('ht1', $ht1, $voidResult);
								$voidResult = str_replace('ht2', $ht2, $voidResult);
								$voidResult = $language->evaluate($voidResult);
							}

						$oddResult = (true == $successResult)
							? 0
							: (($oddType->getRuleVoid() && true === $voidResult) ? 1 : 2);

				$ftFixtureUserOdd->setOddResult($oddResult);
			}
			$em->persist($ftFixtureUserOdd);
		} // game finished
		else {
			// check if this is a double problem cause by Dedoulis duplicate matches
			$ftFixture = $em->getRepository('AppBundle:FtFixture')->findOneBy(array('fId' => $ftFixtureUserOdd->getFixtureId()));
			$hTeamId = $ftFixture->getHTeam();
			$aTeamId = $ftFixture->getATeam();
			$matchDatetime = $ftFixture->getMatchDateTime();

			$criteria = new \Doctrine\Common\Collections\Criteria();
			$criteria->where($criteria->expr()->eq('hTeam', $hTeamId));
			$criteria->andWhere($criteria->expr()->eq('aTeam', $aTeamId));
			$criteria->andWhere($criteria->expr()->eq('matchDatetime', $matchDatetime));
			$criteria->andWhere($criteria->expr()->neq('fId', $ftFixture->getFId()));

			$correctFixture = $em->getRepository('AppBundle:FtFixture')->matching($criteria)->first();

			if (!$correctFixture) continue;

			// if match abandoned or postponed then REMOVE User Odd
			if (in_array($correctFixture->getStatus(), ['PP', 'AB'])) {
				$em->remove($ftFixtureUserOdd);
			}
			else if (in_array($correctFixture->getStatus(), ['F', 'FE', 'FP'])) {
				$oddType = $em->getRepository('AppBundle:OddType')->findOneById($ftFixtureUserOdd->getOddTypeId());

					if ($oddType) {
						$ht1 = $correctFixture->getHt1();
						$ht2 = $correctFixture->getHt2();
						$ft1 = $correctFixture->getFt1();
						$ft2 = $correctFixture->getFt2();

						$ruleResult = $oddType->getRuleSuccess();

						$successResult = str_replace('ft1', $ft1, $ruleResult);
						$successResult = str_replace('ft2', $ft2, $successResult);
						$successResult = str_replace('ht1', $ht1, $successResult);
						$successResult = str_replace('ht2', $ht2, $successResult);
						$successResult = $language->evaluate($successResult);

						if ($oddType->getRuleVoid()) {
							$ruleVoidResult = $oddType->getRuleVoid();

							$voidResult = str_replace('ft1', $ft1, $ruleVoidResult);
							$voidResult = str_replace('ft2', $ft2, $voidResult);
							$voidResult = str_replace('ht1', $ht1, $voidResult);
							$voidResult = str_replace('ht2', $ht2, $voidResult);
							$voidResult = $language->evaluate($voidResult);
						}
								
						$oddResult = (true == $successResult)
							? 0
							: (($oddType->getRuleVoid() && true === $voidResult) ? 1 : 2);

						$ftFixtureUserOdd->setFixtureId($correctFixture->getFId());
						$ftFixtureUserOdd->setOddResult($oddResult);

						// connect to betarades and update bet_wbs_previews
						$sqlUpdate = "
							UPDATE ".$dbPrefix."wbs_previews SET
							fixture_id = ".$correctFixture->getFId().",
							status = '".$correctFixture->getStatus()."',
							ht_score = '".$correctFixture->getHt1().'-'.$correctFixture->getHt2()."',
							ft_score = '".$correctFixture->getFt1().'-'.$correctFixture->getFt2()."',
							et_score = '".$correctFixture->getEt1().'-'.$correctFixture->getEt2()."',
							pt_score = '".$correctFixture->getPt1().'-'.$correctFixture->getPt2()."',
							events = '".$correctFixture->getEvents()."',
							updated_at = '".date('Y-m-d H:i:s', time())."'
							WHERE fixture_id = ".$ftFixture->getFId()."
						";

						$stmt = $connection->prepare($sqlUpdate);
						$stmt->execute();
					}

						$em->persist($ftFixtureUserOdd);
					} // game finished
					else {
						// game not finished yet by Dedoulis, keep it intact
						$em->persist($ftFixtureUserOdd);
					}
				}
			}

			$em->flush();
		}

		$em->clear();
		return new Response('User daily odds fixed');
	}

}