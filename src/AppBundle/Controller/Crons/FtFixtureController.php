<?php

namespace AppBundle\Controller\Crons;

use AppBundle\Entity\DomainWeatherPreview;
use Symfony\Component\HttpFoundation\Response;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;

/**
 * SetOddTypes controller.
 *
 * @Route("/cron")
 */
class FtFixtureController extends Controller
{
	/**
	 * Set fixture info for betfair exchange in betfair exchange server
	 *
	 * @Method("GET")
	 * @Rest\Get("/set-fixture-info-cache")
	 */
	public function setFixtureInfoCacheAction($domainId = null) {
		// this should work for betarades domain only
		$em     = $this->getDoctrine()->getManager();
		// $logger = $this->get('logger');
		$helper = $this->container->get("wbs.globalhelper.service");
		$result = array();
		$clients = null;
		
		$url = $this->container->hasParameter('betfair.url') ? $this->container->getParameter('betfair.url') : null;
		if ($url) {
			$clients = $helper->curlRequest($url, false, null);
		}

		// $logger->info('[CVYZ] updating cache');
		$dateFrom = Date('Y-m-d 07:00:00');
		$dateTo 	= Date('Y-m-d 06:59:59', strtotime("+2 days"));

		$result = $em->getRepository('AppBundle:FtFixture')->getInfoForDates($dateFrom, $dateTo, $clients);
		if (isset($domainId)) {
			$domain = $em->getRepository('AppBundle:Domain')->findOneById($domainId);
		}
		
		if (!empty($domain)) {
			// to make a curl request via https you need to set up a cacert.pem file location
			// due to complexity, simply call node http for now and use the http node port
			// $protocol = (true === $domain->getProtocol() ? 'https' : 'http');

			// $nodePort = $domain->getNodePortId();
			$protocol = 'https';
			$nodePort = 2083;
			//$hostName = $domain->getDbHost();
			// cors not working for IP, must declare domain name
			$hostName = 'nodejs.betwidgets.com';

			$url = $protocol . "://" . $hostName . ":" . $nodePort . "/set-fixture-info";
			$helper->curlRequest($url, true, json_encode($result['fixtures']));
			$em->clear();
			return new Response('Fixture info is set in cache for ' . $domain->getName());
		}

		else {
			$em->clear();
			return new Response('No Domain specified');
		}

		// OBSOLETE
		// clients are now being set up in node server asking for data from rest server
		// $url = $protocol."://".$hostName.":".$nodePort."/set-clients";
		// $helper->curlRequest($url, false, json_encode($result['clients']));
	}

    /**
     * Get all today's fixtures for all domains for updating weather conditions
     *
     * @Method("GET")
     * @Rest\Get("/all-matches")
     */
    public function getAllMatchesAction() {
		$em                 = $this->getDoctrine()->getManager();
		$helper             = $this->container->get("wbs.globalhelper.service");
		$domains            = array();
		$domainConnections  = array();
		
		$language = $em->getRepository('AppBundle:Language')->findOneById(1);

		$activeDomains = $em->getRepository('AppBundle:Domain')->findBy(array('isActive' => true, 'language' => $language));

		if ($activeDomains) {
			foreach ($activeDomains as $activeDomain) {
				$domains[$activeDomain->getId()] = $activeDomain;
				$domainConnections[$activeDomain->getId()] = $helper->getDomainConnection($activeDomain);
			}
		}

		$leagues = $em->getRepository('AppBundle:Sport')->getActiveDomainCompetitions();
		$leaguesInClause = $helper->getSportsInClause($leagues);

		if (!$leaguesInClause) {
			echo '<pre>';
			var_dump('no leagues for active domains found');
			echo '</pre>';
			die();
		}

		$date = strtotime("+1 day");
		$dateFrom = date('Y-m-d 06:00:00', time());
		$dateTo = date('Y-m-d 05:59:59', $date);
  
		$leagueMatches = $em->getRepository('AppBundle:FtFixture')->getLeagueMatches($leaguesInClause, $dateFrom, $dateTo);
		
		$leagueNames  = array();
		$teamNames    = array();
		if ($leagueMatches) {
			foreach ($leagueMatches as $leagueMatch) {
				if ($curDomains = (isset($leagues[$leagueMatch['sport_id']]['domains'])) ? $leagues[$leagueMatch['sport_id']]['domains'] : null) {
					if ($curDomains) {
						foreach ($curDomains as $curDomain) {
							if (isset($domains[$curDomain])) {
								$wpPreview = $em->getRepository('AppBundle:DomainFtFixture')->findOneBy(array('domainId' => $domains[$curDomain], 'fixtureId' => $leagueMatch['f_id']));
								if ($wpPreview) {
									$domainWeatherPreview = $em->getRepository('AppBundle:DomainWeatherPreview')->findOneBy(array('domainId' => $domains[$curDomain], 'fixtureId' => $leagueMatch['f_id']));
									if ($domainWeatherPreview) {
										continue;
									}
									$domainWeatherPreview = new DomainWeatherPreview($leagueMatch['f_id'], $curDomain, $wpPreview->getPreviewId());
									$domainWeatherPreview->setHomeTeamId($leagueMatch['h_team']);
									$em->persist($domainWeatherPreview);
									$em->flush();
								}
							}
						}
					}
				}
			}
		}

		// close all open mysql connections
		foreach ($domainConnections as $domainConnection) {
			$domainConnection->close();
		}

		return new Response('Got all matches');
	}
	
	/**
     * Set weather conditions per ft fixture and domain
     *
     * @Method("GET")
     * @Rest\Get("/weather-conditions")
     */
	public function getSetFtFixtureWeatherConditionsAction()
	{
		$em = $this->getDoctrine()->getManager();
		$helper = $this->container->get("wbs.globalhelper.service");

		$domainWeatherPreviews = $em->getRepository('AppBundle:DomainWeatherPreview')->findBy([], ['createdAt' => 'DESC'], 25);

		if (!$domainWeatherPreviews) {
			return new Response('No matches found to set the weather for');
		}

		$updatesCounter = 0;

		foreach ($domainWeatherPreviews as $domainWeatherPreview) {
			$qb = $em->createQueryBuilder();
			$qb->select('ff.weatherApiId, f.matchDatetime')
				->from('AppBundle:TeamFootballField', 'tff')
				->innerJoin('AppBundle:FtTeam', 't', 'WITH', 't = tff.team')
				->innerJoin('AppBundle:FootballField', 'ff', 'WITH', 'ff = tff.footballField')
				->innerJoin('AppBundle:FtFixture', 'f', 'WITH', 'f = ?1')
				->where('tff.isDefault = 1')
				->andWhere('t.teamId = ?2')
				->setParameter(1, $domainWeatherPreview->getFixtureId())
				->setParameter(2, $domainWeatherPreview->getHomeTeamId());
			
			$query = $qb->getQuery();
			$footballField = $query->setMaxResults(1)->getOneOrNullResult();
			if (!$footballField || !isset($footballField['weatherApiId'])) {
				$em->remove($domainWeatherPreview);
				$em->flush();

				continue;
			}

			// we have weatherApiId and matchDatetime... lets call api weather and parse the response

			// get weather and parse it for requested weather api id
			$apiWeatherUrl = "https://api.openweathermap.org/data/2.5/forecast?id=" . $footballField['weatherApiId'] . "&APPID=fdef0b3ae93f90205fc3a4e19eea6dfb&units=metric&lang=el";
			
			// NOTICE
			// $footballField['matchDatetime'] uses UTC timezone but the datetime is GMT+2
			$matchDateTimeUTC = $footballField['matchDatetime']->sub(new \DateInterval ("PT2H"));
			$matchTimeStart = $matchDateTimeUTC->format('Hi');
			$matchDay = $matchDateTimeUTC->format('d');

			$weatherApiResponse = $helper->curlRequest($apiWeatherUrl);

			$weatherConditions = [];
			if ($weatherApiResponse) {
				$weatherList = json_decode($weatherApiResponse, true)['list'];

				foreach ($weatherList as $weatherPerInterval) {
					if ((int) $matchDay != (int) date('d', $weatherPerInterval['dt'])) {
						continue;
					}

					$weatherConditions[date('Hi', $weatherPerInterval['dt'])] = $weatherPerInterval;
				}

				$closestStartingTimeKey = $this->getClosest($matchTimeStart, array_keys($weatherConditions));

				if (!$closestStartingTimeKey) {
					continue;
				}

				$eventWeatherForecast = $this->parseEventWeatherConditions($weatherConditions[$closestStartingTimeKey]);

				if (!$this->validatedEventWeatherForecast($eventWeatherForecast)) {
					continue;
				}

				$domain = $em->getRepository('AppBundle:Domain')->findOneById($domainWeatherPreview->getDomainId());
				$domainConnection = $helper->getDomainConnection($domain);

				// update preview for each domain
				$updated = $this->updatePost($domainConnection, $domain->getDbPrefix(), $eventWeatherForecast, $domainWeatherPreview->getPreviewId());
				// remove from domain_weather_preview
				if ($updated) {
					$em->remove($domainWeatherPreview);
					$em->flush();
				}
			}
			$updatesCounter++;
		}

		$em->clear();
		return new Response('Weather is set for ' . $updatesCounter . ' previews.');
	}

	private function updatePost($conn, $dbPrefix, $data, $previewId)
	{
		$sqlUpdate = "
		UPDATE ".$dbPrefix."wbs_previews SET
		weather = '".$data['weather']."',
		weather_temperature = ".$data['weather_temperature'].",
		weather_humidity = ".$data['weather_humidity'].",
		weather_wind = ".$data['weather_wind'].",
		weather_icon = '".$data['weather_icon']."',
		updated_at = '".date('Y-m-d H:i:s', time())."'
		WHERE id = ".$previewId."
	  ";
  
	  $stmt = $conn->prepare($sqlUpdate);
	  $stmt->execute();
  
	  return true;
	}

	private function parseEventWeatherConditions($eventWeatherConditions)
	{
		$data = [];

		$data['weather_temperature'] = round($eventWeatherConditions['main']['temp']);
		$data['weather_humidity'] = $eventWeatherConditions['main']['humidity'];
		$data['weather_wind'] = $this->convertWindToBeaufort((float) $eventWeatherConditions['wind']['speed']);
		$data['weather'] = mb_convert_case($eventWeatherConditions['weather'][0]['description'], MB_CASE_TITLE, 'UTF-8');
		$data['weather_icon'] = $eventWeatherConditions['weather'][0]['icon'];
		
		return $data;
	}

	private function convertWindToBeaufort($windSpeed)
	{
		switch (true) {
			case ($windSpeed < 0.3):
				return 0;
				break;
			case ($windSpeed >= 0.3 && $windSpeed < 1.5):
				return 1;
				break;
			case ($windSpeed >= 1.5 && $windSpeed < 3.3):
				return 2;
				break;
			case ($windSpeed >= 3.3 && $windSpeed < 5.5):
				return 3;
				break;
			case ($windSpeed >= 5.5 && $windSpeed < 8.0):
				return 4;
				break;
			case ($windSpeed >= 8.0 && $windSpeed < 10.8):
				return 5;
				break;
			case ($windSpeed >= 10.8 && $windSpeed < 13.9):
				return 6;
				break;
			case ($windSpeed >= 13.9 && $windSpeed < 17.2):
				return 7;
				break;
			case ($windSpeed >= 17.2 && $windSpeed < 20.7):
				return 8;
				break;
			case ($windSpeed >= 20.7 && $windSpeed < 24.5):
				return 9;
				break;
			case ($windSpeed >= 24.5 && $windSpeed < 28.4):
				return 10;
				break;
			case ($windSpeed >= 28.4 && $windSpeed < 32.6):
				return 11;
				break;
			case ($windSpeed >= 32.6):
				return 12;
				break;
			default:
				return 0;
		}
	}

	private function validatedEventWeatherForecast($eventWeatherForecast)
	{
		return
			isset($eventWeatherForecast['weather_temperature']) &&
			isset($eventWeatherForecast['weather_humidity']) &&
			isset($eventWeatherForecast['weather_wind']) &&
			isset($eventWeatherForecast['weather']) &&
			isset($eventWeatherForecast['weather_icon']);
	}

	private function getClosest($search, $arr) {
		$closest = null;
		foreach ($arr as $item) {
			if ($closest === null || abs($search - $closest) > abs($item - $search)) {
				$closest = $item;
			}
		}
		return $closest;
	}
}