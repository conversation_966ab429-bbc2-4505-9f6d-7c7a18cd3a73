<?php

namespace AppBundle\Controller\Crons;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use FOS\RestBundle\Controller\Annotations as Rest;

// use Doctrine\Common\Util\Debug;

// use AppBundle\Entity\CronTask;
// use AppBundle\Form\CronTaskType;
//
// use AppBundle\Entity\DomainFixtures;
// use AppBundle\Entity\DomainFixturesRelationship;
// use AppBundle\Entity\Domain;
// use AppBundle\Entity\DomainCompetitions;
// use AppBundle\Entity\TeamStats;
// use AppBundle\Entity\TeamStatsInit;

/**
 * GeneratePost controller.
 *
 * @Route("/cron")
 */
class TeamStatsController extends Controller
{

  /**
   * Initialize team stats data once (not a cronjob)
   *
   * @Method("GET")
   * @Rest\Get("/team-stats/init")
   */
  public function initTeamStatsAction($sportOptions = null) {
    // similar to ini_set('max_execution_time', 600);
    // this action is run once, but takes far too long for php to handle, need to extend time limits
    set_time_limit(600);


    $em     = $this->getDoctrine()->getManager();
    $helper = $this->container->get("wbs.globalhelper.service");
    $calc   = $this->container->get("wbs.teamstats.service");

    try {
    if ($sportOptions) {
      $sportIds = $sportOptions;
    }
    else {
      $sportIds = $em->getRepository('AppBundle:Sport')->getSportsForPreviews();
    }

    if ($sportIds) {
      foreach ($sportIds as $sportId) {
        // if (304 != $sportId) continue;
        $temp = $em->getRepository('AppBundle:FtLeagueSeasonSport')->getLatestSportSeason($sportId);
        // if ($temp) {
        //   $currentSeason[$league] = $temp;
        //   $currentSeason[$league]['sportId'] = $league;
        // }
        $temp ? $currentSeason[$sportId] = $temp : $currentSeason[$sportId] = null;

        if ($currentSeason[$sportId]) {
          $sportSeasonTeams = $em->getRepository('AppBundle:FtFixture')->getTeamsInSportSeason($sportId, $currentSeason[$sportId]['season']);

          if ($sportSeasonTeams) {
            foreach ($sportSeasonTeams as $team) {

              $teamStats = $em->getRepository('AppBundle:TeamStats')->findOneBy(array('leagueSeasonId' => $currentSeason[$sportId]['id'], 'teamId' => $team));
              try {
                if (!$teamStats) {
                  $teamStats = new \AppBundle\Entity\TeamStats();
                  $teamStats->setLeagueSeasonId($currentSeason[$sportId]['id']);
                  $teamStats->setTeamId($team);

                  $em->persist($teamStats);
                  $em->flush();
                }

                $calc->calculateTeamStats($team, $currentSeason[$sportId]['id'], $teamStats);

              }
              catch (\Exception $e) {
                echo '<pre>';
                var_dump($e->getMessage());
                echo '</pre>';
              }
            } // foreach $sportSeasonTeams
          } // if $sportSeasonTeams
        } // if $currentSeason[$sportId]
      } // foreach $sportIds
    }

    // CAUTION
    // get sport Ids to use inside query IN CLAUSE is much faster than looping through leagues
    // and getting matches query for each league
    // if ($leagues) {
    //   $leaguesInClause = '('.implode (", ", $leagues).')';
    // }

    } catch (\Exception $e) {
      echo '<pre>';
      var_dump($e->getMessage());
      echo '</pre>';
    }

    $em->clear();

    return new Response('job done');
  }

 /**
   * Update team stats data for finished games
   *
   * @Method("GET")
   * @Rest\Get("/team-stats/update")
   */
  public function fixTeamStatsAction($sportId = null, $season = null, $domainId = null) {

    // TODO fix stats in all domain
    try {
      if (!empty($sportId) && !empty($domainId)) {
        $em               = $this->getDoctrine()->getManager();
        $helper           = $this->container->get('wbs.globalhelper.service');
        $translator       = $this->get('translator');
        $sportsClearCache = array();
        $projectDir = $this->get('kernel')->getProjectDir();
        $environment = $this->container->get('kernel')->getEnvironment();

        if (empty($season)) {
          $getLastSeasonQuery = "SELECT ftlss.id, ftlss.season FROM ft_league_season_sport ftlss WHERE ftlss.sport_id = $sportId ORDER BY ftlss.season DESC LIMIT 1";

          $stmt = $em->getConnection()->prepare($getLastSeasonQuery);
          $stmt->execute();
          $sportSeason = $stmt->fetch();

          if (!empty($sportSeason)) {
            $leagueSeasonId = $sportSeason['id'];
            $sportSeason = $sportSeason['season'];
          }
        }
        else {
          $sportSeason = $season;
          $getLastSeasonQuery = "SELECT ftlss.id FROM ft_league_season_sport ftlss WHERE ftlss.sport_id = $sportId AND ftlss.season = '$season'";

          $stmt = $em->getConnection()->prepare($getLastSeasonQuery);
          $stmt->execute();
          $leagueSeasonSportId = $stmt->fetch();
          if (!empty($leagueSeasonSportId)) {
            $leagueSeasonId = $leagueSeasonSportId['id'];
          }
        }

        if (!empty($sportSeason) && !empty($leagueSeasonId)) {

          $getWinsLossesDrawsQuery = "
          SELECT
          fls.team_id teamId,
          t.name_gr as teamName,
          COUNT(1) totalPlayed,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 > ft2, 1, 0) ELSE 0 END) h_wins,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 > ht2, 1, 0) ELSE 0 END) h_wins_ht,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 < ft2, 1, 0) ELSE 0 END) a_wins,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 < ht2, 1, 0) ELSE 0 END) a_wins_ht,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 < ft2, 1, 0) ELSE 0 END) h_loses,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 < ht2, 1, 0) ELSE 0 END) h_loses_ht,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 > ft2, 1, 0) ELSE 0 END) a_loses,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 > ht2, 1, 0) ELSE 0 END) a_loses_ht,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 = ft2, 1, 0) ELSE 0 END) h_draws,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 = ht2, 1, 0) ELSE 0 END) h_draws_ht,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 = ft2, 1, 0) ELSE 0 END) a_draws,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 = ht2, 1, 0) ELSE 0 END) a_draws_ht,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 > ht2 AND ft1 > ft2, 1, 0) ELSE 0 END) h_win_win,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 > ht2 AND ft1 = ft2, 1, 0) ELSE 0 END) h_win_draw,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 > ht2 AND ft2 > ft1, 1, 0) ELSE 0 END) h_win_lose,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht2 > ht1 AND ft2 > ft1, 1, 0) ELSE 0 END) h_lose_lose,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht2 > ht1 AND ft1 = ft2, 1, 0) ELSE 0 END) h_lose_draw,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht2 > ht1 AND ft1 > ft2, 1, 0) ELSE 0 END) h_lose_win,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 = ht2 AND ft1 = ft2, 1, 0) ELSE 0 END) h_draw_draw,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 = ht2 AND ft1 > ft2, 1, 0) ELSE 0 END) h_draw_win,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 = ht2 AND ft2 > ft1, 1, 0) ELSE 0 END) h_draw_lose,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht2 > ht1 AND ft2 > ft1, 1, 0) ELSE 0 END) a_win_win,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht2 > ht1 AND ft1 = ft2, 1, 0) ELSE 0 END) a_win_draw,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht2 > ht1 AND ft1 > ft2, 1, 0) ELSE 0 END) a_win_lose,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 > ht2 AND ft1 > ft2, 1, 0) ELSE 0 END) a_lose_lose,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 > ht2 AND ft1 = ft2, 1, 0) ELSE 0 END) a_lose_draw,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 > ht2 AND ft2 > ft1, 1, 0) ELSE 0 END) a_lose_win,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 = ht2 AND ft1 = ft2, 1, 0) ELSE 0 END) a_draw_draw,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 = ht2 AND ft2 > ft1, 1, 0) ELSE 0 END) a_draw_win,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 = ht2 AND ft1 > ft2, 1, 0) ELSE 0 END) a_draw_lose,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 > 0 AND ft2 > 0, 1, 0) ELSE 0 END) h_goal_goal,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 > 0 AND ft2 > 0, 1, 0) ELSE 0 END) a_goal_goal,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 = 0 OR ft2 = 0, 1, 0) ELSE 0 END) h_no_goal,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 = 0 OR ft2 = 0, 1, 0) ELSE 0 END) a_no_goal,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 != 0, 1, 0) ELSE 0 END) h_goals_for,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft2 != 0, 1, 0) ELSE 0 END) a_goals_for,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft2 != 0, 1, 0) ELSE 0 END) h_goals_against,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 != 0, 1, 0) ELSE 0 END) a_goals_against,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 + ft2 > 2, 1, 0) ELSE 0 END) h_over_25,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 + ht2 > 2, 1, 0) ELSE 0 END) h_over_25_1,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF((ft1 - ht1 + ft2 - ht2) > 2, 1, 0) ELSE 0 END) h_over_25_2,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 + ft2 > 2, 1, 0) ELSE 0 END) a_over_25,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 + ht2 > 2, 1, 0) ELSE 0 END) a_over_25_1,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF((ft1 - ht1 + ft2 - ht2) > 2, 1, 0) ELSE 0 END) a_over_25_2,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 + ft2 < 3, 1, 0) ELSE 0 END) h_under_25,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 + ht2 < 3, 1, 0) ELSE 0 END) h_under_25_1,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF((ft1 - ht1 + ft2 - ht2) < 3, 1, 0) ELSE 0 END) h_under_25_2,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 + ft2 < 3, 1, 0) ELSE 0 END) a_under_25,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 + ht2 < 3, 1, 0) ELSE 0 END) a_under_25_1,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF((ft1 - ht1 + ft2 - ht2) < 3, 1, 0) ELSE 0 END) a_under_25_2,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 + ft2 > 1, 1, 0) ELSE 0 END) h_over_15,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 + ht2 > 1, 1, 0) ELSE 0 END) h_over_15_1,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF((ft1 - ht1 + ft2 - ht2) > 1, 1, 0) ELSE 0 END) h_over_15_2,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 + ft2 > 1, 1, 0) ELSE 0 END) a_over_15,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 + ht2 > 1, 1, 0) ELSE 0 END) a_over_15_1,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF((ft1 - ht1 + ft2 - ht2) > 1, 1, 0) ELSE 0 END) a_over_15_2,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 + ft2 < 2, 1, 0) ELSE 0 END) h_under_15,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 + ht2 < 2, 1, 0) ELSE 0 END) h_under_15_1,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF((ft1 - ht1 + ft2 - ht2) < 2, 1, 0) ELSE 0 END) h_under_15_2,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 + ft2 < 2, 1, 0) ELSE 0 END) a_under_15,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 + ht2 < 2, 1, 0) ELSE 0 END) a_under_15_1,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF((ft1 - ht1 + ft2 - ht2) < 2, 1, 0) ELSE 0 END) a_under_15_2,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 + ft2 > 3, 1, 0) ELSE 0 END) h_over_35,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 + ht2 > 3, 1, 0) ELSE 0 END) h_over_35_1,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF((ft1 - ht1 + ft2 - ht2) > 3, 1, 0) ELSE 0 END) h_over_35_2,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 + ft2 > 3, 1, 0) ELSE 0 END) a_over_35,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 + ht2 > 3, 1, 0) ELSE 0 END) a_over_35_1,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF((ft1 - ht1 + ft2 - ht2) > 3, 1, 0) ELSE 0 END) a_over_35_2,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 + ft2 < 4, 1, 0) ELSE 0 END) h_under_35,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 + ht2 < 4, 1, 0) ELSE 0 END) h_under_35_1,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF((ft1 - ht1 + ft2 - ht2) < 4, 1, 0) ELSE 0 END) h_under_35_2,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 + ft2 < 4, 1, 0) ELSE 0 END) a_under_35,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 + ht2 < 4, 1, 0) ELSE 0 END) a_under_35_1,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF((ft1 - ht1 + ft2 - ht2) < 4, 1, 0) ELSE 0 END) a_under_35_2,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 + ft2 <= 1, 1, 0) ELSE 0 END) h_goals_01,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 + ft2 <= 1, 1, 0) ELSE 0 END) a_goals_01,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 + ft2 >= 2 AND ft1 + ft2 <= 3, 1, 0) ELSE 0 END) h_goals_23,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 + ft2 >= 2 AND ft1 + ft2 <= 3, 1, 0) ELSE 0 END) a_goals_23,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 + ft2 >= 4 AND ft1 + ft2 <= 6, 1, 0) ELSE 0 END) h_goals_46,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 + ft2 >= 4 AND ft1 + ft2 <= 6, 1, 0) ELSE 0 END) a_goals_46,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 + ft2 >= 7, 1, 0) ELSE 0 END) h_goals_7,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 + ft2 >= 7, 1, 0) ELSE 0 END) a_goals_7,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft1 = 0, 1, 0) ELSE 0 END) h_not_scored,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft2 = 0, 1, 0) ELSE 0 END) a_not_scored,
          SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ft2 = 0, 1, 0) ELSE 0 END) h_not_conceded,
          SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ft1 = 0, 1, 0) ELSE 0 END) a_not_conceded
        FROM
          ft_league_standings fls
            INNER JOIN ft_fixture f ON f.sport_id = fls.sport_id AND f.season = fls.season AND (f.h_team = fls.team_id OR f.a_team = fls.team_id) AND f.status IN ('F', 'FE', 'FP')
            INNER JOIN ft_team t ON t.team_id = fls.team_id
        WHERE 
            fls.sport_id = $sportId
        AND fls.season = '$sportSeason'
        GROUP BY fls.team_id
        ";
          $stmt = $em->getConnection()->prepare($getWinsLossesDrawsQuery);
          $stmt->execute();
          $getWinsLossesDrawsResult = $stmt->fetchAll();
          foreach ($getWinsLossesDrawsResult as $result) {
            $sqlUpdateStats = "
            UPDATE team_stats SET
              h_wins = " . $result['h_wins'] . ",
              h_wins_ht = " . $result['h_wins_ht'] . ",
              a_wins = " . $result['a_wins'] . ",
              a_wins_ht = " . $result['a_wins_ht'] . ",
              h_loses = " . $result['h_loses'] . ",
              h_loses_ht = " . $result['h_loses_ht'] . ",
              a_loses = " . $result['a_loses'] . ",
              a_loses_ht = " . $result['a_loses_ht'] . ",
              h_draws = " . $result['h_draws'] . ",
              h_draws_ht = " . $result['h_draws_ht'] . ",
              a_draws = " . $result['a_draws'] . ",
              a_draws_ht = " . $result['a_draws_ht'] . ",
              h_win_win = " . $result['h_win_win'] . ",
              h_win_draw = " . $result['h_win_draw'] . ",
              h_win_lose = " . $result['h_win_lose'] . ",
              h_lose_lose = " . $result['h_lose_lose'] . ",
              h_lose_draw = " . $result['h_lose_draw'] . ",
              h_lose_win = " . $result['h_lose_win'] . ",
              h_draw_draw = " . $result['h_draw_draw'] . ",
              h_draw_win = " . $result['h_draw_win'] . ",
              h_draw_lose = " . $result['h_draw_lose'] . ",
              a_win_win = " . $result['a_win_win'] . ",
              a_win_draw = " . $result['a_win_draw'] . ",
              a_win_lose = " . $result['a_win_lose'] . ",
              a_lose_lose = " . $result['a_lose_lose'] . ",
              a_lose_draw = " . $result['a_lose_draw'] . ",
              a_lose_win = " . $result['a_lose_win'] . ",
              a_draw_draw = " . $result['a_draw_draw'] . ",
              a_draw_win = " . $result['a_draw_win'] . ",
              a_draw_lose = " . $result['a_draw_lose'] . ",
              h_played = " . ($result['h_wins'] + $result['h_loses'] + $result['h_draws']) . ",
              a_played = " . ($result['a_wins'] + $result['a_loses'] + $result['a_draws']) . ",
              h_goal_goal = " . $result['h_goal_goal'] . ",
              a_goal_goal = " . $result['a_goal_goal'] . ",
              h_no_goal = " . $result['h_no_goal'] . ",
              a_no_goal = " . $result['a_no_goal'] . ",
              h_goals_for = " . $result['h_goals_for'] . ",
              a_goals_for = " . $result['a_goals_for'] . ",
              h_goals_against = " . $result['h_goals_against'] . ",
              a_goals_against = " . $result['a_goals_against'] . ",
              h_goals_01 = " . $result['h_goals_01'] . ",
              a_goals_01 = " . $result['a_goals_01'] . ",
              h_goals_23 = " . $result['h_goals_23'] . ",
              a_goals_23 = " . $result['a_goals_23'] . ",
              h_goals_46 = " . $result['h_goals_46'] . ",
              a_goals_46 = " . $result['a_goals_46'] . ",
              h_goals_7 = " . $result['h_goals_7'] . ",
              a_goals_7 = " . $result['a_goals_7'] . ",
              h_over_25 = " . $result['h_over_25'] . ",
              h_over_25_1 = " . $result['h_over_25_1'] . ",
              h_over_25_2 = " . $result['h_over_25_2'] . ",
              a_over_25 = " . $result['a_over_25'] . ",
              a_over_25_1 = " . $result['a_over_25_1'] . ",
              a_over_25_2 = " . $result['a_over_25_2'] . ",
              h_under_25 = " . $result['h_under_25'] . ",
              h_under_25_1 = " . $result['h_under_25_1'] . ",
              h_under_25_2 = " . $result['h_under_25_2'] . ",
              a_under_25 = " . $result['a_under_25'] . ",
              a_under_25_1 = " . $result['a_under_25_1'] . ",
              a_under_25_2 = " . $result['a_under_25_2'] . ",
              h_over_15 = " . $result['h_over_15'] . ",
              h_over_15_1 = " . $result['h_over_15_1'] . ",
              h_over_15_2 = " . $result['h_over_15_2'] . ",
              a_over_15 = " . $result['a_over_15'] . ",
              a_over_15_1 = " . $result['a_over_15_1'] . ",
              a_over_15_2 = " . $result['a_over_15_2'] . ",
              h_under_15 = " . $result['h_under_15'] . ",
              h_under_15_1 = " . $result['h_under_15_1'] . ",
              h_under_15_2 = " . $result['h_under_15_2'] . ",
              a_under_15 = " . $result['a_under_15'] . ",
              a_under_15_1 = " . $result['a_under_15_1'] . ",
              a_under_15_2 = " . $result['a_under_15_2'] . ",
              h_over_35 = " . $result['h_over_35'] . ",
              h_over_35_1 = " . $result['h_over_35_1'] . ",
              h_over_35_2 = " . $result['h_over_35_2'] . ",
              a_over_35 = " . $result['a_over_35'] . ",
              a_over_35_1 = " . $result['a_over_35_1'] . ",
              a_over_35_2 = " . $result['a_over_35_2'] . ",
              h_under_35 = " . $result['h_under_35'] . ",
              h_under_35_1 = " . $result['h_under_35_1'] . ",
              h_under_35_2 = " . $result['h_under_35_2'] . ",
              a_under_35 = " . $result['a_under_35'] . ",
              a_under_35_1 = " . $result['a_under_35_1'] . ",
              a_under_35_2 = " . $result['a_under_35_2'] . ",
              h_not_scored = " . $result['h_not_scored'] . ",
              a_not_scored = " . $result['a_not_scored'] . ",
              h_not_conceded = " . $result['h_not_conceded'] . ",
              a_not_conceded = " . $result['a_not_conceded'] . "
              WHERE league_season_id = " . $leagueSeasonId . " AND team_id = " . $result['teamId'];
              $stmt = $em->getConnection()->prepare($sqlUpdateStats);
              $stmt->execute();

              //calculate streaks and red/yellow cards
              $matchOneByOneQuery = "
              SELECT ft_team.team_id, ft_team.name_gr, ft_fixture.ft1, ft_fixture.ft2, ft_fixture.h_team, ft_fixture.a_team, ft_fixture.events
              FROM ft_league_standings
                INNER JOIN ft_fixture ON ft_fixture.sport_id = ft_league_standings.sport_id AND ft_fixture.season = ft_league_standings.season AND (ft_fixture.h_team = ft_league_standings.team_id OR ft_fixture.a_team = ft_league_standings.team_id) AND ft_fixture.status IN ('F', 'FE', 'FP')
                INNER JOIN ft_team ON ft_team.team_id = ft_league_standings.team_id
              WHERE
                ft_league_standings.sport_id = $sportId
              AND ft_league_standings.season = '$sportSeason'
              AND ft_team.team_id = " . $result['teamId'] . " ORDER BY ft_fixture.match_datetime asc
              ";
              $stmt = $em->getConnection()->prepare($matchOneByOneQuery);
              $stmt->execute();
              $matchOneByOneResult = $stmt->fetchAll();

              $t_win_streak_current = 0;
              $t_win_streak_final = 0;
              $t_loss_streak_current = 0;
              $t_loss_streak_final = 0;
              $t_draw_streak_current = 0;
              $t_draw_streak_final = 0;
              $t_no_win_streak_current = 0;
              $t_no_win_streak_final = 0;
              $t_no_loss_streak_current = 0;
              $t_no_loss_streak_final = 0;
              $t_no_draw_streak_current = 0;
              $t_no_draw_streak_final = 0;

              $h_win_streak_current = 0;
              $h_win_streak_final = 0;
              $h_loss_streak_current = 0;
              $h_loss_streak_final = 0;
              $h_draw_streak_current = 0;
              $h_draw_streak_final = 0;
              $h_no_win_streak_current = 0;
              $h_no_win_streak_final = 0;
              $h_no_loss_streak_current = 0;
              $h_no_loss_streak_final = 0;
              $h_no_draw_streak_current = 0;
              $h_no_draw_streak_final = 0;

              $a_win_streak_current = 0;
              $a_win_streak_final = 0;
              $a_loss_streak_current = 0;
              $a_loss_streak_final = 0;
              $a_draw_streak_current = 0;
              $a_draw_streak_final = 0;
              $a_no_win_streak_current = 0;
              $a_no_win_streak_final = 0;
              $a_no_loss_streak_current = 0;
              $a_no_loss_streak_final = 0;
              $a_no_draw_streak_current = 0;
              $a_no_draw_streak_final = 0;

              $t_not_scored_streak = 0;
              $h_not_scored_streak = 0;
              $a_not_scored_streak = 0;
              $t_no_goal_streak = 0;
              $h_no_goal_streak = 0;
              $a_no_goal_streak = 0;

              $t_goals_for_streak = 0;
              $h_goals_for_streak = 0;
              $a_goals_for_streak = 0;

              $t_goals_against_streak = 0;
              $h_goals_against_streak = 0;
              $a_goals_against_streak = 0;
              $t_not_conceded_streak = 0;
              $h_not_conceded_streak = 0;
              $a_not_conceded_streak = 0;

              $t_red_cards = 0;
              $t_yellow_cards = 0;

              $hGoalsMinute015 = 0;
              $hGoalsMinute015Conceded = 0;
              $hGoalsMinute1630 = 0;
              $hGoalsMinute1630Conceded = 0;
              $hGoalsMinute3145 = 0;
              $hGoalsMinute3145Conceded = 0;
              $hGoalsMinute4660 = 0;
              $hGoalsMinute4660Conceded = 0;
              $hGoalsMinute6175 = 0;
              $hGoalsMinute6175Conceded = 0;
              $hGoalsMinute7690 = 0;
              $hGoalsMinute7690Conceded = 0;

              $aGoalsMinute015 = 0;
              $aGoalsMinute015Conceded = 0;
              $aGoalsMinute1630 = 0;
              $aGoalsMinute1630Conceded = 0;
              $aGoalsMinute3145 = 0;
              $aGoalsMinute3145Conceded = 0;
              $aGoalsMinute4660 = 0;
              $aGoalsMinute4660Conceded = 0;
              $aGoalsMinute6175 = 0;
              $aGoalsMinute6175Conceded = 0;
              $aGoalsMinute7690 = 0;
              $aGoalsMinute7690Conceded = 0;

              foreach ($matchOneByOneResult as $match) {

                if ($match['h_team'] == $result['teamId']) {
                  $match_yellow_cards = substr_count($match['events'], '##yc##1');
                  $t_yellow_cards = $t_yellow_cards + $match_yellow_cards;
                  $match_red_cards = substr_count($match['events'], '##rc##1');
                  $t_red_cards = $t_red_cards + $match_red_cards;
                }

                elseif ($match['a_team'] == $result['teamId']) {
                  $match_yellow_cards = substr_count($match['events'], '##yc##2');
                  $t_yellow_cards = $t_yellow_cards + $match_yellow_cards;
                  $match_red_cards = substr_count($match['events'], '##rc##2');
                  $t_red_cards = $t_red_cards + $match_red_cards;
                }

                if ($match['h_team'] == $match['team_id']) {

                  if (!empty($match['events'])) {
                    $html = explode('##', $match['events']);

                    for ($i = 0; $i <= count($html) - 1; $i++) {
                      // Parse event
                      if ($i % 4 == 0) {
                        $playerName = $html[$i];
                      }
                      elseif ($i % 4 == 1) {
                        $eventType = $html[$i];
                      }
                      elseif ($i % 4 == 2) {
                        $team = $html[$i];
                      }
                      elseif ($i % 4 == 3) {
                        $minute = (int) $html[$i];
                        // Calculate
                        if ($eventType == 'goal' || $eventType == 'og' || $eventType == 'pen') {
                          if ($team == 1) {
                            if ($minute <= 15) {
                              $hGoalsMinute015++;
                            }
                            elseif ($minute >= 16 && $minute <= 30) {
                              $hGoalsMinute1630++;
                            }
                            elseif ($minute >= 31 && $minute <= 45) {
                              $hGoalsMinute3145++;
                            }
                            elseif ($minute >= 46 && $minute <= 60) {
                              $hGoalsMinute4660++;
                            }
                            elseif ($minute >= 61 && $minute <= 75) {
                              $hGoalsMinute6175++;
                            }
                            elseif ($minute >= 76) {
                              $hGoalsMinute7690++;
                            }
                          }
                          else {
                            if ($minute <= 15) {
                              $aGoalsMinute015Conceded++;
                            }
                            elseif ($minute >= 16 && $minute <= 30) {
                              $aGoalsMinute1630Conceded++;
                            }
                            elseif ($minute >= 31 && $minute <= 45) {
                              $aGoalsMinute3145Conceded++;
                            }
                            elseif ($minute >= 46 && $minute <= 60) {
                              $aGoalsMinute4660Conceded++;
                            }
                            elseif ($minute >= 61 && $minute <= 75) {
                              $aGoalsMinute6175Conceded++;
                            }
                            elseif ($minute >= 76) {
                              $aGoalsMinute7690Conceded++;
                            }
                          }
                        }

                      } // $i % 3
                    } // for $events $html
                  }

                  if ($match['ft1'] > $match['ft2']) {
                    $t_win_streak_current++;
                    $t_draw_streak_current = 0;
                    $t_loss_streak_current = 0;
                    if ($t_win_streak_final < $t_win_streak_current) {
                      $t_win_streak_final =  $t_win_streak_current;
                    }

                    $h_win_streak_current++;
                    $h_draw_streak_current = 0;
                    $h_loss_streak_current = 0;
                    if ($h_win_streak_final < $h_win_streak_current) {
                      $h_win_streak_final =  $h_win_streak_current;
                    }

                    $t_no_loss_streak_current++;
                    $t_no_draw_streak_current++;
                    $t_no_win_streak_current = 0;
                    if ($t_no_loss_streak_final < $t_no_loss_streak_current) {
                      $t_no_loss_streak_final =  $t_no_loss_streak_current;
                    }
                    if ($t_no_draw_streak_final < $t_no_draw_streak_current) {
                      $t_no_draw_streak_final =  $t_no_draw_streak_current;
                    }

                    $h_no_loss_streak_current++;
                    $h_no_draw_streak_current++;
                    $h_no_win_streak_current = 0;
                    if ($h_no_loss_streak_final < $h_no_loss_streak_current) {
                      $h_no_loss_streak_final =  $h_no_loss_streak_current;
                    }
                    if ($h_no_draw_streak_final < $h_no_draw_streak_current) {
                      $h_no_draw_streak_final =  $h_no_draw_streak_current;
                    }
                  }

                  elseif ($match['ft1'] < $match['ft2']) {
                    $t_loss_streak_current++;
                    $t_draw_streak_current = 0;
                    $t_win_streak_current = 0;
                    if ($t_loss_streak_final < $t_loss_streak_current) {
                      $t_loss_streak_final =  $t_loss_streak_current;
                    }

                    $h_loss_streak_current++;
                    $h_draw_streak_current = 0;
                    $h_win_streak_current = 0;
                    if ($h_loss_streak_final < $h_loss_streak_current) {
                      $h_loss_streak_final =  $h_loss_streak_current;
                    }

                    $t_no_win_streak_current++;
                    $t_no_draw_streak_current++;
                    $t_no_loss_streak_current = 0;
                    if ($t_no_win_streak_final < $t_no_win_streak_current) {
                      $t_no_win_streak_final =  $t_no_win_streak_current;
                    }
                    if ($t_no_draw_streak_final < $t_no_draw_streak_current) {
                      $t_no_draw_streak_final =  $t_no_draw_streak_current;
                    }

                    $h_no_win_streak_current++;
                    $h_no_draw_streak_current++;
                    $h_no_loss_streak_current = 0;
                    if ($h_no_win_streak_final < $h_no_win_streak_current) {
                      $h_no_win_streak_final =  $h_no_win_streak_current;
                    }
                    if ($h_no_draw_streak_final < $h_no_draw_streak_current) {
                      $h_no_draw_streak_final =  $h_no_draw_streak_current;
                    }
                  }

                  elseif ($match['ft1'] == $match['ft2']) {
                    $t_draw_streak_current++;
                    $t_win_streak_current = 0;
                    $t_loss_streak_current = 0;
                    if ($t_draw_streak_final < $t_draw_streak_current) {
                      $t_draw_streak_final =  $t_draw_streak_current;
                    }

                    $h_draw_streak_current++;
                    $h_win_streak_current = 0;
                    $h_loss_streak_current = 0;
                    if ($h_draw_streak_final < $h_draw_streak_current) {
                      $h_draw_streak_final =  $h_draw_streak_current;
                    }

                    $t_no_win_streak_current++;
                    $t_no_loss_streak_current++;
                    $t_no_draw_streak_current = 0;
                    if ($t_no_win_streak_final < $t_no_win_streak_current) {
                      $t_no_win_streak_final =  $t_no_win_streak_current;
                    }
                    if ($t_no_loss_streak_final < $t_no_loss_streak_current) {
                      $t_no_loss_streak_final =  $t_no_loss_streak_current;
                    }

                    $h_no_win_streak_current++;
                    $h_no_loss_streak_current++;
                    $h_no_draw_streak_current = 0;
                    if ($h_no_win_streak_final < $h_no_win_streak_current) {
                      $h_no_win_streak_final =  $h_no_win_streak_current;
                    }
                    if ($h_no_loss_streak_final < $h_no_loss_streak_current) {
                      $h_no_loss_streak_final =  $h_no_loss_streak_current;
                    }

                  }

                  if ($match['ft1'] == 0) {
                    $t_not_scored_streak++;
                    $h_not_scored_streak++;
                  }
                  else {
                    $t_not_scored_streak = 0;
                    $h_not_scored_streak = 0;
                  }

                  if ($match['ft1'] == 0 || $match['ft2'] == 0) {
                    $t_no_goal_streak++;
                    $h_no_goal_streak++;
                  }
                  else {
                    $t_no_goal_streak = 0;
                    $h_no_goal_streak = 0;
                  }

                  if ($match['ft1'] != 0) {
                    $t_goals_for_streak++;
                    $h_goals_for_streak++;
                  }
                  else {
                    $t_goals_for_streak = 0;
                    $h_goals_for_streak = 0;
                  }

                  if ($match['ft2'] != 0) {
                    $t_goals_against_streak++;
                    $h_goals_against_streak++;
                    $t_not_conceded_streak = 0;
                    $h_not_conceded_streak = 0;
                  }
                  else {
                    $t_goals_against_streak = 0;
                    $h_goals_against_streak = 0;
                    $t_not_conceded_streak++;
                    $h_not_conceded_streak++;
                  }

                }

                elseif ($match['a_team'] == $match['team_id']) {

                  if (!empty($match['events'])) {

                    $html = explode('##', $match['events']);

                    for ($i = 0; $i <= count($html) - 1; $i++) {
                      // Parse event
                      if ($i % 4 == 0) {
                        $playerName = $html[$i];
                      }
                      elseif ($i % 4 == 1) {
                        $eventType = $html[$i];
                      }
                      elseif ($i % 4 == 2) {
                        $team = $html[$i];
                      }
                      elseif ($i % 4 == 3) {
                        $minute = (int) $html[$i];
                        // Calculate
                        if ($eventType == 'goal' || $eventType == 'og' || $eventType == 'pen') {
                          if ($team == 1) {
                            if ($minute <= 15) {
                              $hGoalsMinute015Conceded++;
                            }
                            elseif ($minute >= 16 && $minute <= 30) {
                              $hGoalsMinute1630Conceded++;
                            }
                            elseif ($minute >= 31 && $minute <= 45) {
                              $hGoalsMinute3145Conceded++;
                            }
                            elseif ($minute >= 46 && $minute <= 60) {
                              $hGoalsMinute4660Conceded++;
                            }
                            elseif ($minute >= 61 && $minute <= 75) {
                              $hGoalsMinute6175Conceded++;
                            }
                            elseif ($minute >= 76) {
                              $hGoalsMinute7690Conceded++;
                            }
                          }
                          else {
                            if ($minute <= 15) {
                              $aGoalsMinute015++;
                            }
                            elseif ($minute >= 16 && $minute <= 30) {
                              $aGoalsMinute1630++;
                            }
                            elseif ($minute >= 31 && $minute <= 45) {
                              $aGoalsMinute3145++;
                            }
                            elseif ($minute >= 46 && $minute <= 60) {
                              $aGoalsMinute4660++;
                            }
                            elseif ($minute >= 61 && $minute <= 75) {
                              $aGoalsMinute6175++;
                            }
                            elseif ($minute >= 76) {
                              $aGoalsMinute7690++;
                            }
                          }
                        }

                      } // $i % 3
                    } // for $events $html
                  }

                  if ($match['ft2'] > $match['ft1']) {
                    $t_win_streak_current++;
                    $t_draw_streak_current = 0;
                    $t_loss_streak_current = 0;
                    if ($t_win_streak_final < $t_win_streak_current) {
                      $t_win_streak_final =  $t_win_streak_current;
                    }

                    $a_win_streak_current++;
                    $a_draw_streak_current = 0;
                    $a_loss_streak_current = 0;
                    if ($a_win_streak_final < $a_win_streak_current) {
                      $a_win_streak_final =  $a_win_streak_current;
                    }

                    $t_no_loss_streak_current++;
                    $t_no_draw_streak_current++;
                    $t_no_win_streak_current = 0;
                    if ($t_no_loss_streak_final < $t_no_loss_streak_current) {
                      $t_no_loss_streak_final =  $t_no_loss_streak_current;
                    }
                    if ($t_no_draw_streak_final < $t_no_draw_streak_current) {
                      $t_no_draw_streak_final =  $t_no_draw_streak_current;
                    }

                    $a_no_loss_streak_current++;
                    $a_no_draw_streak_current++;
                    $a_no_win_streak_current = 0;
                    if ($a_no_loss_streak_final < $a_no_loss_streak_current) {
                      $a_no_loss_streak_final =  $a_no_loss_streak_current;
                    }
                    if ($a_no_draw_streak_final < $a_no_draw_streak_current) {
                      $a_no_draw_streak_final =  $a_no_draw_streak_current;
                    }
                  }

                  elseif($match['ft2'] < $match['ft1']) {
                    $t_loss_streak_current++;
                    $t_draw_streak_current = 0;
                    $t_win_streak_current = 0;
                    if ($t_loss_streak_final < $t_loss_streak_current) {
                      $t_loss_streak_final =  $t_loss_streak_current;
                    }

                    $a_loss_streak_current++;
                    $a_draw_streak_current = 0;
                    $a_win_streak_current = 0;
                    if ($a_loss_streak_final < $a_loss_streak_current) {
                      $a_loss_streak_final =  $a_loss_streak_current;
                    }

                    $t_no_win_streak_current++;
                    $t_no_draw_streak_current++;
                    $t_no_loss_streak_current = 0;
                    if ($t_no_win_streak_final < $t_no_win_streak_current) {
                      $t_no_win_streak_final =  $t_no_win_streak_current;
                    }
                    if ($t_no_draw_streak_final < $t_no_draw_streak_current) {
                      $t_no_draw_streak_final =  $t_no_draw_streak_current;
                    }

                    $a_no_win_streak_current++;
                    $a_no_draw_streak_current++;
                    $a_no_loss_streak_current = 0;
                    if ($a_no_win_streak_final < $a_no_win_streak_current) {
                      $a_no_win_streak_final =  $a_no_win_streak_current;
                    }
                    if ($a_no_draw_streak_final < $a_no_draw_streak_current) {
                      $a_no_draw_streak_final =  $a_no_draw_streak_current;
                    }

                  }

                  elseif($match['ft2'] == $match['ft1']) {
                    $t_draw_streak_current++;
                    $t_win_streak_current = 0;
                    $t_loss_streak_current = 0;
                    if ($t_draw_streak_final < $t_draw_streak_current) {
                      $t_draw_streak_final =  $t_draw_streak_current;
                    }

                    $a_draw_streak_current++;
                    $a_win_streak_current = 0;
                    $a_loss_streak_current = 0;
                    if ($a_draw_streak_final < $a_draw_streak_current) {
                      $a_draw_streak_final =  $a_draw_streak_current;
                    }

                    $t_no_win_streak_current++;
                    $t_no_loss_streak_current++;
                    $t_no_draw_streak_current = 0;
                    if ($t_no_win_streak_final < $t_no_win_streak_current) {
                      $t_no_win_streak_final =  $t_no_win_streak_current;
                    }
                    if ($t_no_loss_streak_final < $t_no_loss_streak_current) {
                      $t_no_loss_streak_final =  $t_no_loss_streak_current;
                    }

                    $a_no_win_streak_current++;
                    $a_no_loss_streak_current++;
                    $a_no_draw_streak_current = 0;
                    if ($a_no_win_streak_final < $a_no_win_streak_current) {
                      $a_no_win_streak_final =  $a_no_win_streak_current;
                    }
                    if ($a_no_loss_streak_final < $a_no_loss_streak_current) {
                      $a_no_loss_streak_final =  $a_no_loss_streak_current;
                    }

                  }

                  if ($match['ft2'] == 0) {
                    $t_not_scored_streak++;
                    $a_not_scored_streak++;
                  }
                  else {
                    $t_not_scored_streak = 0;
                    $a_not_scored_streak = 0;
                  }

                  if ($match['ft1'] == 0 || $match['ft2'] == 0) {
                    $t_no_goal_streak++;
                    $a_no_goal_streak++;
                  }
                  else {
                    $t_no_goal_streak = 0;
                    $a_no_goal_streak = 0;
                  }

                  if ($match['ft1'] != 0) {
                    $t_goals_for_streak++;
                    $a_goals_for_streak++;
                  }
                  else {
                    $t_goals_for_streak = 0;
                    $a_goals_for_streak = 0;
                  }

                  if ($match['ft1'] != 0) {
                    $t_goals_against_streak++;
                    $a_goals_against_streak++;
                    $t_not_conceded_streak = 0;
                    $a_not_conceded_streak = 0;
                  }
                  else {
                    $t_goals_against_streak = 0;
                    $a_goals_against_streak = 0;
                    $t_not_conceded_streak++;
                    $a_not_conceded_streak++;
                  }

                }

              }
              // echo $result['teamId'] . ' teamId has ' . $t_win_streak_final . ' wins streak, ' . $t_loss_streak_final . ' loses streak and ' . $t_draw_streak_final . ' draws streak.';
              $sqlUpdateStreakStats = "
              UPDATE team_stats SET
                t_yellow_cards = " . $t_yellow_cards . ",
                t_red_cards = " . $t_red_cards . ",
                t_win_streak = " . $t_win_streak_current . ",
                t_lose_streak = " . $t_loss_streak_current . ",
                t_draw_streak = " . $t_draw_streak_current . ",
                t_no_win_streak = " . $t_no_win_streak_current . ",
                t_no_lose_streak = " . $t_no_loss_streak_current . ",
                t_no_draw_streak = " . $t_no_draw_streak_current . ",
                h_win_streak = " . $h_win_streak_current . ",
                h_loses_streak = " . $h_loss_streak_current . ",
                h_draw_streak = " . $h_draw_streak_current . ",
                h_no_win_streak = " . $h_no_win_streak_current . ",
                h_no_lose_streak = " . $h_no_loss_streak_current . ",
                h_no_draw_streak = " . $h_no_draw_streak_current . ",
                a_win_streak = " . $a_win_streak_current . ",
                a_loses_streak = " . $a_loss_streak_current . ",
                a_draw_streak = " . $a_draw_streak_current . ",
                a_no_win_streak = " . $a_no_win_streak_current . ",
                a_no_lose_streak = " . $a_no_loss_streak_current . ",
                a_no_draw_streak = " . $a_no_draw_streak_current . ",
                t_not_scored_streak = " . $t_not_scored_streak . ",
                h_not_scored_streak = " . $h_not_scored_streak . ",
                a_not_scored_streak = " . $a_not_scored_streak . ",
                t_no_goal_streak = " . $t_no_goal_streak . ",
                h_no_goal_streak = " . $h_no_goal_streak . ",
                a_no_goal_streak = " . $a_no_goal_streak . ",
                t_goals_for_streak = " . $t_goals_for_streak . ",
                h_goals_for_streak = " . $h_goals_for_streak . ",
                a_goals_for_streak = " . $a_goals_for_streak . ",
                t_goals_against_streak = " . $t_goals_against_streak . ",
                h_goals_against_streak = " . $h_goals_against_streak . ",
                a_goals_against_streak = " . $a_goals_against_streak . ",
                t_not_conceded_streak = " . $t_not_conceded_streak . ",
                h_not_conceded_streak = " . $h_not_conceded_streak . ",
                a_not_conceded_streak = " . $a_not_conceded_streak . ",
                h_goals_minute_015 = " . $hGoalsMinute015 . ",
                h_goals_minute_1630 = " . $hGoalsMinute1630 . ",
                h_goals_minute_3145 = " . $hGoalsMinute3145 . ",
                h_goals_minute_4660 = " . $hGoalsMinute4660 . ",
                h_goals_minute_6175 = " . $hGoalsMinute6175 . ",
                h_goals_minute_7690 = " . $hGoalsMinute7690 . ",
                a_goals_minute_015 = " . $aGoalsMinute015 . ",
                a_goals_minute_1630 = " . $aGoalsMinute1630 . ",
                a_goals_minute_3145 = " . $aGoalsMinute3145 . ",
                a_goals_minute_4660 = " . $aGoalsMinute4660 . ",
                a_goals_minute_6175 = " . $aGoalsMinute6175 . ",
                a_goals_minute_7690 = " . $aGoalsMinute7690 . ",
                h_goals_minute_015_conceded = " . $hGoalsMinute015Conceded . ",
                h_goals_minute_1630_conceded = " . $hGoalsMinute1630Conceded . ",
                h_goals_minute_3145_conceded = " . $hGoalsMinute3145Conceded . ",
                h_goals_minute_4660_conceded = " . $hGoalsMinute4660Conceded . ",
                h_goals_minute_6175_conceded = " . $hGoalsMinute6175Conceded . ",
                h_goals_minute_7690_conceded = " . $hGoalsMinute7690Conceded . ",
                a_goals_minute_015_conceded = " . $aGoalsMinute015Conceded . ",
                a_goals_minute_1630_conceded = " . $aGoalsMinute1630Conceded . ",
                a_goals_minute_3145_conceded = " . $aGoalsMinute3145Conceded . ",
                a_goals_minute_4660_conceded = " . $aGoalsMinute4660Conceded . ",
                a_goals_minute_6175_conceded = " . $aGoalsMinute6175Conceded . ",
                a_goals_minute_7690_conceded = " . $aGoalsMinute7690Conceded . "
                WHERE league_season_id = " . $leagueSeasonId . " AND team_id = " . $result['teamId'];
                $stmt = $em->getConnection()->prepare($sqlUpdateStreakStats);
                $stmt->execute();
          }
        }
        else {
          return new Response('Something went wrong. Season does not exist');
        }
      }
      else {
        return new Response('No sport id or domain id specified. Please run the command with the -s option and -d option.');
      }
    } catch (\Exception $e) {
      echo '<pre>';
      var_dump($e->getMessage());
      echo '</pre>';
    }
    
    $sportOptions = $sportId;
    $domain = $em->getRepository('AppBundle:Domain')->findOneById($domainId);
    $sports = $em->getRepository('AppBundle:Sport')->findById($sportOptions);
    foreach ($sports as $sport) {
        if (1 === (int) $sport->getIsAdditional()) {
          $sportsClearCache[(int) $sport->getParentId()]['sport_id'] = (int) $sport->getParentId();
          $sportsClearCache[(int) $sport->getParentId()]['domain'][] = $domainId;
        } else {
          $sportsClearCache[(int) $sport->getId()]['sport_id'] = (int) $sport->getId();
          $sportsClearCache[(int) $sport->getId()]['domain'][] = $domainId;
        }
    }
    if ($domain && $sportOptions) {
      $helper->clearStandingsMemcache($em, $sportsClearCache, $translator, $projectDir, $environment);
    }
    return new Response('Stats fixed for sportId ' . $sportId);

  }
}
