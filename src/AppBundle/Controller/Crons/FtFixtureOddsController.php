<?php

namespace AppBundle\Controller\Crons;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use FOS\RestBundle\Controller\Annotations as Rest;

use Symfony\Component\Cache\Adapter\FilesystemAdapter;

/**
 * Fixture Odds controller.
 *
 * @Route("/cron")
 */
class FtFixtureOddsController extends Controller
{

  public function createAction() {
    try {
      $em     = $this->getDoctrine()->getManager();
      $logger = $this->get('logger');
      $helper = $this->container->get("wbs.globalhelper.service");

      // TODO right now previews are created only for betarades.gr
      // but it should be
      // $domains = $domains = $em->getRepository('AppBundle:Domain')->findBy(array('isActive' => true));
      // temp fix is to get the domain name from the parameters.yml configuration
      // get active domains here and create the connections
      $domainName = $this->container->hasParameter('domain.name') ? $this->container->getParameter('domain.name') : null;
      $tempDomain = $em->getRepository('AppBundle:Domain')->findOneByName($domainName);

      $connection = $helper->getDomainConnection($tempDomain);
      // $domainCompetitions = array();

      // works for specific domain only
      $odds = $em->getRepository('AppBundle:FtFixtureOdd')->getTodaysOdds($tempDomain->getId());

      if ($odds) {
        foreach ($odds as $odd) {
          $this->insertPost($connection, $tempDomain->getDbPrefix(), $odd);
        }
      }

      $connection->close();
      $em->clear();
      return new Response('creating odds finished!');
    }
    catch (\Exception $e) {
      return new Response($e->getMessage());
    }
  }

  private function insertPost($connection, $dbPrefix, $data) {
    $odd_1      = $data['odd_1'] ?: 'null';
    $odd_x      = $data['odd_x'] ?: 'null';
    $odd_2      = $data['odd_2'] ?: 'null';
    $odd_u25    = $data['odd_u25'] ?: 'null';
    $odd_o25    = $data['odd_o25'] ?: 'null';
    $p_odd_1    = $data['p_odd_1'] ?: 'null';
    $p_odd_x    = $data['p_odd_x'] ?: 'null';
    $p_odd_2    = $data['p_odd_2'] ?: 'null';
    $p_odd_u25  = $data['p_odd_u25'] ?: 'null';
    $p_odd_o25  = $data['p_odd_o25'] ?: 'null';

    $sqlInsert = "
    INSERT INTO ".$dbPrefix."odds (book_id, odd_1, odd_x, odd_2, odd_u25, odd_o25, p_odd_1, p_odd_x, p_odd_2, p_odd_u25, p_odd_o25, match_datetime, last_modified, preview_id) VALUES (
      ".$data['bookmaker_id'].",
      ".$odd_1.",
      ".$odd_x.",
      ".$odd_2.",
      ".$odd_u25.",
      ".$odd_o25.",
      ".$p_odd_1.",
      ".$p_odd_x.",
      ".$p_odd_2.",
      ".$p_odd_u25.",
      ".$p_odd_o25.",
      '".$data['match_datetime']."',
      '".$data['last_modified']."',
      ".$data['preview_id']."
      ) ON DUPLICATE KEY UPDATE book_id = VALUES(book_id), odd_1 = VALUES(odd_1), odd_x = VALUES(odd_x), odd_2 = VALUES(odd_2), odd_u25 = VALUES(odd_u25), odd_o25 = VALUES(odd_o25)
      , p_odd_1 = VALUES(p_odd_1), p_odd_x = VALUES(p_odd_x), p_odd_2 = VALUES(p_odd_2), p_odd_u25 = VALUES(p_odd_u25), p_odd_o25 = VALUES(p_odd_o25), match_datetime = VALUES(match_datetime), last_modified = VALUES(last_modified), preview_id = VALUES(preview_id)
    ";

    $stmt = $connection->prepare($sqlInsert);
    $stmt->execute();

    return;
  }
}
