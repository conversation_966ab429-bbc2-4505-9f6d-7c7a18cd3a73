<?php

namespace AppBundle\Controller\Tools;

use AppBundle\Entity\City;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;

/**
 * City controller.
 *
 * @Route("/admin/tools/cities")
 */
class CityController extends Controller
{

    /**
    * City Locator
    *
    * @Route("/find", name="tools_city_find_index")
    * @Method("GET")
    */
	public function findCityAction()
	{
        $em = $this->getDoctrine()->getManager();
        $helper = $this->container->get('wbs.globalhelper.service');

        return $this->render('city/find.html.twig', array());
    }
}
