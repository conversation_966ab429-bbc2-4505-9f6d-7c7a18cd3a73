<?php

namespace AppBundle\Controller\Tools;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\BufferedOutput;
use Symfony\Component\HttpFoundation\Response;

use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

/**
 * Standings Tool controller.
 *
 * @Route("/admin/tools/standings")
 */
class StandingsToolController extends Controller
{
	/**
		* Standings first page loader
		*
		* @Route("/get", name="tools_standings_index")
		* @Method("GET")
		*/
	public function indexAction()
	{
		$em = $this->getDoctrine()->getManager();

		// $domains = $em->getRepository('AppBundle:Domain')->findBy(array('isActive' => true));
		$domains = $em->getRepository('AppBundle:Domain')->findAll();

		$choicesArr = array();
		if ($domains) {
			foreach ($domains as $domain) {
				$choicesArr[$domain->getName()] = $domain->getId();
			}
		}

		$formBuilder = $this->createFormBuilder()
			->add('domain', ChoiceType::class, array(
				'choices' => $choicesArr,
				'label'		=> false,
			));

		$form = $formBuilder->getForm();

		return $this->render('tools/standings/index.html.twig', array(
			'form' => $form->createView(),
		));
	}

	/**
		* Generate season command
		*
		* @Route("/generate-season", name="checker_generateseason", options={"expose": true})
		* @Method("GET")
		*/
	public function generateSeasonAction() {
		$kernel = $this->get('kernel');
		$application = new Application($kernel);
		$application->setAutoExit(false);

		$input = new ArrayInput(array(
			'command'	=> 'wbs:generate:season',
			'--debug'	=> true,
		));

		$output = new BufferedOutput();
		$application->run($input, $output);

		$content = $output->fetch();

		return new Response($content);
	}

	/**
		* Generate leagues command
		*
		* @Route("/generate-leagues", name="checker_generateleagues", options={"expose": true})
		* @Method("GET")
		*/
	public function generateLeaguesAction() {
		$kernel = $this->get('kernel');
		$application = new Application($kernel);
		$application->setAutoExit(false);

		$input = new ArrayInput(array(
			'command'	=> 'wbs:feed:parse',
			'name'		=> 'leagues',
		));

		$output = new BufferedOutput();
		$application->run($input, $output);

		$content = $output->fetch();

		return new Response($content);
	}
}
