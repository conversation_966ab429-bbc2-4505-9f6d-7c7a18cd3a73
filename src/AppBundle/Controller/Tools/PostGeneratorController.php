<?php

namespace AppBundle\Controller\Tools;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\BufferedOutput;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;

/**
 * Post Generator Tool controller.
 *
 * @Route("/admin/tools/posts")
 */
class PostGeneratorController extends Controller
{
    /**
     * List domains and sports
     *
     * @Route("/index", name="tools_generate_posts_index")
     * @Method("GET")
     */
    public function indexAction()
    {
        $em = $this->getDoctrine()->getManager();
        $domains = $em->getRepository('AppBundle:Domain')->findAll();

        $choicesArr = array();
        if ($domains) {
            foreach ($domains as $domain) {
                $choicesArr[$domain->getName()] = $domain->getId();
            }
        }

        $formBuilder = $this->createFormBuilder()
            ->add('domain', ChoiceType::class, array(
                'choices' => $choicesArr,
                'label'		=> false,
            ));

        $form = $formBuilder->getForm();

        return $this->render('tools/generateposts/index.html.twig', array(
            'form' => $form->createView(),
        ));
    }

    /**
     * Call wbs:post:create command for domain and sport
     *
     * @Method("GET")
     * @Route("/generate/{domainId}/{sportId}", name="tools_generate_posts_create", options={"expose": true})
     */
    public function generatePostsAction(Request $request, $domainId, $sportId)
    {
        if (empty($domainId) || empty($sportId)) {
            return new Response('Domain Id or Sport Id was not provided');
        }

        $kernel = $this->get('kernel');
        $application = new Application($kernel);
        $application->setAutoExit(false);

        $input = new ArrayInput(array(
            'command' => 'wbs:post:create',
            '--domain' => $domainId,
            '--sport' => $sportId,
            '--env' => 'prod'
        ));

        $output = new BufferedOutput();
        $application->run($input, $output);
        $content = $output->fetch();

        return new Response($content);
    }
}
