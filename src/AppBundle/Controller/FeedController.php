<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

use JMS\Serializer\SerializerBuilder;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Doctrine\Common\Collections\ArrayCollection;
use AppBundle\Entity\Feed;
use AppBundle\Form\FeedType;

/**
 * Feed controller.
 *
 * @Route("/feed")
 */
class FeedController extends Controller
{

  /**
   * Lists all Feed entities.
   *
   * @Route("/", name="feed_index")
   * @Method("GET")
   */
  public function indexAction()
  {
    $em = $this->getDoctrine()->getManager();

    $feeds = $em->getRepository('AppBundle:Feed')->findAll();

    return $this->render('feed/index.html.twig', array(
        'feeds' => $feeds,
    ));
  }

    /**
     * Lists all Feed entities.
     *
     * @Route("/react", name="feed_react")
     * @Method("GET")
     */
    public function reactAction()
    {
        // $em = $this->getDoctrine()->getManager();

        // $feeds = $em->getRepository('AppBundle:Feed')->findAll();
        //
        // $encoder = new JsonEncoder();
        // $normalizer = new ObjectNormalizer();
        //
        // $normalizer->setCircularReferenceHandler(function ($object) {
        //     return $object->getFeedXmls();
        // });
        //
        // $serializer = new Serializer(array($normalizer), array($encoder));
        // $lala = $serializer->serialize($feeds, 'json');
        // $data = array('feeds' => $lala);

        // return new JsonResponse($lala);

        return $this->render('feed/index.html.twig', array(
        ));
    }

    /**
     * Lists all Feed entities.
     *
     * @Route("/all", name="feed_all")
     * @Method("GET")
     */
    public function allAction()
    {
      $em = $this->getDoctrine()->getManager();

      $feeds = $em->getRepository('AppBundle:Feed')->findAll();

      $encoder = new JsonEncoder();
      $normalizer = new ObjectNormalizer();

      $normalizer->setCircularReferenceHandler(function ($object) {
          return $object->getFeedXmls();
      });

      $serializer = new Serializer(array($normalizer), array($encoder));
      $lala = $serializer->serialize($feeds, 'json');
      $data = array('feeds' => $lala);

      return new JsonResponse($data);
    }

    /**
     * Creates a new Feed entity.
     *
     * @Route("/new", name="feed_new")
     * @Method({"GET", "POST"})
     */
    public function newAction(Request $request)
    {
        $feed = new Feed();
        $form = $this->createForm('AppBundle\Form\FeedType', $feed);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($feed);
            $em->flush();

            return $this->redirectToRoute('feed_show', array('id' => $feed->getId()));
        }

        return $this->render('feed/new.html.twig', array(
            'feed' => $feed,
            'form' => $form->createView(),
        ));
    }

    /**
     * Finds and displays a Feed entity.
     *
     * @Route("/{id}", name="feed_show")
     * @Method("GET")
     */
    public function showAction(Feed $feed)
    {
        $deleteForm = $this->createDeleteForm($feed);

        return $this->render('feed/show.html.twig', array(
            'feed' => $feed,
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Displays a form to edit an existing Feed entity.
     *
     * @Route("/{id}/edit", name="feed_edit")
     * @Method({"GET", "POST"})
     */
    public function editAction(Request $request, Feed $feed)
    {
        $originalFeedXmls = new ArrayCollection();

        $deleteForm = $this->createDeleteForm($feed);
        $editForm = $this->createForm('AppBundle\Form\FeedType', $feed);
        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            // print_r($feed->getFeedXmls());
            // die('working something');
            $em = $this->getDoctrine()->getManager();
            foreach ($originalFeedXmls as $feedXml) {
                if (false === $feed->getFeedXml()->contains($feedXml)) {
                    $feed->getFeedXmls()->removeElement($feedXml);

                    $em->persist($feedXml);
                }
            }

            $em->persist($feed);
            $em->flush();

            return $this->redirectToRoute('feed_edit', array('id' => $feed->getId()));
        }

        return $this->render('feed/edit.html.twig', array(
            'feed' => $feed,
            'edit_form' => $editForm->createView(),
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Deletes a Feed entity.
     *
     * @Route("/{id}", name="feed_delete")
     * @Method("DELETE")
     */
    public function deleteAction(Request $request, Feed $feed)
    {
        $form = $this->createDeleteForm($feed);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->remove($feed);
            $em->flush();
        }

        return $this->redirectToRoute('feed_index');
    }

    /**
     * Creates a form to delete a Feed entity.
     *
     * @param Feed $feed The Feed entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createDeleteForm(Feed $feed)
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('feed_delete', array('id' => $feed->getId())))
            ->setMethod('DELETE')
            ->getForm()
        ;
    }
}
