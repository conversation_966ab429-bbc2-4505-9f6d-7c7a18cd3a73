<?php

namespace AppBundle\Controller;

use AppBundle\Entity\FootballField;
use AppBundle\Form\FootballFieldType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;

/**
 * Football Field controller.
 *
 * @Route("/admin/football-field")
 */
class FootballFieldController extends Controller
{
    /**
     * Football Field Index
     *
     * @Route("/", name="football_field_index")
     * @Method("GET")
     */
	public function indexAction()
	{
        $em = $this->getDoctrine()->getManager();

        $footballFields = $em->getRepository('AppBundle:FootballField')->findAll();

        return $this->render('footballfield/index.html.twig', array(
            'footballFields' => $footballFields,
        ));
    }

    /**
     * Creates a new FootballField entity.
     *
     * @Route("/new", name="football_field_new")
     * @Method({"GET", "POST"})
     */
    public function newAction(Request $request)
    {
        $footballField = new FootballField();
        $form = $this->createForm(FootballFieldType::class, $footballField);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($footballField);
            $em->flush();

            return $this->redirectToRoute('football_field_show', array('id' => $footballField->getId()));
        }

        return $this->render('footballfield/new.html.twig', array(
            'footballField' => $footballField,
            'form' => $form->createView(),
        ));
    }

    /**
     * Finds and displays a footballField entity.
     *
     * @Route("/{id}", name="football_field_show")
     * @Method("GET")
     */
    public function showAction(FootballField $footballField)
    {
        $deleteForm = $this->createDeleteForm($footballField);

        return $this->render('footballfield/show.html.twig', array(
            'footballField' => $footballField,
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Displays a form to edit an existing FootballField entity.
     *
     * @Route("/{id}/edit", name="football_field_edit")
     * @Method({"GET", "POST"})
     */
    public function editAction(Request $request, FootballField $footballField)
    {
        $deleteForm = $this->createDeleteForm($footballField);
        $editForm = $this->createForm('AppBundle\Form\FootballFieldType', $footballField);
        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($footballField);
            $em->flush();

            return $this->redirectToRoute('football_field_edit', array('id' => $footballField->getId()));
        }

        return $this->render('footballfield/edit.html.twig', array(
            'footballField' => $footballField,
            'edit_form' => $editForm->createView(),
            'delete_form' => $deleteForm->createView(),
        ));
    }


    /**
     * Deletes a Football Field entity.
     *
     * @Route("/{id}", name="football_field_delete")
     * @Method("DELETE")
     */
    public function deleteAction(Request $request, FootballField $footballField)
    {
        $form = $this->createDeleteForm($footballField);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->remove($footballField);
            $em->flush();
        }

        return $this->redirectToRoute('football_field_index');
    }

    /**
     * Creates a form to delete a footballField entity.
     *
     * @param footballField $footballField The footballField entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createDeleteForm(FootballField $footballField)
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('football_field_delete', array('id' => $footballField->getId())))
            ->setMethod('DELETE')
            ->getForm()
        ;
    }
}
