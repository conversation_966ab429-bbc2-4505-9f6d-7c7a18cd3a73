<?php

namespace AppBundle\Controller\Api;

use Monolog\Logger;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\FOSRestController;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;
use Psr\Log\LoggerInterface;

use AppBundle\Entity\FtTeamPlayer;
use AppBundle\Entity\FtFixtureTeamData;

/**
 * FtLeagueStandings REST API controller.
 *
 * @Route("/api/v2/startabs")
 */
class RestFtFixtureTeamDataController extends FOSRestController
{

	/**
	 * Get team rosters for hTeamId and aTeamId
	 *
	 * @Method("GET")
	 * @Rest\Get("/{fixtureId}", name="teamrosters_get")
	 */
  	public function getTeamRostersAction(Request $request, $fixtureId) {
        $em 					= $this->getDoctrine()->getManager();
		$result 				= array();
		$result['formation'] 	= array();
		$result['hRoster'] 		= array();
		$result['aRoster'] 		= array();
		$result['hLineup'] 		= array();
		$result['aLineup'] 		= array();
		$result['hAbsences'] 	= array();
		$result['aAbsences'] 	= array();
		$result['hFormation']	= array();
		$result['aFormation']	= array();
		$teamFormations = $em->getRepository('AppBundle:FtTeamFormation')->getAllTeamFormations($this->get('kernel')->getProjectDir(), $this->container->get('kernel')->getEnvironment());
		if ($teamFormations) {
			$result['formation'] = $teamFormations;
		}

		$fixture = $em->getRepository('AppBundle:FtFixture')->findOneBy(array('fId' => $fixtureId));

		if ($fixture) {
            if ($fixture->getHTeam()) {
				$hTeamPlayers = $em->getRepository('AppBundle:FtTeamPlayer')->findBy(array('teamId' => $fixture->getHTeam()));
				if ($hTeamPlayers) $result['hRoster'] = $hTeamPlayers;
            }

			if ($fixture->getATeam()) {
				$aTeamPlayers = $em->getRepository('AppBundle:FtTeamPlayer')->findBy(array('teamId' => $fixture->getATeam()));
				if ($aTeamPlayers) $result['aRoster'] = $aTeamPlayers;
            }

			$fixtureData = $em->getRepository('AppBundle:FtFixtureTeamData')->findOneBy(array('fixtureId' => $fixtureId));
            if ($fixtureData) {
				// $this->getLineupData($fixtureId, $fixture->getHTeam(), $fixtureData->getHLineup(), $fixture->getATeam(), $fixtureData->getALineup());
				$result['hLineup'] = $this->getLineupData($fixtureData->getHLineup()) ?: array();
				$result['aLineup'] = $this->getLineupData($fixtureData->getALineup()) ?: array();

				$result['hAbsences'] = $this->getAbsencesData($fixtureData->getHAbsences()) ?: array();
				$result['aAbsences'] = $this->getAbsencesData($fixtureData->getAAbsences()) ?: array();

				$result['hFormation'] = (string) $fixtureData->getHFormationId();
				$result['aFormation'] = (string) $fixtureData->getAFormationId();
            } // fixtureData
			// no current data exist, get data for each team from earlier fixtures
			else {
				$hPreviousFixtureData = $em->getRepository('AppBundle:FtFixtureTeamData')->getPreviousFixtureTeamData($fixture->getHTeam(), $fixtureId, true);
                if ($hPreviousFixtureData) {
					if ($fixture->getHTeam() == $hPreviousFixtureData[0]->getHId()) {
						$result['hLineup'] = $this->getLineupData($hPreviousFixtureData[0]->getHLineup()) ?: array();
						$result['hAbsences'] = $this->getAbsencesData($hPreviousFixtureData[0]->getHAbsences()) ?: array();
						$result['hFormation'] = (string) $hPreviousFixtureData[0]->getHFormationId();
					}
					else {
						$result['hLineup'] = $this->getLineupData($hPreviousFixtureData[0]->getALineup()) ?: array();
						$result['hAbsences'] = $this->getAbsencesData($hPreviousFixtureData[0]->getAAbsences()) ?: array();
						$result['hFormation'] = (string) $hPreviousFixtureData[0]->getAFormationId();
					}
                }

				$aPreviousFixtureData = $em->getRepository('AppBundle:FtFixtureTeamData')->getPreviousFixtureTeamData($fixture->getATeam(), $fixtureId, false);
                if ($aPreviousFixtureData) {
					if ($fixture->getATeam() == $aPreviousFixtureData[0]->getAId()) {
						$result['aLineup'] = $this->getLineupData($aPreviousFixtureData[0]->getALineup()) ?: array();
						$result['aAbsences'] = $this->getAbsencesData($aPreviousFixtureData[0]->getAAbsences()) ?: array();
						$result['aFormation'] = (string) $aPreviousFixtureData[0]->getAFormationId();
					}
					else {
						$result['aLineup'] = $this->getLineupData($aPreviousFixtureData[0]->getHLineup()) ?: array();
						$result['aAbsences'] = $this->getAbsencesData($aPreviousFixtureData[0]->getHAbsences()) ?: array();
						$result['aFormation'] = (string) $aPreviousFixtureData[0]->getHFormationId();
					}
				}
			}
		} // fixture

		$em->clear();
		$view = $this->view($result, Response::HTTP_OK);
		return $view;
	}

	/**
   * Set team rosters for fixture_id
   *
   * @Method("POST")
   * @Rest\Get("/{fixtureId}")
   */
	public function setTeamStartAbsAction(Request $request, $fixtureId) {
		// hLineup, aLineup, hAbsences, aAbsences
		$em 		= $this->getDoctrine()->getManager();
		$result 	= array('msg' => 'saved');
		$hAbsences 	= '';
		$aAbsences 	= '';
		$hLineup 	= '';
		$aLineup 	= '';
		$hFormation = null;
		$aFormation = null;
		$teamFormationsDir = $this->get('kernel')->getProjectDir() . '/var/cache/' . $this->container->get('kernel')->getEnvironment() . '/team_formations';
		$cache      = new FilesystemAdapter('', 0, $teamFormationsDir);
		$helper     = $this->container->get('wbs.globalhelper.service');

		try {

			$reqData = $request->request->get('data');
			if ($reqData) {
				// get cached formations
				$teamFormationCache = $cache->getItem('team.formations');
				$formationsCache = $teamFormationCache->isHit() ? $teamFormationCache->get() : $em->getRepository('AppBundle:FtTeamFormation')->getAllTeamFormations($this->get('kernel')->getProjectDir(), $this->container->get('kernel')->getEnvironment());
				if ($reqData['hAbsences']) {
					// manipulate hAbsences
					$hAbsences = $this->absencesEncode($reqData['hAbsences']);
				}

				if ($reqData['aAbsences']) {
					$aAbsences = $this->absencesEncode($reqData['aAbsences']);
					// manipulate aAbsences
				}

				if ($reqData['hLineup'] && $reqData['hFormation']) {
					$hFormation = (int) $reqData['hFormation'];

					$hLineup = $this->lineupsEncode($reqData['hLineup'], $reqData['hFormation'], $formationsCache);
					// manipulate hLineup and hFormation
				}

				if ($reqData['aLineup'] && $reqData['aFormation']) {
					$aFormation = (int) $reqData['aFormation'];

					$aLineup = $this->lineupsEncode($reqData['aLineup'], $reqData['aFormation'], $formationsCache);
					// manipulate hLineup and hFormation
				}

				$startAbsDomains = $em->getRepository('AppBundle:Domain')->findBy(array('isStartAbs' => true));

				$domains = array();
				// $domainConnections  = array();
				
				if ($startAbsDomains) {
					foreach ($startAbsDomains as $domain) {
						$domainConnection = null;
						$language = $em->getRepository('AppBundle:Language')->findOneById($domain->getLanguage()->getId());

						$domainConnection = $helper->getDomainConnection($domain);
						$previewId = $em->getRepository('AppBundle:DomainFtFixture')->findOneBy(array('fixtureId' => $fixtureId, 'domainId' => $domain));
						if ($previewId) {
							try {
								$locale = $language->getLocale();
								$this->updatePreview(
									$domainConnection, 
									$previewId->getPreviewId(), 
									isset($hAbsences['names_'.$locale]) ? str_replace( "'", '', $hAbsences['names_'.$locale] ) : null,
									isset($aAbsences['names_'.$locale]) ?  str_replace( "'", '', $aAbsences['names_'.$locale] ) : null,
									isset($hLineup['names_'.$locale]) ?  str_replace( "'", '', $hLineup['names_'.$locale] ) : null,
									isset($aLineup['names_'.$locale]) ?  str_replace( "'", '', $aLineup['names_'.$locale] ) : null
								);
							}
							catch (\Exception $e) {

							}
						}
					}
				}

				$fixtureData = $em->getRepository('AppBundle:FtFixtureTeamData')->findOneBy(array('fixtureId' => $fixtureId));

				if ($fixtureData) {
					// edit
					$fixtureData->setHFormationId($hFormation ?: null);
					$fixtureData->setAFormationId($aFormation ?: null);
					$fixtureData->setHLineup(isset($hLineup['ids']) ? $hLineup['ids'] : null);
					$fixtureData->setALineup(isset($aLineup['ids']) ? $aLineup['ids'] : null);
					$fixtureData->setHAbsences(isset($hAbsences['ids']) ? $hAbsences['ids'] : null);
					$fixtureData->setAAbsences(isset($aAbsences['ids']) ? $aAbsences['ids'] : null);
					$em->persist($fixtureData);
					$em->flush();
				}
				else {
					// $tmpFixture = $em->getRepository('AppBundle:FtFixture')->findOneById($fixtureId);
					$tmpFixture = $em->getRepository('AppBundle:FtFixture')->findOneBy(array('fId' => $fixtureId));
					// insert
					$fixtureData = new FtFixtureTeamData();
					$fixtureData->setFixtureId($fixtureId);
					$fixtureData->setHId($tmpFixture->getHTeam());
					$fixtureData->setAId($tmpFixture->getATeam());
					$fixtureData->setHFormationId($hFormation);
					$fixtureData->setAFormationId($aFormation);
					$fixtureData->setHLineup(isset($hLineup['ids']) ? $hLineup['ids'] : '');
					$fixtureData->setALineup(isset($aLineup['ids']) ? $aLineup['ids'] : '');
					$fixtureData->setHAbsences(isset($hAbsences['ids']) ? $hAbsences['ids'] : null);
					$fixtureData->setAAbsences(isset($aAbsences['ids']) ? $aAbsences['ids'] : null);
					$em->persist($fixtureData);
					$em->flush();
				}

			} // reqData
		}
		catch (\Exception $e) {
			$em->clear();
			$result = 'error when saving '.$e->getMessage();
			$view = $this->view($result, Response::HTTP_NOT_FOUND);
			return $view;			
		}

		// on successful save return getTeamRostersAction
		$em->clear();
		$view = $this->routeRedirectView('teamrosters_get', array('fixtureId' => $fixtureId), 301);
		return $view;
		// $view = $this->view($result, Response::HTTP_OK);
		// return $view;
	}

	private function getLineupData($lineup) {
		if (!$lineup) return null;

		$em = $this->getDoctrine()->getManager();
		$result = array();

		$fixedLineup = rtrim(str_replace('#', ',', $lineup), ',');
		$fixedLineup = str_replace(')', '', str_replace('(', ',', $fixedLineup));

		$lineupPlayers = $em->getRepository('AppBundle:FtFixtureTeamData')->getLineups($fixedLineup, $lineup);

		if ($lineupPlayers) {
			// $exploded = explode(',', rtrim(str_replace('#\n', ',', $lineup), ','));
			$exploded = explode(',', str_replace('#', ',', rtrim(str_replace('#\n', ',', $lineup), ',')));

			array_pop($exploded);

			$myValue = '';
			foreach ($exploded as $key => $value) {
				$myValue = $value;
				if ($value) {
					preg_match('/\((.*?)\)/', $value, $match);
					
					// if player has children, remove them
					if ($match) {
						$myValue = str_replace($match[0], '', $value);
					}

					// search 
					$keyInPlayerNames = array_search($myValue, array_column($lineupPlayers, 'id'));
					// add main player
					$result[$key] = array('id' => $lineupPlayers[$keyInPlayerNames]['id'], 'title' => $lineupPlayers[$keyInPlayerNames]['title'], 'name_el' => $lineupPlayers[$keyInPlayerNames]['name_el'], 'name_en' => $lineupPlayers[$keyInPlayerNames]['name_en']);

					// child, add to parent
					if ($match) {
						$parentKey = array_search($match[1], array_column($lineupPlayers, 'id'));
						$result[$key]['expanded'] = true;
						$result[$key]['children'][] = array('id' => $lineupPlayers[$parentKey]['id'], 'title' => $lineupPlayers[$parentKey]['title'], 'name_el' => $lineupPlayers[$parentKey]['name_el'], 'name_en' => $lineupPlayers[$parentKey]['name_en']);
					}
				}
			} // foreach $exploded
		}

		$em->clear();

		return $result;
	}

	private function getAbsencesData($absences) {
		$em 					= $this->getDoctrine()->getManager();
		$result 			= array();
		$singlePlayer	= array();

		$absences = explode('#', rtrim($absences, '#'));

		foreach ($absences as $a) {
			$singlePlayer = explode(',', $a);
			if ($singlePlayer) {
				if (isset($singlePlayer[1]) && $singlePlayer[1] <> '') {
					$result[] = array(
						'reason' => isset($singlePlayer[0]) ? $singlePlayer[0] : '',
						'playerId' => isset($singlePlayer[1]) ? (int) $singlePlayer[1] : '', 
						'position' => isset($singlePlayer[2]) ? $singlePlayer[2] : '',
						'played' => isset($singlePlayer[3]) ? $singlePlayer[3] : '', 
						'goals' => isset($singlePlayer[4]) ? $singlePlayer[4] : ''
					);
				}
			}
		}

		$em->clear();
		return $result;
	}

	private function absencesEncode($absences) {
		$translator = $this->get('translator');

		$result = array();
		$result['ids'] = '';
		$result['names_el'] = '';
		$result['names_en'] = '';

		$absencesOrdering = ['C', 'D', 'R',  'G', 'L', 'N', 'Q', 'F'];
		usort($absences, function ($item1, $item2) use ($absencesOrdering) {
			if (!isset($item1) || !isset($item2)) return 0;

			  if (array_search($item1['reason'],array_values($absencesOrdering)) < array_search($item2['reason'],array_values($absencesOrdering))) {
					  return -1;
			  } elseif (array_search($item1['reason'],array_values($absencesOrdering)) > array_search($item2['reason'],array_values($absencesOrdering))) {
					  return 1;
			  } else {
					  return 0;
			  }
		});


		foreach ($absences as $a) {
			$result['ids'] .= $a['reason'].','.$a['playerId'].','.$a['position'].','.$a['played'].','.$a['goals'].'#';
			$result['names_el'] .= $a['reason'].','.$a['name_el'].','.$translator->trans($a['position'], array(), 'messages', 'el').','.$a['played'].','.$a['goals'].'#\n';
			$result['names_en'] .= $a['reason'].','.$a['name_en'].','.$translator->trans($a['position'], array(), 'messages', 'en').','.$a['played'].','.$a['goals'].'#\n';
		}

		return $result;
	}

	private function lineupsEncode($lineup, $formation, $formations) {
		$result = array();
		$result['ids'] = '';
		$result['names_el'] = '';
		$result['names_en'] = '';

		$formationKey = array_search($formation, array_column($formations, 'id'));
		$formationName = $formations[$formationKey]['name'];
		$formationNum = explode('-', $formationName);

		$myCounter = 0;

		// add goalkeeper (outside of the formation loop)
		$result['ids'] .= $lineup[$myCounter]['id'] . (isset($lineup[$myCounter]['children']) ? '('.$lineup[$myCounter]['children'][0]['id'].')' : '') . '#';
		$result['names_el'] .= $lineup[$myCounter]['name_el'] . (isset($lineup[$myCounter]['children']) ? ' ('.$lineup[$myCounter]['children'][0]['name_el'].')' : '') . '#\n';
		$result['names_en'] .= $lineup[$myCounter]['name_en'] . (isset($lineup[$myCounter]['children']) ? ' ('.$lineup[$myCounter]['children'][0]['name_en'].')' : '') . '#\n';

		// add the rest of the players via the formation loop)
		for ($j = 0; $j <= count($formationNum) - 1 ; $j++) {
			for ($k = 1; $k <= $formationNum[$j]; $k++) {

				// if last player in the formation row, break line
				if ($k === (int) $formationNum[$j]) {
					$delimiter = '#';
					$domainDelimiter = '#\n';
				}
				else {
					$delimiter = ',';
					$domainDelimiter = ',';
				}

				$myCounter++;
				$result['ids'] .= $lineup[$myCounter]['id'] . (isset($lineup[$myCounter]['children']) ? '('.$lineup[$myCounter]['children'][0]['id'].')' : '') . $delimiter;
				$result['names_el'] .= $lineup[$myCounter]['name_el'] . (isset($lineup[$myCounter]['children']) ? ' ('.$lineup[$myCounter]['children'][0]['name_el'].')' : '') . $domainDelimiter;
				$result['names_en'] .= $lineup[$myCounter]['name_en'] . (isset($lineup[$myCounter]['children']) ? ' ('.$lineup[$myCounter]['children'][0]['name_en'].')' : '') . $domainDelimiter;
			}
		}

		return $result;
	}

	private function updatePreview($conn, $previewId, $hAbsences, $aAbsences, $hLineup, $aLineup) {
		$result = false;

		try {
			$conn->beginTransaction();

			$sqlInsert = "
			UPDATE bet_wbs_previews SET 
			h_team_lineup = " . ($hLineup ? "'".$hLineup."'" : 'null' ). ",
			h_team_absenses = " . ($hAbsences ? "'".$hAbsences."'" : 'null' ). ",
			a_team_lineup = " . ($aLineup ? "'".$aLineup."'" : 'null' ). ",
			a_team_absenses = " . ($aAbsences ? "'".$aAbsences."'" : 'null' ). "
			WHERE id = $previewId
			";

      $stmt = $conn->prepare($sqlInsert);
      $stmt->execute();

			$conn->commit();
		}
		catch (\Exception $e) {
			echo '<pre>';
			var_dump($e->getMessage());
			echo '</pre>';

      		$conn->rollback();
		}

		return $result;
	}
}