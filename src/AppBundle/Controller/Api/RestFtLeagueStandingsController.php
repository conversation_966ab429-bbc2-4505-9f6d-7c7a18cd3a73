<?php

namespace AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\FOSRestController;
use Symfony\Component\HttpFoundation\JsonResponse;

use Symfony\Component\Translation\Translator;
use Symfony\Component\Translation\MessageSelector;
use Symfony\Component\Translation\Loader\YamlFileLoader;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;

//  ("/api/v2/standings/{sportId}/{season}/{fetchStats}/{lan}", defaults={"sportId" = 304, "season" = null, "fetchStats" = "true", "lan" = "gr"})
// * @Route("/api/v2/standings")

/**
 * FtLeagueStandings REST API controller.
 * @Route("/")
 */
class RestFtLeagueStandingsController extends FOSRestController
{

  /**
   * @Method("GET")
   * @Rest\Get("/api/v2/standings/{sportId}/{season}/{fetchStats}/{domainId}/{teamId1}/{teamId2}", defaults={"sportId" = 304, "season" = null, "fetchStats" = 0, "domainId" = 2, "teamId1" = "null", "teamId2" = "null"})
   */
  public function indexAction(Request $request, $sportId, $season, $fetchStats, $domainId, $teamId1 = null, $teamId2 = null) {
    $em     = $this->getDoctrine()->getManager();
    $helper = $this->container->get("wbs.globalhelper.service");

    $domain = $em->getRepository('AppBundle:Domain')->findOneById($domainId);
    if (!$domain) return 'domain does not exist';

    $locale = $domain->getLanguage()->getLocale();
    $languageCode   = strtolower($helper->getLanguageByLocale($locale));
    $localeFallback = $this->container->getParameter('locale');

    if ( $season === 'null' ) {
      $seasonSport = $em->getRepository('AppBundle:FtLeagueSeasonSport')->getLatestSportSeason($sportId);
      if ($seasonSport) {
        $season = $seasonSport['season'];
      }
    }

    // $sportId is always for the main competition, NOT for the additionals
    // $sportId can be a-mexico, a-kuprou, champions-league, superleague but NOT a-mexico-clausura
    $leagueStandings = $em->getRepository('AppBundle:FtLeagueStandings')->getLeagueStandings($sportId, $season, $languageCode, $locale, $localeFallback);

    $data = $this->fixStandings($leagueStandings, $teamId1, $teamId2);

    if (!(int) $teamId1 || !(int) $teamId2) {
      // fetch scorers
      $leagueScorers = $em->getRepository('AppBundle:FtLeagueSeasonScorer')->getLeagueScorers($sportId, $season, $languageCode, $localeFallback);

      if ($leagueScorers) {
        foreach ($leagueScorers as $single) {
          $data['scorers'][$single['leagueName']][] = array(
            'playerName'  => $single['playerName'],
            'teamId'      => $single['teamId'],
            'nameEn'      => $single['nameEn'],
            'goals'       => $single['goals'],
            'penalties'   => $single['penalties'],
            'scorerFirst' => $single['scorerFirst']
          );
        }
      }
    }

    // Βαθμολογίες Ημιχρόνων (Α + Β)
    // $translator = new Translator($locale, new MessageSelector());
    // $translator->addLoader('yaml', new YamlFileLoader());
    // $transFile = $this->get('kernel')->getRootDir().'/Resources/translations/messages.'.$locale.'.yml';
    // $translator->addResource('yaml', $transFile, $locale);

    $translator = $this->get('translator');
    $translator->setLocale($locale);

    $translations = array($translator->trans('1st Half Standings'), $translator->trans('2nd Half Standings'));

    $leagueStandingsHalves = $em->getRepository('AppBundle:FtLeagueStandings')->getLeagueStandingsHalves($sportId, $season, $languageCode, $locale, $localeFallback, $translations);

    $dataHalves = $this->fixStandings($leagueStandingsHalves, null, null);
    if ($dataHalves && isset($dataHalves['standings'])) $data['halves'] = $dataHalves['standings'];

    $translations = array($translator->trans('Home'), $translator->trans('Away'), $translator->trans('TEAM'));
    $dataHalfFinal = $em->getRepository('AppBundle:FtLeagueStandings')->getTeam1x2HalfFinal($sportId, $season, $languageCode, $localeFallback, $translations);
    if ($dataHalfFinal) {
      // home
      $data['home11'] = $dataHalfFinal['home11'];
      $data['home1X'] = $dataHalfFinal['home1X'];
      $data['home12'] = $dataHalfFinal['home12'];
      $data['homeX1'] = $dataHalfFinal['homeX1'];
      $data['homeXX'] = $dataHalfFinal['homeXX'];
      $data['homeX2'] = $dataHalfFinal['homeX2'];
      $data['home21'] = $dataHalfFinal['home21'];
      $data['home2X'] = $dataHalfFinal['home2X'];
      $data['home22'] = $dataHalfFinal['home22'];
      // away
      $data['away11'] = $dataHalfFinal['away11'];
      $data['away1X'] = $dataHalfFinal['away1X'];
      $data['away12'] = $dataHalfFinal['away12'];
      $data['awayX1'] = $dataHalfFinal['awayX1'];
      $data['awayXX'] = $dataHalfFinal['awayXX'];
      $data['awayX2'] = $dataHalfFinal['awayX2'];
      $data['away21'] = $dataHalfFinal['away21'];
      $data['away2X'] = $dataHalfFinal['away2X'];
      $data['away22'] = $dataHalfFinal['away22'];
    }

    $translations = array($translator->trans('Goals'), $translator->trans('TEAM'));
    $dataGoals = $em->getRepository('AppBundle:FtLeagueStandings')->getTeamGoals($sportId, $season, $languageCode, $localeFallback, $translations);
    if ($dataGoals) {
      $data['goals01']  = $dataGoals['goals01'];
      $data['goals02']  = $dataGoals['goals02'];
      $data['goals23']  = $dataGoals['goals23'];
      $data['goals3']   = $dataGoals['goals3'];
      $data['goals46']  = $dataGoals['goals46'];
      $data['goals7']   = $dataGoals['goals7'];
    }

    // fetch stats
    if (1 === (int) $fetchStats) {
      // 'UEFA', 'CHL', 'WCQEU'
      $leagueCups = array(310, 309, 317);
      $finalScorers = array();

      if (in_array($sportId, $leagueCups)) {
        $teamForm        = array();
        $teamTotalStats  = array();
        $previousNext    = array();
        $mostWins        = array();
        $mostDraws       = array();
        $mostLoses       = array();
        $mostWinsStreak  = array();
        $mostDrawsStreak = array();
        $mostLossStreak  = array();
        $noWinStreak     = array();
        $noLossStreak    = array();
        $mostOver25      = array();
        $mostUnder25     = array();
        $mostGoalGoal    = array();
        $mostNoGoal      = array();
        $yellowCards     = array();
        $redCards        = array();
        $leagueStats     = array();
      }
      else {
        $translations = array($translator->trans('Team Form'), $translator->trans('#'), $translator->trans('TEAM'), $translator->trans('W-D-L'));
        $teamForm         = $em->getRepository('AppBundle:TeamStats')->getTeamForm($sportId, $season, $languageCode, $localeFallback, $translations);
        
        $translations = array($translator->trans('Stats Analytics'), $translator->trans('#'), $translator->trans('TEAM'), $translator->trans('Pts'), $translator->trans('M'), $translator->trans('W'), $translator->trans('D'), $translator->trans('L'));
        $teamTotalStats   = $em->getRepository('AppBundle:TeamStats')->getTeamTotalStats($sportId, $season, $languageCode, $localeFallback, $translations);

        $translations = array($translator->trans('Previous Matches'), $translator->trans('DATE'), $translator->trans('MATCH'), $translator->trans('RESULT'), $translator->trans('Next Matches'));
        $previousNext     = $em->getRepository('AppBundle:FtFixture')->getPreviousNextMatches($sportId, $season, $languageCode, $localeFallback, $translations);

        $translations = array($translator->trans('Most Wins'), $translator->trans('#'), $translator->trans('TEAM'), $translator->trans('W'));
        $mostWins         = $em->getRepository('AppBundle:TeamStats')->getMostWins($sportId, $season, $languageCode, $localeFallback, $translations);

        $translations = array($translator->trans('Most Draws'), $translator->trans('#'), $translator->trans('TEAM'), $translator->trans('D'));
        $mostDraws        = $em->getRepository('AppBundle:TeamStats')->getMostDraws($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Most Loses'), $translator->trans('#'), $translator->trans('TEAM'), $translator->trans('L'));
        $mostLoses        = $em->getRepository('AppBundle:TeamStats')->getMostLoses($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Win Streak'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostWinsStreak   = $em->getRepository('AppBundle:TeamStats')->getWinStreak($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Draw Streak'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostDrawsStreak  = $em->getRepository('AppBundle:TeamStats')->getDrawStreak($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Lose Streak'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostLossStreak   = $em->getRepository('AppBundle:TeamStats')->getLossStreak($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('No Win Streak'), $translator->trans('#'), $translator->trans('TEAM'));
        $noWinStreak      = $em->getRepository('AppBundle:TeamStats')->getNoWinStreak($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('No Loss Streak'), $translator->trans('#'), $translator->trans('TEAM'));
        $noLossStreak     = $em->getRepository('AppBundle:TeamStats')->getNoLossStreak($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Most Over'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostOver25       = $em->getRepository('AppBundle:TeamStats')->getOver25($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Most Under'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostUnder25      = $em->getRepository('AppBundle:TeamStats')->getUnder25($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Most Goal Goal'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostGoalGoal     = $em->getRepository('AppBundle:TeamStats')->getGoalGoal($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Most No Goal'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostNoGoal       = $em->getRepository('AppBundle:TeamStats')->getNoGoal($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Yellow Cards'), $translator->trans('#'), $translator->trans('TEAM'));
        $yellowCards      = $em->getRepository('AppBundle:TeamStats')->getYellowCards($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Red Cards'), $translator->trans('#'), $translator->trans('TEAM'));
        $redCards         = $em->getRepository('AppBundle:TeamStats')->getRedCards($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array(
          $translator->trans('Competition Statistics'), 
          $translator->trans('Statistics'), 
          $translator->trans('Total Matches'), 
          $translator->trans('Home Wins'), 
          $translator->trans('Draws'), 
          $translator->trans('Away Wins'), 
          $translator->trans('Total Goals'), 
          $translator->trans('Total Goals') . ' ' . $translator->trans('Home'), 
          $translator->trans('Total Goals') . ' ' . $translator->trans('Away'), 
          $translator->trans('M.V. Goals Home'), 
          $translator->trans('M.V. Goals Away'));
        $leagueStats      = $em->getRepository('AppBundle:TeamStats')->getLeagueStats($sportId, $season, $languageCode, $localeFallback, $translations);
      }

      $data['teamForm']         = $teamForm;
      $data['teamTotalStats']   = $teamTotalStats;
      $data['previousMatches']  = isset($previousNext[0]) ? $previousNext[0] : null;
      $data['nextMatches']      = isset($previousNext[1]) ? $previousNext[1] : null;
      $data['mostWins']         = $mostWins;
      $data['mostDraws']        = $mostDraws;
      $data['mostLoses']        = $mostLoses;
      $data['mostWinsStreak']   = $mostWinsStreak;
      $data['mostDrawsStreak']  = $mostDrawsStreak;
      $data['mostLossStreak']   = $mostLossStreak;
      $data['noWinStreak']      = $noWinStreak;
      $data['noLossStreak']     = $noLossStreak;
      $data['mostOver25']       = $mostOver25;
      $data['mostUnder25']      = $mostUnder25;
      $data['mostGoalGoal']     = $mostGoalGoal;
      $data['mostNoGoal']       = $mostNoGoal;
      $data['yellowCards']      = $yellowCards;
      $data['redCards']         = $redCards;
      $data['leagueStats']      = $leagueStats;
    }

    $result = $data;
    $em->clear();

    $view = $this->view([$result], Response::HTTP_OK);
    return $view;
    // return $this->handleView($view);
  }

  private function fixStandings($data, $teamId1, $teamId2) {
    $result = null;
    $teamInfo = null;
    $homeAwayFilter = ((int) $teamId1 && (int) $teamId2);

    if ($data) {
      foreach ($data['standings'] as $single) {
        if ($homeAwayFilter) {
          if (($teamId1 != $single['team_id']) && ($teamId2 != $single['team_id'])) continue;
        }

        if (isset($data['info'])) {
          $teamInfo = $this->checkRankAndInfo($single['sport_id'], $single['rank'], $data['info']);
        }
        
        if ($teamInfo) {
          $result['standings'][$single['leagueName']][] = array(
            'teamId'      => $single['team_id'],
            'teamName'    => $single['teamName'],
            'rank'        => $single['rank'],
            'overall_p'   => $single['overall_p'],
            'overall_pts' => $single['overall_pts'],
            'overall_w'   => $single['overall_w'],
            'overall_d'   => $single['overall_d'],
            'overall_l'   => $single['overall_l'],
            'overall_gf'  => $single['overall_gf'],
            'overall_ga'  => $single['overall_ga'],
            'home_p'      => $single['home_p'],
            'home_w'      => $single['home_w'],
            'home_d'      => $single['home_d'],
            'home_l'      => $single['home_l'],
            'home_gf'     => $single['home_gf'],
            'home_ga'     => $single['home_ga'],
            'away_p'      => $single['away_p'],
            'away_w'      => $single['away_w'],
            'away_d'      => $single['away_d'],
            'away_l'      => $single['away_l'],
            'away_gf'     => $single['away_gf'],
            'away_ga'     => $single['away_ga'],
            'penalty_pts' => $single['penalty_pts'],
            'penalty_hgf' => $single['penalty_hgf'],
            'penalty_hga' => $single['penalty_hga'],
            'penalty_agf' => $single['penalty_agf'],
            'penalty_aga' => $single['penalty_aga'],
            'info'        => $teamInfo
          );
        }
        else {
          $result['standings'][$single['leagueName']][] = array(
            'teamId'      => $single['team_id'],
            'teamName'    => $single['teamName'],
            'rank'        => $single['rank'],
            'overall_p'   => $single['overall_p'],
            'overall_pts' => $single['overall_pts'],
            'overall_w'   => $single['overall_w'],
            'overall_d'   => $single['overall_d'],
            'overall_l'   => $single['overall_l'],
            'overall_gf'  => $single['overall_gf'],
            'overall_ga'  => $single['overall_ga'],
            'home_p'      => isset($single['home_p']) ? $single['home_p'] : 0,
            'home_w'      => $single['home_w'],
            'home_d'      => $single['home_d'],
            'home_l'      => $single['home_l'],
            'home_gf'     => $single['home_gf'],
            'home_ga'     => $single['home_ga'],
            'away_p'      => isset($single['away_p']) ? $single['away_p'] : 0,
            'away_w'      => $single['away_w'],
            'away_d'      => $single['away_d'],
            'away_l'      => $single['away_l'],
            'away_gf'     => $single['away_gf'],
            'away_ga'     => $single['away_ga'],
            'penalty_pts' => isset($single['penalty_pts']) ? $single['penalty_pts'] : 0,
            'penalty_hgf' => isset($single['penalty_hgf']) ? $single['penalty_hgf'] : 0,
            'penalty_hga' => isset($single['penalty_hga']) ? $single['penalty_hga'] : 0,
            'penalty_agf' => isset($single['penalty_agf']) ? $single['penalty_agf'] : 0,
            'penalty_aga' => isset($single['penalty_aga']) ? $single['penalty_aga'] : 0
          );
        }
      }
    }

    return $result;
  }

  private function checkRankAndInfo($sportId, $rank, $info) {
    $result = null;

    if ($info) {
      foreach ($info as $single) {

        if ($single['sport_id'] != $sportId) continue;

        $values = explode(',', str_replace(' ', '', $single['value']));
        if (in_array($rank, $values)) {
          $result = array('id' => $single['option_id'], 'value' => $single['optionName']);
        }
      }
    }

    return $result;
  }

  /**
   * @Method("GET")
   * @Rest\Get("/api/v4/standings/{sportId}/{season}/{fetchStats}/{locale}/{teamId1}/{teamId2}", defaults={"sportId" = 304, "season" = null, "fetchStats" = 0, "locale" = "el", "teamId1" = "null", "teamId2" = "null"})
   */
  public function getStandingsAction(Request $request, $sportId, $season, $fetchStats, $locale, $teamId1 = null, $teamId2 = null) {
    // $em     = $this->getDoctrine()->getManager();
    $helper     = $this->container->get("wbs.globalhelper.service");
    $translator = $this->get('translator');
    $standingsCacheDir = $this->get('kernel')->getProjectDir() . '/var/cache/' . $this->container->get('kernel')->getEnvironment() . '/standings_cache';
    $cache      = new FilesystemAdapter('', 0, $standingsCacheDir);

    $cacheKey = "standings-cache-$sportId-$locale";
    //echo $cacheKey;
    $standingsCache = $cache->getItem($cacheKey);

    if (!$standingsCache->isHit()) {
      $myCache = $helper->cacheStandings($translator, $sportId, $locale, $season, $fetchStats, null, null);
      $standingsCache->set($myCache);
      $standingsCache->expiresAfter(60*60*24*1); // one day expiration
      $cache->save($standingsCache);
      // recalculate data
      // $data[] = 'calculating, no cache found';
    }

    // $data[] = 'nothing to show yet';
    $result = $standingsCache->get();
    // $em->clear();

    $response = new JsonResponse();
    $response->setData([$result]);

    return $response;
  }

  /**
   * @Method("GET")
   * @Rest\Get("/api/v2/available-competitions/{locale}/{domainId}", defaults={"locale" = "el"})
   */
  public function getAvailableCompetitionsAction(Request $request, $locale, $domainId) {
    $em = $this->getDoctrine()->getManager();

    $data = $em->getRepository('AppBundle:DomainSport')->getAvailableCompetitionsWithStandings($locale, $domainId);
    $em->close();

    return $data;
  }

}
