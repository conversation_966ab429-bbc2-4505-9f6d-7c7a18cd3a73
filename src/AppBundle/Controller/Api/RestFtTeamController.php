<?php

namespace AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\FOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;

/**
 * FtTeams REST API controller.
 *
 * @Route("/api/v2")
 */
class RestFtTeamController extends FOSRestController
{
  /**
   * Get team for mundial
   *
   * @Method("GET")
   * @Rest\Get("/team-mundial/{teamId}")
   */
	public function getFtTeamMundialAction(Request $request, $teamId) {
		$helper = $this->container->get("wbs.globalhelper.service");

		$teamSports = array();
		$teamSports[122] = 713;
		$teamSports[3555] = 713;
		$teamSports[3909] = 713;
		$teamSports[4429] = 713;

		$teamSports[124] = 714;
		$teamSports[3467] = 714;
		$teamSports[3470] = 714;
		$teamSports[3637] = 714;

		$teamSports[3781] = 715;
		$teamSports[4079] = 715;
		$teamSports[4420] = 715;
		$teamSports[4439] = 715;

		$teamSports[3536] = 716;
		$teamSports[3541] = 716;
		$teamSports[3972] = 716;
		$teamSports[3974] = 716;

		$teamSports[3508] = 717;
		$teamSports[3977] = 717;
		$teamSports[4083] = 717;
		$teamSports[4124] = 717;

		$teamSports[3332] = 718;
		$teamSports[3718] = 718;
		$teamSports[3910] = 718;
		$teamSports[4208] = 718;

		$teamSports[3973] = 719;
		$teamSports[4032] = 719;
		$teamSports[4347] = 719;
		$teamSports[4410] = 719;

		$teamSports[3270] = 720;
		$teamSports[3309] = 720;
		$teamSports[3400] = 720;
		$teamSports[3553] = 720;

		$em = $this->getDoctrine()->getManager();
		$result = array();

		if ($teamId) {
			$teamData = $em->getRepository('AppBundle:FtTeam')->getTeamMundialData($helper, $teamId);
			if ($teamData) $result['teamData'] = $teamData;

			$teamStandings = $em->getRepository('AppBundle:FtLeagueStandings')->getLeagueStandings($teamSports[$teamId], '2021', 'gr', 'el', 'en');
			if ($teamStandings && isset($teamStandings['standings'])) $result['standings'] = $teamStandings['standings'];
		}

		$em->clear();
		$view = $this->view($result, Response::HTTP_OK);
		return $view;	
	}

	/**
	 * Get team extra data
	 *
	 * @Method("GET")
	 * @Rest\Get("/team-extra-data/{teamId}/{templateId}")
	 */
	public function getFtTeamExtradataAction(Request $request, $teamId, $templateId) {
		$helper = $this->container->get("wbs.globalhelper.service");
		$teamSports = $this->getTeamSportsByTemplateId($templateId);

		$em = $this->getDoctrine()->getManager();
		$result = array();

		if ($teamId) {
			$teamData = $em->getRepository('AppBundle:FtTeam')->getTeamExtraData($helper, $teamId, $templateId);
			if ($teamData) $result['teamData'] = $teamData;

			$latestSportSeason = $em->getRepository('AppBundle:FtLeagueSeasonSport')->getLatestSportSeason($teamSports[$teamId]);
			$teamStandings = $em->getRepository('AppBundle:FtLeagueStandings')->getLeagueStandings($teamSports[$teamId], (isset($latestSportSeason['season']) ? $latestSportSeason['season'] : '2021'), 'gr', 'el', 'en');

			if ($teamStandings && isset($teamStandings['standings'])) $result['standings'] = $teamStandings['standings'];
		}

		$em->clear();
		$view = $this->view($result, Response::HTTP_OK);
		return $view;	
	}

	private function getTeamSportsByTemplateId($templateId)
	{
		$teamSports = array();

		// World Cup
		if (2 == $templateId) {
			$teamSports[122] = 713;
			$teamSports[3555] = 713;
			$teamSports[3909] = 713;
			$teamSports[4429] = 713;
	
			$teamSports[124] = 714;
			$teamSports[3467] = 714;
			$teamSports[3470] = 714;
			$teamSports[3637] = 714;
	
			$teamSports[3781] = 715;
			$teamSports[4079] = 715;
			$teamSports[4420] = 715;
			$teamSports[4439] = 715;
	
			$teamSports[3536] = 716;
			$teamSports[3541] = 716;
			$teamSports[3972] = 716;
			$teamSports[3974] = 716;
	
			$teamSports[3508] = 717;
			$teamSports[3977] = 717;
			$teamSports[4083] = 717;
			$teamSports[4124] = 717;
	
			$teamSports[3332] = 718;
			$teamSports[3718] = 718;
			$teamSports[3910] = 718;
			$teamSports[4208] = 718;
	
			$teamSports[3973] = 719;
			$teamSports[4032] = 719;
			$teamSports[4347] = 719;
			$teamSports[4410] = 719;
	
			$teamSports[3270] = 720;
			$teamSports[3309] = 720;
			$teamSports[3400] = 720;
			$teamSports[3553] = 720;
	
			return $teamSports;
		}

		// Copa America
		if (3 == $templateId) {
			$teamSports[3541] = 802;
			$teamSports[3644] = 802;
			$teamSports[3433] = 802;
			$teamSports[3453] = 802;
			$teamSports[4429] = 802;
	
			$teamSports[4083] = 803;
			$teamSports[3309] = 803;
			$teamSports[3915] = 803;
			$teamSports[4420] = 803;
			$teamSports[4432] = 803;
	
			// $teamSports[4429] = 804;
			// $teamSports[3915] = 804;
			// $teamSports[3270] = 804;
			// $teamSports[3433] = 804;

			return $teamSports;
		}

		// Copa Africa
		if (4 == $templateId) {
			$teamSports[3909] = 805;
			$teamSports[3468] = 805;
			$teamSports[3778] = 805;
			$teamSports[3710] = 805;

			$teamSports[3972] = 806;
			$teamSports[2898] = 806;
			$teamSports[3365] = 806;
			$teamSports[2899] = 806;

			$teamSports[3553] = 807;
			$teamSports[2895] = 807;
			$teamSports[3848] = 807;
			$teamSports[3364] = 807;

			$teamSports[3467] = 808;
			$teamSports[2244] = 808;
			$teamSports[3554] = 808;
			$teamSports[3552] = 808;

			$teamSports[3973] = 809;
			$teamSports[3908] = 809;
			$teamSports[3780] = 809;
			$teamSports[2883] = 809;

			$teamSports[3357] = 810;
			$teamSports[5021] = 810;
			$teamSports[3779] = 810;
			$teamSports[3469] = 810;

			return $teamSports;
		}

		return $teamSports;
	}

	/**
	 * Get teams by country
	 *
	 * @Method("GET")
	 * @Rest\Get("/teams/{countryId}", name="teams_by_country_get", options={"expose": true})
	 */
	public function getTeamsByCountryIdAction(Request $request, $countryId)
	{
		$em = $this->getDoctrine()->getManager();
		$teams = array();

		if ($countryId) {
			$teams = $em->getRepository('AppBundle:FtTeam')->getTeamsByCountryId((int) $countryId);
			// $teams = $em->getRepository('AppBundle:FtTeam')->findBy(['country' => (int) $countryId]);
		}

		$em->clear();
		$view = $this->view($teams, Response::HTTP_OK);
		return $view;	
	}

	/**
	 * Get teams by country
	 *
	 * @Method("GET")
	 * @Rest\Get("/participants/{page}", name="all_teams_get", defaults={"page" = 1})
	 */
	public function getAllTeams(Request $request, $page)
	{
		$em = $this->getDoctrine()->getManager();

		$offset = ($page-1) * 100;
		$limit = 100;

		$teams = $em->getRepository('AppBundle:FtTeam')->getAllTeams($offset, $limit);

		$em->clear();
		if ( !empty($teams) ) {
			return new JsonResponse($teams, Response::HTTP_OK);
		}
		else {
			return new JsonResponse([],Response::HTTP_NOT_FOUND);
		}
	}
}
