<?php

namespace AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\FOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\JsonResponse;
use AppBundle\Repository\FtLeagueFlagRepository;

/**
 * FtLeagueFlags REST API controller.
 *
 * @Route("/api/v2")
 */

class RestFtLeagueFlagsController extends FOSRestController
{
    /**
     * JSON GET Request for FtLeagueFlags entities.
     *
     * @Method("GET")
     * @Rest\Get("/get-active-league-flags", name="ftleagueflags_rest")
     *
     */

    public function indexAction(Request $request)
    {
        $em = $this->getDoctrine()->getManager();

        $leagueFlags = $em->getRepository('AppBundle:FtLeagueFlag')->getFlagsWithCompetitions();

        return new JsonResponse([$leagueFlags], Response::HTTP_OK);
    }
}
