<?php

namespace AppBundle\Controller\Api;

use AppBundle\Entity\TeamFootballField;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use FOS\RestBundle\Controller\FOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;

/**
 * Football Field REST API controller.
 *
 * @Route("/api/v2")
 */
class RestTeamFootballFieldController extends FOSRestController
{
	/**
	 * Get football fields by team
	 *
	 * @Method("GET")
	 * @Rest\Get("/team-football-fields/{teamId}", name="football_fields_by_team_get", options={"expose": true})
	 */
	public function getFootballFieldsByTeamIdAction(Request $request, $teamId)
	{
		$em = $this->getDoctrine()->getManager();
		$footballFields = array();

		if ($teamId) {
			$team = $em->getRepository('AppBundle:FtTeam')->findOneBy(['teamId' => (int) $teamId]);
			if (!$team) return $this->view($footballFields, Response::HTTP_OK);
			$footballFields = $em->getRepository('AppBundle:TeamFootballField')->getFootballFieldsByTeamId($team, $teamId);
		}

		$em->clear();
		$view = $this->view($footballFields, Response::HTTP_OK);
		return $view;	
    }
    
	/**
	 * Delete football fields by team
	 *
	 * @Method("POST")
	 * @Rest\Get("/team-football-fields/{teamId}/{footballFieldIds}", name="team_football_fields_by_team_new_relation", options={"expose": true}, defaults={"footballFieldIds"=0})
	 */
	public function deleteFootballFieldsByTeamIdAction(Request $request, $teamId, $footballFieldIds)
	{
		$em = $this->getDoctrine()->getManager();

		if ($teamId) {
			$team = $em->getRepository('AppBundle:FtTeam')->findOneBy(['teamId' => (int) $teamId]);
			if (!$team) return $this->view($footballFields, Response::HTTP_OK);
			
			$teamFootballFields = $em->getRepository('AppBundle:TeamFootballField')->findBy(['team' => $team]);

			if ($teamFootballFields) {
				foreach ($teamFootballFields as $teamFootballField) {
					$em->remove($teamFootballField);
					$em->flush();
				}
			}

			if (!empty($footballFieldIds)) {
				$footballFieldsArr = explode(',', $footballFieldIds);
				foreach ($footballFieldsArr as $footballFieldId) {
					$footballField = $em->getRepository('AppBundle:FootballField')->findOneById($footballFieldId);
					$teamFootballField = new TeamFootballField();
					$teamFootballField->setTeam($team);
					$teamFootballField->setFootballField($footballField);
					$teamFootballField->setIsDefault(true);

					$em->persist($teamFootballField);
					$em->flush();
				}
			}
		}

		$em->clear();
		$view = $this->view('all football fields for team are updated', Response::HTTP_OK);
		return $view;	
	}
}
