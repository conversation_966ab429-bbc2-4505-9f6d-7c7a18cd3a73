<?php

namespace AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\FOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;

/**
 * Country REST API controller.
 *
 * @Route("/api/v2")
 */
class RestCountryController extends FOSRestController
{
	/**
	 * Get all countries
	 *
	 * @Method("GET")
	 * @Rest\Get("/countries", name="countries_get", options={"expose": true})
	 */
		public function getCountriesAction(Request $request)
		{
			$em = $this->getDoctrine()->getManager();
			
			$countries = $em->getRepository('AppBundle:City')->createQueryBuilder('c')
				->select('DISTINCT cc.id, cc.nameGr')
				->innerJoin('c.country', 'cc')
				->getQuery()
				->getResult();

			$em->clear();
			$view = $this->view($countries, Response::HTTP_OK);
			return $view;	
	}
}
