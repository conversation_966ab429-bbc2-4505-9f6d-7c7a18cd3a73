<?php

namespace AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\FOSRestController;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * FtLeagueStandings REST API controller.
 *
 * @Route("/api/v2/stats")
 */
class RestTeamStatsController extends FOSRestController
{
  const LOGO_URL = 'https://www.europaleague.gr/betarades/cdn/teams/';

  /**
   *
   *
   * @Method("GET")
   * @Rest\Get("/match/{homeTeamId}/{awayTeamId}/{sportId}/{season}/{domainId}/{isPortrait}", defaults={"sportId" = 304, "season" = null, "lan" = "2", "isPortrait" = "0"})
   */
  public function indexAction(Request $request, $homeTeamId, $awayTeamId, $sportId, $season, $domainId, $isPortrait = 0) {
    $em           = $this->getDoctrine()->getManager();
    $helper       = $this->container->get("wbs.globalhelper.service");
    // if lan was taken by domain
    $domain       = $em->getRepository('AppBundle:Domain')->findOneById($domainId);
    $domainSportSlug = $em->getRepository('AppBundle:DomainSportSlug')->findOneBy(array('domain' => $domainId, 'sport' => $sportId));
    $sportSlug = ($domainSportSlug) ? $domainSportSlug->getSlug() : '';

    $locale       = $domain->getLanguage()->getLocale();
    $languageCode   = $helper->getLanguageByLocale($locale);
    $teamName1    = '';
    $teamName2    = '';

    $translated = $this->get('translator');
    $translated->setLocale($locale);

    if ( $season === 'null' ) {
      $seasonSport = $em->getRepository('AppBundle:FtLeagueSeasonSport')->getLatestSportSeason($sportId);
      if ($seasonSport) {
        $season = $seasonSport['season'];
      }
    }

		$localeFallback = $this->container->getParameter('locale');
    $team1Name = $em->getRepository('AppBundle:FtTeam')->getTeamNameByLocale($homeTeamId, $locale, $localeFallback);
    $team2Name = $em->getRepository('AppBundle:FtTeam')->getTeamNameByLocale($awayTeamId, $locale, $localeFallback);

    // Genika Statistika (1)
    $stats01Data = $em->getRepository('AppBundle:TeamStats')->getStats01($homeTeamId, $awayTeamId, $sportId, $season);
    $team1Data = null;
    $team2Data = null;

    $result = array();
    $result['info']['homeTeam']['id']   = $homeTeamId;
    $result['info']['homeTeam']['name'] = $team1Name;
    $result['info']['awayTeam']['id']   = $awayTeamId;
    $result['info']['awayTeam']['name'] = $team2Name;

    if ($stats01Data && (isset($stats01Data[0]['rank']) || isset($stats01Data[1]['rank']))) {
      $result['stats01']['homeTeam'] = isset($stats01Data[0]['rank']) ? $stats01Data[0] : null;
      $result['stats01']['awayTeam'] = isset($stats01Data[1]['rank']) ? $stats01Data[1] : null;

      // $team1Data = $stats01Data[0];
      // $team1Data['teamName'] = $team1Name;
      // $team2Data = $stats01Data[1];
      // $team2Data['teamName'] = $team2Name;
      // $templateName = (1 === (int) $isPortrait) ? 'stats/stats01resp.html.twig' : 'stats/stats01.html.twig';
      // $stats01 = $this->renderView($templateName, array('team1Data' => $team1Data, 'team2Data' => $team2Data, 'logoUrl' => self::LOGO_URL));
    }

    // Teleutaioi Agwnes (2)
    $stats02Data = $em->getRepository('AppBundle:TeamStats')->getStats02($homeTeamId, $awayTeamId, $sportId, $season, $languageCode, $localeFallback);

    if ($stats02Data) {
      $result['stats02']['homeTeam'] = isset($stats02Data['home']) ? $stats02Data['home'] : array();
      $result['stats02']['awayTeam'] = isset($stats02Data['away']) ? $stats02Data['away'] : array();

      // $team1Data = $stats02Data['home'];
      // $team1Data['teamName'] = $team1Name;
      // $team2Data = $stats02Data['away'];
      // $team2Data['teamName'] = $team2Name;
      // $stats02 = $this->renderView('stats/stats02.html.twig', array('team1Data' => $team1Data, 'team2Data' => $team2Data, 'logoUrl' => self::LOGO_URL, 'homeTeam'=> array('id' => $homeTeamId, 'name' => $team1Name), 'awayTeam'=> array('id' => $awayTeamId, 'name' => $team2Name)));
    }

    // Over/Under (3)
    $stats03Data = $em->getRepository('AppBundle:TeamStats')->getStats03($homeTeamId, $awayTeamId, $sportId, $season, $team1Name, $team2Name);

    if ($stats03Data) {
      $result['stats03'] = $stats03Data;

      //$stats03 = $this->renderView('stats/stats03.html.twig', array('data' => $stats03Data, 'logoUrl' => self::LOGO_URL, 'homeTeamName' => $team1Name, 'awayTeamName' => $team2Name, 'isPortrait' => $isPortrait));
    }

    // Goal & HalfTimes (4)
    $stats04Data = $em->getRepository('AppBundle:TeamStats')->getStats04($homeTeamId, $awayTeamId, $sportId, $season, $team1Name, $team2Name);

    if ($stats04Data) {
      $result['stats04'] = $stats04Data;
      // $stats04 = $this->renderView('stats/stats04.html.twig', array('data' => $stats04Data, 'logoUrl' => self::LOGO_URL, 'homeTeamName' => $team1Name, 'awayTeamName' => $team2Name, 'isPortrait' => $isPortrait));
    }

    // HalfTimes & Finals (5)
    $stats05Data = $em->getRepository('AppBundle:TeamStats')->getStats05($homeTeamId, $awayTeamId, $sportId, $season, $team1Name, $team2Name);

    if ($stats05Data) {
      $result['stats05'] = $stats05Data;
      // $templateName = (1 === (int) $isPortrait) ? 'stats/stats05resp.html.twig' : 'stats/stats05.html.twig';
      // $stats05 = $this->renderView($templateName, array('data' => $stats05Data, 'logoUrl' => self::LOGO_URL, 'homeTeamName' => $team1Name, 'awayTeamName' => $team2Name));
    }

    // Goals per Minute (6)
    $stats06Data = $em->getRepository('AppBundle:TeamStats')->getStats06($homeTeamId, $awayTeamId, $sportId, $season);

    if ($stats06Data) {
      $result['stats06'] = $stats06Data;
      // $stats06 = $this->renderView('stats/stats06.html.twig', array('data' => $stats06Data, 'logoUrl' => self::LOGO_URL, 'homeTeamName' => $team1Name, 'awayTeamName' => $team2Name));
    }

    // Scorers (7)
    $stats07Data = $em->getRepository('AppBundle:TeamStats')->getStats07($homeTeamId, $awayTeamId, $sportId, $season, $team1Name, $team2Name);

    if ($stats07Data) {
      $result['stats07'] = $stats07Data;
      // $stats07 = $this->renderView('stats/stats07.html.twig', array('data' => $stats07Data, 'logoUrl' => self::LOGO_URL));
    }

    // Streaks (8)
    $translations = array();
    $translations['scoring_in_the_last']  = $this->get('translator')->trans('Scoring in the last');
    $translations['goals_against']        = $this->get('translator')->trans('Goals against');
    $translations['failed_to_score']      = $this->get('translator')->trans('Failed to score');
    $translations['clean_sheet']          = $this->get('translator')->trans('Clean Sheet');
    $translations['winning']              = $this->get('translator')->trans('Winning');
    $translations['unbeaten']             = $this->get('translator')->trans('Unbeaten');
    $translations['losing']               = $this->get('translator')->trans('Losing');
    $translations['winless']              = $this->get('translator')->trans('Winless');
    
    $stats08Data = $em->getRepository('AppBundle:TeamStats')->getStats08($homeTeamId, $awayTeamId, $sportId, $season, $translations);

    if ($stats08Data) {
      $result['stats08'] = $stats08Data;
      // $stats08 = $this->renderView('stats/stats08.html.twig', array('data' => $stats08Data, 'logoUrl' => self::LOGO_URL, 'homeTeam'=> array('id' => $homeTeamId, 'name' => $team1Name), 'awayTeam'=> array('id' => $awayTeamId, 'name' => $team2Name), 'isPortrait' => $isPortrait));
    }

    // Standings (9)
    $sportTitle = $em->getRepository('AppBundle:Sport')->findTranslationByTypeAndLocale($sportId);
    $sport = $em->getRepository('AppBundle:Sport')->findOneById($sportId);

    $stats09Data = $em->getRepository('AppBundle:TeamStats')->getStats09($homeTeamId, $awayTeamId, $sportId, $season, $languageCode, $localeFallback);

    if ($stats09Data) {
      $result['stats09']['sportTitle']  = $sportTitle;
      $result['stats09']['sportSlug']   = $sportSlug;
      $result['stats09']                = $stats09Data;
    }

    // Next Games (10)
    $stats10Data = $em->getRepository('AppBundle:TeamStats')->getStats10($homeTeamId, $awayTeamId, $sportId, $season, $languageCode, $localeFallback);

    if ($stats10Data) {
      $result['stats10'] = $stats10Data;
    }

    // $html = $this->renderView('stats/get.html.twig', array('data' => 'lala'));

    // hometeam/awayteam form
    $stats11Data = $em->getRepository('AppBundle:TeamStats')->getStats11($homeTeamId, $awayTeamId, $languageCode, $localeFallback, $locale);
    if ($stats11Data) {
      $result['stats11'] = $stats11Data;
    }

    $response = new JsonResponse();
    $response->setData($result);
    return $response;

    // $view = $this->view($result, Response::HTTP_OK);
    // return $view;   
  }

  /**
   *
   *
   * @Method("GET")
   * @Rest\Get("/full/{type}/{domainId}", defaults={"type" = "over-under"})
   */
  public function getStatsByTypeAction(Request $request, $type, $domainId) {
    $em     = $this->getDoctrine()->getManager();
    $helper = $this->container->get("wbs.globalhelper.service");

    $imagePath      = $this->container->hasParameter('comp.image.path') ? $this->container->getParameter('comp.image.path') : null;
    if (!empty($imagePath)) $imagePath = $imagePath . 'svg/1x1/';
    $domain         = $em->getRepository('AppBundle:Domain')->findOneById($domainId);
    $locale         = $domain->getLanguage()->getLocale();
    $languageCode   = $helper->getLanguageByLocale($locale);
    $localeFallback = $this->container->getParameter('locale');
    $domainUrl      = $helper->getDomainUrl($domain);

    $translated = $this->get('translator');
    $translated->setLocale($locale);

    if ('over-under' === $type) {
      $overUnderData      = $em->getRepository('AppBundle:TeamStats')->getOverUnder($domainId, $locale, $localeFallback);
      $teamOverUnderData  = $em->getRepository('AppBundle:TeamStats')->getTeamOverUnder($domainId, $locale, $localeFallback, $languageCode);

      $overUnder = $this->renderView('full-stats/overunder.html.twig', array('data' => $overUnderData, 'teamData' => $teamOverUnderData, 'imagePath' => $imagePath, 'domainUrl' => $domainUrl));
      $response = new Response($overUnder);
      // $response->headers->set('Content-Type', 'text/plain');
      $response->headers->set('Content-Type', 'text/html');
    }
    elseif ('ng-gg' === $type) {
      $ngGgData      = $em->getRepository('AppBundle:TeamStats')->getNgGg($domainId, $locale, $localeFallback);
      $teamNgGgData  = $em->getRepository('AppBundle:TeamStats')->getTeamNgGg($domainId, $locale, $localeFallback, $languageCode);

      $ngGg = $this->renderView('full-stats/nogoalgoalgoal.html.twig', array('data' => $ngGgData, 'teamData' => $teamNgGgData, 'imagePath' => $imagePath, 'domainUrl' => $domainUrl));
      $response = new Response($ngGg);

      // $response->headers->set('Content-Type', 'text/plain');
      $response->headers->set('Content-Type', 'text/html');
    }
    elseif ('1x2' === $type) {
      $team1x2Data  = $em->getRepository('AppBundle:TeamStats')->getTeam1x2($domainId, $locale, $localeFallback, $languageCode);

      $team1x2 = $this->renderView('full-stats/winsdrawsloses.html.twig', array('teamData' => $team1x2Data, 'imagePath' => $imagePath, 'domainUrl' => $domainUrl));
      $response = new Response($team1x2);

      // $response->headers->set('Content-Type', 'text/plain');
      $response->headers->set('Content-Type', 'text/html');
    }
    elseif ('number-of-goals' === $type) {
      $numberOfGoalsData  = $em->getRepository('AppBundle:TeamStats')->getTeamNumberOfGoals($domainId, $locale, $localeFallback, $languageCode);

      $numberOfGoals = $this->renderView('full-stats/numberofgoals.html.twig', array('teamData' => $numberOfGoalsData, 'imagePath' => $imagePath, 'domainUrl' => $domainUrl));
      $response = new Response($numberOfGoals);

      // $response->headers->set('Content-Type', 'text/plain');
      $response->headers->set('Content-Type', 'text/html');
    }
    elseif ('streak' === $type) {
      $streakData  = $em->getRepository('AppBundle:TeamStats')->getTeamStreak($domainId, $locale, $localeFallback, $languageCode);

      $streak = $this->renderView('full-stats/streak.html.twig', array('teamData' => $streakData, 'imagePath' => $imagePath, 'domainUrl' => $domainUrl));
      $response = new Response($streak);

      // $response->headers->set('Content-Type', 'text/plain');
      $response->headers->set('Content-Type', 'text/html');
    }
    elseif ('competition' === $type) {
      // must workaround in getting priority in champions bar for order reasons
      // $footballPriority = $helper->curlRequest($hostName.'/wp-json/wbs/v2/sports/football-priority-cb');
      $footballPriority = null;
      $competitionData  = $em->getRepository('AppBundle:TeamStats')->getCompetitionStats($domainId, $locale, $localeFallback, $footballPriority);

      $competition = $this->renderView('full-stats/competition.html.twig', array('data' => $competitionData, 'imagePath' => $imagePath));
      $response = new Response($competition);

      // $response->headers->set('Content-Type', 'text/plain');
      $response->headers->set('Content-Type', 'text/html');
    }
    elseif ('todays-matches' === $type) {
      // anti na pairnw to champions bar apo to domain, na fernw ta matches apo to domain_ft_fixture gia hmeromhnia mias hmeras
      // gia to domain pou me kalei
      // $hostName = $domain->getDbHost();
      $hostName = $domain->getDbNodeHost();
      $protocol = (1 === (int) $domain->getProtocol()) ? 'https' : 'http';
      $nodePort = $domain->getNodePortId();
      $curlUrl = $protocol . '://' . $hostName . ':' . $nodePort . '/v2/champions_bar_football';

      $footballCb = $helper->curlRequest($curlUrl);
      $todaysMatchesData = $em->getRepository('AppBundle:TeamStats')->getTodaysMatchesStats($domainId, $footballCb);

      $todaysMatches = $this->renderView('full-stats/todaysmatches.html.twig', array('data' => $todaysMatchesData, 'imagePath' => $imagePath));
      $response = new Response($todaysMatches);
      $response->headers->set('Content-Type', 'text/html');      
    }

    return $response;
  }
}
