<?php

namespace AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\FOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;

/**
 * Football Field REST API controller.
 *
 * @Route("/api/v2")
 */
class RestFootballFieldController extends FOSRestController
{
	/**
	 * Get football fields by country
	 *
	 * @Method("GET")
	 * @Rest\Get("/football-fields/{countryId}", name="football_fields_by_country_get", options={"expose": true})
	 */
	public function getFootballFieldsByCountryIdAction(Request $request, $countryId)
	{
		$em = $this->getDoctrine()->getManager();
		$footballFields = array();

		if ($countryId) {
			$country = $em->getRepository('AppBundle:Country')->findOneById((int) $countryId);
			$footballFields = $em->getRepository('AppBundle:FootballField')->getFootballFieldsByCountryId((int) $countryId, $country);
		}

		$em->clear();
		$view = $this->view($footballFields, Response::HTTP_OK);
		return $view;	
	}
}
