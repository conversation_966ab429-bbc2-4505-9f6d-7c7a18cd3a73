<?php

namespace AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\FOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;

/**
 * FtLeagueStandings REST API controller.
 *
 * @Route("/api/v2")
 */
class RestFtFixtureController extends FOSRestController {

  /**
   * Set team names
   *
   * @Method("POST")
   * @Rest\Post("/team-names", name="teamnames_get")
   */
  public function getTeamNamesAction(Request $request) {
		$em 				= $this->getDoctrine()->getManager();
		$logger 		= $this->get('logger');
		$teamNames 	= array();

		//request->get('fixtureIds')
		$fixtureIds = $request->request->get('fixtureIds');
		$logger->info(implode(',', $fixtureIds));

		$em->clear();
    $view = $this->view(json_encode($fixtureIds), Response::HTTP_OK);
    return $view;		
	}

  /**
   * Get Fixture Info for betfair.server
   *
   * @Method("GET")
   * @Rest\Get("/fixture-info/{type}", name="fixtureinfo_get")
   */
  public function getFixtureInfoAction(Request $request, $type = 'fixtures') {
		$em = $this->getDoctrine()->getManager();
    $helper = $this->container->get("wbs.globalhelper.service");
    $result = array();
		$clients = null;
    
    $url = $this->container->hasParameter('betfair.url') ? $this->container->getParameter('betfair.url') : null;
    if ($url) {
      $clients = $helper->curlRequest($url, false, null);
    }

		// $logger->info('[CVYZ] updating cache');
		$dateFrom = Date('Y-m-d 07:00:00');
		$dateTo 	= Date('Y-m-d 06:59:59', strtotime("+2 days"));

    $result = $em->getRepository('AppBundle:FtFixture')->getInfoForDates($dateFrom, $dateTo, $clients);

		$arrKey = $type === 'fixtures' ? 'fixtures' : 'clients';
		$view = $this->view(array($result[$arrKey]), Response::HTTP_OK);
		return $view;
	}

}