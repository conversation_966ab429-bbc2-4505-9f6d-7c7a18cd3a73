<?php

namespace AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\FOSRestController;

use AppBundle\Entity\FtTeamPlayer;
/**
 * FtTeamPlayer REST API controller.
 *
 * @Route("/api/v2/players")
 */
class RestFtTeamPlayerController extends FOSRestController
{

	/**
   * Get team players for fixture_id
   *
   * @Method("GET")
   * @Rest\Get("/{hTeamId}/{aTeamId}")
   */
	public function getFtTeamPlayersAction(Request $request, $hTeamId, $aTeamId) {
		$em = $this->getDoctrine()->getManager();
		$result = array();

		if ($hTeamId) {
			$hTeamPlayers = $em->getRepository('AppBundle:FtTeamPlayer')->findBy(array('teamId' => $hTeamId));
			if ($hTeamPlayers) $result['hTeamPlayers'] = $hTeamPlayers;
		}

		if ($aTeamId) {
			$aTeamPlayers = $em->getRepository('AppBundle:FtTeamPlayer')->findBy(array('teamId' => $aTeamId));
			if ($aTeamPlayers) $result['aTeamPlayers'] = $aTeamPlayers;
		}

		$em->clear();
		$view = $this->view($result, Response::HTTP_OK);
		return $view;	
	}

  /**
   * Insert team player for team id
   *
   * @Method("POST")
   * @Rest\Post("/")
   */
  public function createFtTeamPlayerAction(Request $request) {
		$em = $this->getDoctrine()->getManager();
		$result = array('msg' => 'initial value');

		$reqPlayer = $request->request->get('player');

		// save team players first
		// home team players
		if ($reqPlayer) {
			if (isset($reqPlayer['team_id']) && 
				isset($reqPlayer['name_el']) && 
				isset($reqPlayer['name_en']) && 
				isset($reqPlayer['position'])
				) {
				$player = new FtTeamPlayer();
				$player->setTeamId($reqPlayer['team_id']);
				$player->setNameEl($reqPlayer['name_el']);
				$player->setNameEn($reqPlayer['name_en']);
				$player->setPosition($reqPlayer['position']);

				$em->persist($player);
				$em->flush();

				$result['id'] = $player->getId();
				$result['msg'] = 'INSERTED';
			}
			else {
				$result['msg'] = 'INVALID PLAYER DATA';
				$em->clear();
				$view = $this->view($result, Response::HTTP_NOT_FOUND);
				return $view;
			}
		}
		else {
			$result['msg'] = 'PLAYER MISSING';
			$em->clear();
			$view = $this->view($result, Response::HTTP_NOT_FOUND);
			return $view;
		}

		$em->clear();
		$view = $this->view($result, Response::HTTP_OK);
		return $view;
	}

  /**
   * Update team player for player id
   *
   * @Method("PUT")
   * @Rest\Put("/{playerId}")
   */
  public function updateFtTeamPlayerAction(Request $request, $playerId) {
		$em 		= $this->getDoctrine()->getManager();
		// $logger	= $this->get('logger');
		$result = array('msg' => 'initial value');

		$reqPlayer = $request->request->get('player');

		// save team players first
		// home team players
		if ($reqPlayer && $playerId) {
			if (isset($reqPlayer['team_id']) && 
				isset($reqPlayer['name_el']) && 
				isset($reqPlayer['name_en']) && 
				isset($reqPlayer['position'])
				) {

				$player = $em->getRepository('AppBundle:FtTeamPlayer')->findOneById($playerId);

				if ($player) {
					$player->setTeamId($reqPlayer['team_id']);
					$player->setNameEl($reqPlayer['name_el']);
					$player->setNameEn($reqPlayer['name_en']);
					$player->setPosition($reqPlayer['position']);
	
					$em->persist($player);
					$em->flush();

					$result['id'] = $player->getId();
					$result['msg'] = 'UPDATED';

				}
			}
		}

		$em->clear();
		$view = $this->view($result, Response::HTTP_OK);

		return $view;
	}

	/**
   * Delete team player for player id
   *
   * @Method("DELETE")
   * @Rest\Delete("/{playerId}")
   */
  public function deleteFtTeamPlayerAction(Request $request, $playerId) {
		$em = $this->getDoctrine()->getManager();
		$result = null;

		// save team players first
		// home team players
		if ($playerId) {
			$player = $em->getRepository('AppBundle:FtTeamPlayer')->findOneById($playerId);

			if ($player) {
				$em->remove($player);
				$em->flush();

				$result = 'DELETED';
			}
		}

		$em->clear();
		$view = $this->view($result, Response::HTTP_OK);
		return $view;
	}
}