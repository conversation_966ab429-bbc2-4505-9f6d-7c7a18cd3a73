<?php

namespace AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\FOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\Annotations\RequestParam;

/**
 * FtLeagueStandings REST API controller.
 *
 * @Route("/api/auth")
 */
class RestAuthController extends FOSRestController {

  /**
   * Login user and authorize token
   *
   * @Method("POST")
   * @Rest\Post("/login", name="auth_login")
   */
  public function loginAction(Request $request) {
    $em = $this->getDoctrine()->getManager();

    $userName = $request->request->get('username');
    $password = $request->request->get('password');

    if(is_null($userName) || is_null($password)) {
      $view = $this->view('Username or password not set', Response::HTTP_OK);
      return $view;      
    }

    $user_manager = $this->get('fos_user.user_manager');
    $factory = $this->get('security.encoder_factory');

    $user = $user_manager->findUserByUsername($userName);
    $result = $user;
    $encoder = $factory->getEncoder($user);
    $salt = $user->getSalt();

    // $user = $this->get('security.token_storage')->getToken()->getUser();
    
    // $securityContext = $this->get('security.context');
    $tokenManager = $this->get('fos_oauth_server.access_token_manager.default');
    $token        = $this->get('security.token_storage')->getToken();
    $accessToken  = $tokenManager->findTokenByToken($token->getToken());
    $view = $this->view($accessToken, Response::HTTP_OK);
    return $view;     

    if($encoder->isPasswordValid($user->getPassword(), $password, $salt)) {
      // $em->getRepository('AppBundle:AccessToken')
      // should now get token and return it
      // $tokenManager = $this->get('fos_oauth_server.access_token_manager.default');
      // $token        = $this->get('security.token_storage')->getToken();
      // // $bearerToken = $this->get('fos_oauth_server.server')->getBearerToken($request);
      // $accessToken  = $tokenManager->findTokenByToken($token->getToken());
      // // $accessToken  = $tokenManager->findTokenByToken($bearerToken);
      // $tokenManager = 'aua';
      

      $tokenManager = $this->get('fos_oauth_server.access_token_manager.default');
      $token = $this->container->get('security.token_storage')->getToken();
      $isAuthenticated = $token->isAuthenticated();
      // if ($isAuthenticated) {
        // $client = $token->getClient();
        // $authToken = $this->get('fos_oauth_server.storage')->createAccessToken($client);
        // $authToken = $this->get('fos_oauth_server.access_token_manager.default')->createToken();
      // }

      // $token = $this->container->get('security.token_storage')->getToken();
      // $accessToken  = $tokenManager->findTokenByToken($token->getToken());
      // /** @var \FOS\OAuthServerBundle\Model\AccessToken $token */
      // $token = $this->container->get('fos_oauth_server.storage')->getAccessToken($_token->getToken());
      // /** @var \FOS\OAuthServerBundle\Model\ClientInterface */
      // $client = $token->getClient();
      // $client = $accessToken->getClient();
      // $result = $client;

      // TODO
      $result = $token;
    } else {
      $result = 'user NOT logged in';
    }

		$view = $this->view($result, Response::HTTP_OK);
		return $view;
  }
  
  /**
   * Logout user
   *
   * @Method("GET")
   * @Rest\Post("/logout", name="auth_logout")
   */
  public function logoutAction(Request $request) {
    // delete his token?
    $result = 'logout';

    $view = $this->view($result, Response::HTTP_OK);
    return $view;
  }
}