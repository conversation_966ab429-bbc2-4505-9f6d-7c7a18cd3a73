<?php

namespace AppBundle\Controller\Api;

use AppBundle\Entity\FtLeagueSeasonSport;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use FOS\RestBundle\Controller\FOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;

/**
 * Sport REST API controller.
 *
 * @Route("/api/v2")
 */
class RestSportController extends FOSRestController
{
	/**
	 * Get sport season by sport id
	 *
	 * @Method("GET")
	 * @Rest\Get("/sport/{sportId}/seasons", name="sport_seasons_get", options={"expose": true})
	 */
	public function getSportSeasonsAction(Request $request, $sportId)
	{
		$em = $this->getDoctrine()->getManager();
				
		$latestSportSeason = $em->getRepository('AppBundle:FtLeagueSeasonSport')->getLatestSportSeasons($sportId, 5);

		$em->clear();
		$view = $this->view($latestSportSeason, Response::HTTP_OK);
		return $view;
	}
}
