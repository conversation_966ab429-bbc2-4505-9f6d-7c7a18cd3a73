<?php

namespace AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\FOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;

/**
 * City REST API controller.
 *
 * @Route("/api/v2")
 */
class RestCityController extends FOSRestController
{
	/**
	 * Get cities by country id
	 *
	 * @Method("GET")
	 * @Rest\Get("/cities/{countryId}", name="cities_get", options={"expose": true})
	 */
    public function getCitiesByCountryAction(Request $request, $countryId)
    {
		$em = $this->getDoctrine()->getManager();
		$cities = array();

		if ($countryId) {
			$cities = $em->getRepository('AppBundle:City')->getCityByCountryId((int) $countryId);
		}

		$em->clear();
		$view = $this->view($cities, Response::HTTP_OK);
		return $view;	
	}
}
