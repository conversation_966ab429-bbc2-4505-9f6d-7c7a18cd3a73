<?php

namespace AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\FOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\JsonResponse;


/**
 * FtCompetitionSchedule REST API controller.
 *
 * @Route("/api/v2/competition")
 */
class RestFtCompetitionScheduleController extends FOSRestController
{

    /**
     * JSON GET Request for FtCompetitionSchedule entities.
     *
     * @Method("GET")
     * @Rest\Get("/schedule/{locale}/{site}/{matchType}", name="ftcompetitionschedule_rest", defaults={"locale" = "el", "site" = "betarades", "matchType" = null})
     *
     */

	public function indexAction(Request $request, $locale, $site, $matchType = null)
	{
        $translator = $this->get('translator');
        $translator->setLocale($locale);

        $labels = array(
            'group' => $translator->trans('GROUP'),
            'team' => $translator->trans('TEAM'),
            'games' => $translator->trans('GAMES SMALL'),
            'game' => $translator->trans('GAME'),
            'goals' => $translator->trans('GOALS'),
            'points' => $translator->trans('POINTS SMALL'),
            'schedule' => $translator->trans('SCHEDULE')
        );

        $em = $this->getDoctrine()->getManager();

        $phaseMap = $this->get('app.twig.competition_extension')->getPhaseMap();

        $ftCompetitionStandingsFinal = [];
        $ftCompetitionStandingsSchedulesFinal = [];
        $ftCompetitionStandingsFinal['teams'] = null;

        $ftCompetitionStandings = $em->getRepository('AppBundle:FtLeagueStandings')->findCustomCompetitionTeams();

        $counter = 1;
        foreach ($ftCompetitionStandings as $ftCompetitionStanding) {

            $ftCompetitionStanding['name'] = ($locale == 'ro') ?  $ftCompetitionStanding['name_ro'] : $ftCompetitionStanding['name_gr'];

            unset($ftCompetitionStanding['name_gr']);
            unset($ftCompetitionStanding['name_ro']);

            $ftCompetitionStandingsFinal['teams'][$counter] = $ftCompetitionStanding;

            $counter++;
        }

        // Get matches based on match_type if provided
        if ($matchType) {
            $ftCompetitionSchedules = $em->getRepository('AppBundle:FtCompetitionSchedule')->findCompetitionMatchesByType($matchType);
        } else {
            $ftCompetitionSchedules = $em->getRepository('AppBundle:FtCompetitionSchedule')->findAllCompetitionMatches();
        }

        $ftCompetitionStandingsSchedulesFinal['matches'] = null;
        $ftCompetitionStandingsSchedulesFinal['groups'] = null;

        foreach ($ftCompetitionSchedules as $ftCompetitionSchedule) {

            if ($site == 'betarades' && isset($ftCompetitionSchedule['link_betarades'])) {
                $ftCompetitionSchedule['cta']['url'] = $ftCompetitionSchedule['link_betarades'];
                $ftCompetitionSchedule['cta']['text'] = $ftCompetitionSchedule['link_text_betarades'];
            }
            elseif ($site == 'bethome' && isset($ftCompetitionSchedule['link_bethome'])) {
                $ftCompetitionSchedule['cta']['url'] = $ftCompetitionSchedule['link_bethome'];
                $ftCompetitionSchedule['cta']['text'] = $ftCompetitionSchedule['link_text_bethome'];
            }
            elseif ($site == 'pariurix' && isset($ftCompetitionSchedule['link_pariurix'])) {
                $ftCompetitionSchedule['cta']['url'] = $ftCompetitionSchedule['link_pariurix'];
                $ftCompetitionSchedule['cta']['text'] = $ftCompetitionSchedule['link_text_pariurix'];
            }

            if (($site == 'betarades' || $site == 'bethome') && isset($ftCompetitionSchedule['tv_gr'])) {
                $ftCompetitionSchedule['tv'] = $ftCompetitionSchedule['tv_gr'];
            }
            elseif ($site == 'pariurix' && isset($ftCompetitionSchedule['tv_ro'])) {
                $ftCompetitionSchedule['tv'] = $ftCompetitionSchedule['tv_ro'];
            }

            $date = new \DateTime($ftCompetitionSchedule['match_datetime']);
            $ftCompetitionSchedule['timestamp'] = $date->getTimestamp();
            $ftCompetitionSchedule['h'] = $this->returnTeamIndex($ftCompetitionStandingsFinal['teams'], $ftCompetitionSchedule['h']);
            $ftCompetitionSchedule['a'] = $this->returnTeamIndex($ftCompetitionStandingsFinal['teams'], $ftCompetitionSchedule['a']);

            if (empty($ftCompetitionSchedule['score'])) {
                $ftCompetitionSchedule['score'] = '-:-';
            }

            $ftCompetitionSchedule['odds'] = [
                'home' => (float)$ftCompetitionSchedule['home_odds'],
                'away' => (float)$ftCompetitionSchedule['away_odds']
            ];

            if (!empty($ftCompetitionSchedule['affiliate_link'])) {
                $ftCompetitionSchedule['affiliate_url'] = $ftCompetitionSchedule['affiliate_link'];
            }

            unset($ftCompetitionSchedule['id']);
            unset($ftCompetitionSchedule['link_betarades']);
            unset($ftCompetitionSchedule['link_text_betarades']);
            unset($ftCompetitionSchedule['link_bethome']);
            unset($ftCompetitionSchedule['link_text_bethome']);
            unset($ftCompetitionSchedule['link_pariurix']);
            unset($ftCompetitionSchedule['link_text_pariurix']);
            unset($ftCompetitionSchedule['tv_gr']);
            unset($ftCompetitionSchedule['tv_ro']);
            unset($ftCompetitionSchedule['match_datetime']);
            unset($ftCompetitionSchedule['home_odds']);
            unset($ftCompetitionSchedule['away_odds']);
            unset($ftCompetitionSchedule['affiliate_link']);

            $phaseId = $ftCompetitionSchedule['phase_id'];

            $tempPhaseId = $phaseId;
            unset($ftCompetitionSchedule['phase_id']);

            // Check if phase_id is 'none' or empty
            if (!$tempPhaseId || $tempPhaseId == 'none') {
                $ftCompetitionStandingsSchedulesFinal['matches'][] = $ftCompetitionSchedule;
            } else {
                $phaseSlug = isset($phaseMap[$tempPhaseId]['slug']) ? $phaseMap[$tempPhaseId]['slug'] : 'phase-' . $tempPhaseId;
                $phaseName = isset($phaseMap[$tempPhaseId]['name']) ? $phaseMap[$tempPhaseId]['name'] : 'Phase ' . $tempPhaseId;

                $ftCompetitionStandingsSchedulesFinal['groups'][$phaseSlug] = $ftCompetitionStandingsSchedulesFinal['groups'][$phaseSlug] ?? [];
                $ftCompetitionSchedule['group_name'] = $phaseName;
                $ftCompetitionStandingsSchedulesFinal['groups'][$phaseSlug][] = $ftCompetitionSchedule;
            }
        }

        for ($i = 1; $i <= count($ftCompetitionStandingsFinal['teams']); $i++) {
            unset($ftCompetitionStandingsFinal['teams'][$i]['team_id']);
        }

        $ftCompetitionSchedulesData = array(
            'labels' => $labels,
            'teams' => $ftCompetitionStandingsFinal['teams'],

        );

        // Add matches to the response if there are any
        if (!empty($ftCompetitionStandingsSchedulesFinal['matches'])) {
            $ftCompetitionSchedulesData['matches'] = $ftCompetitionStandingsSchedulesFinal['matches'];
        }

        // Add groups to the response if there are any
        if (!empty($ftCompetitionStandingsSchedulesFinal['groups'])) {
            $ftCompetitionSchedulesData['groups'] = $ftCompetitionStandingsSchedulesFinal['groups'];
        }

        return new JsonResponse($ftCompetitionSchedulesData, Response::HTTP_OK);
    }

    /**
     * JSON GET Request for Euro Teams Latest Matches.
     *
     * @Method("GET")
     * @Rest\Get("/teams/{teamId}", name="ftcompetitionschedule_rest_team")
     *
     */

    public function getLatestTenMatchesForEuroTeam(Request $request, $teamId)
    {
        $em = $this->getDoctrine()->getManager();

        $ftEuroTeamMatches = $em->getRepository('AppBundle:FtFixture')->getTeamLastTenFinishedMatches($teamId);

        $ftEuroTeamMatchesFinal = [];
        foreach ($ftEuroTeamMatches as $match) {
            $date = new \DateTime($match['match_datetime']);
            unset($match['match_datetime']);
            $match['match_date'] = $date->format('Y-d-m');
            $ftEuroTeamMatchesFinal[] = $match;
        }

        return new JsonResponse($ftEuroTeamMatchesFinal, Response::HTTP_OK);
    }


    private function returnStandingsFlag(int $team_id)
    {
        $imagePath = $this->container->hasParameter('comp.image.path') ? $this->container->getParameter('comp.image.path') : null;

        $ftCompetitionStandingFlag = '';

        if ($team_id == 4208) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/GER.svg';
        }
        elseif ($team_id == 4408) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/SCO.svg';
        }
        elseif ($team_id == 3269) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/HUN.svg';
        }
        elseif ($team_id == 4124) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/SWI.svg';
        }
        elseif ($team_id == 3637) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/SPA.svg';
        }
        elseif ($team_id == 3974) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/CRO.svg';
        }
        elseif ($team_id == 3850) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/ITA.svg';
        }
        elseif ($team_id == 3826) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/ALB.svg';
        }
        elseif ($team_id == 4328) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/SLO.svg';
        }
        elseif ($team_id == 3781) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/DEN.svg';
        }
        elseif ($team_id == 3508) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/SER.svg';
        }
        elseif ($team_id == 4032) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/ENG.svg';
        }
        elseif ($team_id == 4278) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/NED.svg';
        }
        elseif ($team_id == 4079) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/FRA.svg';
        }
        elseif ($team_id == 3400) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/POL.svg';
        }
        elseif ($team_id == 3621) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/AUS.svg';
        }
        elseif ($team_id == 4196) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/UKR.svg';
        }
        elseif ($team_id == 92) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/SLV.svg';
        }
        elseif ($team_id == 4347) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/BELG.svg';
        }
        elseif ($team_id == 3746) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/ROM.svg';
        }
        elseif ($team_id == 3470) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/POR.svg';
        }
        elseif ($team_id == 4166) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/CZE.svg';
        }
        elseif ($team_id == 3758) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/GEO.svg';
        }
        elseif ($team_id == 4235) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/TUR.svg';
        }
        elseif ($team_id == 3541) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/ARG.svg';
        }
        elseif ($team_id == 4420) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/PER.svg';
        }
        elseif ($team_id == 3433) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/CHI.svg';
        }
        elseif ($team_id == 3403) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/CANADA.svg';
        }
        elseif ($team_id == 3718) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/MEX.svg';
        }
        elseif ($team_id == 3915) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/ECU.svg';
        }
        elseif ($team_id == 4432) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/VEN.svg';
        }
        elseif ($team_id == 4424) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/JAM.svg';
        }
        elseif ($team_id == 3264) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/USA.svg';
        }
        elseif ($team_id == 4429) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/URU.svg';
        }
        elseif ($team_id == 4410) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/PAN.svg';
        }
        elseif ($team_id == 3644) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/BOL.svg';
        }
        elseif ($team_id == 4083) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/BRA.svg';
        }
        elseif ($team_id == 3309) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/COL.svg';
        }
        elseif ($team_id == 3453) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/PAR.svg';
        }
        elseif ($team_id == 3977) {
            $ftCompetitionStandingFlag = $imagePath . 'svg/1x1/COS.svg';
        }

        return $ftCompetitionStandingFlag;
    }

    private function returnTeamUrl(int $team_id, string $site)
    {
        $ftCompetitionTeamUrl = '';
        if( $site == 'betarades' ) {
            if ($team_id == 4208) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-germanias-podosfairou/';
            }
            elseif ($team_id == 4408) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-skotias-podosfairou/';
            }
            elseif ($team_id == 3269) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-ouggarias-podosfairou/';
            }
            elseif ($team_id == 4124) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-elvetias-podosfairou/';
            }
            elseif ($team_id == 3637) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-ispanias-podosfairou/';
            }
            elseif ($team_id == 3974) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-kroatias-podosfairou/';
            }
            elseif ($team_id == 3850) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-italias-podosfairou/';
            }
            elseif ($team_id == 3826) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-alvanias-podosfairou/';
            }
            elseif ($team_id == 4328) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-slovenias-podosfairou/';
            }
            elseif ($team_id == 3781) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-danias-podosfairou/';
            }
            elseif ($team_id == 3508) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-servias-podosfairou/';
            }
            elseif ($team_id == 4032) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-agglias-podosfairou/';
            }
            elseif ($team_id == 4278) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-ollandias-podosfairou/';
            }
            elseif ($team_id == 4079) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-gallias-podosfairou/';
            }
            elseif ($team_id == 3400) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-polonias-podosfairou/';
            }
            elseif ($team_id == 3621) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-austrias-podosfairou/';
            }
            elseif ($team_id == 4196) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-oukranias-podosfairou/';
            }
            elseif ($team_id == 92) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-slovakias-podosfairou/';
            }
            elseif ($team_id == 4347) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-velgiou-podosfairou/';
            }
            elseif ($team_id == 3746) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-roumanias-podosfairou/';
            }
            elseif ($team_id == 3470) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-portogalias-podosfairou/';
            }
            elseif ($team_id == 4166) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-tsexias-podosfairou/';
            }
            elseif ($team_id == 3758) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-georgias-podosfairou/';
            }
            elseif ($team_id == 4235) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-tourkias-podosfairou/';
            }
            elseif ($team_id == 3541) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-argentinis-podosfairou/';
            }
            elseif ($team_id == 4420) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-perou-podosfairou/';
            }
            elseif ($team_id == 3433) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-xilis-podosfairou/';
            }
            elseif ($team_id == 3403) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-kanada-podosfairou/';
            }
            elseif ($team_id == 3718) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-mexikou-podosfairou/';
            }
            elseif ($team_id == 3915) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-isimerinou-podosfairou/';
            }
            elseif ($team_id == 4432) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-venezouelas-podosfairou/';
            }
            elseif ($team_id == 4424) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-tzamaika-podosfairou/';
            }
            elseif ($team_id == 3264) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-ipa-podosfairou/';
            }
            elseif ($team_id == 4429) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-ourougouais-podosfairou/';
            }
            elseif ($team_id == 4410) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-panama-podosfairou/';
            }
            elseif ($team_id == 3644) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-bolibias-podosfairou/';
            }
            elseif ($team_id == 4083) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-vrazilias-podosfairou/';
            }
            elseif ($team_id == 3309) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-kolomvias-podosfairou/';
            }
            elseif ($team_id == 3453) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-paragouais-podosfairou/';
            }
            elseif ($team_id == 3977) {
                $ftCompetitionTeamUrl = 'https://www.betarades.gr/ethniki-kosta-rika-podosfairou/';
            }
        }
        elseif( $site == 'pariurix' ) {
            if ($team_id == 4208) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/germania';
            }
            elseif ($team_id == 4408) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/scotia';
            }
            elseif ($team_id == 3269) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/ungaria';
            }
            elseif ($team_id == 4124) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/elvetia';
            }
            elseif ($team_id == 3637) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/spania';
            }
            elseif ($team_id == 3974) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/croatia';
            }
            elseif ($team_id == 3850) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/italia';
            }
            elseif ($team_id == 3826) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/albania';
            }
            elseif ($team_id == 4328) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/slovenia';
            }
            elseif ($team_id == 3781) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/danemarca';
            }
            elseif ($team_id == 3508) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/serbia';
            }
            elseif ($team_id == 4032) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/anglia';
            }
            elseif ($team_id == 4278) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/olanda';
            }
            elseif ($team_id == 4079) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/franta';
            }
            elseif ($team_id == 3400) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/polonia';
            }
            elseif ($team_id == 3621) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/austria';
            }
            elseif ($team_id == 4196) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/ucraina';
            }
            elseif ($team_id == 92) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/slovacia';
            }
            elseif ($team_id == 4347) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/belgia';
            }
            elseif ($team_id == 3746) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/romania';
            }
            elseif ($team_id == 3470) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/portugalia';
            }
            elseif ($team_id == 4166) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/cehia';
            }
            elseif ($team_id == 3758) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/georgia';
            }
            elseif ($team_id == 4235) {
                $ftCompetitionTeamUrl = 'https://pariurix.com/campionatul/euro/2024/echipe/turcia';
            }
        }
        return $ftCompetitionTeamUrl;
    }


    private function returnTeamIndex(array $teams, int $team_id)
    {
        $counter = 1;
        foreach ($teams as $team) {
            if ($team['id'] == $team_id) {
                return $counter;
            }
        $counter++;
        }
    }

    private function defaultHardcodedData(string $phase, string $site, int $competitionId)
    {
        $returnedArray = [];
        if ($competitionId == 1) {
            if ($phase == 'k-16') {
                $returnedArray[0]['h'] = 2;
                $returnedArray[0]['a'] = 6;
                $returnedArray[0]['score'] = null;
                $returnedArray[0]['cta'] = null;
                $returnedArray[0]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[0]['timestamp'] = 1719676800;

                $returnedArray[1]['h'] = 1;
                $returnedArray[1]['a'] = 10;
                $returnedArray[1]['score'] = null;
                $returnedArray[1]['cta'] = null;
                $returnedArray[1]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[1]['timestamp'] = 1719687600;

                $returnedArray[2]['h'] = 9;
                $returnedArray[2]['a'] = '3D/3E/3F';
                $returnedArray[2]['score'] = null;
                $returnedArray[2]['cta'] = null;
                $returnedArray[2]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[2]['timestamp'] = 1719763200;

                $returnedArray[3]['h'] = 5;
                $returnedArray[3]['a'] = '3A/3D/3E/3F';
                $returnedArray[3]['score'] = null;
                $returnedArray[3]['cta'] = null;
                $returnedArray[3]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[3]['timestamp'] = 1719774000;

                $returnedArray[4]['h'] = '2D';
                $returnedArray[4]['a'] = '2E';
                $returnedArray[4]['score'] = null;
                $returnedArray[4]['cta'] = null;
                $returnedArray[4]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[4]['timestamp'] = 1719849600;

                $returnedArray[5]['h'] = 21;
                $returnedArray[5]['a'] = '3A/3B/3C';
                $returnedArray[5]['score'] = null;
                $returnedArray[5]['cta'] = null;
                $returnedArray[5]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[5]['timestamp'] = 1719860400;

                $returnedArray[6]['h'] = '1E';
                $returnedArray[6]['a'] = '3A/3B/3C/3D';
                $returnedArray[6]['score'] = null;
                $returnedArray[6]['cta'] = null;
                $returnedArray[6]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[6]['timestamp'] = 1719936000;

                $returnedArray[7]['h'] = '1D';
                $returnedArray[7]['a'] = '2F';
                $returnedArray[7]['score'] = null;
                $returnedArray[7]['cta'] = null;
                $returnedArray[7]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[7]['timestamp'] = 1719946800;
            } elseif ($phase == 'k-8') {
                $returnedArray[0]['h'] = 5;
                $returnedArray[0]['a'] = 1;
                $returnedArray[0]['score'] = null;
                $returnedArray[0]['cta'] = null;
                $returnedArray[0]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[0]['timestamp'] = 1720195200;

                $returnedArray[1]['h'] = 21;
                $returnedArray[1]['a'] = 14;
                $returnedArray[1]['score'] = null;
                $returnedArray[1]['cta'] = null;
                $returnedArray[1]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[1]['timestamp'] = 1720206000;

                $returnedArray[2]['h'] = 9;
                $returnedArray[2]['a'] = 2;
                $returnedArray[2]['score'] = null;
                $returnedArray[2]['cta'] = null;
                $returnedArray[2]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[2]['timestamp'] = 1720281600;

                $returnedArray[3]['h'] = 'W43';
                $returnedArray[3]['a'] = 'W44';
                $returnedArray[3]['score'] = null;
                $returnedArray[3]['cta'] = null;
                $returnedArray[3]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[3]['timestamp'] = 1720292400;
            } elseif ($phase == 'semifinals') {
                $returnedArray[0]['h'] = 5;
                $returnedArray[0]['a'] = 14;
                $returnedArray[0]['score'] = null;
                $returnedArray[0]['cta'] = null;
                $returnedArray[0]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[0]['timestamp'] = 1720551600;

                $returnedArray[1]['h'] = 'W47';
                $returnedArray[1]['a'] = 'W48';
                $returnedArray[1]['score'] = null;
                $returnedArray[1]['cta'] = null;
                $returnedArray[1]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[1]['timestamp'] = 1720638000;
            } elseif ($phase == 'final') {
                $returnedArray[0]['h'] = 5;
                $returnedArray[0]['a'] = 'W50';
                $returnedArray[0]['score'] = null;
                $returnedArray[0]['cta'] = null;
                $returnedArray[0]['tv'] = ($site == 'pariurix') ? 'PRO TV' : 'ΕΡΤ1';
                $returnedArray[0]['timestamp'] = 1720983600;
            }
        }
        elseif ($competitionId == 2) {
            if ($phase == 'k-8') {
                $returnedArray[0]['h'] = 1;
                $returnedArray[0]['a'] = 6;
                $returnedArray[0]['score'] = null;
                $returnedArray[0]['cta'] = null;
                $returnedArray[0]['tv'] = ($site == 'pariurix') ? '' : 'ΑΝΤ1+';
                $returnedArray[0]['timestamp'] = 1720141200;

                $returnedArray[1]['h'] = 5;
                $returnedArray[1]['a'] = 2;
                $returnedArray[1]['score'] = null;
                $returnedArray[1]['cta'] = null;
                $returnedArray[1]['tv'] = ($site == 'pariurix') ? '' : 'ΑΝΤ1+';
                $returnedArray[1]['timestamp'] = 1720227600;

                $returnedArray[2]['h'] = 9;
                $returnedArray[2]['a'] = 10;
                $returnedArray[2]['score'] = null;
                $returnedArray[2]['cta'] = null;
                $returnedArray[2]['tv'] = ($site == 'pariurix') ? '' : 'ΑΝΤ1+';
                $returnedArray[2]['timestamp'] = 1720303200;

                $returnedArray[3]['h'] = 'TBD';
                $returnedArray[3]['a'] = 'TBD';
                $returnedArray[3]['score'] = null;
                $returnedArray[3]['cta'] = null;
                $returnedArray[3]['tv'] = ($site == 'pariurix') ? '' : 'ΑΝΤ1+';
                $returnedArray[3]['timestamp'] = 1720314000;
            } elseif ($phase == 'semifinals') {
                $returnedArray[0]['h'] = 1;
                $returnedArray[0]['a'] = 'TBD';
                $returnedArray[0]['score'] = null;
                $returnedArray[0]['cta'] = null;
                $returnedArray[0]['tv'] = ($site == 'pariurix') ? '' : 'ΑΝΤ1';
                $returnedArray[0]['timestamp'] = 1720569600;

                $returnedArray[1]['h'] = 'TBD';
                $returnedArray[1]['a'] = 'TBD';
                $returnedArray[1]['score'] = null;
                $returnedArray[1]['cta'] = null;
                $returnedArray[1]['tv'] = ($site == 'pariurix') ? '' : 'ΑΝΤ1';
                $returnedArray[1]['timestamp'] = 1720656000;
            } elseif ($phase == 'third-place-match') {
                $returnedArray[0]['h'] = 2;
                $returnedArray[0]['a'] = 'TBD';
                $returnedArray[0]['score'] = null;
                $returnedArray[0]['cta'] = null;
                $returnedArray[0]['tv'] = ($site == 'pariurix') ? '' : 'ΑΝΤ1+';
                $returnedArray[0]['timestamp'] = 1720915200;
            } elseif ($phase == 'final') {
                $returnedArray[0]['h'] = 1;
                $returnedArray[0]['a'] = 'TBD';
                $returnedArray[0]['score'] = null;
                $returnedArray[0]['cta'] = null;
                $returnedArray[0]['tv'] = ($site == 'pariurix') ? '' : 'ΑΝΤ1';
                $returnedArray[0]['timestamp'] = 1721001600;
            }
        }

        return $returnedArray;
    }
}
