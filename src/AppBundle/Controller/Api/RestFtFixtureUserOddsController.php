<?php

namespace AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\FOSRestController;

//  ("/api/v2/standings/{sportId}/{season}/{fetchStats}/{lan}", defaults={"sportId" = 304, "season" = null, "fetchStats" = "true", "lan" = "gr"})

/**
 * FtLeagueStandings REST API controller.
 *
 * @Route("/api/v3/report")
 */
class RestFtFixtureUserOddsController extends FOSRestController
{

  /**
   * Get report (user/sport total/analytics for period)
   *
   * @Method("GET")
   * @Rest\Get("/general/{type}/{dateFrom}/{dateTo}/{optionId}", defaults={"type" = "weekly", "dateFrom" = "null", "dateTo" = "null", "optionId" = "null"})
   */
  public function getReportAction(Request $request, $type, $dateFrom = null, $dateTo = null, $optionId = null) {
		$em     = $this->getDoctrine()->getManager();
		$result = array();

		// $dateFrom = '2017-10-24 03:00:00';
		// $dateTo 	= '2017-10-25 02:59:59';

		if ('user' === (string) $type) {
			$result = $em->getRepository('AppBundle:FtFixtureUserOdd')->getUsersReport($type, $dateFrom, $dateTo, $optionId);
		}
		else if ('sport' === (string) $type) {
			$result = $em->getRepository('AppBundle:FtFixtureUserOdd')->getSportsReport($type, $dateFrom, $dateTo, $optionId);
		}

		$em->clear();

		$view = $this->view($result, Response::HTTP_OK);
		return $view;
	}

  /**
   * Get report Dashboard
   *
   * @Method("GET")
   * @Rest\Get("/dashboard")
   */
  public function getDashboardAction(Request $request) {
		$em     = $this->getDoctrine()->getManager();
		$result = array();

		// $dateFrom 			= date('Y-m-01 03:00:00');
		// $dateTo 				= date('Y-m-t 02:59:59');
		// $lastMonthFrom 	= date('Y-m-01 03:00:00', strtotime('first day of -1 month'));
		// $lastMonthTo 		= date('Y-m-t 02:59:59', strtotime('first day of -1 month'));

		$dateFrom 			= date('Y-m-01 00:00:00');
		$dateTo 				= date('Y-m-t 23:59:59');
		$lastMonthFrom 	= date('Y-m-01 00:00:00', strtotime('first day of -1 month'));
		$lastMonthTo 		= date('Y-m-t 23:59:59', strtotime('first day of -1 month'));

		$top3UsersThisMonth 		= $em->getRepository('AppBundle:FtFixtureUserOdd')->getTop3Users($dateFrom, $dateTo);
		$top3UsersLastMonth 		= $em->getRepository('AppBundle:FtFixtureUserOdd')->getTop3Users($lastMonthFrom, $lastMonthTo);
		$top5SportsThisMonth 		= $em->getRepository('AppBundle:FtFixtureUserOdd')->getSportsThisMonth('top', $dateFrom, $dateTo);
		$worse5SportsThisMonth	= $em->getRepository('AppBundle:FtFixtureUserOdd')->getSportsThisMonth('worse', $dateFrom, $dateTo);

		if ($top3UsersThisMonth) $result['top3UsersThisMonth'] = $top3UsersThisMonth;
		if ($top3UsersLastMonth) $result['top3UsersLastMonth'] = $top3UsersLastMonth;
		if ($top5SportsThisMonth) $result['top5SportsThisMonth'] = $top5SportsThisMonth;
		if ($worse5SportsThisMonth) $result['worse5SportsThisMonth'] = $worse5SportsThisMonth;

		$em->clear();

		$view = $this->view($result, Response::HTTP_OK);
		return $view;
	}

  /**
   * Get report Dashboard
   *
   * @Method("GET")
   * @Rest\Get("/bonus/{year}")
   */
  public function getBonusAction(Request $request, $year) {
		$em     = $this->getDoctrine()->getManager();
		$result = array();

		$monthlyBonus = $em->getRepository('AppBundle:ReporterBonus')->getMonthlyBonus($year);

		if ($monthlyBonus) $result['monthlyBonus'] = $monthlyBonus;

		$em->clear();

		$view = $this->view($result, Response::HTTP_OK);
		return $view;
	}

}