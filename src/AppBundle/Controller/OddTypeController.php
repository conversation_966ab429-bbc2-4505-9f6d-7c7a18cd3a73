<?php

namespace AppBundle\Controller;

use App<PERSON><PERSON>le\Entity\OddType;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;use Symfony\Component\HttpFoundation\Request;

/**
 * Oddtype controller.
 *
 * @Route("admin/oddtype")
 */
class OddTypeController extends Controller
{
    /**
     * Lists all oddType entities.
     *
     * @Route("/", name="oddtype_index")
     * @Method("GET")
     */
    public function indexAction()
    {
        $em = $this->getDoctrine()->getManager();

        $oddTypes = $em->getRepository('AppBundle:OddType')->findAll();

        return $this->render('oddtype/index.html.twig', array(
            'oddTypes' => $oddTypes,
        ));
    }

    /**
     * Creates a new oddType entity.
     *
     * @Route("/new", name="oddtype_new")
     * @Method({"GET", "POST"})
     */
    public function newAction(Request $request)
    {
        $oddType = new Oddtype();
        $form = $this->createForm('AppBundle\Form\OddTypeType', $oddType);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($oddType);
            $em->flush();

            return $this->redirectToRoute('oddtype_show', array('id' => $oddType->getId()));
        }

        return $this->render('oddtype/new.html.twig', array(
            'oddType' => $oddType,
            'form' => $form->createView(),
        ));
    }

    /**
     * Finds and displays a oddType entity.
     *
     * @Route("/{id}", name="oddtype_show")
     * @Method("GET")
     */
    public function showAction(OddType $oddType)
    {
        $deleteForm = $this->createDeleteForm($oddType);

        return $this->render('oddtype/show.html.twig', array(
            'oddType' => $oddType,
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Displays a form to edit an existing oddType entity.
     *
     * @Route("/{id}/edit", name="oddtype_edit")
     * @Method({"GET", "POST"})
     */
    public function editAction(Request $request, OddType $oddType)
    {
        $deleteForm = $this->createDeleteForm($oddType);
        $editForm = $this->createForm('AppBundle\Form\OddTypeType', $oddType);
        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $this->getDoctrine()->getManager()->flush();

            return $this->redirectToRoute('oddtype_edit', array('id' => $oddType->getId()));
        }

        return $this->render('oddtype/edit.html.twig', array(
            'oddType' => $oddType,
            'edit_form' => $editForm->createView(),
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Deletes a oddType entity.
     *
     * @Route("/{id}", name="oddtype_delete")
     * @Method("DELETE")
     */
    public function deleteAction(Request $request, OddType $oddType)
    {
        $form = $this->createDeleteForm($oddType);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->remove($oddType);
            $em->flush();
        }

        return $this->redirectToRoute('oddtype_index');
    }

    /**
     * Creates a form to delete a oddType entity.
     *
     * @param OddType $oddType The oddType entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createDeleteForm(OddType $oddType)
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('oddtype_delete', array('id' => $oddType->getId())))
            ->setMethod('DELETE')
            ->getForm()
        ;
    }
}
