<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use AppBundle\Entity\FtLeagueStandingsOption;
use AppBundle\Form\FtLeagueStandingsOptionType;

/**
 * FtLeagueStandingsOption controller.
 *
 * @Route("/admin/ftleaguestandingsoption")
 */
class FtLeagueStandingsOptionController extends Controller
{
    /**
     * Lists all FtLeagueStandingsOption entities.
     *
     * @Route("/", name="ftleaguestandingsoption_index")
     * @Method("GET")
     */
    public function indexAction()
    {
        $em = $this->getDoctrine()->getManager();

        $ftLeagueStandingsOptions = $em->getRepository('AppBundle:FtLeagueStandingsOption')->findAll();

        return $this->render('ftleaguestandingsoption/index.html.twig', array(
            'ftLeagueStandingsOptions' => $ftLeagueStandingsOptions,
        ));
    }

    /**
     * Creates a new FtLeagueStandingsOption entity.
     *
     * @Route("/new", name="ftleaguestandingsoption_new")
     * @Method({"GET", "POST"})
     */
    public function newAction(Request $request)
    {
        $ftLeagueStandingsOption = new FtLeagueStandingsOption();
        $form = $this->createForm('AppBundle\Form\FtLeagueStandingsOptionType', $ftLeagueStandingsOption);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($ftLeagueStandingsOption);
            $em->flush();

            return $this->redirectToRoute('ftleaguestandingsoption_show', array('id' => $ftLeagueStandingsOption->getId()));
        }

        return $this->render('ftleaguestandingsoption/new.html.twig', array(
            'ftLeagueStandingsOption' => $ftLeagueStandingsOption,
            'form' => $form->createView(),
        ));
    }

    /**
     * Finds and displays a FtLeagueStandingsOption entity.
     *
     * @Route("/{id}", name="ftleaguestandingsoption_show")
     * @Method("GET")
     */
    public function showAction(FtLeagueStandingsOption $ftLeagueStandingsOption)
    {
        $deleteForm = $this->createDeleteForm($ftLeagueStandingsOption);

        return $this->render('ftleaguestandingsoption/show.html.twig', array(
            'ftLeagueStandingsOption' => $ftLeagueStandingsOption,
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Displays a form to edit an existing FtLeagueStandingsOption entity.
     *
     * @Route("/{id}/edit", name="ftleaguestandingsoption_edit")
     * @Method({"GET", "POST"})
     */
    public function editAction(Request $request, FtLeagueStandingsOption $ftLeagueStandingsOption)
    {
        $deleteForm = $this->createDeleteForm($ftLeagueStandingsOption);
        $editForm = $this->createForm('AppBundle\Form\FtLeagueStandingsOptionType', $ftLeagueStandingsOption);
        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->persist($ftLeagueStandingsOption);
            $em->flush();

            return $this->redirectToRoute('ftleaguestandingsoption_edit', array('id' => $ftLeagueStandingsOption->getId()));
        }

        return $this->render('ftleaguestandingsoption/edit.html.twig', array(
            'ftLeagueStandingsOption' => $ftLeagueStandingsOption,
            'edit_form' => $editForm->createView(),
            'delete_form' => $deleteForm->createView(),
        ));
    }

    /**
     * Deletes a FtLeagueStandingsOption entity.
     *
     * @Route("/{id}", name="ftleaguestandingsoption_delete")
     * @Method("DELETE")
     */
    public function deleteAction(Request $request, FtLeagueStandingsOption $ftLeagueStandingsOption)
    {
        $form = $this->createDeleteForm($ftLeagueStandingsOption);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $em->remove($ftLeagueStandingsOption);
            $em->flush();
        }

        return $this->redirectToRoute('ftleaguestandingsoption_index');
    }

    /**
     * Creates a form to delete a FtLeagueStandingsOption entity.
     *
     * @param FtLeagueStandingsOption $ftLeagueStandingsOption The FtLeagueStandingsOption entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createDeleteForm(FtLeagueStandingsOption $ftLeagueStandingsOption)
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('ftleaguestandingsoption_delete', array('id' => $ftLeagueStandingsOption->getId())))
            ->setMethod('DELETE')
            ->getForm()
        ;
    }
}
