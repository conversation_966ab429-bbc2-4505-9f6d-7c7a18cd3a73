<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\BufferedOutput;
use Symfony\Component\HttpFoundation\Response;

/**
 * Tools controller.
 *
 * @Route("/admin/tools")
 */
class ToolsController extends Controller
{

	// TODO must move the following Actions to a service

	/**
		* Generate season command
		*
		* @Route("/generate-season", name="checker_generateseason1", options={"expose": true})
		* @Method("GET")
		*/
	public function generateSeasonAction() {
		$kernel = $this->get('kernel');
		$application = new Application($kernel);
		$application->setAutoExit(false);

		$input = new ArrayInput(array(
			'command'	=> 'wbs:generate:season',
			'--debug'	=> true,
		));

		$output = new BufferedOutput();
		$application->run($input, $output);
		$content = $output->fetch();

		return new Response($content);
	}

	/**
		* Generate leagues command
		*
		* @Route("/generate-leagues", name="checker_generateleagues1", options={"expose": true})
		* @Method("GET")
		*/
	public function generateLeaguesAction() {
		$kernel = $this->get('kernel');
		$application = new Application($kernel);
		$application->setAutoExit(false);

		$input = new ArrayInput(array(
			'command'	=> 'wbs:feed:parse',
			'name'		=> 'leagues',
		));

		$output = new BufferedOutput();
		$application->run($input, $output);

		$content = $output->fetch();

		return new Response($content);
	}
}
