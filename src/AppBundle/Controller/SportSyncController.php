<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\FOSRestController;

/**
 * Test controller.
 *
 * @Route("/admin/sync")
 */
class SportSyncController extends FOSRestController
{

  /**
   * Sync Sports with all Domain Sports in WP
   *
   * @Method("GET")
   * @Rest\Get("/{message}", name="sportsync_index")
   */
  public function indexAction($message = '') {
    $em = $this->getDoctrine()->getManager();
    $domains = $em->getRepository('AppBundle:Domain')->findBy(array('syncOnlySports' => true));

    $em->clear();
		return $this->render('sportsync/index.html.twig', array(
      'domains' => $domains,
      'message' => $message,
    ));
  }

  /**
   * Sync Sports with all Domain Sports in WP
   *
   * @Method("GET")
   * @Rest\Get("/do/{domainId}", name="sportsync_sync")
   */
  public function syncAction($domainId) {
    $em = $this->getDoctrine()->getManager();
    $helper = $this->container->get("wbs.globalhelper.service");

    if ('all' === $domainId) {
      $domains = $em->getRepository('AppBundle:Domain')->findBy(array('syncOnlySports' => true));

      foreach ($domains as $domain) {
        if ($domain) {
          // get domain connection
          $connection = $helper->getDomainConnection($domain);

          if (!$connection) continue;

          $this->startSyncing($connection, $domain, $helper, $em);
          $connection->close();
        }
      }
    }
    else {
      $domain = $em->getRepository('AppBundle:Domain')->findOneById($domainId);

      if ($domain) {
        // get domain connection
        $connection = $helper->getDomainConnection($domain);

        if ($connection) {
          // do something here
          $this->startSyncing($connection, $domain, $helper, $em);
          $connection->close();
        }
      }
    }

    // return new Response('syncing done');
    return $this->redirectToRoute('sportsync_index', array('message' => 'syncing done'));
  }

  // OBSOLETE
  private function getWPSportIds($connection, $prefix) {
    $sql = "SELECT id FROM ".$prefix."wbs_sports";
    $stmt = $connection->prepare($sql);
    $stmt->execute();

    // fetchColumn returns false if none exist, so convert to empty array
    return $stmt->fetchAll(\PDO::FETCH_COLUMN) ?: array();
  }

	/**
   * Create WP Sport (insert/update) for domain and locale
   */
  private function createWPSport($connection, $helper, $prefix, $sport, $locale, $showAsRegion, $slug) {
    $em = $this->getDoctrine()->getManager();

    $translationArr = $em->getRepository('AppBundle:Sport')->findTranslationByTypeAndLocale($sport->getId(), 'normal', $locale);
    // fallback to english title & description
    if (!$translationArr) {
      $translationArr = $em->getRepository('AppBundle:Sport')->findTranslationByTypeAndLocale($sport->getId(), 'normal', 'en');
    } 

    $transRestArr = $em->getRepository('AppBundle:Sport')->findRestOfTranslationsByLocale($sport->getId(), $locale);
    // fallback to english translations
    if (!$transRestArr) {
      $transRestArr = $em->getRepository('AppBundle:Sport')->findRestOfTranslationsByLocale($sport->getId(), 'en');
    }

    $leagueFlag = $em->getRepository('AppBundle:FtLeagueFlag')->findOneBy(array('sport' => $sport->getId()));

    // cvyz Temporary image_name and competition_number should only be changed in domain level, and not by the feed
    // $imageName = null;
    // $competitionNumber = null;

    // $imageName = isset($leagueFlag) && $leagueFlag->getImageName() != '' ? '"'.$leagueFlag->getImageName().'"' : 'null';
    // if ($locale === 'el') {
    //   $competitionNumber = isset($leagueFlag) && $leagueFlag->getCompetitionNumberGr() != '' ? '"'.$leagueFlag->getCompetitionNumberGr().'"' : 'null';
    // }
    // else {
    //   $competitionNumber = isset($leagueFlag) && $leagueFlag->getCompetitionNumberGr() != ''  ? '"'.$leagueFlag->getCompetitionNumberEn().'"' : 'null';
    // }

    // if sport has no title/description DO NOT CREATE
    if (!$translationArr) return;

    $title          = $translationArr[0]['title'];
    $description    = $translationArr[0]['description'];
    $imageName      = $sport->getImageName() ?: null;
    $createdAt      = $helper->getCurrentTimestampDate();
    $syncedAt       = $helper->getCurrentTimestampDate();
    $parentId       = $sport->getParent() ? $sport->getParent()->getId(): 'null';
    $isSport        = $sport->getIsSport() ?: 0;
    $isRegion       = $sport->getIsRegion() ?: 0;
    $isCompetition  = $sport->getIsCompetition() ?: 0;
    $isAdditional   = $sport->getIsAdditional() ?: 0;
    $hasAdditionals = $sport->getHasAdditionals() ?: 0;

    try {
      $sql = "INSERT INTO ".$prefix."wbs_sports (id, parent_id, title, description, slug, is_sport, is_region, is_competition, is_additional, has_additionals, show_as_region, image_name, tips_from, tips_to, created_at, synced_at)
       VALUES (
       ".$sport->getId().",
       ".$parentId.",
       '".addslashes($title)."',
       '".addslashes($description)."',
       '".$slug."',
       ".$isSport.",
       ".$isRegion.",
       ".$isCompetition.",
       ".$isAdditional.",
       ".$hasAdditionals.",
       ".$showAsRegion.",
       ".($imageName ? "'".$imageName."'" : 'null' ).",
       '".$helper->getCurrentTimestampDate($sport->getTipsDateFrom())."',
       '".$helper->getCurrentTimestampDate($sport->getTipsDateTo())."',
       '".$createdAt."',
       '".$syncedAt."') ON DUPLICATE KEY UPDATE title = VALUES(title), description = VALUES(description), slug = VALUES(slug), parent_id = VALUES(parent_id), is_sport = VALUES(is_sport), is_region = VALUES(is_region), is_competition = VALUES(is_competition), is_additional = VALUES(is_additional), has_additionals = VALUES(has_additionals), updated_at = '$syncedAt', synced_at = '$syncedAt' ;"
      ;
//       '".$syncedAt."') ON DUPLICATE KEY UPDATE title = VALUES(title), description = VALUES(description), slug = VALUES(slug), parent_id = VALUES(parent_id), is_sport = VALUES(is_sport), is_region = VALUES(is_region), is_competition = VALUES(is_competition), is_additional = VALUES(is_additional), has_additionals = VALUES(has_additionals), show_as_region = VALUES(show_as_region), image_name = VALUES(image_name), updated_at = '$syncedAt', synced_at = '$syncedAt' ;"

      $stmt = $connection->prepare($sql);
      $stmt->execute();

      // now add translations
      // do not add translations for NORMAL
      // foreach ($translationArr as $single) {
      //   $this->insertTranslations($connection, $prefix, $sport->getId(), $single);
      // }

      foreach ($transRestArr as $single) {
        $this->insertTranslations($connection, $prefix, $sport->getId(), $single, strtoupper($single['type']));
      }

    }
    catch (\Exception $e) {
      echo '<pre>';
      var_dump($e->getMessage());
      echo '</pre>';
      die();
    }

    $em->clear();
    return 'done';
  }

  private function insertTranslations($connection, $prefix, $sportId, $trans, $type = 'NORMAL') {
    if ($trans['title'] === '' && $trans['description'] === '') return;

    $sqlInsert = "INSERT INTO ".$prefix."wbs_sport_names (sport_id, type, title, description) VALUES ($sportId, '$type', '".addslashes($trans['title'])."', '".addslashes($trans['description'])."') ";
    $stmt = $connection->prepare($sqlInsert);
    $stmt->execute();
  }

  private function startSyncing($connection, $domain, $helper, $em) {
    // find all sports and sync with domain
    $sports = $em->getRepository('AppBundle:Sport')->findAll();

    if ($sports) {
      foreach ($sports as $sport) {
        $sportId = $sport->getId();
        $showAsRegion = 0;
        //$domainSport = $em->getRepository('AppBundle:DomainSport')->findOneBySport($sport);
        $domainSport = $em->getRepository('AppBundle:DomainSport')->findOneBy(array('domain' => $domain, 'sport' => $sport));
        $domainSportSlug = $em->getRepository('AppBundle:DomainSportSlug')->findOneBy(array('domain' => $domain, 'sport' => $sport));

        if ($domainSport) {
          $showAsRegion = $domainSport->getShowAsRegion() ? 1 : 0;
        }

        if ($domainSportSlug) {
          $slug = $domainSportSlug->getSlug();
        }

        $this->createWPSport($connection, $helper, $domain->getDbPrefix(), $sport, $domain->getLanguage()->getLocale(), $showAsRegion, $slug);
      }
    }
  }
}
