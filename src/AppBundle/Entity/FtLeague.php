<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * FtLeague
 *
 * @ORM\Table(name="ft_league")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtLeagueRepository")
 */
class FtLeague
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="l_id", type="string", length=10, unique=true)
     */
    private $lId;

    /**
     * @var string
     *
     * @ORM\Column(name="name_en", type="string", length=255, nullable=true)
     */
    private $nameEn;

    /**
     * @var string
     *
     * @ORM\Column(name="name_gr", type="string", length=255, nullable=true)
     */
    private $nameGr;

    /**
     * @var string
     *
     * @ORM\Column(name="name_ro", type="string", length=255, nullable=true)
     */
    private $nameRo;

    /**
     * @var string
     *
     * @ORM\Column(name="c_name_en", type="string", length=255, nullable=true)
     */
    private $cNameEn;

    /**
     * @var string
     *
     * @ORM\Column(name="c_name_gr", type="string", length=255, nullable=true)
     */
    private $cNameGr;

    /**
     * @ORM\ManyToOne(targetEntity="Country", inversedBy="ftLeagues")
     * @ORM\JoinColumn(name="country_id", referencedColumnName="id")
     */
    private $country;

   /**
    * @var \DateTime $createdAt
    *
    * @Gedmo\Timestampable(on="create")
    * @ORM\Column(name="created_at", type="datetime")
    */
    private $createdAt;

    /**
     * @var \DateTime $updatedAt
     *
     * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
     * @ORM\Column(name="updated_at", type="datetime", nullable=true)
     */
    private $updatedAt;


    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set lId
     *
     * @param string $lId
     *
     * @return FtLeague
     */
    public function setLId($lId)
    {
        $this->lId = $lId;

        return $this;
    }

    /**
     * Get lId
     *
     * @return string
     */
    public function getLId()
    {
        return $this->lId;
    }

    /**
     * Set nameEn
     *
     * @param string $nameEn
     *
     * @return FtLeague
     */
    public function setNameEn($nameEn)
    {
        $this->nameEn = $nameEn;

        return $this;
    }

    /**
     * Get nameEn
     *
     * @return string
     */
    public function getNameEn()
    {
        return $this->nameEn;
    }

    /**
     * Set nameGr
     *
     * @param string $nameGr
     *
     * @return FtLeague
     */
    public function setNameGr($nameGr)
    {
        $this->nameGr = $nameGr;

        return $this;
    }

    /**
     * Get nameGr
     *
     * @return string
     */
    public function getNameGr()
    {
        return $this->nameGr;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return FtLeague
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }


    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return FtLeague
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set country
     *
     * @param \AppBundle\Entity\Country $country
     *
     * @return FtLeague
     */
    public function setCountry(\AppBundle\Entity\Country $country = null)
    {
        $this->country = $country;

        return $this;
    }

    /**
     * Get country
     *
     * @return \AppBundle\Entity\Country
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * Set cNameEn
     *
     * @param string $cNameEn
     *
     * @return FtLeague
     */
    public function setCNameEn($cNameEn)
    {
        $this->cNameEn = $cNameEn;

        return $this;
    }

    /**
     * Get cNameEn
     *
     * @return string
     */
    public function getCNameEn()
    {
        return $this->cNameEn;
    }

    /**
     * Set cNameGr
     *
     * @param string $cNameGr
     *
     * @return FtLeague
     */
    public function setCNameGr($cNameGr)
    {
        $this->cNameGr = $cNameGr;

        return $this;
    }

    /**
     * Get cNameGr
     *
     * @return string
     */
    public function getCNameGr()
    {
        return $this->cNameGr;
    }

    /**
     * Set nameRo
     *
     * @param string $nameRo
     *
     * @return FtLeague
     */
    public function setNameRo($nameRo)
    {
        $this->nameRo = $nameRo;

        return $this;
    }

    /**
     * Get nameRo
     *
     * @return string
     */
    public function getNameRo()
    {
        return $this->nameRo;
    }
}
