<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * FtLeagueFlag
 *
 * @ORM\Table(name="ft_league_flag")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtLeagueFlagRepository")
 */
class FtLeagueFlag
{
  /**
   * @var int
   *
   * @ORM\Column(name="id", type="integer")
   * @ORM\Id
   * @ORM\GeneratedValue(strategy="AUTO")
   */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="league_id", type="string", length=10, nullable=true)
     */
    private $leagueId;

    /**
     * @var string
     *
     * @ORM\Column(name="code", type="string", length=4, nullable=true)
     */
    private $code;

    /**
     * @var string
     *
     * @ORM\Column(name="competition_number_en", type="string", length=4, nullable=true)
     */
    private $competitionNumberEn;

    /**
     * @var string
     *
     * @ORM\Column(name="competition_number_gr", type="string", length=4, nullable=true)
     */
    private $competitionNumberGr;

    /**
     * @var string
     *
     * @ORM\Column(name="image_name", type="string", length=32, nullable=true)
     */
    private $imageName;

    /**
     * @ORM\OneToOne(targetEntity="Sport", inversedBy="ftLeagueFlag")
     * @ORM\JoinColumn(name="sport_id", referencedColumnName="id")
     */
    private $sport;

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set leagueId
     *
     * @param string $leagueId
     *
     * @return FtLeagueFlag
     */
    public function setLeagueId($leagueId)
    {
        $this->leagueId = $leagueId;

        return $this;
    }

    /**
     * Get leagueId
     *
     * @return string
     */
    public function getLeagueId()
    {
        return $this->leagueId;
    }

    /**
     * Set code
     *
     * @param string $code
     *
     * @return FtLeagueFlag
     */
    public function setCode($code)
    {
        $this->code = $code;

        return $this;
    }

    /**
     * Get code
     *
     * @return string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * Set competitionNumberEn
     *
     * @param string $competitionNumberEn
     *
     * @return FtLeagueFlag
     */
    public function setCompetitionNumberEn($competitionNumberEn)
    {
        $this->competitionNumberEn = $competitionNumberEn;

        return $this;
    }

    /**
     * Get competitionNumberEn
     *
     * @return string
     */
    public function getCompetitionNumberEn()
    {
        return $this->competitionNumberEn;
    }

    /**
     * Set competitionNumberGr
     *
     * @param string $competitionNumberGr
     *
     * @return FtLeagueFlag
     */
    public function setCompetitionNumberGr($competitionNumberGr)
    {
        $this->competitionNumberGr = $competitionNumberGr;

        return $this;
    }

    /**
     * Get competitionNumberGr
     *
     * @return string
     */
    public function getCompetitionNumberGr()
    {
        return $this->competitionNumberGr;
    }

    /**
     * Set imageName
     *
     * @param string $imageName
     *
     * @return FtLeagueFlag
     */
    public function setImageName($imageName)
    {
        $this->imageName = $imageName;

        return $this;
    }

    /**
     * Get imageName
     *
     * @return string
     */
    public function getImageName()
    {
        return $this->imageName;
    }

    /**
     * Set sport
     *
     * @param \AppBundle\Entity\Sport $sport
     *
     * @return FtLeagueFlag
     */
    public function setSport(\AppBundle\Entity\Sport $sport = null)
    {
        $this->sport = $sport;

        return $this;
    }

    /**
     * Get sport
     *
     * @return \AppBundle\Entity\Sport
     */
    public function getSport()
    {
        return $this->sport;
    }
}
