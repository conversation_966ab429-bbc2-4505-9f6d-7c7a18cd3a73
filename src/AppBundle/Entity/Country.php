<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * Country
 *
 * @ORM\Table(name="country")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\CountryRepository")
 */
class Country
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="c_id", type="string", length=5, unique=true)
     */
    private $cId;

    /**
     * @var string
     *
     * @ORM\Column(name="code", type="string", length=3, nullable=true)
     */
    private $code;

    /**
     * @var string
     *
     * @ORM\Column(name="name_en", type="string", length=100, nullable=true)
     */
    private $nameEn;

    /**
     * @var string
     *
     * @ORM\Column(name="name_gr", type="string", length=100, nullable=true)
     */
    private $nameGr;

    /**
     * @var string
     *
     * @ORM\Column(name="name_ro", type="string", length=100, nullable=true)
     */
    private $nameRo;

    /**
     * @var string
     *
     * @ORM\Column(name="c_name_en", type="string", length=100, nullable=true)
     */
    private $cNameEn;

    /**
     * @var string
     *
     * @ORM\Column(name="c_name_gr", type="string", length=100, nullable=true)
     */
    private $cNameGr;

    /**
     * @var bool
     *
     * @ORM\Column(name="is_continent", type="boolean")
     */
    private $isContinent;

    /**
     * @var string
     *
     * @ORM\Column(name="continent", type="string", length=10, nullable=true)
     */
    private $continent;

    /**
     * @ORM\OneToMany(targetEntity="FtLeague", mappedBy="country")
     */
    private $ftLeagues;

    /**
     * @ORM\OneToMany(targetEntity="FtTeam", mappedBy="country")
     */
    private $ftTeams;

    /**
     * @ORM\OneToMany(targetEntity="City", mappedBy="country")
     */
    private $cities;

    /**
     * @var \DateTime $createdAt
     *
     * @Gedmo\Timestampable(on="create")
     * @ORM\Column(name="created_at", type="datetime")
     */
    private $createdAt;

    /**
     * @var \DateTime $updatedAt
     *
     * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
     * @ORM\Column(name="updated_at", type="datetime", nullable=true)
     */
    private $updatedAt;


    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set cId
     *
     * @param string $cId
     *
     * @return Country
     */
    public function setCId($cId)
    {
        $this->cId = $cId;

        return $this;
    }

    /**
     * Get cId
     *
     * @return string
     */
    public function getCId()
    {
        return $this->cId;
    }

    /**
     * Set nameEn
     *
     * @param string $nameEn
     *
     * @return Country
     */
    public function setNameEn($nameEn)
    {
        $this->nameEn = $nameEn;

        return $this;
    }

    /**
     * Get nameEn
     *
     * @return string
     */
    public function getNameEn()
    {
        return $this->nameEn;
    }

    /**
     * Set nameGr
     *
     * @param string $nameGr
     *
     * @return Country
     */
    public function setNameGr($nameGr)
    {
        $this->nameGr = $nameGr;

        return $this;
    }

    /**
     * Get nameGr
     *
     * @return string
     */
    public function getNameGr()
    {
        return $this->nameGr;
    }

    /**
     * Set isContinent
     *
     * @param boolean $isContinent
     *
     * @return Country
     */
    public function setIsContinent($isContinent)
    {
        $this->isContinent = $isContinent;

        return $this;
    }

    /**
     * Get isContinent
     *
     * @return bool
     */
    public function getIsContinent()
    {
        return $this->isContinent;
    }

    /**
     * Set continent
     *
     * @param string $continent
     *
     * @return Country
     */
    public function setContinent($continent)
    {
        $this->continent = $continent;

        return $this;
    }

    /**
     * Get continent
     *
     * @return string
     */
    public function getContinent()
    {
        return $this->continent;
    }
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->ftLeagues = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Add ftLeague
     *
     * @param \AppBundle\Entity\FtLeague $ftLeague
     *
     * @return Country
     */
    public function addFtLeague(\AppBundle\Entity\FtLeague $ftLeague)
    {
        $this->ftLeagues[] = $ftLeague;

        return $this;
    }

    /**
     * Remove ftLeague
     *
     * @param \AppBundle\Entity\FtLeague $ftLeague
     */
    public function removeFtLeague(\AppBundle\Entity\FtLeague $ftLeague)
    {
        $this->ftLeagues->removeElement($ftLeague);
    }

    /**
     * Get ftLeagues
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getFtLeagues()
    {
        return $this->ftLeagues;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return Country
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return Country
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set code
     *
     * @param string $code
     *
     * @return Country
     */
    public function setCode($code)
    {
        $this->code = $code;

        return $this;
    }

    /**
     * Get code
     *
     * @return string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * Set cNameEn
     *
     * @param string $cNameEn
     *
     * @return Country
     */
    public function setCNameEn($cNameEn)
    {
        $this->cNameEn = $cNameEn;

        return $this;
    }

    /**
     * Get cNameEn
     *
     * @return string
     */
    public function getCNameEn()
    {
        return $this->cNameEn;
    }

    /**
     * Set cNameGr
     *
     * @param string $cNameGr
     *
     * @return Country
     */
    public function setCNameGr($cNameGr)
    {
        $this->cNameGr = $cNameGr;

        return $this;
    }

    /**
     * Get cNameGr
     *
     * @return string
     */
    public function getCNameGr()
    {
        return $this->cNameGr;
    }

    /**
     * Add ftTeam
     *
     * @param \AppBundle\Entity\FtTeam $ftTeam
     *
     * @return Country
     */
    public function addFtTeam(\AppBundle\Entity\FtTeam $ftTeam)
    {
        $this->ftTeams[] = $ftTeam;

        return $this;
    }

    /**
     * Remove ftTeam
     *
     * @param \AppBundle\Entity\FtTeam $ftTeam
     */
    public function removeFtTeam(\AppBundle\Entity\FtTeam $ftTeam)
    {
        $this->ftTeams->removeElement($ftTeam);
    }

    /**
     * Get ftTeams
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getFtTeams()
    {
        return $this->ftTeams;
    }

    /**
     * Add Cities
     *
     * @param \AppBundle\Entity\City $city
     *
     * @return Country
     */
    public function addCity(\AppBundle\Entity\City $city)
    {
        $this->cities[] = $city;

        return $this;
    }

    /**
     * Remove city
     *
     * @param \AppBundle\Entity\City $city
     */
    public function removeCity(\AppBundle\Entity\City $city)
    {
        $this->cities->removeElement($city);
    }

    /**
     * Get cities
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getCities()
    {
        return $this->cities;
    }

    /**
     * Set nameRo
     *
     * @param string $nameRo
     *
     * @return Country
     */
    public function setNameRo($nameRo)
    {
        $this->nameRo = $nameRo;

        return $this;
    }

    /**
     * Get nameRo
     *
     * @return string
     */
    public function getNameRo()
    {
        return $this->nameRo;
    }
}
