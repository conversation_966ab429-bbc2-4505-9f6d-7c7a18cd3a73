<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * FtTeamPlayer
 *
 * @ORM\Table(name="ft_team_player")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtTeamPlayerRepository")
 */
class FtTeamPlayer
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer", options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\Column(name="team_id", type="integer", options={"unsigned"=true})
     */
    private $teamId;

    /**
     * @var int
     *
     * @ORM\Column(name="position", type="smallint", options={"unsigned"=true})
     */
    private $position;

    /**
     * @var string
     *
     * @ORM\Column(name="name_el", type="string", length=191)
     */
    private $nameEl;

    /**
     * @var string
     *
     * @ORM\Column(name="name_en", type="string", length=191)
     */
    private $nameEn;

    /**
     * @var string
     *
     * @ORM\Column(name="name_ro", type="string", length=191, nullable=true)
     */
    private $nameRo;

     /**
      * @var \DateTime $createdAt
			*
			* @Gedmo\Timestampable(on="create")
			* @ORM\Column(name="created_at", type="datetime")
			*/
		private $createdAt;
  
    /**
     * @var \DateTime $updatedAt
     *
     * @Gedmo\Timestampable(on="change", field={"teamId","nameEl","nameEn","nameRo"})
     * @ORM\Column(name="updated_at", type="datetime", nullable=true)
     */
		private $updatedAt;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set teamId
     *
     * @param integer $teamId
     *
     * @return FtTeamPlayer
     */
    public function setTeamId($teamId)
    {
        $this->teamId = $teamId;

        return $this;
    }

    /**
     * Get teamId
     *
     * @return int
     */
    public function getTeamId()
    {
        return $this->teamId;
    }

    /**
     * Set position
     *
     * @param integer $position
     *
     * @return FtTeamPlayer
     */
    public function setPosition($position)
    {
        $this->position = $position;

        return $this;
    }

    /**
     * Get position
     *
     * @return int
     */
    public function getPosition()
    {
        return $this->position;
    }

    /**
     * Set nameEl
     *
     * @param string $nameEl
     *
     * @return FtTeamPlayer
     */
    public function setNameEl($nameEl)
    {
        $this->nameEl = $nameEl;

        return $this;
    }

    /**
     * Get nameEl
     *
     * @return string
     */
    public function getNameEl()
    {
        return $this->nameEl;
    }

    /**
     * Set nameEn
     *
     * @param string $nameEn
     *
     * @return FtTeamPlayer
     */
    public function setNameEn($nameEn)
    {
        $this->nameEn = $nameEn;

        return $this;
    }

    /**
     * Get nameEn
     *
     * @return string
     */
    public function getNameEn()
    {
        return $this->nameEn;
    }

    /**
     * Set nameRo
     *
     * @param string $nameRo
     *
     * @return FtTeamPlayer
     */
    public function setNameRo($nameRo)
    {
        $this->nameRo = $nameRo;

        return $this;
    }

    /**
     * Get nameRo
     *
     * @return string
     */
    public function getNameRo()
    {
        return $this->nameRo;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return FtTeamPlayer
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return FtTeamPlayer
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }
}
