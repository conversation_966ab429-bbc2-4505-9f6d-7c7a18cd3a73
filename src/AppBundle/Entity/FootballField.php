<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * FootballField
 *
 * @ORM\Table(name="football_field",
 *    uniqueConstraints={
 *        @ORM\UniqueConstraint(name="football_field_unique", columns={"name", "weather_api_id"})
 *    }
 * )
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FootballFieldRepository")
 */
class FootballField
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer", options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    private $name;

    /**
     * @var int
     *
     * @ORM\Column(name="weather_api_id", type="integer", options={"unsigned"=true})
     */
    private $weatherApiId;

    /**
     * @ORM\OneToMany(targetEntity="TeamFootballField", mappedBy="footballField", cascade={"persist"})
     */
    private $teamFootballFields;

    /**
     * @var \DateTime $createdAt
     *
     * @Gedmo\Timestampable(on="create")
     * @ORM\Column(name="created_at", type="datetime")
     */
    private $createdAt;

    /**
     * @var \DateTime $updatedAt
     *
     * @Gedmo\Timestampable(on="change", field={"name"})
     * @ORM\Column(name="updated_at", type="datetime", nullable=true)
     */
    private $updatedAt;

    public function __construct()
    {
        $this->teamFootballFields = new \Doctrine\Common\Collections\ArrayCollection();
    }
    
    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set weatherApiId
     *
     * @param integer $weatherApiId
     *
     * @return FootballField
     */
    public function setWeatherApiId($weatherApiId)
    {
        $this->weatherApiId = $weatherApiId;

        return $this;
    }

    /**
     * Get weatherApiId
     *
     * @return int
     */
    public function getWeatherApiId()
    {
        return $this->weatherApiId;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return FootballField
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return Country
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return Country
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Add teamFootballField
     *
     * @param \AppBundle\Entity\TeamFootballField $teamFootballField
     *
     * @return FootballField
     */
    public function addTeamFootballField(\AppBundle\Entity\TeamFootballField $teamFootballField)
    {
        $this->teamFootballFields[] = $teamFootballField;

        return $this;
    }

    /**
     * Remove teamFootballField
     *
     * @param \AppBundle\Entity\TeamFootballField $teamFootballField
     */
    public function removeTeamFootballField(\AppBundle\Entity\TeamFootballField $teamFootballField)
    {
        $this->teamFootballFields->removeElement($teamFootballField);
    }

    /**
     * Get teamFootballField
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getTeamFootballFields()
    {
        return $this->teamFootballFields;
    }
}
