<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

 /**
  * FtFixture
  *
* @ORM\Table(name="ft_fixture", indexes={@ORM\Index(name="search_idx", columns={"league", "season", "match_datetime"}), @ORM\Index(name="generatepost_idx", columns={"status", "sport_id", "match_datetime"}), @ORM\Index(name="homefinished_idx", columns={"status", "h_team"}), @ORM\Index(name="awayfinished_idx", columns={"status", "a_team"}), @ORM\Index(name="versushistory_idx", columns={"status", "a_team", "h_team"}), @ORM\Index(name="matchdatetime_idx", columns={"match_datetime"}), @ORM\Index(name="stats02_idx", columns={"sport_id", "season"}), @ORM\Index(name="search_idx_sport", columns={"sport_id"})}, uniqueConstraints={@ORM\UniqueConstraint(columns={"f_id"})})
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtFixtureRepository")
 */

class FtFixture
{
  /**
   ** @var int
   *
   ** @ORM\Column(name="id", type="integer")
   ** @ORM\Id
   ** @ORM\GeneratedValue(strategy="AUTO")
   */
//   private $id;

  /**
   * @var int
   * @ORM\Column(name="sport_id", type="integer", nullable=true)
   */
  private $sportId;

  /**
   * @var string
   *
   * @ORM\Column(name="f_id", type="string", length=32, unique=true)
   * @ORM\id
   * @ORM\GeneratedValue(strategy="NONE")
   */
  private $fId;

  /**
   * @var datetime
   *
   * @ORM\Column(name="match_datetime", type="datetime")
   */
  private $matchDatetime;

  /**
   * @var int
   * @ORM\Column(name="h_team", type="integer")
   */
  private $hTeam;

  /**
   * @var int
   * @ORM\Column(name="a_team", type="integer")
   */
  private $aTeam;

  /**
   * @var string
   * @ORM\Column(name="league", type="string", length=10)
   */
  private $league;

  /**
   * @var string
   * @ORM\Column(name="season", type="string", length=10)
   */
  private $season;

  /**
   * @var string
   * @ORM\Column(name="status", type="string", length=5)
   */
  private $status;

  /**
   * @var string
   * @ORM\Column(name="minute", type="string", length=5)
   */
  private $minute;

  /**
   * @var int
   *
   * @ORM\Column(name="week", type="integer", nullable=true)
   */
  private $week;

  /**
   * @var int
   *
   * @ORM\Column(name="ft1", type="integer")
   */
  private $ft1;

  /**
   * @var int
   *
   * @ORM\Column(name="ft2", type="integer")
   */
  private $ft2;

  /**
   * @var int
   *
   * @ORM\Column(name="ht1", type="integer")
   */
  private $ht1;

  /**
   * @var int
   *
   * @ORM\Column(name="ht2", type="integer")
   */
  private $ht2;

  /**
   * @var int
   *
   * @ORM\Column(name="et1", type="integer")
   */
  private $et1;

  /**
   * @var int
   *
   * @ORM\Column(name="et2", type="integer")
   */
  private $et2;

  /**
   * @var int
   *
   * @ORM\Column(name="pt1", type="integer")
   */
  private $pt1;

  /**
   * @var int
   *
   * @ORM\Column(name="pt2", type="integer")
   */
  private $pt2;

  /**
   * @var string
   * @ORM\Column(name="events", type="text")
   */
  private $events;

  /**
   * @var datetime
   *
   * @ORM\Column(name="updated", type="datetime")
   */
  private $updated;

  /**
   * @var \DateTime $createdAt
   *
   * @Gedmo\Timestampable(on="create")
   * @ORM\Column(name="created_at", type="datetime")
   */
  private $createdAt;

  /**
   * @var \DateTime $updatedAt
   *
   * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
   * @ORM\Column(name="updated_at", type="datetime", nullable=true)
   */
  private $updatedAt;

    /**
     ** Get id
     *
     ** @return integer
     */
    // public function getId()
    // {
    //     return $this->id;
    // }

    /**
     * Set sportId
     *
     * @param integer $sportId
     *
     * @return FtFixture
     */
    public function setSportId($sportId)
    {
        $this->sportId = $sportId;

        return $this;
    }

    /**
     * Get sportId
     *
     * @return string
     */
    public function getSportId()
    {
        return $this->sportId;
    }

    /**
     * Set fId
     *
     * @param string $fId
     *
     * @return FtFixture
     */
    public function setFId($fId)
    {
        $this->fId = $fId;

        return $this;
    }

    /**
     * Get fId
     *
     * @return string
     */
    public function getFId()
    {
        return $this->fId;
    }

    /**
     * Set matchDatetime
     *
     * @param \DateTime $matchDatetime
     *
     * @return FtFixture
     */
    public function setMatchDatetime($matchDatetime)
    {
        $this->matchDatetime = $matchDatetime;

        return $this;
    }

    /**
     * Get matchDatetime
     *
     * @return \DateTime
     */
    public function getMatchDatetime()
    {
        return $this->matchDatetime;
    }

    /**
     * Set hTeam
     *
     * @param integer $hTeam
     *
     * @return FtFixture
     */
    public function setHTeam($hTeam)
    {
        $this->hTeam = $hTeam;

        return $this;
    }

    /**
     * Get hTeam
     *
     * @return integer
     */
    public function getHTeam()
    {
        return $this->hTeam;
    }

    /**
     * Set aTeam
     *
     * @param integer $aTeam
     *
     * @return FtFixture
     */
    public function setATeam($aTeam)
    {
        $this->aTeam = $aTeam;

        return $this;
    }

    /**
     * Get aTeam
     *
     * @return integer
     */
    public function getATeam()
    {
        return $this->aTeam;
    }

    /**
     * Set league
     *
     * @param string $league
     *
     * @return FtFixture
     */
    public function setLeague($league)
    {
        $this->league = $league;

        return $this;
    }

    /**
     * Get league
     *
     * @return string
     */
    public function getLeague()
    {
        return $this->league;
    }

    /**
     * Set season
     *
     * @param string $season
     *
     * @return FtFixture
     */
    public function setSeason($season)
    {
        $this->season = $season;

        return $this;
    }

    /**
     * Get season
     *
     * @return string
     */
    public function getSeason()
    {
        return $this->season;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return FtFixture
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set minute
     *
     * @param string $minute
     *
     * @return FtFixture
     */
    public function setMinute($minute)
    {
        $this->minute = $minute;

        return $this;
    }

    /**
     * Get minute
     *
     * @return string
     */
    public function getMinute()
    {
        return $this->minute;
    }

    /**
     * Set ft1
     *
     * @param integer $ft1
     *
     * @return FtFixture
     */
    public function setFt1($ft1)
    {
        $this->ft1 = $ft1;

        return $this;
    }

    /**
     * Get ft1
     *
     * @return integer
     */
    public function getFt1()
    {
        return $this->ft1;
    }

    /**
     * Set ft2
     *
     * @param integer $ft2
     *
     * @return FtFixture
     */
    public function setFt2($ft2)
    {
        $this->ft2 = $ft2;

        return $this;
    }

    /**
     * Get ft2
     *
     * @return integer
     */
    public function getFt2()
    {
        return $this->ft2;
    }

    /**
     * Set ht1
     *
     * @param integer $ht1
     *
     * @return FtFixture
     */
    public function setHt1($ht1)
    {
        $this->ht1 = $ht1;

        return $this;
    }

    /**
     * Get ht1
     *
     * @return integer
     */
    public function getHt1()
    {
        return $this->ht1;
    }

    /**
     * Set ht2
     *
     * @param integer $ht2
     *
     * @return FtFixture
     */
    public function setHt2($ht2)
    {
        $this->ht2 = $ht2;

        return $this;
    }

    /**
     * Get ht2
     *
     * @return integer
     */
    public function getHt2()
    {
        return $this->ht2;
    }

    /**
     * Set et1
     *
     * @param integer $et1
     *
     * @return FtFixture
     */
    public function setEt1($et1)
    {
        $this->et1 = $et1;

        return $this;
    }

    /**
     * Get et1
     *
     * @return integer
     */
    public function getEt1()
    {
        return $this->et1;
    }

    /**
     * Set et2
     *
     * @param integer $et2
     *
     * @return FtFixture
     */
    public function setEt2($et2)
    {
        $this->et2 = $et2;

        return $this;
    }

    /**
     * Get et2
     *
     * @return integer
     */
    public function getEt2()
    {
        return $this->et2;
    }

    /**
     * Set pt1
     *
     * @param integer $pt1
     *
     * @return FtFixture
     */
    public function setPt1($pt1)
    {
        $this->pt1 = $pt1;

        return $this;
    }

    /**
     * Get pt1
     *
     * @return integer
     */
    public function getPt1()
    {
        return $this->pt1;
    }

    /**
     * Set pt2
     *
     * @param integer $pt2
     *
     * @return FtFixture
     */
    public function setPt2($pt2)
    {
        $this->pt2 = $pt2;

        return $this;
    }

    /**
     * Get pt2
     *
     * @return integer
     */
    public function getPt2()
    {
        return $this->pt2;
    }

    /**
     * Set events
     *
     * @param string $events
     *
     * @return FtFixture
     */
    public function setEvents($events)
    {
        $this->events = $events;

        return $this;
    }

    /**
     * Get events
     *
     * @return string
     */
    public function getEvents()
    {
        return $this->events;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return FtFixture
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return FtFixture
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set week
     *
     * @param integer $week
     *
     * @return FtFixture
     */
    public function setWeek($week)
    {
        $this->week = $week;

        return $this;
    }

    /**
     * Get week
     *
     * @return integer
     */
    public function getWeek()
    {
        return $this->week;
    }

    /**
     * Set updated
     *
     * @param \DateTime $updated
     *
     * @return FtFixture
     */
    public function setUpdated($updated)
    {
        $this->updated = $updated;

        return $this;
    }

    /**
     * Get updated
     *
     * @return \DateTime
     */
    public function getUpdated()
    {
        return $this->updated;
    }
}
