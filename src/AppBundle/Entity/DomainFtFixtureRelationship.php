<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;

 /**
  * DomainFtFixtureRelationship
  *
  * @ORM\Table(name="domain_ft_fixture_relationship", uniqueConstraints={@ORM\UniqueConstraint(name="unique_idx", columns={"fixture_id", "domain_id", "preview_id"})})
  * @ORM\Entity(repositoryClass="AppBundle\Repository\DomainFtFixtureRelationshipRepository")
  */
class DomainFtFixtureRelationship
{
  /**
  * @var int
  *
  * @ORM\Column(name="id", type="integer")
  * @ORM\Id
  * @ORM\GeneratedValue(strategy="AUTO")
  */
  private $id;

  /**
  * @var int
  * @ORM\Column(name="fixture_id", type="integer")
  */
  private $fixtureId;

  /**
   * @var int
   * @ORM\Column(name="domain_id", type="integer")
   */
  private $domainId;

  /**
  * @var int
  * @ORM\Column(name="preview_id", type="integer")
   */
  private $previewId;

  /**
   * @var \DateTime $createdAt
   *
   * @Gedmo\Timestampable(on="create")
   * @ORM\Column(name="created_at", type="datetime")
   */
  private $createdAt;

  /**
   * @var \DateTime $updatedAt
   *
   * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
   * @ORM\Column(name="updated_at", type="datetime", nullable=true)
   */
  private $updatedAt;


    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set fixtureId
     *
     * @param integer $fixtureId
     *
     * @return DomainFtFixtureRelationship
     */
    public function setFixtureId($fixtureId)
    {
        $this->fixtureId = $fixtureId;

        return $this;
    }

    /**
     * Get fixtureId
     *
     * @return integer
     */
    public function getFixtureId()
    {
        return $this->fixtureId;
    }

    /**
     * Set domainId
     *
     * @param integer $domainId
     *
     * @return DomainFtFixtureRelationship
     */
    public function setDomainId($domainId)
    {
        $this->domainId = $domainId;

        return $this;
    }

    /**
     * Get domainId
     *
     * @return integer
     */
    public function getDomainId()
    {
        return $this->domainId;
    }

    /**
     * Set previewId
     *
     * @param integer $previewId
     *
     * @return DomainFtFixtureRelationship
     */
    public function setPreviewId($previewId)
    {
        $this->previewId = $previewId;

        return $this;
    }

    /**
     * Get previewId
     *
     * @return integer
     */
    public function getPreviewId()
    {
        return $this->previewId;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return DomainFtFixtureRelationship
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return DomainFtFixtureRelationship
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }
}
