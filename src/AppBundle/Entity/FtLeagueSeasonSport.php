<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;

/**
 * FtLeagueSeasonSport
 *
 * @ORM\Table(name="ft_league_season_sport",
 *     uniqueConstraints={@ORM\UniqueConstraint(name="league_season_sport_idx", columns={
 *         "sport_id", "season"
 *     })})
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtLeagueSeasonSportRepository")
 */
class FtLeagueSeasonSport
{
  /**
   * @var int
   *
   * @ORM\Column(name="id", type="integer")
   * @ORM\Id
   * @ORM\GeneratedValue(strategy="AUTO")
   */
  private $id;

  /**
   * @var string
   *
   * @ORM\Column(name="season", type="string", length=10)
   */
  private $season;

  /**
   * @ORM\ManyToOne(targetEntity="Sport", inversedBy="ftLeagueSeasonSports")
   * @ORM\JoinColumn(name="sport_id", referencedColumnName="id")
   */
  private $sport;

 /**
  * @var \DateTime $createdAt
  *
  * @Gedmo\Timestampable(on="create")
  * @ORM\Column(name="created_at", type="datetime")
  */
  private $createdAt;

  /**
   * @var \DateTime $updatedAt
   *
   * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
   * @ORM\Column(name="updated_at", type="datetime", nullable=true)
   */
  private $updatedAt;


    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set season
     *
     * @param string $season
     *
     * @return FtLeagueSeasonSport
     */
    public function setSeason($season)
    {
        $this->season = $season;

        return $this;
    }

    /**
     * Get season
     *
     * @return string
     */
    public function getSeason()
    {
        return $this->season;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return FtLeagueSeasonSport
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return FtLeagueSeasonSport
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set sport
     *
     * @param \AppBundle\Entity\Sport $sport
     *
     * @return FtLeagueSeasonSport
     */
    public function setSport(\AppBundle\Entity\Sport $sport = null)
    {
        $this->sport = $sport;

        return $this;
    }

    /**
     * Get sport
     *
     * @return \AppBundle\Entity\Sport
     */
    public function getSport()
    {
        return $this->sport;
    }
}
