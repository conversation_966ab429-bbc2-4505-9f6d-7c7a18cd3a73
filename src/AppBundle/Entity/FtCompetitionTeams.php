<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * FtCompetitionTeams
 *
 * @ORM\Table(name="ft_competition_teams")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtCompetitionTeamsRepository")
 */
class FtCompetitionTeams
{
	/**
	 * @var int
	 *
	 * @ORM\Column(name="id", type="integer")
	 * @ORM\Id
	 * @ORM\GeneratedValue(strategy="AUTO")
	 */
	private $id;

	/**
	 * @var string
	 *
	 * @ORM\Column(name="name_en", type="string", length=100, nullable=true)
	 */
	private $nameEn;

	/**
	 * @var string
	 *
	 * @ORM\Column(name="name_gr", type="string", length=100)
	 */
	private $nameGr;

	/**
	 * @var string
	 *
	 * @ORM\Column(name="name_ro", type="string", length=100, nullable=true)
	 */
	private $nameRo;

	/**
	 * @var string
	 *
	 * @ORM\Column(name="flag", type="string", length=255)
	 */
	private $flag;

    /**
     * @var int
     *
     * @ORM\Column(name="team_index", type="integer", nullable=true)
     */
    private $teamIndex;

    /**
     * @var int
     * @ORM\Column(name="team_id", type="integer", nullable=true)
     */
    private $teamId;
	/**
	 * @var string
	 *
	 * @ORM\Column(name="team_url_gr", type="string", length=255, nullable=true)
	 */
	private $teamUrlGr;

	/**
	 * @var int
	 *
	 * @ORM\Column(name="pts", type="integer", nullable=true)
	 */
	private $pts;

	/**
	 * @var int
	 *
	 * @ORM\Column(name="gp", type="integer", nullable=true)
	 */
	private $gp;

	/**
	 * @var string
	 *
	 * @ORM\Column(name="goals", type="string", length=255, nullable=true)
	 */
	private $goals;


  /**
   * @ORM\Column(name="league_season_id", type="integer", nullable=true)
   */
  private $leagueSeasonId;


    /**
	 * Get id
	 *
	 * @return integer
	 */
	public function getId()
	{
		return $this->id;
	}

	/**
	 * Set nameEn
	 *
	 * @param string $nameEn
	 *
	 * @return FtCompetitionTeams
	 */
	public function setNameEn($nameEn)
	{
		$this->nameEn = $nameEn;

		return $this;
	}

	/**
	 * Get nameEn
	 *
	 * @return string
	 */
	public function getNameEn()
	{
		return $this->nameEn;
	}

	/**
	 * Set nameGr
	 *
	 * @param string $nameGr
	 *
	 * @return FtCompetitionTeams
	 */
	public function setNameGr($nameGr)
	{
		$this->nameGr = $nameGr;

		return $this;
	}

	/**
	 * Get nameGr
	 *
	 * @return string
	 */
	public function getNameGr()
	{
		return $this->nameGr;
	}

	/**
	 * Set nameRo
	 *
	 * @param string $nameRo
	 *
	 * @return FtCompetitionTeams
	 */
	public function setNameRo($nameRo)
	{
		$this->nameRo = $nameRo;

		return $this;
	}

	/**
	 * Get nameRo
	 *
	 * @return string
	 */
	public function getNameRo()
	{
		return $this->nameRo;
	}

	/**
	 * Set flag
	 *
	 * @param string $flag
	 *
	 * @return FtCompetitionTeams
	 */
	public function setFlag($flag)
	{
		$this->flag = $flag;

		return $this;
	}

	/**
	 * Get flag
	 *
	 * @return string
	 */
	public function getFlag()
	{
		return $this->flag;
	}

	/**
	 * Set teamUrlGr
	 *
	 * @param string $teamUrlGr
	 *
	 * @return FtCompetitionTeams
	 */
	public function setTeamUrlGr($teamUrlGr)
	{
		$this->teamUrlGr = $teamUrlGr;

		return $this;
	}

	/**
	 * Get teamUrlGr
	 *
	 * @return string
	 */
	public function getTeamUrlGr()
	{
		return $this->teamUrlGr;
	}

	/**
	 * Set pts
	 *
	 * @param integer $pts
	 *
	 * @return FtCompetitionTeams
	 */
	public function setPts($pts)
	{
		$this->pts = $pts;

		return $this;
	}

	/**
	 * Get pts
	 *
	 * @return int
	 */
	public function getPts()
	{
		return $this->pts;
	}

	/**
	 * Set gp
	 *
	 * @param integer $gp
	 *
	 * @return FtCompetitionTeams
	 */
	public function setGp($gp)
	{
		$this->gp = $gp;

		return $this;
	}

	/**
	 * Get gp
	 *
	 * @return int
	 */
	public function getGp()
	{
		return $this->gp;
	}

	/**
	 * Set goals
	 *
	 * @param string $goals
	 *
	 * @return FtCompetitionTeams
	 */
	public function setGoals($goals)
	{
		$this->goals = $goals;

		return $this;
	}

	/**
	 * Get goals
	 *
	 * @return string
	 */
	public function getGoals()
	{
		return $this->goals;
	}

    /**
     * Set teamIndex
     *
     * @param integer $teamIndex
     *
     * @return FtCompetitionTeams
     */
    public function setTeamIndex($teamIndex)
    {
        $this->teamIndex = $teamIndex;
        return $this;
    }
    /**
     * Get teamIndex
     *
     * @return int
     */
    public function getTeamIndex()
    {
        return $this->teamIndex;
    }

    /**
     * Set teamId
     *
     * @param int|null $teamId
     * @return FtCompetitionTeams
     */
    public function setTeamId(?int $teamId): self
    {
        $this->teamId = $teamId;
        return $this;
    }

    /**
     * Get teamId
     *
     * @return int|null
     */
    public function getTeamId(): ?int
    {
        return $this->teamId;
    }


    public function getLeagueSeasonId(): ?int
    {
      return $this->leagueSeasonId;
    }

    public function setLeagueSeasonId(?int $id): self
    {
      $this->leagueSeasonId = $id;
      return $this;
  }
}
