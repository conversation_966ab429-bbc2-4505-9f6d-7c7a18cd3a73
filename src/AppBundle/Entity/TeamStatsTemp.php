<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * TeamStatsTemp
 *
 * @ORM\Table(name="team_stats_temp",
 *      indexes={@ORM\Index(name="search_idx", columns={"team_id", "league_season_id"})},
 *      uniqueConstraints={@ORM\UniqueConstraint(name="team_lss_idx", columns={
 *         "team_id", "league_season_id"
 *      })})
 * @ORM\Entity(repositoryClass="AppBundle\Repository\TeamStatsTempRepository")
 */
class TeamStatsTemp
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\Column(name="team_id", type="integer")
     */
    private $teamId;

    /**
     * @var int
     *
     * @ORM\Column(name="league_season_id", type="integer")
     */
    private $leagueSeasonId;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set teamId
     *
     * @param integer $teamId
     *
     * @return TeamStatsTemp
     */
    public function setTeamId($teamId)
    {
        $this->teamId = $teamId;

        return $this;
    }

    /**
     * Get teamId
     *
     * @return int
     */
    public function getTeamId()
    {
        return $this->teamId;
    }

    /**
     * Set leagueSeasonId
     *
     * @param integer $leagueSeasonId
     *
     * @return TeamStatsTemp
     */
    public function setLeagueSeasonId($leagueSeasonId)
    {
        $this->leagueSeasonId = $leagueSeasonId;

        return $this;
    }

    /**
     * Get leagueSeasonId
     *
     * @return integer
     */
    public function getLeagueSeasonId()
    {
        return $this->leagueSeasonId;
    }
}
