<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Feed
 *
 * @ORM\Table(name="feed")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FeedRepository")
 */
class Feed
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="username", type="string", length=60, nullable=true)
     */
    private $username;

    /**
     * @var string
     *
     * @ORM\Column(name="password", type="string", length=60, nullable=true)
     */
    private $password;

    /**
     * @var int
     *
     * @ORM\Column(name="language", type="smallint", nullable=true)
     */
    private $language;

    /**
     * @var bool
     *
     * @ORM\Column(name="active", type="boolean", nullable=true)
     */
    private $active;


    /**
     * @ORM\OneToMany(targetEntity="FeedXml", mappedBy="feed", cascade={"persist"})
     */
     private $feedXmls;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set username
     *
     * @param string $username
     *
     * @return Feed
     */
    public function setUsername($username)
    {
        $this->username = $username;

        return $this;
    }

    /**
     * Get username
     *
     * @return string
     */
    public function getUsername()
    {
        return $this->username;
    }

    /**
     * Set password
     *
     * @param string $password
     *
     * @return Feed
     */
    public function setPassword($password)
    {
        $this->password = $password;

        return $this;
    }

    /**
     * Get password
     *
     * @return string
     */
    public function getPassword()
    {
        return $this->password;
    }

    /**
     * Set language
     *
     * @param integer $language
     *
     * @return Feed
     */
    public function setLanguage($language)
    {
        $this->language = $language;

        return $this;
    }

    /**
     * Get language
     *
     * @return int
     */
    public function getLanguage()
    {
        return $this->language;
    }

    /**
     * Set active
     *
     * @param boolean $active
     *
     * @return Feed
     */
    public function setActive($active)
    {
        $this->active = $active;

        return $this;
    }

    /**
     * Get active
     *
     * @return bool
     */
    public function getActive()
    {
        return $this->active;
    }
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->feedXmls = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Add feedXml
     *
     * @param \AppBundle\Entity\FeedXml $feedXml
     *
     * @return Feed
     */
    public function addFeedXml(\AppBundle\Entity\FeedXml $feedXml)
    {
      // print_r($feedXml);
      // echo '<br/>';
      // print_r($this);
      // die('yello');
      // $feedXml->addFeed($this);
      $feedXml->setFeed($this);
      $this->feedXmls[] = $feedXml;

      return $this;
    }

    /**
     * Remove feedXml
     *
     * @param \AppBundle\Entity\FeedXml $feedXml
     */
    public function removeFeedXml(\AppBundle\Entity\FeedXml $feedXml)
    {
        $this->feedXmls->removeElement($feedXml);
    }

    /**
     * Get feedXmls
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getFeedXmls()
    {
        return $this->feedXmls;
    }
}
