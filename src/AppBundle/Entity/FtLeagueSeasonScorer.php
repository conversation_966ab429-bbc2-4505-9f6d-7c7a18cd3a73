<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * FtLeagueSeasonScorer
 *
 * @ORM\Table(name="ft_league_season_scorer", uniqueConstraints={@ORM\UniqueConstraint(columns={"sport_id", "l_id", "season", "player_name"})})
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtLeagueSeasonScorerRepository")
 */
class FtLeagueSeasonScorer
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\Column(name="sport_id", type="integer")
     */
    private $sportId;

    /**
     * @var string
     *
     * @ORM\Column(name="l_id", type="string", length=10)
     */
    private $lId;

    /**
     * @var string
     *
     * @ORM\Column(name="season", type="string", length=10)
     */
    private $season;

    /**
     * @var string
     *
     * @ORM\Column(name="player_name", type="string", length=60)
     */
    private $playerName;

    /**
     * @var int
     *
     * @ORM\Column(name="team_id", type="integer", nullable=true)
     */
    private $teamId;

    /**
     * @var int
     *
     * @ORM\Column(name="goals", type="smallint", nullable=true, options={"unsigned"=true})
     */
    private $goals;

    /**
     * @var int
     *
     * @ORM\Column(name="penalties", type="smallint", nullable=true, options={"unsigned"=true})
     */
    private $penalties;

    /**
     * @var int
     *
     * @ORM\Column(name="scorer_first", type="smallint", nullable=true, options={"unsigned"=true})
     */
    private $scorerFirst;

    /**
     * @var \DateTime $createdAt
     *
     * @Gedmo\Timestampable(on="create")
     * @ORM\Column(name="created_at", type="datetime")
     */
     private $createdAt;

     /**
      * @var \DateTime $updatedAt
      *
      * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
      * @ORM\Column(name="updated_at", type="datetime", nullable=true)
      */
     private $updatedAt;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set sportId
     *
     * @param integer $sportId
     *
     * @return FtLeagueSeasonScorer
     */
    public function setSportId($sportId)
    {
        $this->sportId = $sportId;

        return $this;
    }

    /**
     * Get sportId
     *
     * @return int
     */
    public function getSportId()
    {
        return $this->sportId;
    }

    /**
     * Set lId
     *
     * @param string $lId
     *
     * @return FtLeagueSeasonScorer
     */
    public function setLId($lId)
    {
        $this->lId = $lId;

        return $this;
    }

    /**
     * Get lId
     *
     * @return string
     */
    public function getLId()
    {
        return $this->lId;
    }

    /**
     * Set season
     *
     * @param string $season
     *
     * @return FtLeagueSeasonScorer
     */
    public function setSeason($season)
    {
        $this->season = $season;

        return $this;
    }

    /**
     * Get season
     *
     * @return string
     */
    public function getSeason()
    {
        return $this->season;
    }

    /**
     * Set playerName
     *
     * @param string $playerName
     *
     * @return FtLeagueSeasonScorer
     */
    public function setPlayerName($playerName)
    {
        $this->playerName = $playerName;

        return $this;
    }

    /**
     * Get playerName
     *
     * @return string
     */
    public function getPlayerName()
    {
        return $this->playerName;
    }

    /**
     * Set teamId
     *
     * @param integer $teamId
     *
     * @return FtLeagueSeasonScorer
     */
    public function setTeamId($teamId)
    {
        $this->teamId = $teamId;

        return $this;
    }

    /**
     * Get teamId
     *
     * @return int
     */
    public function getTeamId()
    {
        return $this->teamId;
    }

    /**
     * Set goals
     *
     * @param integer $goals
     *
     * @return FtLeagueSeasonScorer
     */
    public function setGoals($goals)
    {
        $this->goals = $goals;

        return $this;
    }

    /**
     * Get goals
     *
     * @return int
     */
    public function getGoals()
    {
        return $this->goals;
    }

    /**
     * Set penalties
     *
     * @param integer $penalties
     *
     * @return FtLeagueSeasonScorer
     */
    public function setPenalties($penalties)
    {
        $this->penalties = $penalties;

        return $this;
    }

    /**
     * Get penalties
     *
     * @return int
     */
    public function getPenalties()
    {
        return $this->penalties;
    }

    /**
     * Set scorerFirst
     *
     * @param integer $scorerFirst
     *
     * @return FtLeagueSeasonScorer
     */
    public function setScorerFirst($scorerFirst)
    {
        $this->scorerFirst = $scorerFirst;

        return $this;
    }

    /**
     * Get scorerFirst
     *
     * @return int
     */
    public function getScorerFirst()
    {
        return $this->scorerFirst;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return FtLeagueSeasonScorer
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return FtLeagueSeasonScorer
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }
}
