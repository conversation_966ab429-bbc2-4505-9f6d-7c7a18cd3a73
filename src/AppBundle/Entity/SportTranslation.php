<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use AppBundle\Entity\SuperClass\AbstractSportTranslation;

/**
 * @ORM\Table(name="sport_translations",
 *     uniqueConstraints={@ORM\UniqueConstraint(name="sport_translation_idx", columns={
 *         "locale", "sport_id", "type"
 *     })}
 * )
 * @ORM\Entity(repositoryClass="Gedmo\Translatable\Entity\Repository\TranslationRepository")
 */
class SportTranslation extends AbstractSportTranslation
{
  /**
   * Convenient constructor
   */
  public function __construct()
  {
  }

  /**
   * @ORM\ManyToOne(targetEntity="Sport", inversedBy="translations")
   * @ORM\JoinColumn(name="sport_id", referencedColumnName="id", onDelete="CASCADE")
   */
  protected $sport;

  /**
   * @ORM\Column(name="type", length=10, nullable=true)
   */
  protected $type;

    /**
     * Set type
     *
     * @param string $type
     *
     * @return SportGedmoTranslation
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }
}
