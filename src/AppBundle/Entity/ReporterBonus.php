<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;


// , indexes={@ORM\Index(name="search_idx", columns={"match_datetime", "author_id", "fixture_id", "odd_type_id"})})
/**
 * ReporterBonus
 *
 * @ORM\Table(name="reporter_bonus", uniqueConstraints={@ORM\UniqueConstraint(columns={"month_no", "year_no", "author_id"})})
 * @ORM\Entity(repositoryClass="AppBundle\Repository\ReporterBonusRepository")
 */
class ReporterBonus
{
	/**
	 * @var int
	 *
	 * @ORM\Column(name="id", type="integer", options={"unsigned"=true})
	 * @ORM\Id
	 * @ORM\GeneratedValue(strategy="AUTO")
	 */
	private $id;

	/**
	 * @var smallint
	 *
	 * @ORM\Column(name="month_no", type="smallint", options={"unsigned"=true})
	 */
	private $monthNo;

	/**
	 * @var smallint
	 *
	 * @ORM\Column(name="year_no", type="smallint", options={"unsigned"=true})
	 */
			private $yearNo;

	/**
	 * @var int
	 *
	 * @ORM\Column(name="author_id", type="integer", options={"unsigned"=true})
	 */
			private $authorId;

	/**
	 * @var string
	 *
	 * @ORM\Column(name="author_name", type="string", length=191)
	 */
	private $authorName;
		
	/**
	 * @var smallint
	 *
	 * @ORM\Column(name="points", type="smallint", options={"unsigned"=true})
	 */
	private $points;

	/**
	 * @var smallint
	 *
	 * @ORM\Column(name="bet_count", type="smallint", options={"unsigned"=true})
	 */
	private $betCount;

	/**
	 * @var smallint
	 *
	 * @ORM\Column(name="bet_profit", type="smallint")
	 */
	private $betProfit;

	/**
	 * @var \DateTime $createdAt
	 *
	 * @Gedmo\Timestampable(on="create")
	 * @ORM\Column(name="created_at", type="datetime")
	 */
		private $createdAt;

  /**
   * @var \DateTime $updatedAt
   *
   * @Gedmo\Timestampable(on="update")
   * @ORM\Column(name="updated_at", type="datetime", nullable=true)
   */
  private $updatedAt;

	/**
	 * Get id
	 *
	 * @return integer
	 */
	public function getId()
	{
		return $this->id;
	}

	/**
	 * Set monthNo
	 *
	 * @param smallint $monthNo
	 *
	 * @return ReporterBonus
	 */
	public function setMonthNo($monthNo)
	{
		$this->monthNo = $monthNo;

		return $this;
	}

	/**
	 * Get monthNo
	 *
	 * @return smallint
	 */
	public function getMonthNo()
	{
		return $this->monthNo;
	}
	
	/**
	 * Set yearNo
	 *
	 * @param smallint $yearNo
	 *
	 * @return ReporterBonus
	 */
	public function setYearNo($yearNo)
	{
		$this->yearNo = $yearNo;

		return $this;
	}

	/**
	 * Get yearNo
	 *
	 * @return smallint
	 */
	public function getYearNo()
	{
		return $this->yearNo;
	}

	/**
	 * Set points
	 *
	 * @param smallint $points
	 *
	 * @return ReporterBonus
	 */
	public function setPoints($points)
	{
		$this->points = $points;

		return $this;
	}

	/**
	 * Get points
	 *
	 * @return smallint
	 */
	public function getPoints()
	{
		return $this->points;
	}

	/**
	 * Set betCount
	 *
	 * @param smallint $betCount
	 *
	 * @return ReporterBonus
	 */
	public function setBetCount($betCount)
	{
		$this->betCount = $betCount;

		return $this;
	}

	/**
	 * Get betCount
	 *
	 * @return smallint
	 */
	public function getBetCount()
	{
		return $this->betCount;
	}

	/**
	 * Set betProfit
	 *
	 * @param smallint $betProfit
	 *
	 * @return ReporterBonus
	 */
	public function setBetProfit($betProfit)
	{
		$this->betProfit = $betProfit;

		return $this;
	}

	/**
	 * Get betProfit
	 *
	 * @return smallint
	 */
	public function getBetProfit()
	{
		return $this->betProfit;
	}

	/**
	 * Set authorId
	 *
	 * @param integer $authorId
	 *
	 * @return ReporterBonus
	 */
	public function setAuthorId($authorId)
	{
		$this->authorId = $authorId;

		return $this;
	}

	/**
	 * Get authorId
	 *
	 * @return integer
	 */
	public function getAuthorId()
	{
		return $this->authorId;
	}

	/**
	 * Set authorName
	 *
	 * @param string $authorName
	 *
	 * @return ReporterBonus
	 */
	public function setAuthorName($authorName)
	{
		$this->authorName = $authorName;

		return $this;
	}

	/**
	 * Get authorName
	 *
	 * @return string
	 */
	public function getAuthorName()
	{
		return $this->authorName;
	}

	/**
	 * Set createdAt
	 *
	 * @param \DateTime $createdAt
	 *
	 * @return ReporterBonus
	 */
	public function setCreatedAt($createdAt)
	{
		$this->createdAt = $createdAt;

		return $this;
	}

	/**
	 * Get createdAt
	 *
	 * @return \DateTime
	 */
	public function getCreatedAt()
	{
		return $this->createdAt;
	}

	/**
	 * Set updatedAt
	 *
	 * @param \DateTime $updatedAt
	 *
	 * @return ReporterBonus
	 */
	public function setUpdatedAt($updatedAt)
	{
		$this->updatedAt = $updatedAt;

		return $this;
	}

	/**
	 * Get updatedAt
	 *
	 * @return \DateTime
	 */
	public function getUpdatedAt()
	{
		return $this->updatedAt;
	}
}
