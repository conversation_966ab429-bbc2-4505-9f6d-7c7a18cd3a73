<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * Domain
 *
 * @ORM\Table(name="domain", indexes={@ORM\Index(name="unique_idx", columns={"is_active"})})
 * @ORM\Entity(repositoryClass="AppBundle\Repository\DomainRepository")
 */
class Domain
{
  /**
   * @var int
   *
   * @ORM\Column(name="id", type="integer")
   * @ORM\Id
   * @ORM\GeneratedValue(strategy="AUTO")
   */
  private $id;

  /**
   * @var string
   *
   * @ORM\Column(name="name", type="string", length=100)
   */
  private $name;

  /**
   * @var smallint
   *
   * @ORM\Column(name="host_port", type="smallint", nullable=true, options={"default":80})
   */
  private $hostPort;

  /**
   * @ORM\ManyToOne(targetEntity="Language", inversedBy="domains")
   * @ORM\JoinColumn(name="language_id", referencedColumnName="id")
   */
  private $language;

  /**
   * @var string
   *
   * @ORM\Column(name="db_host", type="string", length=100)
   */
  private $dbHost;

  /**
   * @var string
   *
   * @ORM\Column(name="db_node_host", type="string", nullable=true, length=100)
   */
  private $dbNodeHost;

  /**
   * @var smallint
   *
   * @ORM\Column(name="db_port", type="smallint", nullable=true, options={"default":3306})
   */
  private $dbPort;

  /**
   * @var string
   *
   * @ORM\Column(name="db_name", type="string", length=100)
   */
  private $dbName;

  /**
   * @var string
   *
   * @ORM\Column(name="db_prefix", type="string", length=10)
   */
  private $dbPrefix;

  /**
   * @var string
   *
   * @ORM\Column(name="db_username", type="string", length=100)
   */
  private $dbUsername;

  /**
   * @var string
   *
   * @ORM\Column(name="db_password", type="string", length=100)
   */
  private $dbPassword;

  /**
   * @var string
   *
   * @ORM\Column(name="post_type", type="string", length=100)
   */
  private $postType;

  /**
   * @var int
   *
   * @ORM\Column(name="category_id", type="integer", options={"default":0})
   */
  private $categoryId;

  /**
   * @var bool
   *
   * @ORM\Column(name="is_active", type="boolean", options={"default":0})
   */
  private $isActive;

  /**
   * @var bool
   *
   * @ORM\Column(name="sync_only_sports", type="boolean", options={"default":0})
   */
  private $syncOnlySports;

  /**
   * @var bool
   *
   * @ORM\Column(name="create_posts", type="boolean", options={"default":false})
   */
  private $createPosts;

  /**
   * @var string
   *
   * @ORM\Column(name="standings_prefix", type="string", length=100, nullable=true)
   */
  private $standingsPrefix;

  /**
   * @ORM\OneToMany(targetEntity="Bookmaker", mappedBy="domain")
   */
  private $bookmakers;

  /**
   * @ORM\OneToMany(targetEntity="DomainSport", mappedBy="domain")
   */
  private $domainSports;

  /**
   * @ORM\OneToMany(targetEntity="DomainSportSlug", mappedBy="domain")
   */
   private $domainSportSlugs;

  /**
   * @var bool
   *
   * @ORM\Column(name="protocol", type="boolean", options={"default":0})
   */
  private $protocol;

  /**
   * @var int
   *
   * @ORM\Column(name="node_port_id", type="integer", options={"default":2086})
   */
   private $nodePortId;

  /**
   * @var bool
   *
   * @ORM\Column(name="is_start_abs", type="boolean", options={"default":false})
   */
  private $isStartAbs;

  /**
   * @var \DateTime $createdAt
   *
   * @Gedmo\Timestampable(on="create")
   * @ORM\Column(name="created_at", type="datetime")
   */
  private $createdAt;

  /**
   * @var \DateTime $updatedAt
   *
   * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
   * @ORM\Column(name="updated_at", type="datetime", nullable=true)
   */
  private $updatedAt;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->bookmakers = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return Domain
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set language
     *
     * @param integer $language
     *
     * @return Domain
     */
    public function setLanguage($language)
    {
        $this->language = $language;

        return $this;
    }

    /**
     * Get language
     *
     * @return integer
     */
    public function getLanguage()
    {
        return $this->language;
    }

    /**
     * Set dbHost
     *
     * @param string $dbHost
     *
     * @return Domain
     */
    public function setDbHost($dbHost)
    {
        $this->dbHost = $dbHost;

        return $this;
    }

    /**
     * Get dbHost
     *
     * @return string
     */
    public function getDbHost()
    {
        return $this->dbHost;
    }

    /**
     * Set dbNodeHost
     *
     * @param string $dbNodeHost
     *
     * @return Domain
     */
    public function setDbNodeHost($dbNodeHost)
    {
        $this->dbNodeHost = $dbNodeHost;

        return $this;
    }

    /**
     * Get dbNodeHost
     *
     * @return string
     */
    public function getDbNodeHost()
    {
        return $this->dbNodeHost;
    }

    /**
     * Set dbPort
     *
     * @param integer $dbPort
     *
     * @return Domain
     */
    public function setDbPort($dbPort)
    {
        $this->dbPort = $dbPort;

        return $this;
    }

    /**
     * Get dbPort
     *
     * @return integer
     */
    public function getDbPort()
    {
        return $this->dbPort;
    }

    /**
     * Set dbName
     *
     * @param string $dbName
     *
     * @return Domain
     */
    public function setDbName($dbName)
    {
        $this->dbName = $dbName;

        return $this;
    }

    /**
     * Get dbName
     *
     * @return string
     */
    public function getDbName()
    {
        return $this->dbName;
    }

    /**
     * Set dbPrefix
     *
     * @param string $dbPrefix
     *
     * @return Domain
     */
    public function setDbPrefix($dbPrefix)
    {
        $this->dbPrefix = $dbPrefix;

        return $this;
    }

    /**
     * Get dbPrefix
     *
     * @return string
     */
    public function getDbPrefix()
    {
        return $this->dbPrefix;
    }

    /**
     * Set dbUsername
     *
     * @param string $dbUsername
     *
     * @return Domain
     */
    public function setDbUsername($dbUsername)
    {
        $this->dbUsername = $dbUsername;

        return $this;
    }

    /**
     * Get dbUsername
     *
     * @return string
     */
    public function getDbUsername()
    {
        return $this->dbUsername;
    }

    /**
     * Set dbPassword
     *
     * @param string $dbPassword
     *
     * @return Domain
     */
    public function setDbPassword($dbPassword)
    {
        $this->dbPassword = $dbPassword;

        return $this;
    }

    /**
     * Get dbPassword
     *
     * @return string
     */
    public function getDbPassword()
    {
        return $this->dbPassword;
    }

    /**
     * Set postType
     *
     * @param string $postType
     *
     * @return Domain
     */
    public function setPostType($postType)
    {
        $this->postType = $postType;

        return $this;
    }

    /**
     * Get postType
     *
     * @return string
     */
    public function getPostType()
    {
        return $this->postType;
    }

    /**
     * Set categoryId
     *
     * @param integer $categoryId
     *
     * @return Domain
     */
    public function setCategoryId($categoryId)
    {
        $this->categoryId = $categoryId;

        return $this;
    }

    /**
     * Get categoryId
     *
     * @return integer
     */
    public function getCategoryId()
    {
        return $this->categoryId;
    }

    /**
     * Set isActive
     *
     * @param boolean $isActive
     *
     * @return Domain
     */
    public function setIsActive($isActive)
    {
        $this->isActive = $isActive;

        return $this;
    }

    /**
     * Get isActive
     *
     * @return boolean
     */
    public function getIsActive()
    {
        return $this->isActive;
    }

    /**
     * Set syncOnlySports
     *
     * @param boolean $syncOnlySports
     *
     * @return Domain
     */
    public function setSyncOnlySports($syncOnlySports)
    {
        $this->syncOnlySports = $syncOnlySports;

        return $this;
    }

    /**
     * Get syncOnlySports
     *
     * @return boolean
     */
    public function getSyncOnlySports()
    {
        return $this->syncOnlySports;
    }

    /**
     * Set standingsPrefix
     *
     * @param string $standingsPrefix
     *
     * @return Domain
     */
    public function setStandingsPrefix($standingsPrefix)
    {
        $this->standingsPrefix = $standingsPrefix;

        return $this;
    }

    /**
     * Get standingsPrefix
     *
     * @return string
     */
    public function getStandingsPrefix()
    {
        return $this->standingsPrefix;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return Domain
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return Domain
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Add bookmaker
     *
     * @param \AppBundle\Entity\Bookmaker $bookmaker
     *
     * @return Domain
     */
    public function addBookmaker(\AppBundle\Entity\Bookmaker $bookmaker)
    {
        $this->bookmakers[] = $bookmaker;

        return $this;
    }

    /**
     * Remove bookmaker
     *
     * @param \AppBundle\Entity\Bookmaker $bookmaker
     */
    public function removeBookmaker(\AppBundle\Entity\Bookmaker $bookmaker)
    {
        $this->bookmakers->removeElement($bookmaker);
    }

    /**
     * Get bookmakers
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getBookmakers()
    {
        return $this->bookmakers;
    }

    /**
     * Add domainSport
     *
     * @param \AppBundle\Entity\DomainSport $domainSport
     *
     * @return Domain
     */
    public function addDomainSport(\AppBundle\Entity\DomainSport $domainSport)
    {
        $this->domainSports[] = $domainSport;

        return $this;
    }

    /**
     * Remove domainSport
     *
     * @param \AppBundle\Entity\DomainSport $domainSport
     */
    public function removeDomainSport(\AppBundle\Entity\DomainSport $domainSport)
    {
        $this->domainSports->removeElement($domainSport);
    }

    /**
     * Get domainSports
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getDomainSports()
    {
        return $this->domainSports;
    }

    public function __toString() {
        return $this->name;
    }

    /**
     * Set hostPort
     *
     * @param integer $hostPort
     *
     * @return Domain
     */
    public function setHostPort($hostPort)
    {
        $this->hostPort = $hostPort;

        return $this;
    }

    /**
     * Get hostPort
     *
     * @return integer
     */
    public function getHostPort()
    {
        return $this->hostPort;
    }

    /**
     * Set createPosts
     *
     * @param boolean $createPosts
     *
     * @return Domain
     */
    public function setCreatePosts($createPosts)
    {
        $this->createPosts = $createPosts;

        return $this;
    }

    /**
     * Get createPosts
     *
     * @return boolean
     */
    public function getCreatePosts()
    {
        return $this->createPosts;
    }

    /**
     * Set protocol
     *
     * @param boolean $protocol
     *
     * @return Domain
     */
    public function setProtocol($protocol)
    {
        $this->protocol = $protocol;

        return $this;
    }

    /**
     * Get protocol
     *
     * @return boolean
     */
    public function getProtocol()
    {
        return $this->protocol;
    }

    /**
     * Set nodePortId
     *
     * @param integer $nodePortId
     *
     * @return Domain
     */
    public function setNodePortId($nodePortId)
    {
        $this->nodePortId = $nodePortId;

        return $this;
    }

    /**
     * Get nodePortId
     *
     * @return integer
     */
    public function getNodePortId()
    {
        return $this->nodePortId;
    }

    /**
     * Add domainSportSlug
     *
     * @param \AppBundle\Entity\DomainSportSlug $domainSportSlug
     *
     * @return Domain
     */
    public function addDomainSportSlug(\AppBundle\Entity\DomainSportSlug $domainSportSlug)
    {
        $this->domainSportSlugs[] = $domainSportSlug;

        return $this;
    }

    /**
     * Remove domainSportSlug
     *
     * @param \AppBundle\Entity\DomainSportSlug $domainSportSlug
     */
    public function removeDomainSportSlug(\AppBundle\Entity\DomainSportSlug $domainSportSlug)
    {
        $this->domainSportSlugs->removeElement($domainSportSlug);
    }

    /**
     * Get domainSportSlugs
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getDomainSportSlugs()
    {
        return $this->domainSportSlugs;
    }

    /**
     * Set isStartAbs
     *
     * @param boolean $isStartAbs
     *
     * @return Domain
     */
    public function setIsStartAbs($isStartAbs)
    {
        $this->isStartAbs = $isStartAbs;

        return $this;
    }

    /**
     * Get isStartAbs
     *
     * @return boolean
     */
    public function getIsStartAbs()
    {
        return $this->isStartAbs;
    }
}
