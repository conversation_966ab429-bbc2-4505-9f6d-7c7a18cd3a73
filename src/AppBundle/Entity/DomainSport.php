<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * DomainSport
 *
 * @ORM\Table(name="domain_sport")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\DomainSportRepository")
 */
class DomainSport
{
  /**
   * @ORM\Column(type="integer")
   * @ORM\Id
   * @ORM\GeneratedValue
   */
  private $id;

  /**
   * @ORM\ManyToOne(targetEntity="Domain", inversedBy="domainSports")
   * @ORM\JoinColumn(name="domain_id", referencedColumnName="id")
   */
  private $domain;

  /**
   * @ORM\ManyToOne(targetEntity="Sport", inversedBy="domainSports")
   * @ORM\JoinColumn(name="sport_id", referencedColumnName="id")
   */
  private $sport;

  /**
   * @var bool
   *
   * @ORM\Column(name="has_standings", type="boolean")
   */
  private $hasStandings;

  /**
   * @var bool
   *
   * @ORM\Column(name="has_posts", type="boolean")
   */
  private $hasPosts;

  /**
   * @var bool
   *
   * @ORM\Column(name="show_as_region", type="boolean")
   */
  private $showAsRegion;

  /**
   * @var \DateTime $createdAt
   *
   * @Gedmo\Timestampable(on="create")
   * @ORM\Column(name="created_at", type="datetime")
   */
  private $createdAt;

  /**
   * @var \DateTime $updatedAt
   *
   * @Gedmo\Timestampable(on="update")
   * @ORM\Column(name="updated_at", type="datetime", nullable=true)
   */
  private $updatedAt;

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return DomainSport
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return DomainSport
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set domain
     *
     * @param \AppBundle\Entity\Domain $domain
     *
     * @return DomainSport
     */
    public function setDomain(\AppBundle\Entity\Domain $domain = null)
    {
        $this->domain = $domain;

        return $this;
    }

    /**
     * Get domain
     *
     * @return \AppBundle\Entity\Domain
     */
    public function getDomain()
    {
        return $this->domain;
    }

    /**
     * Set hasStandings
     *
     * @param boolean $hasStandings
     *
     * @return DomainSport
     */
    public function setHasStandings($hasStandings)
    {
        $this->hasStandings = $hasStandings;

        return $this;
    }

    /**
     * Get hasStandings
     *
     * @return boolean
     */
    public function getHasStandings()
    {
        return $this->hasStandings;
    }

    /**
     * Set hasPosts
     *
     * @param boolean $hasPosts
     *
     * @return DomainSport
     */
    public function setHasPosts($hasPosts)
    {
        $this->hasPosts = $hasPosts;

        return $this;
    }

    /**
     * Get hasPosts
     *
     * @return boolean
     */
    public function getHasPosts()
    {
        return $this->hasPosts;
    }

    /**
     * Set showAsRegion
     *
     * @param boolean $showAsRegion
     *
     * @return DomainSport
     */
    public function setShowAsRegion($showAsRegion)
    {
        $this->showAsRegion = $showAsRegion;

        return $this;
    }

    /**
     * Get showAsRegion
     *
     * @return boolean
     */
    public function getShowAsRegion()
    {
        return $this->showAsRegion;
    }

    /**
     * Set sport
     *
     * @param \AppBundle\Entity\Sport $sport
     *
     * @return DomainSport
     */
    public function setSport(\AppBundle\Entity\Sport $sport = null)
    {
        $this->sport = $sport;

        return $this;
    }

    /**
     * Get sport
     *
     * @return \AppBundle\Entity\Sport
     */
    public function getSport()
    {
        return $this->sport;
    }
}
