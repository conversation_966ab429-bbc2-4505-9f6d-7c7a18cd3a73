<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * TeamFootballField
 *
 * @ORM\Table(name="team_football_field",
 *    uniqueConstraints={
 *        @ORM\UniqueConstraint(name="team_football_field_unique", columns={"team_id", "football_field_id"})
 *    }
 * )
 * @ORM\Entity(repositoryClass="AppBundle\Repository\TeamFootballFieldRepository")
 */
class TeamFootballField
{
    /**
     * @ORM\Column(name="id", type="integer", options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="FtTeam", inversedBy="teamFootballFields")
     * @ORM\JoinColumn(name="team_id", referencedColumnName="id")
     */
    private $team;

    /**
     * @ORM\ManyToOne(targetEntity="FootballField", inversedBy="teamFootballFields")
     * @ORM\JoinColumn(name="football_field_id", referencedColumnName="id")
     */
    private $footballField;

    /**
     * @var bool
     *
     * @ORM\Column(name="is_default", type="boolean", options={"default":false})
     */
    private $isDefault;

    /**
     * @var \DateTime $createdAt
     *
     * @Gedmo\Timestampable(on="create")
     * @ORM\Column(name="created_at", type="datetime")
     */
    private $createdAt;

    /**
     * @var \DateTime $updatedAt
     *
     * @Gedmo\Timestampable(on="update")
     * @ORM\Column(name="updated_at", type="datetime", nullable=true)
     */
    private $updatedAt;

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return DomainSport
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return DomainSport
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set team
     *
     * @param \AppBundle\Entity\FtTeam $team
     *
     * @return TeamFootballField
     */
    public function setTeam(\AppBundle\Entity\FtTeam $team = null)
    {
        $this->team = $team;

        return $this;
    }

    /**
     * Get team
     *
     * @return \AppBundle\Entity\FtTeam
     */
    public function getTeam()
    {
        return $this->team;
    }

    /**
     * Set isDefault
     *
     * @param boolean $isDefault
     *
     * @return TeamFootballField
     */
    public function setIsDefault($isDefault)
    {
        $this->isDefault = $isDefault;

        return $this;
    }

    /**
     * Get isDefault
     *
     * @return boolean
     */
    public function getIsDefault()
    {
        return $this->isDefault;
    }

    /**
     * Set footballField
     *
     * @param \AppBundle\Entity\FootballField $footballField
     *
     * @return TeamFootballField
     */
    public function setFootballField(\AppBundle\Entity\FootballField $footballField = null)
    {
        $this->footballField = $footballField;

        return $this;
    }

    /**
     * Get footballField
     *
     * @return \AppBundle\Entity\FootballField
     */
    public function getFootballField()
    {
        return $this->footballField;
    }
}
