<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Ged<PERSON>;

/**
 * FtLeagueStandings
 *
 * @ORM\Table(name="ft_league_standings", uniqueConstraints={@ORM\UniqueConstraint(columns={"sport_id", "l_id", "season", "team_id"})}, indexes={@ORM\Index(name="rank_idx", columns={"rank"})})
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtLeagueStandingsRepository")
 */
class FtLeagueStandings
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\Column(name="sport_id", type="integer", options={"unsigned"=true})
     */
    private $sportId;

    /**
     * @var string
     *
     * @ORM\Column(name="l_id", type="string", length=10)
     */
    private $lId;

    /**
     * @var string
     *
     * @ORM\Column(name="season", type="string", length=10)
     */
    private $season;

    /**
     * @var int
     *
     * @ORM\Column(name="team_id", type="integer", options={"unsigned"=true})
     */
    private $teamId;

    /**
     * @var int
     *
     * @ORM\Column(name="rank", type="integer", options={"unsigned"=true})
     */
    private $rank;

    /**
     * @var string
     *
     * @ORM\Column(name="cnf", type="string", length=5, nullable=true)
     */
    private $cnf;

    /**
     * @var int
     *
     * @ORM\Column(name="overall_p", type="smallint", options={"unsigned"=true})
     */
    private $overallP;

    /**
     * @var int
     *
     * @ORM\Column(name="overall_w", type="smallint", options={"unsigned"=true})
     */
    private $overallW;

    /**
     * @var int
     *
     * @ORM\Column(name="overall_d", type="smallint", options={"unsigned"=true})
     */
    private $overallD;

    /**
     * @var int
     *
     * @ORM\Column(name="overall_l", type="smallint", options={"unsigned"=true})
     */
    private $overallL;

    /**
     * @var int
     *
     * @ORM\Column(name="overall_gf", type="smallint", options={"unsigned"=true})
     */
    private $overallGF;

    /**
     * @var int
     *
     * @ORM\Column(name="overall_ga", type="smallint", options={"unsigned"=true})
     */
    private $overallGA;

    /**
     * @var int
     *
     * @ORM\Column(name="overall_pts", type="smallint")
     */
    private $overallPts;

    /**
     * @var int
     *
     * @ORM\Column(name="home_p", type="smallint", options={"unsigned"=true})
     */
    private $homeP;

    /**
     * @var int
     *
     * @ORM\Column(name="home_w", type="smallint", options={"unsigned"=true})
     */
    private $homeW;

    /**
     * @var int
     *
     * @ORM\Column(name="home_d", type="smallint", options={"unsigned"=true})
     */
    private $homeD;

    /**
     * @var int
     *
     * @ORM\Column(name="home_l", type="smallint", options={"unsigned"=true})
     */
    private $homeL;

    /**
     * @var int
     *
     * @ORM\Column(name="home_gf", type="smallint", options={"unsigned"=true})
     */
    private $homeGF;

    /**
     * @var int
     *
     * @ORM\Column(name="home_ga", type="smallint", options={"unsigned"=true})
     */
    private $homeGA;

    /**
     * @var int
     *
     * @ORM\Column(name="away_p", type="smallint", options={"unsigned"=true})
     */
    private $awayP;

    /**
     * @var int
     *
     * @ORM\Column(name="away_w", type="smallint", options={"unsigned"=true})
     */
    private $awayW;

    /**
     * @var int
     *
     * @ORM\Column(name="away_d", type="smallint", options={"unsigned"=true})
     */
    private $awayD;

    /**
     * @var int
     *
     * @ORM\Column(name="away_l", type="smallint", options={"unsigned"=true})
     */
    private $awayL;

    /**
     * @var int
     *
     * @ORM\Column(name="away_gf", type="smallint", options={"unsigned"=true})
     */
    private $awayGF;

    /**
     * @var int
     *
     * @ORM\Column(name="away_ga", type="smallint", options={"unsigned"=true})
     */
    private $awayGA;

    /**
     * @var int
     *
     * @ORM\Column(name="penalty_pts", type="smallint")
     */
    private $penaltyPts;

    /**
     * @var int
     *
     * @ORM\Column(name="penalty_hgf", type="smallint")
     */
    private $penaltyHGF;

    /**
     * @var int
     *
     * @ORM\Column(name="penalty_hga", type="smallint")
     */
    private $penaltyHGA;

    /**
     * @var int
     *
     * @ORM\Column(name="penalty_agf", type="smallint")
     */
    private $penaltyAGF;

    /**
     * @var int
     *
     * @ORM\Column(name="penalty_aga", type="smallint")
     */
    private $penaltyAGA;

    /**
     * @var \DateTime $createdAt
     *
     * @Gedmo\Timestampable(on="create")
     * @ORM\Column(name="created_at", type="datetime")
     */
     private $createdAt;

     /**
      * @var \DateTime $updatedAt
      *
      * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
      * @ORM\Column(name="updated_at", type="datetime", nullable=true)
      */
     private $updatedAt;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set sportId
     *
     * @param integer $sportId
     *
     * @return FtLeagueStandings
     */
    public function setSportId($sportId)
    {
        $this->sportId = $sportId;

        return $this;
    }

    /**
     * Get sportId
     *
     * @return int
     */
    public function getSportId()
    {
        return $this->sportId;
    }

    /**
     * Set lId
     *
     * @param string $lId
     *
     * @return FtLeagueStandings
     */
    public function setLId($lId)
    {
        $this->lId = $lId;

        return $this;
    }

    /**
     * Get lId
     *
     * @return string
     */
    public function getLId()
    {
        return $this->lId;
    }

    /**
     * Set season
     *
     * @param string $season
     *
     * @return FtLeagueStandings
     */
    public function setSeason($season)
    {
        $this->season = $season;

        return $this;
    }

    /**
     * Get season
     *
     * @return string
     */
    public function getSeason()
    {
        return $this->season;
    }

    /**
     * Set teamId
     *
     * @param integer $teamId
     *
     * @return FtLeagueStandings
     */
    public function setTeamId($teamId)
    {
        $this->teamId = $teamId;

        return $this;
    }

    /**
     * Get teamId
     *
     * @return int
     */
    public function getTeamId()
    {
        return $this->teamId;
    }

    /**
     * Set rank
     *
     * @param integer $rank
     *
     * @return FtLeagueStandings
     */
    public function setRank($rank)
    {
        $this->rank = $rank;

        return $this;
    }

    /**
     * Get rank
     *
     * @return int
     */
    public function getRank()
    {
        return $this->rank;
    }

    /**
     * Set cnf
     *
     * @param string $cnf
     *
     * @return FtLeagueStandings
     */
    public function setCnf($cnf)
    {
        $this->cnf = $cnf;

        return $this;
    }

    /**
     * Get cnf
     *
     * @return string
     */
    public function getCnf()
    {
        return $this->cnf;
    }

    /**
     * Set overallP
     *
     * @param integer $overallP
     *
     * @return FtLeagueStandings
     */
    public function setOverallP($overallP)
    {
        $this->overallP = $overallP;

        return $this;
    }

    /**
     * Get overallP
     *
     * @return int
     */
    public function getOverallP()
    {
        return $this->overallP;
    }

    /**
     * Set overallW
     *
     * @param integer $overallW
     *
     * @return FtLeagueStandings
     */
    public function setOverallW($overallW)
    {
        $this->overallW = $overallW;

        return $this;
    }

    /**
     * Get overallW
     *
     * @return int
     */
    public function getOverallW()
    {
        return $this->overallW;
    }

    /**
     * Set overallD
     *
     * @param integer $overallD
     *
     * @return FtLeagueStandings
     */
    public function setOverallD($overallD)
    {
        $this->overallD = $overallD;

        return $this;
    }

    /**
     * Get overallD
     *
     * @return int
     */
    public function getOverallD()
    {
        return $this->overallD;
    }

    /**
     * Set overallL
     *
     * @param integer $overallL
     *
     * @return FtLeagueStandings
     */
    public function setOverallL($overallL)
    {
        $this->overallL = $overallL;

        return $this;
    }

    /**
     * Get overallL
     *
     * @return int
     */
    public function getOverallL()
    {
        return $this->overallL;
    }

    /**
     * Set overallGF
     *
     * @param integer $overallGF
     *
     * @return FtLeagueStandings
     */
    public function setOverallGF($overallGF)
    {
        $this->overallGF = $overallGF;

        return $this;
    }

    /**
     * Get overallGF
     *
     * @return int
     */
    public function getOverallGF()
    {
        return $this->overallGF;
    }

    /**
     * Set overallGA
     *
     * @param integer $overallGA
     *
     * @return FtLeagueStandings
     */
    public function setOverallGA($overallGA)
    {
        $this->overallGA = $overallGA;

        return $this;
    }

    /**
     * Get overallGA
     *
     * @return int
     */
    public function getOverallGA()
    {
        return $this->overallGA;
    }

    /**
     * Set overallPts
     *
     * @param integer $overallPts
     *
     * @return FtLeagueStandings
     */
    public function setOverallPts($overallPts)
    {
        $this->overallPts = $overallPts;

        return $this;
    }

    /**
     * Get overallPts
     *
     * @return int
     */
    public function getOverallPts()
    {
        return $this->overallPts;
    }

    /**
     * Set homeP
     *
     * @param integer $homeP
     *
     * @return FtLeagueStandings
     */
    public function setHomeP($homeP)
    {
        $this->homeP = $homeP;

        return $this;
    }

    /**
     * Get homeP
     *
     * @return int
     */
    public function getHomeP()
    {
        return $this->homeP;
    }

    /**
     * Set homeD
     *
     * @param integer $homeD
     *
     * @return FtLeagueStandings
     */
    public function setHomeD($homeD)
    {
        $this->homeD = $homeD;

        return $this;
    }

    /**
     * Get homeD
     *
     * @return int
     */
    public function getHomeD()
    {
        return $this->homeD;
    }

    /**
     * Set homeL
     *
     * @param integer $homeL
     *
     * @return FtLeagueStandings
     */
    public function setHomeL($homeL)
    {
        $this->homeL = $homeL;

        return $this;
    }

    /**
     * Get homeL
     *
     * @return int
     */
    public function getHomeL()
    {
        return $this->homeL;
    }

    /**
     * Set homeGF
     *
     * @param integer $homeGF
     *
     * @return FtLeagueStandings
     */
    public function setHomeGF($homeGF)
    {
        $this->homeGF = $homeGF;

        return $this;
    }

    /**
     * Get homeGF
     *
     * @return int
     */
    public function getHomeGF()
    {
        return $this->homeGF;
    }

    /**
     * Set homeGA
     *
     * @param integer $homeGA
     *
     * @return FtLeagueStandings
     */
    public function setHomeGA($homeGA)
    {
        $this->homeGA = $homeGA;

        return $this;
    }

    /**
     * Get homeGA
     *
     * @return int
     */
    public function getHomeGA()
    {
        return $this->homeGA;
    }

    /**
     * Set awayP
     *
     * @param integer $awayP
     *
     * @return FtLeagueStandings
     */
    public function setAwayP($awayP)
    {
        $this->awayP = $awayP;

        return $this;
    }

    /**
     * Get awayP
     *
     * @return int
     */
    public function getAwayP()
    {
        return $this->awayP;
    }

    /**
     * Set awayW
     *
     * @param integer $awayW
     *
     * @return FtLeagueStandings
     */
    public function setAwayW($awayW)
    {
        $this->awayW = $awayW;

        return $this;
    }

    /**
     * Get awayW
     *
     * @return int
     */
    public function getAwayW()
    {
        return $this->awayW;
    }

    /**
     * Set awayD
     *
     * @param integer $awayD
     *
     * @return FtLeagueStandings
     */
    public function setAwayD($awayD)
    {
        $this->awayD = $awayD;

        return $this;
    }

    /**
     * Get awayD
     *
     * @return int
     */
    public function getAwayD()
    {
        return $this->awayD;
    }

    /**
     * Set awayL
     *
     * @param integer $awayL
     *
     * @return FtLeagueStandings
     */
    public function setAwayL($awayL)
    {
        $this->awayL = $awayL;

        return $this;
    }

    /**
     * Get awayL
     *
     * @return int
     */
    public function getAwayL()
    {
        return $this->awayL;
    }

    /**
     * Set awayGF
     *
     * @param integer $awayGF
     *
     * @return FtLeagueStandings
     */
    public function setAwayGF($awayGF)
    {
        $this->awayGF = $awayGF;

        return $this;
    }

    /**
     * Get awayGF
     *
     * @return int
     */
    public function getAwayGF()
    {
        return $this->awayGF;
    }

    /**
     * Set awayGA
     *
     * @param integer $awayGA
     *
     * @return FtLeagueStandings
     */
    public function setAwayGA($awayGA)
    {
        $this->awayGA = $awayGA;

        return $this;
    }

    /**
     * Get awayGA
     *
     * @return int
     */
    public function getAwayGA()
    {
        return $this->awayGA;
    }

    /**
     * Set penaltyPts
     *
     * @param integer $penaltyPts
     *
     * @return FtLeagueStandings
     */
    public function setPenaltyPts($penaltyPts)
    {
        $this->penaltyPts = $penaltyPts;

        return $this;
    }

    /**
     * Get penaltyPts
     *
     * @return int
     */
    public function getPenaltyPts()
    {
        return $this->penaltyPts;
    }

    /**
     * Set penaltyHGF
     *
     * @param integer $penaltyHGF
     *
     * @return FtLeagueStandings
     */
    public function setPenaltyHGF($penaltyHGF)
    {
        $this->penaltyHGF = $penaltyHGF;

        return $this;
    }

    /**
     * Get penaltyHGF
     *
     * @return int
     */
    public function getPenaltyHGF()
    {
        return $this->penaltyHGF;
    }

    /**
     * Set penaltyHGA
     *
     * @param integer $penaltyHGA
     *
     * @return FtLeagueStandings
     */
    public function setPenaltyHGA($penaltyHGA)
    {
        $this->penaltyHGA = $penaltyHGA;

        return $this;
    }

    /**
     * Get penaltyHGA
     *
     * @return int
     */
    public function getPenaltyHGA()
    {
        return $this->penaltyHGA;
    }

    /**
     * Set penaltyAGF
     *
     * @param integer $penaltyAGF
     *
     * @return FtLeagueStandings
     */
    public function setPenaltyAGF($penaltyAGF)
    {
        $this->penaltyAGF = $penaltyAGF;

        return $this;
    }

    /**
     * Get penaltyAGF
     *
     * @return int
     */
    public function getPenaltyAGF()
    {
        return $this->penaltyAGF;
    }

    /**
     * Set penaltyAGA
     *
     * @param integer $penaltyAGA
     *
     * @return FtLeagueStandings
     */
    public function setPenaltyAGA($penaltyAGA)
    {
        $this->penaltyAGA = $penaltyAGA;

        return $this;
    }

    /**
     * Get penaltyAGA
     *
     * @return int
     */
    public function getPenaltyAGA()
    {
        return $this->penaltyAGA;
    }

    /**
     * Set homeW
     *
     * @param integer $homeW
     *
     * @return FtLeagueStandings
     */
    public function setHomeW($homeW)
    {
        $this->homeW = $homeW;

        return $this;
    }

    /**
     * Get homeW
     *
     * @return integer
     */
    public function getHomeW()
    {
        return $this->homeW;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return FtLeagueStandings
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return FtLeagueStandings
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }
}
