<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * FtCompetitionSchedule
 *
 * @ORM\Table(name="ft_competition_schedule")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtCompetitionScheduleRepository")
 */
class FtCompetitionSchedule
{
    public const MATCH_TYPE_EURO = 'euro';
    public const MATCH_TYPE_COPA_AMERICA = 'copa_america';
    public const MATCH_TYPE_FIFA_CLUB_WORLD_CUP = 'fifa_club_world_cup';
    public const MATCH_TYPE_FIFA_WORLD_CUP = 'fifa_world_cup';
    public const MATCH_TYPE_EUROLEAGUE_BASKET = 'euroleague_basket';
    public const MATCH_TYPE_EUROBASKET = 'eurobasket';
    public const MATCH_TYPE_FIBA_WORLD_CUP_BASKET = 'fiba_world_cup_basket';
  /**
   * @var int
   *
   * @ORM\Column(name="id", type="integer")
   * @ORM\Id
   * @ORM\GeneratedValue(strategy="AUTO")
   */
  private $id;

  /**
   * @ORM\Column(name="h_team", type="integer")
   */
  private $hTeam;

  /**
   * @ORM\Column(name="a_team", type="integer")
   */
  private $aTeam;

  /**
   * @ORM\Column(name="link_betarades", type="string", length=255, nullable=true)
   */
  private $linkBetarades;

  /**
   * @ORM\Column(name="link_bethome", type="string", length=255, nullable=true)
   */
  private $linkBethome;

  /**
   * @ORM\Column(name="link_pariurix", type="string", length=255, nullable=true)
   */
  private $linkPariurix;

    /**
   * @ORM\Column(name="link_text_betarades", type="string", length=50, nullable=true)
   */
  private $linkTextBetarades;

  /**
   * @ORM\Column(name="link_text_bethome", type="string", length=50, nullable=true)
   */
  private $linkTextBethome;

  /**
   * @ORM\Column(name="link_text_pariurix", type="string", length=50, nullable=true)
   */
  private $linkTextPariurix;

  /**
   * @ORM\Column(name="tv_gr", type="string", length=50, nullable=true)
   */
  private $tvGr;

  /**
   * @ORM\Column(name="tv_ro", type="string", length=50, nullable=true)
   */
  private $tvRo;

  /**
   * @ORM\Column(name="match_datetime", type="datetime")
   */
  private $matchDatetime;

  /**
   * @ORM\Column(name="score", type="string", length=50, nullable=true)
   */
  private $score;

  /**
   * @var bool
   *
   * @ORM\Column(name="is_hot", type="boolean", options={"default":false})
   */
  private $isHot;

    /**
     * @ORM\Column(name="home_odds", type="float", nullable=true)
     */
    private $homeOdds;

    /**
     * @ORM\Column(name="away_odds", type="float", nullable=true)
     */
    private $awayOdds;

    /**
     * @ORM\Column(name="affiliate_link", type="string", length=255, nullable=true)
     */
    private $affiliateLink;

    /**
     * @ORM\Column(name="match_type", type="string", length=20, nullable=true)
     */
    private $matchType;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set homeOdds
     *
     * @param float $homeOdds
     *
     * @return FtCompetitionSchedule
     */
    public function setHomeOdds($homeOdds)
    {
        $this->homeOdds = $homeOdds;

        return $this;
    }

    /**
     * Get homeOdds
     *
     * @return float
     */
    public function getHomeOdds()
    {
        return $this->homeOdds;
    }

    /**
     * Set awayOdds
     *
     * @param float $awayOdds
     *
     * @return FtCompetitionSchedule
     */
    public function setAwayOdds($awayOdds)
    {
        $this->awayOdds = $awayOdds;

        return $this;
    }

    /**
     * Get awayOdds
     *
     * @return float
     */
    public function getAwayOdds()
    {
        return $this->awayOdds;
    }

    /**
     * Set affiliateLink
     *
     * @param string $affiliateLink
     *
     * @return FtCompetitionSchedule
     */
    public function setAffiliateLink($affiliateLink)
    {
        $this->affiliateLink = $affiliateLink;

        return $this;
    }

    /**
     * Get affiliateLink
     *
     * @return string
     */
    public function getAffiliateLink()
    {
        return $this->affiliateLink;
    }

    /**
     * @var int
     *
     * @ORM\Column(name="phase_id", type="integer", nullable=true, options={"default" : 0})
     */
    private $phaseId = 0;

    /**
     * Set hTeam
     *
     * @param integer $hTeam
     *
     * @return FtCompetitionSchedule
     */
    public function setHTeam($hTeam)
    {
        $this->hTeam = $hTeam;

        return $this;
    }

    /**
     * Get hTeam
     *
     * @return int
     */
    public function getHTeam()
    {
        return $this->hTeam;
    }

    /**
     * Set aTeam
     *
     * @param integer $aTeam
     *
     * @return FtCompetitionSchedule
     */
    public function setATeam($aTeam)
    {
        $this->aTeam = $aTeam;

        return $this;
    }

    /**
     * Get aTeam
     *
     * @return int
     */
    public function getATeam()
    {
        return $this->aTeam;
    }

    /**
     * Set linkBetarades
     *
     * @param string $linkBetarades
     *
     * @return FtCompetitionSchedule
     */
    public function setLinkBetarades($linkBetarades)
    {
        $this->linkBetarades = $linkBetarades;

        return $this;
    }

    /**
     * Get linkBetarades
     *
     * @return string
     */
    public function getLinkBetarades()
    {
        return $this->linkBetarades;
    }

    /**
     * Set linkBethome
     *
     * @param string $linkBethome
     *
     * @return FtCompetitionSchedule
     */
    public function setLinkBethome($linkBethome)
    {
        $this->linkBethome = $linkBethome;

        return $this;
    }

    /**
     * Get linkBethome
     *
     * @return string
     */
    public function getLinkBethome()
    {
        return $this->linkBethome;
    }

    /**
     * Set linkPariurix
     *
     * @param string $linkPariurix
     *
     * @return FtCompetitionSchedule
     */
    public function setLinkPariurix($linkPariurix)
    {
        $this->linkPariurix = $linkPariurix;

        return $this;
    }

    /**
     * Get linkPariurix
     *
     * @return string
     */
    public function getLinkPariurix()
    {
        return $this->linkPariurix;
    }

    /**
     * Set linkTextBetarades
     *
     * @param string $linkTextBetarades
     *
     * @return FtCompetitionSchedule
     */
    public function setLinkTextBetarades($linkTextBetarades)
    {
        $this->linkTextBetarades = $linkTextBetarades;

        return $this;
    }

    /**
     * Get linkTextBetarades
     *
     * @return string
     */
    public function getLinkTextBetarades()
    {
        return $this->linkTextBetarades;
    }

    /**
     * Set linkTextBethome
     *
     * @param string $linkTextBethome
     *
     * @return FtCompetitionSchedule
     */
    public function setLinkTextBethome($linkTextBethome)
    {
        $this->linkTextBethome = $linkTextBethome;

        return $this;
    }

    /**
     * Get linkTextBethome
     *
     * @return string
     */
    public function getLinkTextBethome()
    {
        return $this->linkTextBethome;
    }

    /**
     * Set linkTextPariurix
     *
     * @param string $linkTextPariurix
     *
     * @return FtCompetitionSchedule
     */
    public function setLinkTextPariurix($linkTextPariurix)
    {
        $this->linkTextPariurix = $linkTextPariurix;

        return $this;
    }

    /**
     * Get linkTextPariurix
     *
     * @return string
     */
    public function getLinkTextPariurix()
    {
        return $this->linkTextPariurix;
    }

    /**
     * Set tvGr
     *
     * @param string $tvGr
     *
     * @return FtCompetitionSchedule
     */
    public function setTvGr($tvGr)
    {
        $this->tvGr = $tvGr;

        return $this;
    }

    /**
     * Get tvGr
     *
     * @return string
     */
    public function getTvGr()
    {
        return $this->tvGr;
    }

    /**
     * Set tvRo
     *
     * @param string $tvRo
     *
     * @return FtCompetitionSchedule
     */
    public function setTvRo($tvRo)
    {
        $this->tvRo = $tvRo;

        return $this;
    }

    /**
     * Get tvRo
     *
     * @return string
     */
    public function getTvRo()
    {
        return $this->tvRo;
    }


    /**
     * Set matchDatetime
     *
     * @param \DateTime $matchDatetime
     *
     * @return FtCompetitionSchedule
     */
    public function setMatchDatetime($matchDatetime)
    {
        $this->matchDatetime = $matchDatetime;

        return $this;
    }

    /**
     * Get matchDatetime
     *
     * @return \DateTime
     */
    public function getMatchDatetime()
    {
        return $this->matchDatetime;
    }

    /**
     * Set score
     *
     * @param string $score
     *
     * @return FtCompetitionSchedule
     */
    public function setScore($score)
    {
        $this->score = $score;

        return $this;
    }

    /**
     * Get score
     *
     * @return string
     */
    public function getScore()
    {
        return $this->score;
    }

    /**
     * Set isHot
     *
     * @param boolean $isHot
     *
     * @return FtCompetitionSchedule
     */
    public function setIsHot($isHot)
    {
        $this->isHot = $isHot;

        return $this;
    }

    /**
     * Get isHot
     *
     * @return boolean
     */
    public function getIsHot()
    {
        return $this->isHot;
    }

    public static function getMatchTypes(): array
    {
        return [
            'EURO' => self::MATCH_TYPE_EURO,
            'Copa América' => self::MATCH_TYPE_COPA_AMERICA,
            'FIFA Club World Cup' => self::MATCH_TYPE_FIFA_CLUB_WORLD_CUP,
            'FIFA World Cup' => self::MATCH_TYPE_FIFA_WORLD_CUP,
            'EuroLeague (Basketball)' => self::MATCH_TYPE_EUROLEAGUE_BASKET,
            'EuroBasket (Basketball)' => self::MATCH_TYPE_EUROBASKET,
            'FIBA World Cup (Basketball)' => self::MATCH_TYPE_FIBA_WORLD_CUP_BASKET,
        ];
    }

// Getter and setter
    public function getMatchType(): ?string
    {
        return $this->matchType;
    }

    public function setMatchType(?string $matchType): self
    {
        if ($matchType === null) {
            $this->matchType = null;
            return $this;
        }

        if (!in_array($matchType, self::getMatchTypes(), true)) {
            throw new \InvalidArgumentException("Invalid match type: $matchType");
        }

        $this->matchType = $matchType;
        return $this;
    }

    /**
     * Set phaseId
     *
     * @param integer|null $phaseId
     *
     * @return FtCompetitionSchedule
     */
    public function setPhaseId($phaseId)
    {
        if ($phaseId === null || $phaseId === '') {
            $this->phaseId = null;
        } else {
            $this->phaseId = (int)$phaseId;
        }

        return $this;
    }

    /**
     * Get phaseId
     *
     * @return integer
     */
    public function getPhaseId()
    {
        return $this->phaseId;
    }
}
