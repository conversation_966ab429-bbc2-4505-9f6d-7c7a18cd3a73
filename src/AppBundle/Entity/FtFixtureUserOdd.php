<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * FtFixtureOdd
 *
 * @ORM\Table(name="ft_fixture_user_odd", indexes={@ORM\Index(name="search_idx", columns={"match_datetime", "author_id", "fixture_id", "odd_type_id"})})
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtFixtureUserOddRepository")
 */
class FtFixtureUserOdd
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer", options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\Column(name="fixture_id", type="integer")
     */
    private $fixtureId;

    /**
     * @var datetime
     *
     * @ORM\Column(name="match_datetime", type="datetime")
     */
    private $matchDatetime;

    /**
     * @var int
     *
     * @ORM\Column(name="sport_id", type="integer", options={"unsigned"=true})
     */
		private $sportId;

    /**
     * @var int
     *
     * @ORM\Column(name="odd_type_id", type="integer", options={"unsigned"=true})
     */
		private $oddTypeId;

    /**
     * @var string
     *
     * @ORM\Column(name="odd_value", type="decimal", precision=6, scale=2)
     */
		private $oddValue = 0;
		
    /**
     * @var int
     *
     * @ORM\Column(name="author_id", type="integer", options={"unsigned"=true})
     */
    private $authorId;

    /**
     * @var string
     *
     * @ORM\Column(name="author_name", type="string", length=191)
     */
    private $authorName;

    /**
     * @var bool
     *
     * @ORM\Column(name="odd_result", type="smallint", nullable=true, options={"unsigned"=true})
     */
    private $oddResult;

    /**
     * @var \DateTime $createdAt
     *
     * @Gedmo\Timestampable(on="create")
     * @ORM\Column(name="created_at", type="datetime")
     */
     private $createdAt;


    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set fixtureId
     *
     * @param integer $fixtureId
     *
     * @return FtFixtureUserOdd
     */
    public function setFixtureId($fixtureId)
    {
        $this->fixtureId = $fixtureId;

        return $this;
    }

    /**
     * Get fixtureId
     *
     * @return integer
     */
    public function getFixtureId()
    {
        return $this->fixtureId;
    }

    /**
     * Set matchDatetime
     *
     * @param \DateTime $matchDatetime
     *
     * @return FtFixtureUserOdd
     */
    public function setMatchDatetime($matchDatetime)
    {
        $this->matchDatetime = $matchDatetime;

        return $this;
    }

    /**
     * Get matchDatetime
     *
     * @return \DateTime
     */
    public function getMatchDatetime()
    {
        return $this->matchDatetime;
    }

    /**
     * Set sportId
     *
     * @param integer $sportId
     *
     * @return FtFixtureUserOdd
     */
    public function setSportId($sportId)
    {
        $this->sportId = $sportId;

        return $this;
    }

    /**
     * Get sportId
     *
     * @return integer
     */
    public function getSportId()
    {
        return $this->sportId;
    }

    /**
     * Set oddTypeId
     *
     * @param integer $oddTypeId
     *
     * @return FtFixtureUserOdd
     */
    public function setOddTypeId($oddTypeId)
    {
        $this->oddTypeId = $oddTypeId;

        return $this;
    }

    /**
     * Get oddTypeId
     *
     * @return integer
     */
    public function getOddTypeId()
    {
        return $this->oddTypeId;
    }

    /**
     * Set oddValue
     *
     * @param string $oddValue
     *
     * @return FtFixtureUserOdd
     */
    public function setOddValue($oddValue)
    {
        $this->oddValue = $oddValue;

        return $this;
    }

    /**
     * Get oddValue
     *
     * @return string
     */
    public function getOddValue()
    {
        return $this->oddValue;
    }

    /**
     * Set authorId
     *
     * @param integer $authorId
     *
     * @return FtFixtureUserOdd
     */
    public function setAuthorId($authorId)
    {
        $this->authorId = $authorId;

        return $this;
    }

    /**
     * Get authorId
     *
     * @return integer
     */
    public function getAuthorId()
    {
        return $this->authorId;
    }

    /**
     * Set authorName
     *
     * @param string $authorName
     *
     * @return FtFixtureUserOdd
     */
    public function setAuthorName($authorName)
    {
        $this->authorName = $authorName;

        return $this;
    }

    /**
     * Get authorName
     *
     * @return string
     */
    public function getAuthorName()
    {
        return $this->authorName;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return FtFixtureUserOdd
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set oddResult
     *
     * @param boolean $oddResult
     *
     * @return FtFixtureUserOdd
     */
    public function setOddResult($oddResult)
    {
        $this->oddResult = $oddResult;

        return $this;
    }

    /**
     * Get oddResult
     *
     * @return boolean
     */
    public function getOddResult()
    {
        return $this->oddResult;
    }
}
