<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * OddType
 *
 * @ORM\Table(name="odd_type")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\OddTypeRepository")
 */
class OddType
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer", options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=100, unique=true)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="rule_success", type="string", length=255)
     */
    private $ruleSuccess;

    /**
     * @var string
     *
     * @ORM\Column(name="rule_void", type="string", length=255, nullable=true)
     */
    private $ruleVoid;

    /**
     * @var \DateTime $createdAt
     *
     * @Gedmo\Timestampable(on="create")
     * @ORM\Column(name="created_at", type="datetime")
     */
    private $createdAt;
  
    /**
     * @var \DateTime $updatedAt
     *
     * @Gedmo\Timestampable(on="update")
     * @ORM\Column(name="updated_at", type="datetime", nullable=true)
     */
    private $updatedAt;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return OddType
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set ruleSuccess
     *
     * @param string $ruleSuccess
     *
     * @return OddType
     */
    public function setRuleSuccess($ruleSuccess)
    {
        $this->ruleSuccess = $ruleSuccess;

        return $this;
    }

    /**
     * Get ruleSuccess
     *
     * @return string
     */
    public function getRuleSuccess()
    {
        return $this->ruleSuccess;
    }

    /**
     * Set ruleVoid
     *
     * @param string $ruleVoid
     *
     * @return OddType
     */
    public function setRuleVoid($ruleVoid)
    {
        $this->ruleVoid = $ruleVoid;

        return $this;
    }

    /**
     * Get ruleVoid
     *
     * @return string
     */
    public function getRuleVoid()
    {
        return $this->ruleVoid;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return OddType
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return OddType
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }
}
