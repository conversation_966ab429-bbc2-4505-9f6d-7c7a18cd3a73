<?php

namespace AppBundle\Entity\SuperClass;

use Doctrine\ORM\Mapping as ORM;
/*
 * @ORM\Entity(repositoryClass="AppBundle\Repository\SportTranslationRepository")
 */
abstract class AbstractSportTranslation
{
  /**
   * @ORM\Column(type="integer")
   * @ORM\Id
   * @ORM\GeneratedValue
   */
  protected $id;
  /**
   * @var string $locale
   *
   * @ORM\Column(length=10)
   */
  protected $locale;
  /**
   * Related entity with ManyToOne relation
   * must be mapped by user
   */
  protected $sport;
  /**
   * @var string $title
   *
   * @ORM\Column(length=255)
   */
  protected $title;
  /**
   * @var string $description
   *
   * @ORM\Column(length=255)
   */
  protected $description;
  /**
   * Get id
   *
   * @return integer $id
   */
  public function getId()
  {
      return $this->id;
  }
  /**
   * Set locale
   *
   * @param string $locale
   *
   * @return static
   */
  public function setLocale($locale)
  {
      $this->locale = $locale;
      return $this;
  }
  /**
   * Get locale
   *
   * @return string
   */
  public function getLocale()
  {
      return $this->locale;
  }
  /**
   * Set object related
   *
   * @param sport $sport
   *
   * @return static
   */
  public function setSport($sport)
  {
      $this->sport = $sport;
      return $this;
  }
  /**
   * Get sport related
   *
   * @return string
   */
  public function getSport()
  {
      return $this->sport;
  }
  /**
   * Set title
   *
   * @param string $title
   *
   * @return static
   */
  public function setTitle($title)
  {
      $this->title = $title;
      return $this;
  }
  /**
   * Get title
   *
   * @return string
   */
  public function getTitle()
  {
      return $this->title;
  }
  /**
   * Set description
   *
   * @param string $description
   *
   * @return static
   */
  public function setDescription($description)
  {
      $this->description = $description;
      return $this;
  }
  /**
   * Get description
   *
   * @return string
   */
  public function getDescription()
  {
      return $this->description;
  }
}
