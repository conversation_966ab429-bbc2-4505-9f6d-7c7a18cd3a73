<?php

namespace AppBundle\Entity;

use <PERSON>trine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;

/**
 * FtTeamFormation
 *
 * @ORM\Table(name="ft_team_formation")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtTeamFormationRepository")
 */
class FtTeamFormation
{
  /**
   * @var int
   *
   * @ORM\Column(name="id", type="integer", options={"unsigned"=true})
   * @ORM\Id
   * @ORM\GeneratedValue(strategy="AUTO")
   */
  private $id;

  /**
   * @var string
   *
   * @ORM\Column(name="name", type="string", length=100, unique=true)
   */
  private $name;

 /**
  * @var \DateTime $createdAt
  *
  * @Gedmo\Timestampable(on="create")
  * @ORM\Column(name="created_at", type="datetime")
  */
  private $createdAt;

  /**
   * @var \DateTime $updatedAt
   *
   * @Gedmo\Timestampable(on="change", field={"name"})
   * @ORM\Column(name="updated_at", type="datetime", nullable=true)
   */
	private $updatedAt;
	

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return FtTeamFormation
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return FtTeamFormation
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return FtTeamFormation
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }
}
