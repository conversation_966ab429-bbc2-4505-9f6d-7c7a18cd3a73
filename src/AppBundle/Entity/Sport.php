<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;


/**
 * @ORM\Entity
 * @Gedmo\Tree(type="nested")
 * @ORM\Table(name="sport")
 * use repository for handy tree functions
 *** @ORM\Entity(repositoryClass="Gedmo\Tree\Entity\Repository\NestedTreeRepository")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\SportRepository")
 * @Gedmo\TranslationEntity(class="AppBundle\Entity\SportTranslation")
 */
class Sport
{

  /**
   * @ORM\Column(type="integer")
   * @ORM\Id
   * @ORM\GeneratedValue
   */
  private $id;

  /**
   * @Gedmo\TreeLeft
   * @ORM\Column(type="integer")
   */
  private $lft;

  /**
   * @Gedmo\TreeLevel
   * @ORM\Column(type="integer")
   */
  private $lvl;

  /**
   * @Gedmo\TreeRight
   * @ORM\Column(type="integer")
   */
  private $rgt;

  /**
   * @Gedmo\TreeRoot
   * @ORM\ManyToOne(targetEntity="Sport", cascade={"persist"})
   * @ORM\JoinColumn(referencedColumnName="id", onDelete="CASCADE")
   */
  private $root;

  /**
   * @Gedmo\TreeParent
   * @ORM\ManyToOne(targetEntity="Sport", inversedBy="children", cascade={"persist"})
   * @ORM\JoinColumn(referencedColumnName="id", onDelete="CASCADE")
   */
  private $parent;

  /**
   * @ORM\OneToMany(targetEntity="Sport", mappedBy="parent")
   * @ORM\OrderBy({"lft" = "ASC"})
   */
  private $children;

//   /**
//    * @ORM\Column(length=128, unique=true)
//    */
//   private $slug;

  /**
   * @var bool
   *
   * @ORM\Column(name="is_sport", type="boolean")
   */
  private $isSport;

  /**
   * @var bool
   *
   * @ORM\Column(name="is_region", type="boolean")
   */
  private $isRegion;

  /**
   * @var bool
   *
   * @ORM\Column(name="is_competition", type="boolean")
   */
  private $isCompetition;

  /**
   * @var bool
   *
   * @ORM\Column(name="is_additional", type="boolean")
   */
  private $isAdditional;

  /**
   * @var bool
   *
   * @ORM\Column(name="has_additionals", type="boolean")
   */
  private $hasAdditionals;

  /**
   * @var datetime
   *
   * @ORM\Column(name="tips_date_from", type="datetime", nullable=true)
   */
  private $tipsDateFrom;

  /**
   * @var datetime
   *
   * @ORM\Column(name="tips_date_to", type="datetime", nullable=true)
   */
  private $tipsDateTo;

  /**
   * @ORM\OneToMany(targetEntity="DomainSport", mappedBy="sport", cascade={"persist"})
   */
  private $domainSports;

  /**
   * @ORM\OneToMany(targetEntity="DomainSportSlug", mappedBy="sport", cascade={"persist"})
   */
  private $domainSportSlugs;

  /**
   * @ORM\OneToOne(targetEntity="FtLeagueSport", mappedBy="sport", cascade={"persist", "remove"}, orphanRemoval=true)
   */
  private $ftLeagueSport;

  /**
   * @ORM\OneToOne(targetEntity="FtLeagueFlag", mappedBy="sport", cascade={"persist"})
   */
  private $ftLeagueFlag;

  /**
   * @ORM\OneToMany(targetEntity="FtLeagueSeasonSport", mappedBy="sport")
   */
  private $ftLeagueSeasonSports;

  /**
   * @ORM\Column(name="image_name", length=50, nullable=true)
   */
  private $imageName;

  /**
   * @ORM\OneToMany(
   *   targetEntity="SportTranslation",
   *   mappedBy="sport",
   *   cascade={"persist", "remove"}
   * )
   */
  private $translations;

  /**
   * @var smallint
   *
   * @ORM\Column(name="standings_priority", type="smallint", nullable=true, options={"default":1})
   */
  private $standingsPriority;

  /**
   * @var bool
   *
   * @ORM\Column(name="single_standings", type="boolean", options={"default":false})
   */
  private $singleStandings;

  /**
   * @var bool
   *
   * @ORM\Column(name="hide_in_stats", type="boolean", options={"default":false})
   */
  private $hideInStats;

  /**
   * @var \DateTime $createdAt
   *
   * @Gedmo\Timestampable(on="create")
   * @ORM\Column(name="created_at", type="datetime")
   */
  private $createdAt;

  /**
   * @var \DateTime $updatedAt
   *
   * @Gedmo\Timestampable(on="update")
   * @ORM\Column(name="updated_at", type="datetime", nullable=true)
   */
  private $updatedAt;

  /*
   * @Assert\Valid
   */
  // private $translations;
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->children = new \Doctrine\Common\Collections\ArrayCollection();
        $this->domainSports = new \Doctrine\Common\Collections\ArrayCollection();
        $this->translations = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set lft
     *
     * @param integer $lft
     *
     * @return Sport
     */
    public function setLft($lft)
    {
        $this->lft = $lft;

        return $this;
    }

    /**
     * Get lft
     *
     * @return integer
     */
    public function getLft()
    {
        return $this->lft;
    }

    /**
     * Set lvl
     *
     * @param integer $lvl
     *
     * @return Sport
     */
    public function setLvl($lvl)
    {
        $this->lvl = $lvl;

        return $this;
    }

    /**
     * Get lvl
     *
     * @return integer
     */
    public function getLvl()
    {
        return $this->lvl;
    }

    /**
     * Set rgt
     *
     * @param integer $rgt
     *
     * @return Sport
     */
    public function setRgt($rgt)
    {
        $this->rgt = $rgt;

        return $this;
    }

    /**
     * Get rgt
     *
     * @return integer
     */
    public function getRgt()
    {
        return $this->rgt;
    }

    /**
     * Set slug
     *
     * @param string $slug
     *
     * @return Sport
     */
    // public function setSlug($slug)
    // {
    //     $this->slug = $slug;

    //     return $this;
    // }

    /**
     * Get slug
     *
     * @return string
     */
    // public function getSlug()
    // {
    //     return $this->slug;
    // }

    /**
     * Set isSport
     *
     * @param boolean $isSport
     *
     * @return Sport
     */
    public function setIsSport($isSport)
    {
        $this->isSport = $isSport;

        return $this;
    }

    /**
     * Get isSport
     *
     * @return boolean
     */
    public function getIsSport()
    {
        return $this->isSport;
    }

    /**
     * Set isRegion
     *
     * @param boolean $isRegion
     *
     * @return Sport
     */
    public function setIsRegion($isRegion)
    {
        $this->isRegion = $isRegion;

        return $this;
    }

    /**
     * Get isRegion
     *
     * @return boolean
     */
    public function getIsRegion()
    {
        return $this->isRegion;
    }

    /**
     * Set isCompetition
     *
     * @param boolean $isCompetition
     *
     * @return Sport
     */
    public function setIsCompetition($isCompetition)
    {
        $this->isCompetition = $isCompetition;

        return $this;
    }

    /**
     * Get isCompetition
     *
     * @return boolean
     */
    public function getIsCompetition()
    {
        return $this->isCompetition;
    }

    /**
     * Set tipsDateFrom
     *
     * @param \DateTime $tipsDateFrom
     *
     * @return Sport
     */
    public function setTipsDateFrom($tipsDateFrom)
    {
        $this->tipsDateFrom = $tipsDateFrom;

        return $this;
    }

    /**
     * Get tipsDateFrom
     *
     * @return \DateTime
     */
    public function getTipsDateFrom()
    {
        return $this->tipsDateFrom;
    }

    /**
     * Set tipsDateTo
     *
     * @param \DateTime $tipsDateTo
     *
     * @return Sport
     */
    public function setTipsDateTo($tipsDateTo)
    {
        $this->tipsDateTo = $tipsDateTo;

        return $this;
    }

    /**
     * Get tipsDateTo
     *
     * @return \DateTime
     */
    public function getTipsDateTo()
    {
        return $this->tipsDateTo;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return Sport
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return Sport
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set root
     *
     * @param \AppBundle\Entity\Sport $root
     *
     * @return Sport
     */
    public function setRoot(\AppBundle\Entity\Sport $root = null)
    {
        $this->root = $root;

        return $this;
    }

    /**
     * Get root
     *
     * @return \AppBundle\Entity\Sport
     */
    public function getRoot()
    {
        return $this->root;
    }

    /**
     * Set parent
     *
     * @param \AppBundle\Entity\Sport $parent
     *
     * @return Sport
     */
    public function setParent(\AppBundle\Entity\Sport $parent = null)
    {
        $this->parent = $parent;

        return $this;
    }

    /**
     * Get parent
     *
     * @return \AppBundle\Entity\Sport
     */
    public function getParent()
    {
        return $this->parent;
    }

    /**
     * Add child
     *
     * @param \AppBundle\Entity\Sport $child
     *
     * @return Sport
     */
    public function addChild(\AppBundle\Entity\Sport $child)
    {
        $this->children[] = $child;

        return $this;
    }

    /**
     * Remove child
     *
     * @param \AppBundle\Entity\Sport $child
     */
    public function removeChild(\AppBundle\Entity\Sport $child)
    {
        $this->children->removeElement($child);
    }

    /**
     * Get children
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getChildren()
    {
        return $this->children;
    }

    public function __toString() {

        return strval($this->id);
    }

    /**
     * Add translation
     *
     * @param \AppBundle\Entity\SportTranslation $translation
     *
     * @return Sport
     */
    public function addTranslation(\AppBundle\Entity\SportTranslation $translation)
    {
        $this->translations[] = $translation;

        return $this;
    }

    /**
     * Set ftLeagueSport
     *
     * @param \AppBundle\Entity\FtLeagueSport $ftLeagueSport
     *
     * @return Sport
     */
    public function setFtLeagueSport(\AppBundle\Entity\FtLeagueSport $ftLeagueSport = null)
    {
        $this->ftLeagueSport = $ftLeagueSport;

        return $this;
    }

    /**
     * Get ftLeagueSport
     *
     * @return \AppBundle\Entity\FtLeagueSport
     */
    public function getFtLeagueSport()
    {
        return $this->ftLeagueSport;
    }

    /**
     * Remove ftLeagueSport
     *
     * @param \AppBundle\Entity\FtLeagueSport $ftLeagueSport
     */
    public function removeFtLeagueSport(\AppBundle\Entity\FtLeagueSport $ftLeagueSport)
    {
        $this->ftLeagueSport->removeElement($ftLeagueSport);
    }

    /**
     * Set isAdditional
     *
     * @param boolean $isAdditional
     *
     * @return Sport
     */
    public function setIsAdditional($isAdditional)
    {
        $this->isAdditional = $isAdditional;

        return $this;
    }

    /**
     * Get isAdditional
     *
     * @return boolean
     */
    public function getIsAdditional()
    {
        return $this->isAdditional;
    }

    /**
     * Add ftLeagueSeasonSport
     *
     * @param \AppBundle\Entity\FtLeagueSeasonSport $ftLeagueSeasonSport
     *
     * @return Sport
     */
    public function addFtLeagueSeasonSport(\AppBundle\Entity\FtLeagueSeasonSport $ftLeagueSeasonSport)
    {
        $this->ftLeagueSeasonSports[] = $ftLeagueSeasonSport;

        return $this;
    }

    /**
     * Remove ftLeagueSeasonSport
     *
     * @param \AppBundle\Entity\FtLeagueSeasonSport $ftLeagueSeasonSport
     */
    public function removeFtLeagueSeasonSport(\AppBundle\Entity\FtLeagueSeasonSport $ftLeagueSeasonSport)
    {
        $this->ftLeagueSeasonSports->removeElement($ftLeagueSeasonSport);
    }

    /**
     * Get ftLeagueSeasonSports
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getFtLeagueSeasonSports()
    {
        return $this->ftLeagueSeasonSports;
    }

    /**
     * Remove translation
     *
     * @param \AppBundle\Entity\SportTranslation $translation
     */
    public function removeTranslation(\AppBundle\Entity\SportTranslation $translation)
    {
        $this->translations->removeElement($translation);
    }

    /**
     * Get translations
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getTranslations()
    {
        return $this->translations;
    }

    /**
     * Add domainSport
     *
     * @param \AppBundle\Entity\DomainSport $domainSport
     *
     * @return Sport
     */
    public function addDomainSport(\AppBundle\Entity\DomainSport $domainSport)
    {
        $this->domainSports[] = $domainSport;

        return $this;
    }

    /**
     * Remove domainSport
     *
     * @param \AppBundle\Entity\DomainSport $domainSport
     */
    public function removeDomainSport(\AppBundle\Entity\DomainSport $domainSport)
    {
        $this->domainSports->removeElement($domainSport);
    }

    /**
     * Get domainSports
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getDomainSports()
    {
        return $this->domainSports;
    }

    /**
     * Set ftLeagueFlag
     *
     * @param \AppBundle\Entity\FtLeagueFlag $ftLeagueFlag
     *
     * @return Sport
     */
    public function setFtLeagueFlag(\AppBundle\Entity\FtLeagueFlag $ftLeagueFlag = null)
    {
        $this->ftLeagueFlag = $ftLeagueFlag;

        return $this;
    }

    /**
     * Get ftLeagueFlag
     *
     * @return \AppBundle\Entity\FtLeagueFlag
     */
    public function getFtLeagueFlag()
    {
        return $this->ftLeagueFlag;
    }

    /**
     * Set hasAdditionals
     *
     * @param boolean $hasAdditionals
     *
     * @return Sport
     */
    public function setHasAdditionals($hasAdditionals)
    {
        $this->hasAdditionals = $hasAdditionals;

        return $this;
    }

    /**
     * Get hasAdditionals
     *
     * @return boolean
     */
    public function getHasAdditionals()
    {
        return $this->hasAdditionals;
    }

    /**
     * Set standingsPriority
     *
     * @param integer $standingsPriority
     *
     * @return Sport
     */
    public function setStandingsPriority($standingsPriority)
    {
        $this->standingsPriority = $standingsPriority;

        return $this;
    }

    /**
     * Get standingsPriority
     *
     * @return integer
     */
    public function getStandingsPriority()
    {
        return $this->standingsPriority;
    }

    /**
     * Set singleStandings
     *
     * @param boolean $singleStandings
     *
     * @return Sport
     */
    public function setSingleStandings($singleStandings)
    {
        $this->singleStandings = $singleStandings;

        return $this;
    }

    /**
     * Get singleStandings
     *
     * @return boolean
     */
    public function getSingleStandings()
    {
        return $this->singleStandings;
    }

    /**
     * Set hideInStats
     *
     * @param boolean $hideInStats
     *
     * @return Sport
     */
    public function setHideInStats($hideInStats)
    {
        $this->hideInStats = $hideInStats;

        return $this;
    }

    /**
     * Get hideInStats
     *
     * @return boolean
     */
    public function getHideInStats()
    {
        return $this->hideInStats;
    }

    /**
     * Set imageName
     *
     * @param string $imageName
     *
     * @return Sport
     */
    public function setImageName($imageName)
    {
        $this->imageName = $imageName;

        return $this;
    }

    /**
     * Get imageName
     *
     * @return string
     */
    public function getImageName()
    {
        return $this->imageName;
    }

    /**
     * Add domainSportSlug
     *
     * @param \AppBundle\Entity\DomainSportSlug $domainSportSlug
     *
     * @return Sport
     */
    public function addDomainSportSlug(\AppBundle\Entity\DomainSportSlug $domainSportSlug)
    {
        $this->domainSportSlugs[] = $domainSportSlug;

        return $this;
    }

    /**
     * Remove domainSportSlug
     *
     * @param \AppBundle\Entity\DomainSportSlug $domainSportSlug
     */
    public function removeDomainSportSlug(\AppBundle\Entity\DomainSportSlug $domainSportSlug)
    {
        $this->domainSportSlugs->removeElement($domainSportSlug);
    }

    /**
     * Get domainSportSlugs
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getDomainSportSlugs()
    {
        return $this->domainSportSlugs;
    }
}
