<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * FtTeam
 *
 * @ORM\Table(name="ft_team")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtTeamRepository")
 */
class FtTeam
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\Column(name="team_id", type="integer", unique=true)
     */
    private $teamId;

    /**
     * @var bool
     *
     * @ORM\Column(name="is_national", type="boolean", options={"default":0})
     */
    private $isNational;

    /**
     * @var string
     *
     * @ORM\Column(name="name_en", type="string", length=100, nullable=true)
     */
    private $nameEn;

    /**
     * @var string
     *
     * @ORM\Column(name="name_gr", type="string", length=100, nullable=true)
     */
    private $nameGr;

    /**
     * @var string
     *
     * @ORM\Column(name="name_ro", type="string", length=100, nullable=true)
     */
    private $nameRo;

    /**
     * @ORM\ManyToOne(targetEntity="Country", inversedBy="ftTeams")
     * @ORM\JoinColumn(name="country_id", referencedColumnName="id")
     */
    private $country;

    /**
     * @ORM\OneToMany(targetEntity="TeamFootballField", mappedBy="team", cascade={"persist"})
     */
    private $teamFootballFields;

    /**
     * @var \DateTime $createdAt
     *
     * @Gedmo\Timestampable(on="create")
     * @ORM\Column(name="created_at", type="datetime")
     */
    private $createdAt;

    /**
     * @var \DateTime $updatedAt
     *
     * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
     * @ORM\Column(name="updated_at", type="datetime", nullable=true)
     */
    private $updatedAt;

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set isNational
     *
     * @param boolean $isNational
     *
     * @return FtTeam
     */
    public function setIsNational($isNational)
    {
        $this->isNational = $isNational;

        return $this;
    }

    /**
     * Get isNational
     *
     * @return boolean
     */
    public function getIsNational()
    {
        return $this->isNational;
    }

    /**
     * Set nameEn
     *
     * @param string $nameEn
     *
     * @return FtTeam
     */
    public function setNameEn($nameEn)
    {
        $this->nameEn = $nameEn;

        return $this;
    }

    /**
     * Get nameEn
     *
     * @return string
     */
    public function getNameEn()
    {
        return $this->nameEn;
    }

    /**
     * Set nameGr
     *
     * @param string $nameGr
     *
     * @return FtTeam
     */
    public function setNameGr($nameGr)
    {
        $this->nameGr = $nameGr;

        return $this;
    }

    /**
     * Get nameGr
     *
     * @return string
     */
    public function getNameGr()
    {
        return $this->nameGr;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return FtTeam
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return FtTeam
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set country
     *
     * @param \AppBundle\Entity\Country $country
     *
     * @return FtTeam
     */
    public function setCountry(\AppBundle\Entity\Country $country = null)
    {
        $this->country = $country;

        return $this;
    }

    /**
     * Get country
     *
     * @return \AppBundle\Entity\Country
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * Set teamId
     *
     * @param integer $teamId
     *
     * @return FtTeam
     */
    public function setTeamId($teamId)
    {
        $this->teamId = $teamId;

        return $this;
    }

    /**
     * Get teamId
     *
     * @return integer
     */
    public function getTeamId()
    {
        return $this->teamId;
    }

    /**
     * Set nameRo
     *
     * @param string $nameRo
     *
     * @return FtTeam
     */
    public function setNameRo($nameRo)
    {
        $this->nameRo = $nameRo;

        return $this;
    }

    /**
     * Get nameRo
     *
     * @return string
     */
    public function getNameRo()
    {
        return $this->nameRo;
    }

    /**
     * Add teamFootballField
     *
     * @param \AppBundle\Entity\TeamFootballField $teamFootballField
     *
     * @return FtTeam
     */
    public function addTeamFootballField(\AppBundle\Entity\TeamFootballField $teamFootballField)
    {
        $this->teamFootballFields[] = $teamFootballField;

        return $this;
    }

    /**
     * Remove teamFootballField
     *
     * @param \AppBundle\Entity\TeamFootballField $teamFootballField
     */
    public function removeTeamFootballField(\AppBundle\Entity\TeamFootballField $teamFootballField)
    {
        $this->teamFootballFields->removeElement($teamFootballField);
    }

    /**
     * Get teamFootballField
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getTeamFootballFields()
    {
        return $this->teamFootballFields;
    }
}
