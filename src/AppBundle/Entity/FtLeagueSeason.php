<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Ged<PERSON>;

/**
 * FtLeagueSeason
 *
 * @ORM\Table(name="ft_league_season", uniqueConstraints={@ORM\UniqueConstraint(columns={"l_id", "season"})})
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtLeagueSeasonRepository")
 */
class FtLeagueSeason
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="l_id", type="string", length=10)
     */
    private $lId;

    /**
     * @var string
     *
     * @ORM\Column(name="season", type="string", length=10)
     */
    private $season;

    /**
     * @var string
     *
     * @ORM\Column(name="name_en", type="string", length=255, nullable=true)
     */
    private $nameEn;

    /**
     * @var string
     *
     * @ORM\Column(name="name_gr", type="string", length=255, nullable=true)
     */
    private $nameGr;

    /**
     * @var string
     *
     * @ORM\Column(name="c_name_en", type="string", length=255, nullable=true)
     */
    private $cNameEn;

    /**
     * @var string
     *
     * @ORM\Column(name="c_name_gr", type="string", length=255, nullable=true)
     */
    private $cNameGr;

   /**
    * @var \DateTime $createdAt
    *
    * @Gedmo\Timestampable(on="create")
    * @ORM\Column(name="created_at", type="datetime")
    */
    private $createdAt;

    /**
     * @var \DateTime $updatedAt
     *
     * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
     * @ORM\Column(name="updated_at", type="datetime", nullable=true)
     */
    private $updatedAt;


    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set lId
     *
     * @param string $lId
     *
     * @return FtLeagueSeason
     */
    public function setLId($lId)
    {
        $this->lId = $lId;

        return $this;
    }

    /**
     * Get lId
     *
     * @return string
     */
    public function getLId()
    {
        return $this->lId;
    }

    /**
     * Set season
     *
     * @param string $season
     *
     * @return FtLeagueSeason
     */
    public function setSeason($season)
    {
        $this->season = $season;

        return $this;
    }

    /**
     * Get season
     *
     * @return string
     */
    public function getSeason()
    {
        return $this->season;
    }

    /**
     * Set nameEn
     *
     * @param string $nameEn
     *
     * @return FtLeagueSeason
     */
    public function setNameEn($nameEn)
    {
        $this->nameEn = $nameEn;

        return $this;
    }

    /**
     * Get nameEn
     *
     * @return string
     */
    public function getNameEn()
    {
        return $this->nameEn;
    }

    /**
     * Set nameGr
     *
     * @param string $nameGr
     *
     * @return FtLeagueSeason
     */
    public function setNameGr($nameGr)
    {
        $this->nameGr = $nameGr;

        return $this;
    }

    /**
     * Get nameGr
     *
     * @return string
     */
    public function getNameGr()
    {
        return $this->nameGr;
    }

    /**
     * Set cNameEn
     *
     * @param string $cNameEn
     *
     * @return FtLeagueSeason
     */
    public function setCNameEn($cNameEn)
    {
        $this->cNameEn = $cNameEn;

        return $this;
    }

    /**
     * Get cNameEn
     *
     * @return string
     */
    public function getCNameEn()
    {
        return $this->cNameEn;
    }

    /**
     * Set cNameGr
     *
     * @param string $cNameGr
     *
     * @return FtLeagueSeason
     */
    public function setCNameGr($cNameGr)
    {
        $this->cNameGr = $cNameGr;

        return $this;
    }

    /**
     * Get cNameGr
     *
     * @return string
     */
    public function getCNameGr()
    {
        return $this->cNameGr;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return FtLeagueSeason
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return FtLeagueSeason
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }
}
