<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

 /**
  * DomainFtFixture
  *
  * @ORM\Table(name="domain_ft_fixture")
  * @ORM\Entity(repositoryClass="AppBundle\Repository\DomainFtFixtureRepository")
  */
class DomainFtFixture
{
  /**
   * @var int
   *
   * @ORM\Column(name="id", type="integer")
   * @ORM\Id
   * @ORM\GeneratedValue(strategy="AUTO")
   */
   private $id;

  /**
   * @var int
   * @ORM\Column(name="fixture_id", type="integer")
   */
  private $fixtureId;

  /**
   * @var int
   * @ORM\Column(name="domain_id", type="integer")
   */
  private $domainId;

  /**
   * @var int
   * @ORM\Column(name="home_team_id", type="integer")
   */
  private $homeTeamId;

  /**
   * @var int
   * @ORM\Column(name="away_team_id", type="integer")
   */
  private $awayTeamId;

  /**
  * @var int
  * @ORM\Column(name="preview_id", type="integer")
   */
  private $previewId;

  /**
   * @var \DateTime $createdAt
   *
   * @Gedmo\Timestampable(on="create")
   * @ORM\Column(name="created_at", type="datetime")
   */
  private $createdAt;

  /**
   * @var \DateTime $updatedAt
   *
   * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
   * @ORM\Column(name="updated_at", type="datetime", nullable=true)
   */
  private $updatedAt;

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set fixtureId
     *
     * @param integer $fixtureId
     *
     * @return DomainFtFixture
     */
    public function setFixtureId($fixtureId)
    {
        $this->fixtureId = $fixtureId;

        return $this;
    }

    /**
     * Get fixtureId
     *
     * @return integer
     */
    public function getFixtureId()
    {
        return $this->fixtureId;
    }

    /**
     * Set domainId
     *
     * @param integer $domainId
     *
     * @return DomainFtFixture
     */
    public function setDomainId($domainId)
    {
        $this->domainId = $domainId;

        return $this;
    }

    /**
     * Get domainId
     *
     * @return integer
     */
    public function getDomainId()
    {
        return $this->domainId;
    }

    /**
     * Set homeTeamId
     *
     * @param integer $homeTeamId
     *
     * @return DomainFtFixture
     */
    public function setHomeTeamId($homeTeamId)
    {
        $this->homeTeamId = $homeTeamId;

        return $this;
    }

    /**
     * Get homeTeamId
     *
     * @return integer
     */
    public function getHomeTeamId()
    {
        return $this->homeTeamId;
    }

    /**
     * Set awayTeamId
     *
     * @param integer $awayTeamId
     *
     * @return DomainFtFixture
     */
    public function setAwayTeamId($awayTeamId)
    {
        $this->awayTeamId = $awayTeamId;

        return $this;
    }

    /**
     * Get awayTeamId
     *
     * @return integer
     */
    public function getAwayTeamId()
    {
        return $this->awayTeamId;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return DomainFtFixture
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return DomainFtFixture
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set previewId
     *
     * @param integer $previewId
     *
     * @return DomainFtFixture
     */
    public function setPreviewId($previewId)
    {
        $this->previewId = $previewId;

        return $this;
    }

    /**
     * Get previewId
     *
     * @return integer
     */
    public function getPreviewId()
    {
        return $this->previewId;
    }
}
