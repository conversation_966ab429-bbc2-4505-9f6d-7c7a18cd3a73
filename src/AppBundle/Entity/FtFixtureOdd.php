<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * FtFixtureOdd
 *
 * @ORM\Table(name="ft_fixture_odd", uniqueConstraints={@ORM\UniqueConstraint(columns={"f_id", "bookmaker_id"})})
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtFixtureOddRepository")
 */
class FtFixtureOdd
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\Column(name="f_id", type="integer")
     */
    private $fId;

    /**
     * @var int
     *
     * @ORM\Column(name="bookmaker_id", type="integer")
     */
    private $bookmakerId;

    /**
     * @var string
     *
     * @ORM\Column(name="odd_1", type="decimal", precision=6, scale=2, nullable=true)
     */
    private $odd1;

    /**
     * @var string
     *
     * @ORM\Column(name="odd_x", type="decimal", precision=6, scale=2, nullable=true)
     */
    private $oddX;

    /**
     * @var string
     *
     * @ORM\Column(name="odd_2", type="decimal", precision=6, scale=2, nullable=true)
     */
    private $odd2;

    /**
     * @var string
     *
     * @ORM\Column(name="odd_u25", type="decimal", precision=6, scale=2, nullable=true)
     */
    private $oddU25;

    /**
     * @var string
     *
     * @ORM\Column(name="odd_o25", type="decimal", precision=6, scale=2, nullable=true)
     */
    private $oddO25;

    /**
     * @var string
     *
     * @ORM\Column(name="p_odd_1", type="decimal", precision=6, scale=2, nullable=true)
     */
    private $pOdd1;

    /**
     * @var string
     *
     * @ORM\Column(name="p_odd_x", type="decimal", precision=6, scale=2, nullable=true)
     */
    private $pOddX;

    /**
     * @var string
     *
     * @ORM\Column(name="p_odd_2", type="decimal", precision=6, scale=2, nullable=true)
     */
    private $pOdd2;

    /**
     * @var string
     *
     * @ORM\Column(name="p_odd_u25", type="decimal", precision=6, scale=2, nullable=true)
     */
    private $pOddU25;

    /**
     * @var string
     *
     * @ORM\Column(name="p_odd_o25", type="decimal", precision=6, scale=2, nullable=true)
     */
    private $pOddO25;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="match_datetime", type="datetime")
     */
    private $matchDatetime;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="last_modified", type="datetime", nullable=true)
     */
    private $lastModified;

    /**
     * @var \DateTime $createdAt
     *
     * @Gedmo\Timestampable(on="create")
     * @ORM\Column(name="created_at", type="datetime")
     */
     private $createdAt;

     /**
      * @var \DateTime $updatedAt
      *
      * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
      * @ORM\Column(name="updated_at", type="datetime", nullable=true)
      */
     private $updatedAt;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set fId
     *
     * @param integer $fId
     *
     * @return FtFixtureOdd
     */
    public function setFId($fId)
    {
        $this->fId = $fId;

        return $this;
    }

    /**
     * Get fId
     *
     * @return int
     */
    public function getFId()
    {
        return $this->fId;
    }

    /**
     * Set bookmakerId
     *
     * @param integer $bookmakerId
     *
     * @return FtFixtureOdd
     */
    public function setBookmakerId($bookmakerId)
    {
        $this->bookmakerId = $bookmakerId;

        return $this;
    }

    /**
     * Get bookmakerId
     *
     * @return int
     */
    public function getBookmakerId()
    {
        return $this->bookmakerId;
    }

    /**
     * Set odd1
     *
     * @param string $odd1
     *
     * @return FtFixtureOdd
     */
    public function setOdd1($odd1)
    {
        $this->odd1 = $odd1;

        return $this;
    }

    /**
     * Get odd1
     *
     * @return string
     */
    public function getOdd1()
    {
        return $this->odd1;
    }

    /**
     * Set oddX
     *
     * @param string $oddX
     *
     * @return FtFixtureOdd
     */
    public function setOddX($oddX)
    {
        $this->oddX = $oddX;

        return $this;
    }

    /**
     * Get oddX
     *
     * @return string
     */
    public function getOddX()
    {
        return $this->oddX;
    }

    /**
     * Set odd2
     *
     * @param string $odd2
     *
     * @return FtFixtureOdd
     */
    public function setOdd2($odd2)
    {
        $this->odd2 = $odd2;

        return $this;
    }

    /**
     * Get odd2
     *
     * @return string
     */
    public function getOdd2()
    {
        return $this->odd2;
    }

    /**
     * Set oddU25
     *
     * @param string $oddU25
     *
     * @return FtFixtureOdd
     */
    public function setOddU25($oddU25)
    {
        $this->oddU25 = $oddU25;

        return $this;
    }

    /**
     * Get oddU25
     *
     * @return string
     */
    public function getOddU25()
    {
        return $this->oddU25;
    }

    /**
     * Set oddO25
     *
     * @param string $oddO25
     *
     * @return FtFixtureOdd
     */
    public function setOddO25($oddO25)
    {
        $this->oddO25 = $oddO25;

        return $this;
    }

    /**
     * Get oddO25
     *
     * @return string
     */
    public function getOddO25()
    {
        return $this->oddO25;
    }

    /**
     * Set matchDatetime
     *
     * @param \DateTime $matchDatetime
     *
     * @return FtFixtureOdd
     */
    public function setMatchDatetime($matchDatetime)
    {
        $this->matchDatetime = $matchDatetime;

        return $this;
    }

    /**
     * Get matchDatetime
     *
     * @return \DateTime
     */
    public function getMatchDatetime()
    {
        return $this->matchDatetime;
    }

    /**
     * Set lastModified
     *
     * @param \DateTime $lastModified
     *
     * @return FtFixtureOdd
     */
    public function setLastModified($lastModified)
    {
        $this->lastModified = $lastModified;

        return $this;
    }

    /**
     * Get lastModified
     *
     * @return \DateTime
     */
    public function getLastModified()
    {
        return $this->lastModified;
    }

    /**
     * Set pOdd1
     *
     * @param string $pOdd1
     *
     * @return FtFixtureOdd
     */
    public function setPOdd1($pOdd1)
    {
        $this->pOdd1 = $pOdd1;

        return $this;
    }

    /**
     * Get pOdd1
     *
     * @return string
     */
    public function getPOdd1()
    {
        return $this->pOdd1;
    }

    /**
     * Set pOddX
     *
     * @param string $pOddX
     *
     * @return FtFixtureOdd
     */
    public function setPOddX($pOddX)
    {
        $this->pOddX = $pOddX;

        return $this;
    }

    /**
     * Get pOddX
     *
     * @return string
     */
    public function getPOddX()
    {
        return $this->pOddX;
    }

    /**
     * Set pOdd2
     *
     * @param string $pOdd2
     *
     * @return FtFixtureOdd
     */
    public function setPOdd2($pOdd2)
    {
        $this->pOdd2 = $pOdd2;

        return $this;
    }

    /**
     * Get pOdd2
     *
     * @return string
     */
    public function getPOdd2()
    {
        return $this->pOdd2;
    }

    /**
     * Set pOddU25
     *
     * @param string $pOddU25
     *
     * @return FtFixtureOdd
     */
    public function setPOddU25($pOddU25)
    {
        $this->pOddU25 = $pOddU25;

        return $this;
    }

    /**
     * Get pOddU25
     *
     * @return string
     */
    public function getPOddU25()
    {
        return $this->pOddU25;
    }

    /**
     * Set pOddO25
     *
     * @param string $pOddO25
     *
     * @return FtFixtureOdd
     */
    public function setPOddO25($pOddO25)
    {
        $this->pOddO25 = $pOddO25;

        return $this;
    }

    /**
     * Get pOddO25
     *
     * @return string
     */
    public function getPOddO25()
    {
        return $this->pOddO25;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return FtFixtureOdd
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return FtFixtureOdd
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }
}
