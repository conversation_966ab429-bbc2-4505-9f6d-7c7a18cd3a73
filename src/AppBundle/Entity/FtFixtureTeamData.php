<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Ged<PERSON>;

/**
 * FtFixtureTeamData
 *
 * @ORM\Table(name="ft_fixture_team_data")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\FtFixtureTeamDataRepository")
 */
class FtFixtureTeamData
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer", options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="fixture_id", type="string", length=32, unique=true)
     */
    private $fixtureId;

    /**
     * @var int
     *
     * @ORM\Column(name="h_formation_id", type="integer", options={"unsigned"=true}, nullable=true)
     */
    private $hFormationId;

    /**
     * @var string
     *
     * @ORM\Column(name="h_lineup", type="string", length=191, nullable=true)
     */
    private $hLineup;

    /**
     * @var string
     *
     * @ORM\Column(name="h_absences", type="text", nullable=true)
     */
    private $hAbsences;

    /**
     * @var int
     *
     * @ORM\Column(name="a_formation_id", type="integer", options={"unsigned"=true}, nullable=true)
     */
    private $aFormationId;

    /**
     * @var string
     *
     * @ORM\Column(name="a_lineup", type="string", length=191, nullable=true)
     */
    private $aLineup;

    /**
     * @var string
     *
     * @ORM\Column(name="a_absences", type="text", nullable=true)
     */
    private $aAbsences;

    /**
     * @var int
     *
     * @ORM\Column(name="h_id", type="integer", options={"unsigned"=true})
     */
    private $hId;

    /**
     * @var int
     *
     * @ORM\Column(name="a_id", type="integer", options={"unsigned"=true})
     */
    private $aId;

     /**
      * @var \DateTime $createdAt
			*
			* @Gedmo\Timestampable(on="create")
			* @ORM\Column(name="created_at", type="datetime")
			*/
		private $createdAt;
			
		/**
		 * @var \DateTime $updatedAt
		 *
		 * @Gedmo\Timestampable(on="change", field={"hFormationId","hLineup","hAbsences","aFormationId","aLineup","aAbsences"})
		 * @ORM\Column(name="updated_at", type="datetime", nullable=true)
		 */
		private $updatedAt;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set fixtureId
     *
     * @param string $fixtureId
     *
     * @return FtFixtureTeamData
     */
    public function setFixtureId($fixtureId)
    {
        $this->fixtureId = $fixtureId;

        return $this;
    }

    /**
     * Get fixtureId
     *
     * @return string
     */
    public function getFixtureId()
    {
        return $this->fixtureId;
    }

    /**
     * Set hF
     *
     * @param integer $hF
     *
     * @return FtFixtureTeamData
     */
    public function setHF($hF)
    {
        $this->hF = $hF;

        return $this;
    }

    /**
     * Get hF
     *
     * @return int
     */
    public function getHF()
    {
        return $this->hF;
    }

    /**
     * Set hLineup
     *
     * @param string $hLineup
     *
     * @return FtFixtureTeamData
     */
    public function setHLineup($hLineup)
    {
        $this->hLineup = $hLineup;

        return $this;
    }

    /**
     * Get hLineup
     *
     * @return string
     */
    public function getHLineup()
    {
        return $this->hLineup;
    }

    /**
     * Set hAbsences
     *
     * @param string $hAbsences
     *
     * @return FtFixtureTeamData
     */
    public function setHAbsences($hAbsences)
    {
        $this->hAbsences = $hAbsences;

        return $this;
    }

    /**
     * Get hAbsences
     *
     * @return string
     */
    public function getHAbsences()
    {
        return $this->hAbsences;
    }

    /**
     * Set aFormationId
     *
     * @param integer $aFormationId
     *
     * @return FtFixtureTeamData
     */
    public function setAFormationId($aFormationId)
    {
        $this->aFormationId = $aFormationId;

        return $this;
    }

    /**
     * Get aFormationId
     *
     * @return int
     */
    public function getAFormationId()
    {
        return $this->aFormationId;
    }

    /**
     * Set aLineup
     *
     * @param string $aLineup
     *
     * @return FtFixtureTeamData
     */
    public function setALineup($aLineup)
    {
        $this->aLineup = $aLineup;

        return $this;
    }

    /**
     * Get aLineup
     *
     * @return string
     */
    public function getALineup()
    {
        return $this->aLineup;
    }

    /**
     * Set aAbsences
     *
     * @param string $aAbsences
     *
     * @return FtFixtureTeamData
     */
    public function setAAbsences($aAbsences)
    {
        $this->aAbsences = $aAbsences;

        return $this;
    }

    /**
     * Get aAbsences
     *
     * @return string
     */
    public function getAAbsences()
    {
        return $this->aAbsences;
    }

    /**
     * Set hId
     *
     * @param integer $hId
     *
     * @return FtFixtureTeamData
     */
    public function setHId($hId)
    {
        $this->hId = $hId;

        return $this;
    }

    /**
     * Get hId
     *
     * @return int
     */
    public function getHId()
    {
        return $this->hId;
    }

    /**
     * Set aId
     *
     * @param integer $aId
     *
     * @return FtFixtureTeamData
     */
    public function setAId($aId)
    {
        $this->aId = $aId;

        return $this;
    }

    /**
     * Get aId
     *
     * @return int
     */
    public function getAId()
    {
        return $this->aId;
    }

    /**
     * Set hFormationId
     *
     * @param integer $hFormationId
     *
     * @return FtFixtureTeamData
     */
    public function setHFormationId($hFormationId)
    {
        $this->hFormationId = $hFormationId;

        return $this;
    }

    /**
     * Get hFormationId
     *
     * @return integer
     */
    public function getHFormationId()
    {
        return $this->hFormationId;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return FtFixtureTeamData
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return FtFixtureTeamData
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }
}
