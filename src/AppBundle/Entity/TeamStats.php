<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * TeamStats
 *
 * @ORM\Table(name="team_stats", uniqueConstraints={@ORM\UniqueConstraint(columns={"team_id", "league_season_id"})})
 * @ORM\Entity(repositoryClass="AppBundle\Repository\TeamStatsRepository")
 */
class TeamStats
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\Column(name="team_id", type="integer", options={"unsigned"=true})
     */
    private $teamId;

    /**
     * @var int
     *
     * @ORM\Column(name="league_season_id", type="integer", options={"unsigned"=true})
     */
    private $leagueSeasonId;

    /**
     * @var int
     *
     * @ORM\Column(name="h_played", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hPlayed = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_wins", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hWins = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_wins_ht", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hWinsHt = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_win_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hWinStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_no_win_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hNoWinStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_draws", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hDraws = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_draws_ht", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hDrawsHt = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_draw_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hDrawStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_no_draw_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hNoDrawStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_loses", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hLoses = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_loses_ht", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hLosesHt = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_loses_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hLoseStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_no_lose_streak", type="smallint", options={"unsigned"=true, "default":0, "default":0})
     */
    private $hNoLoseStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_for_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsForStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_against_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsAgainstStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_not_scored_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hNotScoredStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_not_conceded_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hNotConcededStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_for", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsFor = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_against", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsAgainst = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_not_scored", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hNotScored = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_not_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hNotConceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_over_15", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hOver15 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_over_15_1", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hOver151 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_over_15_2", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hOver152 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_under_15", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hUnder15 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_under_15_1", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hUnder151 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_under_15_2", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hUnder152 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_over_25", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hOver25 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_over_25_1", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hOver251 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_over_25_2", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hOver252 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_under_25", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hUnder25 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_under_25_1", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hUnder251 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_under_25_2", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hUnder252 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_over_35", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hOver35 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_over_35_1", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hOver351 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_over_35_2", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hOver352 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_under_35", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hUnder35 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_under_35_1", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hUnder351 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_under_35_2", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hUnder352 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_over_25_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hOver25Streak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_under_25_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hUnder25Streak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goal_goal", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalGoal = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goal_goal_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalGoalStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_no_goal", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hNoGoal = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_no_goal_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hNoGoalStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_01", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoals01 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_23", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoals23 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_46", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoals46 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_7", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoals7 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_minute_015", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsMinute015 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_minute_1630", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsMinute1630 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_minute_3145", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsMinute3145 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_minute_4660", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsMinute4660 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_minute_6175", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsMinute6175 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_minute_7690", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsMinute7690 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_minute_015_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsMinute015Conceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_minute_1630_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsMinute1630Conceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_minute_3145_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsMinute3145Conceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_minute_4660_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsMinute4660Conceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_minute_6175_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsMinute6175Conceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_goals_minute_7690_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hGoalsMinute7690Conceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_win_win", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hWinWin = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_draw_win", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hDrawWin = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_lose_win", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hLoseWin = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_win_draw", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hWinDraw = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_draw_draw", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hDrawDraw = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_lose_draw", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hLoseDraw = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_win_lose", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hWinLose = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_draw_lose", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hDrawLose = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="h_lose_lose", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $hLoseLose = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_played", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aPlayed = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_wins", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aWins = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_wins_ht", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aWinsHt = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_win_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aWinStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_no_win_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aNoWinStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_draws", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aDraws = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_draws_ht", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aDrawsHt = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_draw_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aDrawStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_no_draw_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aNoDrawStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_loses", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aLoses = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_loses_ht", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aLosesHt = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_loses_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aLoseStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_no_lose_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aNoLoseStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_for_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsForStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_against_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsAgainstStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_not_scored_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aNotScoredStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_not_conceded_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aNotConcededStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_for", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsFor = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_against", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsAgainst = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_not_scored", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aNotScored = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_not_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aNotConceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_over_15", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aOver15 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_over_15_1", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aOver151 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_over_15_2", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aOver152 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_under_15", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aUnder15 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_under_15_1", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aUnder151 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_under_15_2", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aUnder152 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_over_25", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aOver25 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_over_25_1", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aOver251 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_over_25_2", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aOver252 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_under_25", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aUnder25 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_under_25_1", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aUnder251 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_under_25_2", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aUnder252 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_over_35", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aOver35 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_over_35_1", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aOver351 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_over_35_2", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aOver352 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_under_35", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aUnder35 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_under_35_1", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aUnder351 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_under_35_2", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aUnder352 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_over_25_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aOver25Streak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_under_25_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aUnder25Streak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goal_goal", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalGoal = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goal_goal_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalGoalStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_no_goal", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aNoGoal = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_no_goal_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aNoGoalStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_01", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoals01 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_23", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoals23 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_46", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoals46 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_7", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoals7 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_minute_015", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsMinute015 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_minute_1630", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsMinute1630 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_minute_3145", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsMinute3145 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_minute_4660", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsMinute4660 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_minute_6175", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsMinute6175 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_minute_7690", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsMinute7690 = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_minute_015_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsMinute015Conceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_minute_1630_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsMinute1630Conceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_minute_3145_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsMinute3145Conceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_minute_4660_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsMinute4660Conceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_minute_6175_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsMinute6175Conceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_goals_minute_7690_conceded", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aGoalsMinute7690Conceded = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_win_win", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aWinWin = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_draw_win", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aDrawWin = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_lose_win", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aLoseWin = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_win_draw", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aWinDraw = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_draw_draw", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aDrawDraw = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_lose_draw", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aLoseDraw = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_win_lose", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aWinLose = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_draw_lose", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aDrawLose = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="a_lose_lose", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $aLoseLose = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_played", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tPlayed = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_yellow_cards", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tYellowCards = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_red_cards", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tRedCards = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_win_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tWinStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_draw_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tDrawStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_lose_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tLoseStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_no_win_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tNoWinStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_no_draw_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tNoDrawStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_no_lose_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tNoLoseStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_goals_for_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tGoalsForStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_goals_against_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tGoalsAgainstStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_goal_goal_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tGoalGoalStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_no_goal_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tNoGoalStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_over_25_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tOver25Streak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_under_25_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tUnder25Streak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_not_scored_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tNotScoredStreak = 0;

    /**
     * @var int
     *
     * @ORM\Column(name="t_not_conceded_streak", type="smallint", options={"unsigned"=true, "default":0})
     */
    private $tNotConcededStreak = 0;

    /**
     * @var \DateTime $createdAt
     *
     * @Gedmo\Timestampable(on="create")
     * @ORM\Column(name="created_at", type="datetime")
     */
     private $createdAt;

     /**
      * @var \DateTime $updatedAt
      *
      * @Gedmo\Timestampable(on="change", field={"nameEn", "nameGr"})
      * @ORM\Column(name="updated_at", type="datetime", nullable=true)
      */
     private $updatedAt;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set teamId
     *
     * @param integer $teamId
     *
     * @return TeamStats
     */
    public function setTeamId($teamId)
    {
        $this->teamId = $teamId;

        return $this;
    }

    /**
     * Get teamId
     *
     * @return integer
     */
    public function getTeamId()
    {
        return $this->teamId;
    }

    /**
     * Set leagueSeasonId
     *
     * @param integer $leagueSeasonId
     *
     * @return TeamStats
     */
    public function setLeagueSeasonId($leagueSeasonId)
    {
        $this->leagueSeasonId = $leagueSeasonId;

        return $this;
    }

    /**
     * Get leagueSeasonId
     *
     * @return integer
     */
    public function getLeagueSeasonId()
    {
        return $this->leagueSeasonId;
    }

    /**
     * Set hPlayed
     *
     * @param integer $hPlayed
     *
     * @return TeamStats
     */
    public function setHPlayed($hPlayed)
    {
        $this->hPlayed = $hPlayed;

        return $this;
    }

    /**
     * Get hPlayed
     *
     * @return integer
     */
    public function getHPlayed()
    {
        return $this->hPlayed;
    }

    /**
     * Set hWins
     *
     * @param integer $hWins
     *
     * @return TeamStats
     */
    public function setHWins($hWins)
    {
        $this->hWins = $hWins;

        return $this;
    }

    /**
     * Get hWins
     *
     * @return integer
     */
    public function getHWins()
    {
        return $this->hWins;
    }

    /**
     * Set hWinsHt
     *
     * @param integer $hWinsHt
     *
     * @return TeamStats
     */
    public function setHWinsHt($hWinsHt)
    {
        $this->hWinsHt = $hWinsHt;

        return $this;
    }

    /**
     * Get hWinsHt
     *
     * @return integer
     */
    public function getHWinsHt()
    {
        return $this->hWinsHt;
    }

    /**
     * Set hWinStreak
     *
     * @param integer $hWinStreak
     *
     * @return TeamStats
     */
    public function setHWinStreak($hWinStreak)
    {
        $this->hWinStreak = $hWinStreak;

        return $this;
    }

    /**
     * Get hWinStreak
     *
     * @return integer
     */
    public function getHWinStreak()
    {
        return $this->hWinStreak;
    }

    /**
     * Set hNoWinStreak
     *
     * @param integer $hNoWinStreak
     *
     * @return TeamStats
     */
    public function setHNoWinStreak($hNoWinStreak)
    {
        $this->hNoWinStreak = $hNoWinStreak;

        return $this;
    }

    /**
     * Get hNoWinStreak
     *
     * @return integer
     */
    public function getHNoWinStreak()
    {
        return $this->hNoWinStreak;
    }

    /**
     * Set hDraws
     *
     * @param integer $hDraws
     *
     * @return TeamStats
     */
    public function setHDraws($hDraws)
    {
        $this->hDraws = $hDraws;

        return $this;
    }

    /**
     * Get hDraws
     *
     * @return integer
     */
    public function getHDraws()
    {
        return $this->hDraws;
    }

    /**
     * Set hDrawsHt
     *
     * @param integer $hDrawsHt
     *
     * @return TeamStats
     */
    public function setHDrawsHt($hDrawsHt)
    {
        $this->hDrawsHt = $hDrawsHt;

        return $this;
    }

    /**
     * Get hDrawsHt
     *
     * @return integer
     */
    public function getHDrawsHt()
    {
        return $this->hDrawsHt;
    }

    /**
     * Set hDrawStreak
     *
     * @param integer $hDrawStreak
     *
     * @return TeamStats
     */
    public function setHDrawStreak($hDrawStreak)
    {
        $this->hDrawStreak = $hDrawStreak;

        return $this;
    }

    /**
     * Get hDrawStreak
     *
     * @return integer
     */
    public function getHDrawStreak()
    {
        return $this->hDrawStreak;
    }

    /**
     * Set hNoDrawStreak
     *
     * @param integer $hNoDrawStreak
     *
     * @return TeamStats
     */
    public function setHNoDrawStreak($hNoDrawStreak)
    {
        $this->hNoDrawStreak = $hNoDrawStreak;

        return $this;
    }

    /**
     * Get hNoDrawStreak
     *
     * @return integer
     */
    public function getHNoDrawStreak()
    {
        return $this->hNoDrawStreak;
    }

    /**
     * Set hLoses
     *
     * @param integer $hLoses
     *
     * @return TeamStats
     */
    public function setHLoses($hLoses)
    {
        $this->hLoses = $hLoses;

        return $this;
    }

    /**
     * Get hLoses
     *
     * @return integer
     */
    public function getHLoses()
    {
        return $this->hLoses;
    }

    /**
     * Set hLosesHt
     *
     * @param integer $hLosesHt
     *
     * @return TeamStats
     */
    public function setHLosesHt($hLosesHt)
    {
        $this->hLosesHt = $hLosesHt;

        return $this;
    }

    /**
     * Get hLosesHt
     *
     * @return integer
     */
    public function getHLosesHt()
    {
        return $this->hLosesHt;
    }

    /**
     * Set hLoseStreak
     *
     * @param integer $hLoseStreak
     *
     * @return TeamStats
     */
    public function setHLoseStreak($hLoseStreak)
    {
        $this->hLoseStreak = $hLoseStreak;

        return $this;
    }

    /**
     * Get hLoseStreak
     *
     * @return integer
     */
    public function getHLoseStreak()
    {
        return $this->hLoseStreak;
    }

    /**
     * Set hNoLoseStreak
     *
     * @param integer $hNoLoseStreak
     *
     * @return TeamStats
     */
    public function setHNoLoseStreak($hNoLoseStreak)
    {
        $this->hNoLoseStreak = $hNoLoseStreak;

        return $this;
    }

    /**
     * Get hNoLoseStreak
     *
     * @return integer
     */
    public function getHNoLoseStreak()
    {
        return $this->hNoLoseStreak;
    }

    /**
     * Set hGoalsForStreak
     *
     * @param integer $hGoalsForStreak
     *
     * @return TeamStats
     */
    public function setHGoalsForStreak($hGoalsForStreak)
    {
        $this->hGoalsForStreak = $hGoalsForStreak;

        return $this;
    }

    /**
     * Get hGoalsForStreak
     *
     * @return integer
     */
    public function getHGoalsForStreak()
    {
        return $this->hGoalsForStreak;
    }

    /**
     * Set hGoalsAgainstStreak
     *
     * @param integer $hGoalsAgainstStreak
     *
     * @return TeamStats
     */
    public function setHGoalsAgainstStreak($hGoalsAgainstStreak)
    {
        $this->hGoalsAgainstStreak = $hGoalsAgainstStreak;

        return $this;
    }

    /**
     * Get hGoalsAgainstStreak
     *
     * @return integer
     */
    public function getHGoalsAgainstStreak()
    {
        return $this->hGoalsAgainstStreak;
    }

    /**
     * Set hNotScoredStreak
     *
     * @param integer $hNotScoredStreak
     *
     * @return TeamStats
     */
    public function setHNotScoredStreak($hNotScoredStreak)
    {
        $this->hNotScoredStreak = $hNotScoredStreak;

        return $this;
    }

    /**
     * Get hNotScoredStreak
     *
     * @return integer
     */
    public function getHNotScoredStreak()
    {
        return $this->hNotScoredStreak;
    }

    /**
     * Set hNotConcededStreak
     *
     * @param integer $hNotConcededStreak
     *
     * @return TeamStats
     */
    public function setHNotConcededStreak($hNotConcededStreak)
    {
        $this->hNotConcededStreak = $hNotConcededStreak;

        return $this;
    }

    /**
     * Get hNotConcededStreak
     *
     * @return integer
     */
    public function getHNotConcededStreak()
    {
        return $this->hNotConcededStreak;
    }

    /**
     * Set hGoalsFor
     *
     * @param integer $hGoalsFor
     *
     * @return TeamStats
     */
    public function setHGoalsFor($hGoalsFor)
    {
        $this->hGoalsFor = $hGoalsFor;

        return $this;
    }

    /**
     * Get hGoalsFor
     *
     * @return integer
     */
    public function getHGoalsFor()
    {
        return $this->hGoalsFor;
    }

    /**
     * Set hGoalsAgainst
     *
     * @param integer $hGoalsAgainst
     *
     * @return TeamStats
     */
    public function setHGoalsAgainst($hGoalsAgainst)
    {
        $this->hGoalsAgainst = $hGoalsAgainst;

        return $this;
    }

    /**
     * Get hGoalsAgainst
     *
     * @return integer
     */
    public function getHGoalsAgainst()
    {
        return $this->hGoalsAgainst;
    }

    /**
     * Set hNotScored
     *
     * @param integer $hNotScored
     *
     * @return TeamStats
     */
    public function setHNotScored($hNotScored)
    {
        $this->hNotScored = $hNotScored;

        return $this;
    }

    /**
     * Get hNotScored
     *
     * @return integer
     */
    public function getHNotScored()
    {
        return $this->hNotScored;
    }

    /**
     * Set hNotConceded
     *
     * @param integer $hNotConceded
     *
     * @return TeamStats
     */
    public function setHNotConceded($hNotConceded)
    {
        $this->hNotConceded = $hNotConceded;

        return $this;
    }

    /**
     * Get hNotConceded
     *
     * @return integer
     */
    public function getHNotConceded()
    {
        return $this->hNotConceded;
    }

    /**
     * Set hOver15
     *
     * @param integer $hOver15
     *
     * @return TeamStats
     */
    public function setHOver15($hOver15)
    {
        $this->hOver15 = $hOver15;

        return $this;
    }

    /**
     * Get hOver15
     *
     * @return integer
     */
    public function getHOver15()
    {
        return $this->hOver15;
    }

    /**
     * Set hOver151
     *
     * @param integer $hOver151
     *
     * @return TeamStats
     */
    public function setHOver151($hOver151)
    {
        $this->hOver151 = $hOver151;

        return $this;
    }

    /**
     * Get hOver151
     *
     * @return integer
     */
    public function getHOver151()
    {
        return $this->hOver151;
    }

    /**
     * Set hOver152
     *
     * @param integer $hOver152
     *
     * @return TeamStats
     */
    public function setHOver152($hOver152)
    {
        $this->hOver152 = $hOver152;

        return $this;
    }

    /**
     * Get hOver152
     *
     * @return integer
     */
    public function getHOver152()
    {
        return $this->hOver152;
    }

    /**
     * Set hUnder15
     *
     * @param integer $hUnder15
     *
     * @return TeamStats
     */
    public function setHUnder15($hUnder15)
    {
        $this->hUnder15 = $hUnder15;

        return $this;
    }

    /**
     * Get hUnder15
     *
     * @return integer
     */
    public function getHUnder15()
    {
        return $this->hUnder15;
    }

    /**
     * Set hUnder151
     *
     * @param integer $hUnder151
     *
     * @return TeamStats
     */
    public function setHUnder151($hUnder151)
    {
        $this->hUnder151 = $hUnder151;

        return $this;
    }

    /**
     * Get hUnder151
     *
     * @return integer
     */
    public function getHUnder151()
    {
        return $this->hUnder151;
    }

    /**
     * Set hUnder152
     *
     * @param integer $hUnder152
     *
     * @return TeamStats
     */
    public function setHUnder152($hUnder152)
    {
        $this->hUnder152 = $hUnder152;

        return $this;
    }

    /**
     * Get hUnder152
     *
     * @return integer
     */
    public function getHUnder152()
    {
        return $this->hUnder152;
    }

    /**
     * Set hOver25
     *
     * @param integer $hOver25
     *
     * @return TeamStats
     */
    public function setHOver25($hOver25)
    {
        $this->hOver25 = $hOver25;

        return $this;
    }

    /**
     * Get hOver25
     *
     * @return integer
     */
    public function getHOver25()
    {
        return $this->hOver25;
    }

    /**
     * Set hOver251
     *
     * @param integer $hOver251
     *
     * @return TeamStats
     */
    public function setHOver251($hOver251)
    {
        $this->hOver251 = $hOver251;

        return $this;
    }

    /**
     * Get hOver251
     *
     * @return integer
     */
    public function getHOver251()
    {
        return $this->hOver251;
    }

    /**
     * Set hOver252
     *
     * @param integer $hOver252
     *
     * @return TeamStats
     */
    public function setHOver252($hOver252)
    {
        $this->hOver252 = $hOver252;

        return $this;
    }

    /**
     * Get hOver252
     *
     * @return integer
     */
    public function getHOver252()
    {
        return $this->hOver252;
    }

    /**
     * Set hUnder25
     *
     * @param integer $hUnder25
     *
     * @return TeamStats
     */
    public function setHUnder25($hUnder25)
    {
        $this->hUnder25 = $hUnder25;

        return $this;
    }

    /**
     * Get hUnder25
     *
     * @return integer
     */
    public function getHUnder25()
    {
        return $this->hUnder25;
    }

    /**
     * Set hUnder251
     *
     * @param integer $hUnder251
     *
     * @return TeamStats
     */
    public function setHUnder251($hUnder251)
    {
        $this->hUnder251 = $hUnder251;

        return $this;
    }

    /**
     * Get hUnder251
     *
     * @return integer
     */
    public function getHUnder251()
    {
        return $this->hUnder251;
    }

    /**
     * Set hUnder252
     *
     * @param integer $hUnder252
     *
     * @return TeamStats
     */
    public function setHUnder252($hUnder252)
    {
        $this->hUnder252 = $hUnder252;

        return $this;
    }

    /**
     * Get hUnder252
     *
     * @return integer
     */
    public function getHUnder252()
    {
        return $this->hUnder252;
    }

    /**
     * Set hOver35
     *
     * @param integer $hOver35
     *
     * @return TeamStats
     */
    public function setHOver35($hOver35)
    {
        $this->hOver35 = $hOver35;

        return $this;
    }

    /**
     * Get hOver35
     *
     * @return integer
     */
    public function getHOver35()
    {
        return $this->hOver35;
    }

    /**
     * Set hOver351
     *
     * @param integer $hOver351
     *
     * @return TeamStats
     */
    public function setHOver351($hOver351)
    {
        $this->hOver351 = $hOver351;

        return $this;
    }

    /**
     * Get hOver351
     *
     * @return integer
     */
    public function getHOver351()
    {
        return $this->hOver351;
    }

    /**
     * Set hOver352
     *
     * @param integer $hOver352
     *
     * @return TeamStats
     */
    public function setHOver352($hOver352)
    {
        $this->hOver352 = $hOver352;

        return $this;
    }

    /**
     * Get hOver352
     *
     * @return integer
     */
    public function getHOver352()
    {
        return $this->hOver352;
    }

    /**
     * Set hUnder35
     *
     * @param integer $hUnder35
     *
     * @return TeamStats
     */
    public function setHUnder35($hUnder35)
    {
        $this->hUnder35 = $hUnder35;

        return $this;
    }

    /**
     * Get hUnder35
     *
     * @return integer
     */
    public function getHUnder35()
    {
        return $this->hUnder35;
    }

    /**
     * Set hUnder351
     *
     * @param integer $hUnder351
     *
     * @return TeamStats
     */
    public function setHUnder351($hUnder351)
    {
        $this->hUnder351 = $hUnder351;

        return $this;
    }

    /**
     * Get hUnder351
     *
     * @return integer
     */
    public function getHUnder351()
    {
        return $this->hUnder351;
    }

    /**
     * Set hUnder352
     *
     * @param integer $hUnder352
     *
     * @return TeamStats
     */
    public function setHUnder352($hUnder352)
    {
        $this->hUnder352 = $hUnder352;

        return $this;
    }

    /**
     * Get hUnder352
     *
     * @return integer
     */
    public function getHUnder352()
    {
        return $this->hUnder352;
    }

    /**
     * Set hOver25Streak
     *
     * @param integer $hOver25Streak
     *
     * @return TeamStats
     */
    public function setHOver25Streak($hOver25Streak)
    {
        $this->hOver25Streak = $hOver25Streak;

        return $this;
    }

    /**
     * Get hOver25Streak
     *
     * @return integer
     */
    public function getHOver25Streak()
    {
        return $this->hOver25Streak;
    }

    /**
     * Set hUnder25Streak
     *
     * @param integer $hUnder25Streak
     *
     * @return TeamStats
     */
    public function setHUnder25Streak($hUnder25Streak)
    {
        $this->hUnder25Streak = $hUnder25Streak;

        return $this;
    }

    /**
     * Get hUnder25Streak
     *
     * @return integer
     */
    public function getHUnder25Streak()
    {
        return $this->hUnder25Streak;
    }

    /**
     * Set hGoalGoal
     *
     * @param integer $hGoalGoal
     *
     * @return TeamStats
     */
    public function setHGoalGoal($hGoalGoal)
    {
        $this->hGoalGoal = $hGoalGoal;

        return $this;
    }

    /**
     * Get hGoalGoal
     *
     * @return integer
     */
    public function getHGoalGoal()
    {
        return $this->hGoalGoal;
    }

    /**
     * Set hGoalGoalStreak
     *
     * @param integer $hGoalGoalStreak
     *
     * @return TeamStats
     */
    public function setHGoalGoalStreak($hGoalGoalStreak)
    {
        $this->hGoalGoalStreak = $hGoalGoalStreak;

        return $this;
    }

    /**
     * Get hGoalGoalStreak
     *
     * @return integer
     */
    public function getHGoalGoalStreak()
    {
        return $this->hGoalGoalStreak;
    }

    /**
     * Set hNoGoal
     *
     * @param integer $hNoGoal
     *
     * @return TeamStats
     */
    public function setHNoGoal($hNoGoal)
    {
        $this->hNoGoal = $hNoGoal;

        return $this;
    }

    /**
     * Get hNoGoal
     *
     * @return integer
     */
    public function getHNoGoal()
    {
        return $this->hNoGoal;
    }

    /**
     * Set hNoGoalStreak
     *
     * @param integer $hNoGoalStreak
     *
     * @return TeamStats
     */
    public function setHNoGoalStreak($hNoGoalStreak)
    {
        $this->hNoGoalStreak = $hNoGoalStreak;

        return $this;
    }

    /**
     * Get hNoGoalStreak
     *
     * @return integer
     */
    public function getHNoGoalStreak()
    {
        return $this->hNoGoalStreak;
    }

    /**
     * Set hGoals01
     *
     * @param integer $hGoals01
     *
     * @return TeamStats
     */
    public function setHGoals01($hGoals01)
    {
        $this->hGoals01 = $hGoals01;

        return $this;
    }

    /**
     * Get hGoals01
     *
     * @return integer
     */
    public function getHGoals01()
    {
        return $this->hGoals01;
    }

    /**
     * Set hGoals23
     *
     * @param integer $hGoals23
     *
     * @return TeamStats
     */
    public function setHGoals23($hGoals23)
    {
        $this->hGoals23 = $hGoals23;

        return $this;
    }

    /**
     * Get hGoals23
     *
     * @return integer
     */
    public function getHGoals23()
    {
        return $this->hGoals23;
    }

    /**
     * Set hGoals46
     *
     * @param integer $hGoals46
     *
     * @return TeamStats
     */
    public function setHGoals46($hGoals46)
    {
        $this->hGoals46 = $hGoals46;

        return $this;
    }

    /**
     * Get hGoals46
     *
     * @return integer
     */
    public function getHGoals46()
    {
        return $this->hGoals46;
    }

    /**
     * Set hGoals7
     *
     * @param integer $hGoals7
     *
     * @return TeamStats
     */
    public function setHGoals7($hGoals7)
    {
        $this->hGoals7 = $hGoals7;

        return $this;
    }

    /**
     * Get hGoals7
     *
     * @return integer
     */
    public function getHGoals7()
    {
        return $this->hGoals7;
    }

    /**
     * Set hGoalsMinute015
     *
     * @param integer $hGoalsMinute015
     *
     * @return TeamStats
     */
    public function setHGoalsMinute015($hGoalsMinute015)
    {
        $this->hGoalsMinute015 = $hGoalsMinute015;

        return $this;
    }

    /**
     * Get hGoalsMinute015
     *
     * @return integer
     */
    public function getHGoalsMinute015()
    {
        return $this->hGoalsMinute015;
    }

    /**
     * Set hGoalsMinute1630
     *
     * @param integer $hGoalsMinute1630
     *
     * @return TeamStats
     */
    public function setHGoalsMinute1630($hGoalsMinute1630)
    {
        $this->hGoalsMinute1630 = $hGoalsMinute1630;

        return $this;
    }

    /**
     * Get hGoalsMinute1630
     *
     * @return integer
     */
    public function getHGoalsMinute1630()
    {
        return $this->hGoalsMinute1630;
    }

    /**
     * Set hGoalsMinute3145
     *
     * @param integer $hGoalsMinute3145
     *
     * @return TeamStats
     */
    public function setHGoalsMinute3145($hGoalsMinute3145)
    {
        $this->hGoalsMinute3145 = $hGoalsMinute3145;

        return $this;
    }

    /**
     * Get hGoalsMinute3145
     *
     * @return integer
     */
    public function getHGoalsMinute3145()
    {
        return $this->hGoalsMinute3145;
    }

    /**
     * Set hGoalsMinute4660
     *
     * @param integer $hGoalsMinute4660
     *
     * @return TeamStats
     */
    public function setHGoalsMinute4660($hGoalsMinute4660)
    {
        $this->hGoalsMinute4660 = $hGoalsMinute4660;

        return $this;
    }

    /**
     * Get hGoalsMinute4660
     *
     * @return integer
     */
    public function getHGoalsMinute4660()
    {
        return $this->hGoalsMinute4660;
    }

    /**
     * Set hGoalsMinute6175
     *
     * @param integer $hGoalsMinute6175
     *
     * @return TeamStats
     */
    public function setHGoalsMinute6175($hGoalsMinute6175)
    {
        $this->hGoalsMinute6175 = $hGoalsMinute6175;

        return $this;
    }

    /**
     * Get hGoalsMinute6175
     *
     * @return integer
     */
    public function getHGoalsMinute6175()
    {
        return $this->hGoalsMinute6175;
    }

    /**
     * Set hGoalsMinute7690
     *
     * @param integer $hGoalsMinute7690
     *
     * @return TeamStats
     */
    public function setHGoalsMinute7690($hGoalsMinute7690)
    {
        $this->hGoalsMinute7690 = $hGoalsMinute7690;

        return $this;
    }

    /**
     * Get hGoalsMinute7690
     *
     * @return integer
     */
    public function getHGoalsMinute7690()
    {
        return $this->hGoalsMinute7690;
    }

    /**
     * Set hGoalsMinute015Conceded
     *
     * @param integer $hGoalsMinute015Conceded
     *
     * @return TeamStats
     */
    public function setHGoalsMinute015Conceded($hGoalsMinute015Conceded)
    {
        $this->hGoalsMinute015Conceded = $hGoalsMinute015Conceded;

        return $this;
    }

    /**
     * Get hGoalsMinute015Conceded
     *
     * @return integer
     */
    public function getHGoalsMinute015Conceded()
    {
        return $this->hGoalsMinute015Conceded;
    }

    /**
     * Set hGoalsMinute1630Conceded
     *
     * @param integer $hGoalsMinute1630Conceded
     *
     * @return TeamStats
     */
    public function setHGoalsMinute1630Conceded($hGoalsMinute1630Conceded)
    {
        $this->hGoalsMinute1630Conceded = $hGoalsMinute1630Conceded;

        return $this;
    }

    /**
     * Get hGoalsMinute1630Conceded
     *
     * @return integer
     */
    public function getHGoalsMinute1630Conceded()
    {
        return $this->hGoalsMinute1630Conceded;
    }

    /**
     * Set hGoalsMinute3145Conceded
     *
     * @param integer $hGoalsMinute3145Conceded
     *
     * @return TeamStats
     */
    public function setHGoalsMinute3145Conceded($hGoalsMinute3145Conceded)
    {
        $this->hGoalsMinute3145Conceded = $hGoalsMinute3145Conceded;

        return $this;
    }

    /**
     * Get hGoalsMinute3145Conceded
     *
     * @return integer
     */
    public function getHGoalsMinute3145Conceded()
    {
        return $this->hGoalsMinute3145Conceded;
    }

    /**
     * Set hGoalsMinute4660Conceded
     *
     * @param integer $hGoalsMinute4660Conceded
     *
     * @return TeamStats
     */
    public function setHGoalsMinute4660Conceded($hGoalsMinute4660Conceded)
    {
        $this->hGoalsMinute4660Conceded = $hGoalsMinute4660Conceded;

        return $this;
    }

    /**
     * Get hGoalsMinute4660Conceded
     *
     * @return integer
     */
    public function getHGoalsMinute4660Conceded()
    {
        return $this->hGoalsMinute4660Conceded;
    }

    /**
     * Set hGoalsMinute6175Conceded
     *
     * @param integer $hGoalsMinute6175Conceded
     *
     * @return TeamStats
     */
    public function setHGoalsMinute6175Conceded($hGoalsMinute6175Conceded)
    {
        $this->hGoalsMinute6175Conceded = $hGoalsMinute6175Conceded;

        return $this;
    }

    /**
     * Get hGoalsMinute6175Conceded
     *
     * @return integer
     */
    public function getHGoalsMinute6175Conceded()
    {
        return $this->hGoalsMinute6175Conceded;
    }

    /**
     * Set hGoalsMinute7690Conceded
     *
     * @param integer $hGoalsMinute7690Conceded
     *
     * @return TeamStats
     */
    public function setHGoalsMinute7690Conceded($hGoalsMinute7690Conceded)
    {
        $this->hGoalsMinute7690Conceded = $hGoalsMinute7690Conceded;

        return $this;
    }

    /**
     * Get hGoalsMinute7690Conceded
     *
     * @return integer
     */
    public function getHGoalsMinute7690Conceded()
    {
        return $this->hGoalsMinute7690Conceded;
    }

    /**
     * Set aPlayed
     *
     * @param integer $aPlayed
     *
     * @return TeamStats
     */
    public function setAPlayed($aPlayed)
    {
        $this->aPlayed = $aPlayed;

        return $this;
    }

    /**
     * Get aPlayed
     *
     * @return integer
     */
    public function getAPlayed()
    {
        return $this->aPlayed;
    }

    /**
     * Set aWins
     *
     * @param integer $aWins
     *
     * @return TeamStats
     */
    public function setAWins($aWins)
    {
        $this->aWins = $aWins;

        return $this;
    }

    /**
     * Get aWins
     *
     * @return integer
     */
    public function getAWins()
    {
        return $this->aWins;
    }

    /**
     * Set aWinsHt
     *
     * @param integer $aWinsHt
     *
     * @return TeamStats
     */
    public function setAWinsHt($aWinsHt)
    {
        $this->aWinsHt = $aWinsHt;

        return $this;
    }

    /**
     * Get aWinsHt
     *
     * @return integer
     */
    public function getAWinsHt()
    {
        return $this->aWinsHt;
    }

    /**
     * Set aWinStreak
     *
     * @param integer $aWinStreak
     *
     * @return TeamStats
     */
    public function setAWinStreak($aWinStreak)
    {
        $this->aWinStreak = $aWinStreak;

        return $this;
    }

    /**
     * Get aWinStreak
     *
     * @return integer
     */
    public function getAWinStreak()
    {
        return $this->aWinStreak;
    }

    /**
     * Set aNoWinStreak
     *
     * @param integer $aNoWinStreak
     *
     * @return TeamStats
     */
    public function setANoWinStreak($aNoWinStreak)
    {
        $this->aNoWinStreak = $aNoWinStreak;

        return $this;
    }

    /**
     * Get aNoWinStreak
     *
     * @return integer
     */
    public function getANoWinStreak()
    {
        return $this->aNoWinStreak;
    }

    /**
     * Set aDraws
     *
     * @param integer $aDraws
     *
     * @return TeamStats
     */
    public function setADraws($aDraws)
    {
        $this->aDraws = $aDraws;

        return $this;
    }

    /**
     * Get aDraws
     *
     * @return integer
     */
    public function getADraws()
    {
        return $this->aDraws;
    }

    /**
     * Set aDrawsHt
     *
     * @param integer $aDrawsHt
     *
     * @return TeamStats
     */
    public function setADrawsHt($aDrawsHt)
    {
        $this->aDrawsHt = $aDrawsHt;

        return $this;
    }

    /**
     * Get aDrawsHt
     *
     * @return integer
     */
    public function getADrawsHt()
    {
        return $this->aDrawsHt;
    }

    /**
     * Set aDrawStreak
     *
     * @param integer $aDrawStreak
     *
     * @return TeamStats
     */
    public function setADrawStreak($aDrawStreak)
    {
        $this->aDrawStreak = $aDrawStreak;

        return $this;
    }

    /**
     * Get aDrawStreak
     *
     * @return integer
     */
    public function getADrawStreak()
    {
        return $this->aDrawStreak;
    }

    /**
     * Set aNoDrawStreak
     *
     * @param integer $aNoDrawStreak
     *
     * @return TeamStats
     */
    public function setANoDrawStreak($aNoDrawStreak)
    {
        $this->aNoDrawStreak = $aNoDrawStreak;

        return $this;
    }

    /**
     * Get aNoDrawStreak
     *
     * @return integer
     */
    public function getANoDrawStreak()
    {
        return $this->aNoDrawStreak;
    }

    /**
     * Set aLoses
     *
     * @param integer $aLoses
     *
     * @return TeamStats
     */
    public function setALoses($aLoses)
    {
        $this->aLoses = $aLoses;

        return $this;
    }

    /**
     * Get aLoses
     *
     * @return integer
     */
    public function getALoses()
    {
        return $this->aLoses;
    }

    /**
     * Set aLosesHt
     *
     * @param integer $aLosesHt
     *
     * @return TeamStats
     */
    public function setALosesHt($aLosesHt)
    {
        $this->aLosesHt = $aLosesHt;

        return $this;
    }

    /**
     * Get aLosesHt
     *
     * @return integer
     */
    public function getALosesHt()
    {
        return $this->aLosesHt;
    }

    /**
     * Set aLoseStreak
     *
     * @param integer $aLoseStreak
     *
     * @return TeamStats
     */
    public function setALoseStreak($aLoseStreak)
    {
        $this->aLoseStreak = $aLoseStreak;

        return $this;
    }

    /**
     * Get aLoseStreak
     *
     * @return integer
     */
    public function getALoseStreak()
    {
        return $this->aLoseStreak;
    }

    /**
     * Set aNoLoseStreak
     *
     * @param integer $aNoLoseStreak
     *
     * @return TeamStats
     */
    public function setANoLoseStreak($aNoLoseStreak)
    {
        $this->aNoLoseStreak = $aNoLoseStreak;

        return $this;
    }

    /**
     * Get aNoLoseStreak
     *
     * @return integer
     */
    public function getANoLoseStreak()
    {
        return $this->aNoLoseStreak;
    }

    /**
     * Set aGoalsForStreak
     *
     * @param integer $aGoalsForStreak
     *
     * @return TeamStats
     */
    public function setAGoalsForStreak($aGoalsForStreak)
    {
        $this->aGoalsForStreak = $aGoalsForStreak;

        return $this;
    }

    /**
     * Get aGoalsForStreak
     *
     * @return integer
     */
    public function getAGoalsForStreak()
    {
        return $this->aGoalsForStreak;
    }

    /**
     * Set aGoalsAgainstStreak
     *
     * @param integer $aGoalsAgainstStreak
     *
     * @return TeamStats
     */
    public function setAGoalsAgainstStreak($aGoalsAgainstStreak)
    {
        $this->aGoalsAgainstStreak = $aGoalsAgainstStreak;

        return $this;
    }

    /**
     * Get aGoalsAgainstStreak
     *
     * @return integer
     */
    public function getAGoalsAgainstStreak()
    {
        return $this->aGoalsAgainstStreak;
    }

    /**
     * Set aNotScoredStreak
     *
     * @param integer $aNotScoredStreak
     *
     * @return TeamStats
     */
    public function setANotScoredStreak($aNotScoredStreak)
    {
        $this->aNotScoredStreak = $aNotScoredStreak;

        return $this;
    }

    /**
     * Get aNotScoredStreak
     *
     * @return integer
     */
    public function getANotScoredStreak()
    {
        return $this->aNotScoredStreak;
    }

    /**
     * Set aNotConcededStreak
     *
     * @param integer $aNotConcededStreak
     *
     * @return TeamStats
     */
    public function setANotConcededStreak($aNotConcededStreak)
    {
        $this->aNotConcededStreak = $aNotConcededStreak;

        return $this;
    }

    /**
     * Get aNotConcededStreak
     *
     * @return integer
     */
    public function getANotConcededStreak()
    {
        return $this->aNotConcededStreak;
    }

    /**
     * Set aGoalsFor
     *
     * @param integer $aGoalsFor
     *
     * @return TeamStats
     */
    public function setAGoalsFor($aGoalsFor)
    {
        $this->aGoalsFor = $aGoalsFor;

        return $this;
    }

    /**
     * Get aGoalsFor
     *
     * @return integer
     */
    public function getAGoalsFor()
    {
        return $this->aGoalsFor;
    }

    /**
     * Set aGoalsAgainst
     *
     * @param integer $aGoalsAgainst
     *
     * @return TeamStats
     */
    public function setAGoalsAgainst($aGoalsAgainst)
    {
        $this->aGoalsAgainst = $aGoalsAgainst;

        return $this;
    }

    /**
     * Get aGoalsAgainst
     *
     * @return integer
     */
    public function getAGoalsAgainst()
    {
        return $this->aGoalsAgainst;
    }

    /**
     * Set aNotScored
     *
     * @param integer $aNotScored
     *
     * @return TeamStats
     */
    public function setANotScored($aNotScored)
    {
        $this->aNotScored = $aNotScored;

        return $this;
    }

    /**
     * Get aNotScored
     *
     * @return integer
     */
    public function getANotScored()
    {
        return $this->aNotScored;
    }

    /**
     * Set aNotConceded
     *
     * @param integer $aNotConceded
     *
     * @return TeamStats
     */
    public function setANotConceded($aNotConceded)
    {
        $this->aNotConceded = $aNotConceded;

        return $this;
    }

    /**
     * Get aNotConceded
     *
     * @return integer
     */
    public function getANotConceded()
    {
        return $this->aNotConceded;
    }

    /**
     * Set aOver15
     *
     * @param integer $aOver15
     *
     * @return TeamStats
     */
    public function setAOver15($aOver15)
    {
        $this->aOver15 = $aOver15;

        return $this;
    }

    /**
     * Get aOver15
     *
     * @return integer
     */
    public function getAOver15()
    {
        return $this->aOver15;
    }

    /**
     * Set aOver151
     *
     * @param integer $aOver151
     *
     * @return TeamStats
     */
    public function setAOver151($aOver151)
    {
        $this->aOver151 = $aOver151;

        return $this;
    }

    /**
     * Get aOver151
     *
     * @return integer
     */
    public function getAOver151()
    {
        return $this->aOver151;
    }

    /**
     * Set aOver152
     *
     * @param integer $aOver152
     *
     * @return TeamStats
     */
    public function setAOver152($aOver152)
    {
        $this->aOver152 = $aOver152;

        return $this;
    }

    /**
     * Get aOver152
     *
     * @return integer
     */
    public function getAOver152()
    {
        return $this->aOver152;
    }

    /**
     * Set aUnder15
     *
     * @param integer $aUnder15
     *
     * @return TeamStats
     */
    public function setAUnder15($aUnder15)
    {
        $this->aUnder15 = $aUnder15;

        return $this;
    }

    /**
     * Get aUnder15
     *
     * @return integer
     */
    public function getAUnder15()
    {
        return $this->aUnder15;
    }

    /**
     * Set aUnder151
     *
     * @param integer $aUnder151
     *
     * @return TeamStats
     */
    public function setAUnder151($aUnder151)
    {
        $this->aUnder151 = $aUnder151;

        return $this;
    }

    /**
     * Get aUnder151
     *
     * @return integer
     */
    public function getAUnder151()
    {
        return $this->aUnder151;
    }

    /**
     * Set aUnder152
     *
     * @param integer $aUnder152
     *
     * @return TeamStats
     */
    public function setAUnder152($aUnder152)
    {
        $this->aUnder152 = $aUnder152;

        return $this;
    }

    /**
     * Get aUnder152
     *
     * @return integer
     */
    public function getAUnder152()
    {
        return $this->aUnder152;
    }

    /**
     * Set aOver25
     *
     * @param integer $aOver25
     *
     * @return TeamStats
     */
    public function setAOver25($aOver25)
    {
        $this->aOver25 = $aOver25;

        return $this;
    }

    /**
     * Get aOver25
     *
     * @return integer
     */
    public function getAOver25()
    {
        return $this->aOver25;
    }

    /**
     * Set aOver251
     *
     * @param integer $aOver251
     *
     * @return TeamStats
     */
    public function setAOver251($aOver251)
    {
        $this->aOver251 = $aOver251;

        return $this;
    }

    /**
     * Get aOver251
     *
     * @return integer
     */
    public function getAOver251()
    {
        return $this->aOver251;
    }

    /**
     * Set aOver252
     *
     * @param integer $aOver252
     *
     * @return TeamStats
     */
    public function setAOver252($aOver252)
    {
        $this->aOver252 = $aOver252;

        return $this;
    }

    /**
     * Get aOver252
     *
     * @return integer
     */
    public function getAOver252()
    {
        return $this->aOver252;
    }

    /**
     * Set aUnder25
     *
     * @param integer $aUnder25
     *
     * @return TeamStats
     */
    public function setAUnder25($aUnder25)
    {
        $this->aUnder25 = $aUnder25;

        return $this;
    }

    /**
     * Get aUnder25
     *
     * @return integer
     */
    public function getAUnder25()
    {
        return $this->aUnder25;
    }

    /**
     * Set aUnder251
     *
     * @param integer $aUnder251
     *
     * @return TeamStats
     */
    public function setAUnder251($aUnder251)
    {
        $this->aUnder251 = $aUnder251;

        return $this;
    }

    /**
     * Get aUnder251
     *
     * @return integer
     */
    public function getAUnder251()
    {
        return $this->aUnder251;
    }

    /**
     * Set aUnder252
     *
     * @param integer $aUnder252
     *
     * @return TeamStats
     */
    public function setAUnder252($aUnder252)
    {
        $this->aUnder252 = $aUnder252;

        return $this;
    }

    /**
     * Get aUnder252
     *
     * @return integer
     */
    public function getAUnder252()
    {
        return $this->aUnder252;
    }

    /**
     * Set aOver35
     *
     * @param integer $aOver35
     *
     * @return TeamStats
     */
    public function setAOver35($aOver35)
    {
        $this->aOver35 = $aOver35;

        return $this;
    }

    /**
     * Get aOver35
     *
     * @return integer
     */
    public function getAOver35()
    {
        return $this->aOver35;
    }

    /**
     * Set aOver351
     *
     * @param integer $aOver351
     *
     * @return TeamStats
     */
    public function setAOver351($aOver351)
    {
        $this->aOver351 = $aOver351;

        return $this;
    }

    /**
     * Get aOver351
     *
     * @return integer
     */
    public function getAOver351()
    {
        return $this->aOver351;
    }

    /**
     * Set aOver352
     *
     * @param integer $aOver352
     *
     * @return TeamStats
     */
    public function setAOver352($aOver352)
    {
        $this->aOver352 = $aOver352;

        return $this;
    }

    /**
     * Get aOver352
     *
     * @return integer
     */
    public function getAOver352()
    {
        return $this->aOver352;
    }

    /**
     * Set aUnder35
     *
     * @param integer $aUnder35
     *
     * @return TeamStats
     */
    public function setAUnder35($aUnder35)
    {
        $this->aUnder35 = $aUnder35;

        return $this;
    }

    /**
     * Get aUnder35
     *
     * @return integer
     */
    public function getAUnder35()
    {
        return $this->aUnder35;
    }

    /**
     * Set aUnder351
     *
     * @param integer $aUnder351
     *
     * @return TeamStats
     */
    public function setAUnder351($aUnder351)
    {
        $this->aUnder351 = $aUnder351;

        return $this;
    }

    /**
     * Get aUnder351
     *
     * @return integer
     */
    public function getAUnder351()
    {
        return $this->aUnder351;
    }

    /**
     * Set aUnder352
     *
     * @param integer $aUnder352
     *
     * @return TeamStats
     */
    public function setAUnder352($aUnder352)
    {
        $this->aUnder352 = $aUnder352;

        return $this;
    }

    /**
     * Get aUnder352
     *
     * @return integer
     */
    public function getAUnder352()
    {
        return $this->aUnder352;
    }

    /**
     * Set aOver25Streak
     *
     * @param integer $aOver25Streak
     *
     * @return TeamStats
     */
    public function setAOver25Streak($aOver25Streak)
    {
        $this->aOver25Streak = $aOver25Streak;

        return $this;
    }

    /**
     * Get aOver25Streak
     *
     * @return integer
     */
    public function getAOver25Streak()
    {
        return $this->aOver25Streak;
    }

    /**
     * Set aUnder25Streak
     *
     * @param integer $aUnder25Streak
     *
     * @return TeamStats
     */
    public function setAUnder25Streak($aUnder25Streak)
    {
        $this->aUnder25Streak = $aUnder25Streak;

        return $this;
    }

    /**
     * Get aUnder25Streak
     *
     * @return integer
     */
    public function getAUnder25Streak()
    {
        return $this->aUnder25Streak;
    }

    /**
     * Set aGoalGoal
     *
     * @param integer $aGoalGoal
     *
     * @return TeamStats
     */
    public function setAGoalGoal($aGoalGoal)
    {
        $this->aGoalGoal = $aGoalGoal;

        return $this;
    }

    /**
     * Get aGoalGoal
     *
     * @return integer
     */
    public function getAGoalGoal()
    {
        return $this->aGoalGoal;
    }

    /**
     * Set aGoalGoalStreak
     *
     * @param integer $aGoalGoalStreak
     *
     * @return TeamStats
     */
    public function setAGoalGoalStreak($aGoalGoalStreak)
    {
        $this->aGoalGoalStreak = $aGoalGoalStreak;

        return $this;
    }

    /**
     * Get aGoalGoalStreak
     *
     * @return integer
     */
    public function getAGoalGoalStreak()
    {
        return $this->aGoalGoalStreak;
    }

    /**
     * Set aNoGoal
     *
     * @param integer $aNoGoal
     *
     * @return TeamStats
     */
    public function setANoGoal($aNoGoal)
    {
        $this->aNoGoal = $aNoGoal;

        return $this;
    }

    /**
     * Get aNoGoal
     *
     * @return integer
     */
    public function getANoGoal()
    {
        return $this->aNoGoal;
    }

    /**
     * Set aNoGoalStreak
     *
     * @param integer $aNoGoalStreak
     *
     * @return TeamStats
     */
    public function setANoGoalStreak($aNoGoalStreak)
    {
        $this->aNoGoalStreak = $aNoGoalStreak;

        return $this;
    }

    /**
     * Get aNoGoalStreak
     *
     * @return integer
     */
    public function getANoGoalStreak()
    {
        return $this->aNoGoalStreak;
    }

    /**
     * Set aGoals01
     *
     * @param integer $aGoals01
     *
     * @return TeamStats
     */
    public function setAGoals01($aGoals01)
    {
        $this->aGoals01 = $aGoals01;

        return $this;
    }

    /**
     * Get aGoals01
     *
     * @return integer
     */
    public function getAGoals01()
    {
        return $this->aGoals01;
    }

    /**
     * Set aGoals23
     *
     * @param integer $aGoals23
     *
     * @return TeamStats
     */
    public function setAGoals23($aGoals23)
    {
        $this->aGoals23 = $aGoals23;

        return $this;
    }

    /**
     * Get aGoals23
     *
     * @return integer
     */
    public function getAGoals23()
    {
        return $this->aGoals23;
    }

    /**
     * Set aGoals46
     *
     * @param integer $aGoals46
     *
     * @return TeamStats
     */
    public function setAGoals46($aGoals46)
    {
        $this->aGoals46 = $aGoals46;

        return $this;
    }

    /**
     * Get aGoals46
     *
     * @return integer
     */
    public function getAGoals46()
    {
        return $this->aGoals46;
    }

    /**
     * Set aGoals7
     *
     * @param integer $aGoals7
     *
     * @return TeamStats
     */
    public function setAGoals7($aGoals7)
    {
        $this->aGoals7 = $aGoals7;

        return $this;
    }

    /**
     * Get aGoals7
     *
     * @return integer
     */
    public function getAGoals7()
    {
        return $this->aGoals7;
    }

    /**
     * Set aGoalsMinute015
     *
     * @param integer $aGoalsMinute015
     *
     * @return TeamStats
     */
    public function setAGoalsMinute015($aGoalsMinute015)
    {
        $this->aGoalsMinute015 = $aGoalsMinute015;

        return $this;
    }

    /**
     * Get aGoalsMinute015
     *
     * @return integer
     */
    public function getAGoalsMinute015()
    {
        return $this->aGoalsMinute015;
    }

    /**
     * Set aGoalsMinute1630
     *
     * @param integer $aGoalsMinute1630
     *
     * @return TeamStats
     */
    public function setAGoalsMinute1630($aGoalsMinute1630)
    {
        $this->aGoalsMinute1630 = $aGoalsMinute1630;

        return $this;
    }

    /**
     * Get aGoalsMinute1630
     *
     * @return integer
     */
    public function getAGoalsMinute1630()
    {
        return $this->aGoalsMinute1630;
    }

    /**
     * Set aGoalsMinute3145
     *
     * @param integer $aGoalsMinute3145
     *
     * @return TeamStats
     */
    public function setAGoalsMinute3145($aGoalsMinute3145)
    {
        $this->aGoalsMinute3145 = $aGoalsMinute3145;

        return $this;
    }

    /**
     * Get aGoalsMinute3145
     *
     * @return integer
     */
    public function getAGoalsMinute3145()
    {
        return $this->aGoalsMinute3145;
    }

    /**
     * Set aGoalsMinute4660
     *
     * @param integer $aGoalsMinute4660
     *
     * @return TeamStats
     */
    public function setAGoalsMinute4660($aGoalsMinute4660)
    {
        $this->aGoalsMinute4660 = $aGoalsMinute4660;

        return $this;
    }

    /**
     * Get aGoalsMinute4660
     *
     * @return integer
     */
    public function getAGoalsMinute4660()
    {
        return $this->aGoalsMinute4660;
    }

    /**
     * Set aGoalsMinute6175
     *
     * @param integer $aGoalsMinute6175
     *
     * @return TeamStats
     */
    public function setAGoalsMinute6175($aGoalsMinute6175)
    {
        $this->aGoalsMinute6175 = $aGoalsMinute6175;

        return $this;
    }

    /**
     * Get aGoalsMinute6175
     *
     * @return integer
     */
    public function getAGoalsMinute6175()
    {
        return $this->aGoalsMinute6175;
    }

    /**
     * Set aGoalsMinute7690
     *
     * @param integer $aGoalsMinute7690
     *
     * @return TeamStats
     */
    public function setAGoalsMinute7690($aGoalsMinute7690)
    {
        $this->aGoalsMinute7690 = $aGoalsMinute7690;

        return $this;
    }

    /**
     * Get aGoalsMinute7690
     *
     * @return integer
     */
    public function getAGoalsMinute7690()
    {
        return $this->aGoalsMinute7690;
    }

    /**
     * Set aGoalsMinute015Conceded
     *
     * @param integer $aGoalsMinute015Conceded
     *
     * @return TeamStats
     */
    public function setAGoalsMinute015Conceded($aGoalsMinute015Conceded)
    {
        $this->aGoalsMinute015Conceded = $aGoalsMinute015Conceded;

        return $this;
    }

    /**
     * Get aGoalsMinute015Conceded
     *
     * @return integer
     */
    public function getAGoalsMinute015Conceded()
    {
        return $this->aGoalsMinute015Conceded;
    }

    /**
     * Set aGoalsMinute1630Conceded
     *
     * @param integer $aGoalsMinute1630Conceded
     *
     * @return TeamStats
     */
    public function setAGoalsMinute1630Conceded($aGoalsMinute1630Conceded)
    {
        $this->aGoalsMinute1630Conceded = $aGoalsMinute1630Conceded;

        return $this;
    }

    /**
     * Get aGoalsMinute1630Conceded
     *
     * @return integer
     */
    public function getAGoalsMinute1630Conceded()
    {
        return $this->aGoalsMinute1630Conceded;
    }

    /**
     * Set aGoalsMinute3145Conceded
     *
     * @param integer $aGoalsMinute3145Conceded
     *
     * @return TeamStats
     */
    public function setAGoalsMinute3145Conceded($aGoalsMinute3145Conceded)
    {
        $this->aGoalsMinute3145Conceded = $aGoalsMinute3145Conceded;

        return $this;
    }

    /**
     * Get aGoalsMinute3145Conceded
     *
     * @return integer
     */
    public function getAGoalsMinute3145Conceded()
    {
        return $this->aGoalsMinute3145Conceded;
    }

    /**
     * Set aGoalsMinute4660Conceded
     *
     * @param integer $aGoalsMinute4660Conceded
     *
     * @return TeamStats
     */
    public function setAGoalsMinute4660Conceded($aGoalsMinute4660Conceded)
    {
        $this->aGoalsMinute4660Conceded = $aGoalsMinute4660Conceded;

        return $this;
    }

    /**
     * Get aGoalsMinute4660Conceded
     *
     * @return integer
     */
    public function getAGoalsMinute4660Conceded()
    {
        return $this->aGoalsMinute4660Conceded;
    }

    /**
     * Set aGoalsMinute6175Conceded
     *
     * @param integer $aGoalsMinute6175Conceded
     *
     * @return TeamStats
     */
    public function setAGoalsMinute6175Conceded($aGoalsMinute6175Conceded)
    {
        $this->aGoalsMinute6175Conceded = $aGoalsMinute6175Conceded;

        return $this;
    }

    /**
     * Get aGoalsMinute6175Conceded
     *
     * @return integer
     */
    public function getAGoalsMinute6175Conceded()
    {
        return $this->aGoalsMinute6175Conceded;
    }

    /**
     * Set aGoalsMinute7690Conceded
     *
     * @param integer $aGoalsMinute7690Conceded
     *
     * @return TeamStats
     */
    public function setAGoalsMinute7690Conceded($aGoalsMinute7690Conceded)
    {
        $this->aGoalsMinute7690Conceded = $aGoalsMinute7690Conceded;

        return $this;
    }

    /**
     * Get aGoalsMinute7690Conceded
     *
     * @return integer
     */
    public function getAGoalsMinute7690Conceded()
    {
        return $this->aGoalsMinute7690Conceded;
    }

    /**
     * Set tYellowCards
     *
     * @param integer $tYellowCards
     *
     * @return TeamStats
     */
    public function setTYellowCards($tYellowCards)
    {
        $this->tYellowCards = $tYellowCards;

        return $this;
    }

    /**
     * Get tYellowCards
     *
     * @return integer
     */
    public function getTYellowCards()
    {
        return $this->tYellowCards;
    }

    /**
     * Set tRedCards
     *
     * @param integer $tRedCards
     *
     * @return TeamStats
     */
    public function setTRedCards($tRedCards)
    {
        $this->tRedCards = $tRedCards;

        return $this;
    }

    /**
     * Get tRedCards
     *
     * @return integer
     */
    public function getTRedCards()
    {
        return $this->tRedCards;
    }

    /**
     * Set tNoWinStreak
     *
     * @param integer $tNoWinStreak
     *
     * @return TeamStats
     */
    public function setTNoWinStreak($tNoWinStreak)
    {
        $this->tNoWinStreak = $tNoWinStreak;

        return $this;
    }

    /**
     * Get tNoWinStreak
     *
     * @return integer
     */
    public function getTNoWinStreak()
    {
        return $this->tNoWinStreak;
    }

    /**
     * Set tNoDrawStreak
     *
     * @param integer $tNoDrawStreak
     *
     * @return TeamStats
     */
    public function setTNoDrawStreak($tNoDrawStreak)
    {
        $this->tNoDrawStreak = $tNoDrawStreak;

        return $this;
    }

    /**
     * Get tNoDrawStreak
     *
     * @return integer
     */
    public function getTNoDrawStreak()
    {
        return $this->tNoDrawStreak;
    }

    /**
     * Set tNoLoseStreak
     *
     * @param integer $tNoLoseStreak
     *
     * @return TeamStats
     */
    public function setTNoLoseStreak($tNoLoseStreak)
    {
        $this->tNoLoseStreak = $tNoLoseStreak;

        return $this;
    }

    /**
     * Get tNoLoseStreak
     *
     * @return integer
     */
    public function getTNoLoseStreak()
    {
        return $this->tNoLoseStreak;
    }

    /**
     * Set tGoalsForStreak
     *
     * @param integer $tGoalsForStreak
     *
     * @return TeamStats
     */
    public function setTGoalsForStreak($tGoalsForStreak)
    {
        $this->tGoalsForStreak = $tGoalsForStreak;

        return $this;
    }

    /**
     * Get tGoalsForStreak
     *
     * @return integer
     */
    public function getTGoalsForStreak()
    {
        return $this->tGoalsForStreak;
    }

    /**
     * Set tGoalsAgainstStreak
     *
     * @param integer $tGoalsAgainstStreak
     *
     * @return TeamStats
     */
    public function setTGoalsAgainstStreak($tGoalsAgainstStreak)
    {
        $this->tGoalsAgainstStreak = $tGoalsAgainstStreak;

        return $this;
    }

    /**
     * Get tGoalsAgainstStreak
     *
     * @return integer
     */
    public function getTGoalsAgainstStreak()
    {
        return $this->tGoalsAgainstStreak;
    }

    /**
     * Set tGoalGoalStreak
     *
     * @param integer $tGoalGoalStreak
     *
     * @return TeamStats
     */
    public function setTGoalGoalStreak($tGoalGoalStreak)
    {
        $this->tGoalGoalStreak = $tGoalGoalStreak;

        return $this;
    }

    /**
     * Get tGoalGoalStreak
     *
     * @return integer
     */
    public function getTGoalGoalStreak()
    {
        return $this->tGoalGoalStreak;
    }

    /**
     * Set tNoGoalStreak
     *
     * @param integer $tNoGoalStreak
     *
     * @return TeamStats
     */
    public function setTNoGoalStreak($tNoGoalStreak)
    {
        $this->tNoGoalStreak = $tNoGoalStreak;

        return $this;
    }

    /**
     * Get tNoGoalStreak
     *
     * @return integer
     */
    public function getTNoGoalStreak()
    {
        return $this->tNoGoalStreak;
    }

    /**
     * Set tOver25Streak
     *
     * @param integer $tOver25Streak
     *
     * @return TeamStats
     */
    public function setTOver25Streak($tOver25Streak)
    {
        $this->tOver25Streak = $tOver25Streak;

        return $this;
    }

    /**
     * Get tOver25Streak
     *
     * @return integer
     */
    public function getTOver25Streak()
    {
        return $this->tOver25Streak;
    }

    /**
     * Set tUnder25Streak
     *
     * @param integer $tUnder25Streak
     *
     * @return TeamStats
     */
    public function setTUnder25Streak($tUnder25Streak)
    {
        $this->tUnder25Streak = $tUnder25Streak;

        return $this;
    }

    /**
     * Get tUnder25Streak
     *
     * @return integer
     */
    public function getTUnder25Streak()
    {
        return $this->tUnder25Streak;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return TeamStats
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return TeamStats
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set tNotScoredStreak
     *
     * @param integer $tNotScoredStreak
     *
     * @return TeamStats
     */
    public function setTNotScoredStreak($tNotScoredStreak)
    {
        $this->tNotScoredStreak = $tNotScoredStreak;

        return $this;
    }

    /**
     * Get tNotScoredStreak
     *
     * @return integer
     */
    public function getTNotScoredStreak()
    {
        return $this->tNotScoredStreak;
    }

    /**
     * Set tNotConcededStreak
     *
     * @param integer $tNotConcededStreak
     *
     * @return TeamStats
     */
    public function setTNotConcededStreak($tNotConcededStreak)
    {
        $this->tNotConcededStreak = $tNotConcededStreak;

        return $this;
    }

    /**
     * Get tNotConcededStreak
     *
     * @return integer
     */
    public function getTNotConcededStreak()
    {
        return $this->tNotConcededStreak;
    }

    /**
     * Set tPlayed
     *
     * @param integer $tPlayed
     *
     * @return TeamStats
     */
    public function setTPlayed($tPlayed)
    {
        $this->tPlayed = $tPlayed;

        return $this;
    }

    /**
     * Get tPlayed
     *
     * @return integer
     */
    public function getTPlayed()
    {
        return $this->tPlayed;
    }

    /**
     * Set hWinWin
     *
     * @param integer $hWinWin
     *
     * @return TeamStats
     */
    public function setHWinWin($hWinWin)
    {
        $this->hWinWin = $hWinWin;

        return $this;
    }

    /**
     * Get hWinWin
     *
     * @return integer
     */
    public function getHWinWin()
    {
        return $this->hWinWin;
    }

    /**
     * Set hDrawWin
     *
     * @param integer $hDrawWin
     *
     * @return TeamStats
     */
    public function setHDrawWin($hDrawWin)
    {
        $this->hDrawWin = $hDrawWin;

        return $this;
    }

    /**
     * Get hDrawWin
     *
     * @return integer
     */
    public function getHDrawWin()
    {
        return $this->hDrawWin;
    }

    /**
     * Set hLoseWin
     *
     * @param integer $hLoseWin
     *
     * @return TeamStats
     */
    public function setHLoseWin($hLoseWin)
    {
        $this->hLoseWin = $hLoseWin;

        return $this;
    }

    /**
     * Get hLoseWin
     *
     * @return integer
     */
    public function getHLoseWin()
    {
        return $this->hLoseWin;
    }

    /**
     * Set hWinDraw
     *
     * @param integer $hWinDraw
     *
     * @return TeamStats
     */
    public function setHWinDraw($hWinDraw)
    {
        $this->hWinDraw = $hWinDraw;

        return $this;
    }

    /**
     * Get hWinDraw
     *
     * @return integer
     */
    public function getHWinDraw()
    {
        return $this->hWinDraw;
    }

    /**
     * Set hDrawDraw
     *
     * @param integer $hDrawDraw
     *
     * @return TeamStats
     */
    public function setHDrawDraw($hDrawDraw)
    {
        $this->hDrawDraw = $hDrawDraw;

        return $this;
    }

    /**
     * Get hDrawDraw
     *
     * @return integer
     */
    public function getHDrawDraw()
    {
        return $this->hDrawDraw;
    }

    /**
     * Set hLoseDraw
     *
     * @param integer $hLoseDraw
     *
     * @return TeamStats
     */
    public function setHLoseDraw($hLoseDraw)
    {
        $this->hLoseDraw = $hLoseDraw;

        return $this;
    }

    /**
     * Get hLoseDraw
     *
     * @return integer
     */
    public function getHLoseDraw()
    {
        return $this->hLoseDraw;
    }

    /**
     * Set hWinLose
     *
     * @param integer $hWinLose
     *
     * @return TeamStats
     */
    public function setHWinLose($hWinLose)
    {
        $this->hWinLose = $hWinLose;

        return $this;
    }

    /**
     * Get hWinLose
     *
     * @return integer
     */
    public function getHWinLose()
    {
        return $this->hWinLose;
    }

    /**
     * Set hDrawLose
     *
     * @param integer $hDrawLose
     *
     * @return TeamStats
     */
    public function setHDrawLose($hDrawLose)
    {
        $this->hDrawLose = $hDrawLose;

        return $this;
    }

    /**
     * Get hDrawLose
     *
     * @return integer
     */
    public function getHDrawLose()
    {
        return $this->hDrawLose;
    }

    /**
     * Set hLoseLose
     *
     * @param integer $hLoseLose
     *
     * @return TeamStats
     */
    public function setHLoseLose($hLoseLose)
    {
        $this->hLoseLose = $hLoseLose;

        return $this;
    }

    /**
     * Get hLoseLose
     *
     * @return integer
     */
    public function getHLoseLose()
    {
        return $this->hLoseLose;
    }

    /**
     * Set aWinWin
     *
     * @param integer $aWinWin
     *
     * @return TeamStats
     */
    public function setAWinWin($aWinWin)
    {
        $this->aWinWin = $aWinWin;

        return $this;
    }

    /**
     * Get aWinWin
     *
     * @return integer
     */
    public function getAWinWin()
    {
        return $this->aWinWin;
    }

    /**
     * Set aDrawWin
     *
     * @param integer $aDrawWin
     *
     * @return TeamStats
     */
    public function setADrawWin($aDrawWin)
    {
        $this->aDrawWin = $aDrawWin;

        return $this;
    }

    /**
     * Get aDrawWin
     *
     * @return integer
     */
    public function getADrawWin()
    {
        return $this->aDrawWin;
    }

    /**
     * Set aLoseWin
     *
     * @param integer $aLoseWin
     *
     * @return TeamStats
     */
    public function setALoseWin($aLoseWin)
    {
        $this->aLoseWin = $aLoseWin;

        return $this;
    }

    /**
     * Get aLoseWin
     *
     * @return integer
     */
    public function getALoseWin()
    {
        return $this->aLoseWin;
    }

    /**
     * Set aWinDraw
     *
     * @param integer $aWinDraw
     *
     * @return TeamStats
     */
    public function setAWinDraw($aWinDraw)
    {
        $this->aWinDraw = $aWinDraw;

        return $this;
    }

    /**
     * Get aWinDraw
     *
     * @return integer
     */
    public function getAWinDraw()
    {
        return $this->aWinDraw;
    }

    /**
     * Set aDrawDraw
     *
     * @param integer $aDrawDraw
     *
     * @return TeamStats
     */
    public function setADrawDraw($aDrawDraw)
    {
        $this->aDrawDraw = $aDrawDraw;

        return $this;
    }

    /**
     * Get aDrawDraw
     *
     * @return integer
     */
    public function getADrawDraw()
    {
        return $this->aDrawDraw;
    }

    /**
     * Set aLoseDraw
     *
     * @param integer $aLoseDraw
     *
     * @return TeamStats
     */
    public function setALoseDraw($aLoseDraw)
    {
        $this->aLoseDraw = $aLoseDraw;

        return $this;
    }

    /**
     * Get aLoseDraw
     *
     * @return integer
     */
    public function getALoseDraw()
    {
        return $this->aLoseDraw;
    }

    /**
     * Set aWinLose
     *
     * @param integer $aWinLose
     *
     * @return TeamStats
     */
    public function setAWinLose($aWinLose)
    {
        $this->aWinLose = $aWinLose;

        return $this;
    }

    /**
     * Get aWinLose
     *
     * @return integer
     */
    public function getAWinLose()
    {
        return $this->aWinLose;
    }

    /**
     * Set aDrawLose
     *
     * @param integer $aDrawLose
     *
     * @return TeamStats
     */
    public function setADrawLose($aDrawLose)
    {
        $this->aDrawLose = $aDrawLose;

        return $this;
    }

    /**
     * Get aDrawLose
     *
     * @return integer
     */
    public function getADrawLose()
    {
        return $this->aDrawLose;
    }

    /**
     * Set aLoseLose
     *
     * @param integer $aLoseLose
     *
     * @return TeamStats
     */
    public function setALoseLose($aLoseLose)
    {
        $this->aLoseLose = $aLoseLose;

        return $this;
    }

    /**
     * Get aLoseLose
     *
     * @return integer
     */
    public function getALoseLose()
    {
        return $this->aLoseLose;
    }

    /**
     * Set tWinStreak
     *
     * @param integer $tWinStreak
     *
     * @return TeamStats
     */
    public function setTWinStreak($tWinStreak)
    {
        $this->tWinStreak = $tWinStreak;

        return $this;
    }

    /**
     * Get tWinStreak
     *
     * @return integer
     */
    public function getTWinStreak()
    {
        return $this->tWinStreak;
    }

    /**
     * Set tDrawStreak
     *
     * @param integer $tDrawStreak
     *
     * @return TeamStats
     */
    public function setTDrawStreak($tDrawStreak)
    {
        $this->tDrawStreak = $tDrawStreak;

        return $this;
    }

    /**
     * Get tDrawStreak
     *
     * @return integer
     */
    public function getTDrawStreak()
    {
        return $this->tDrawStreak;
    }

    /**
     * Set tLoseStreak
     *
     * @param integer $tLoseStreak
     *
     * @return TeamStats
     */
    public function setTLoseStreak($tLoseStreak)
    {
        $this->tLoseStreak = $tLoseStreak;

        return $this;
    }

    /**
     * Get tLoseStreak
     *
     * @return integer
     */
    public function getTLoseStreak()
    {
        return $this->tLoseStreak;
    }
}
