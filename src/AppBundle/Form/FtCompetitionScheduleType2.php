<?php

namespace AppBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;

class FtCompetitionScheduleType2 extends AbstractType
{
	/**
	 * {@inheritdoc}
	 */
	public function buildForm(FormBuilderInterface $builder, array $options)
	{
		$builder
			->add('hTeam', ChoiceType::class,
				array(
					'choices'  => array(
						'ΑΡΓΕΝΤΙΝΗ'    => 3541,
						'ΠΕΡΟΥ'        => 4420,
						'ΧΙΛΗ'         => 3433,
						'ΚΑΝΑΔΑΣ'      => 3403,
						'ΜΕΞΙΚΟ'       => 3718,
						'ΙΣΗΜΕΡΙΝΟΣ'   => 3915,
						'ΒΕΝΕΖΟΥΕΛΑ'   => 4432,
						'ΤΖΑΜΑΙΚΑ'     => 4424,
						'ΗΠΑ'          => 3264,
						'ΟΥΡΟΥΓΟΥΑΗ'   => 4429,
						'ΠΑΝΑΜΑΣ'      => 4410,
						'ΒΟΛΙΒΙΑ'      => 3644,
						'ΒΡΑΖΙΛΙΑ'     => 4083,
						'ΚΟΛΟΜΒΙΑ'     => 3309,
						'ΠΑΡΑΓΟΥΑΗ'    => 3453,
						'ΚΟΣΤΑ ΡΙΚΑ'   => 3977,
					),
				)
			)
			->add('aTeam', ChoiceType::class,
				array(
					'choices'  => array(
						'ΑΡΓΕΝΤΙΝΗ'    => 3541,
						'ΠΕΡΟΥ'        => 4420,
						'ΧΙΛΗ'         => 3433,
						'ΚΑΝΑΔΑΣ'      => 3403,
						'ΜΕΞΙΚΟ'       => 3718,
						'ΙΣΗΜΕΡΙΝΟΣ'   => 3915,
						'ΒΕΝΕΖΟΥΕΛΑ'   => 4432,
						'ΤΖΑΜΑΙΚΑ'     => 4424,
						'ΗΠΑ'          => 3264,
						'ΟΥΡΟΥΓΟΥΑΗ'   => 4429,
						'ΠΑΝΑΜΑΣ'      => 4410,
						'ΒΟΛΙΒΙΑ'      => 3644,
						'ΒΡΑΖΙΛΙΑ'     => 4083,
						'ΚΟΛΟΜΒΙΑ'     => 3309,
						'ΠΑΡΑΓΟΥΑΗ'    => 3453,
						'ΚΟΣΤΑ ΡΙΚΑ'   => 3977,
					),
				)
			)
			->add('linkBetarades')
			->add('linkTextBetarades')
			->add('linkBethome')
			->add('linkTextBethome')
			->add('linkPariurix')
			->add('linkTextPariurix')
			->add('tvGr')
			->add('tvRo')
			->add('matchDatetime')
			->add('score')
			->add('isHot')
			->add('phaseId', ChoiceType::class,
				array(
					'choices'  => array(
						'-'                 => 0,
						'Group A'           => 1,
						'Group B'           => 2,
						'Group C'           => 3,
						'Group D'           => 4,
						'Best 8 Phase'      => 8,
						'Semifinals'        => 9,
						'Third Place Match' => 10,
						'Finals'            => 11,
					)
				)
			)
			->add('competitionId', HiddenType::class, [
				'data' => '2',
			]);
	}
	
	/**
	 * {@inheritdoc}
	 */
	public function configureOptions(OptionsResolver $resolver)
	{
		$resolver->setDefaults(array(
			'data_class' => 'AppBundle\Entity\FtCompetitionSchedule',
			'more_data' => null
		));
	}

	/**
	 * {@inheritdoc}
	 */
	public function getBlockPrefix()
	{
		return 'appbundle_ftcompetitionschedule';
	}
}
