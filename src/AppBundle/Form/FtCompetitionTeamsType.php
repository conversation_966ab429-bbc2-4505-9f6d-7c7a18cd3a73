<?php

namespace AppBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;

class FtCompetitionTeamsType extends AbstractType
{
  /**
   * {@inheritdoc}
   */
  public function buildForm(FormBuilderInterface $builder, array $options)
  {
    $builder
        ->add('nameEn')
        ->add('nameGr')
        ->add('nameRo')
        ->add('flag')
        ->add('teamUrlGr')
        ->add('teamId', HiddenType::class, [
            'required' => false,
        ])

        ->add('teamSearch', TextType::class, [
            'mapped'      => false,
            'required'    => false,
            'attr'        => [
                'class'       => 'team-search form-control',
                'placeholder' => 'Search team en to get the id',
                'style'       => 'width:100%;',
            ],
        ])
        ->add('leagueSeasonId', ChoiceType::class, [
            'choices'     => $options['season_choices'],
            'placeholder' => 'Select league & season',
            'required'    => false,
        ])
        ->add('pts')
        ->add('gp')
        ->add('goals')
        ->add('teamIndex');
  }

  public function configureOptions(OptionsResolver $resolver)
  {
    $resolver->setDefaults([
        'data_class'     => 'AppBundle\Entity\FtCompetitionTeams',
        'season_choices' => []
    ]);

  }

	/**
	 * {@inheritdoc}
	 */
	public function getBlockPrefix()
	{
		return 'appbundle_ftcompetitionteams';
	}
}
