<?php

namespace AppBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\FormEvent;

class SportType extends AbstractType
{

  /**
   * @param FormBuilderInterface $builder
   * @param array $options
   */
  public function buildForm(FormBuilderInterface $builder, array $options)
  {
    $builder
      ->add('parent', null, array(
        'attr' => array(
          'placeholder' => 'Parent',
        )
      ))
      ->add('imageName', null, array(
        'attr' => array(
          'placeholder' => 'Image Name',
        )
      ))
      ->add('domainSportSlugs', CollectionType::class, array(
        'label'         => 'Domain Sport Slug',
        'entry_type'    => DomainSportSlugType::class,
        'allow_add'     => true,
        'allow_delete'  => true,
        'attr'          => array(
          'class' => 'domain-sport-slugs',
          'name'  => 'Domain Sport Slugs',
        )
      ))
      // ->add('slug', null, array(
      //   'attr' => array(
      //     'placeholder' => 'Slug',
      //   )
      // ))
      ->add('translations', CollectionType::class, array(
          'label'         => 'Μεταφράσεις',
          'entry_type'    => SportTranslationType::class,
          'allow_add'     => true,
          'allow_delete'  => true,
          'attr'          => array(
            'class' => 'translations',
            'name'  => 'Μεταφράσεις',
          ),
      ))
      ;

    if ($options['is_competition']) {
      $builder
        ->add('dummyCompetition', CheckBoxType::class, array('label' => 'Δεν αποτελεί Competition', 'mapped' => false))
        // ->add('hideInStats', CheckBoxType::class, array('label' => 'Να μην χρησιμοποιείται στα στατιστικά', 'mapped' => true, 'required' => false, 'empty_data' => 0))
        ->add('hideInStats')
        ->add('ftLeagueSport', FtLeagueSportType::class, array('required' => false)
        )
        ->add('domainSports', CollectionType::class, array(
            'label'         => 'Domain Sport',
            'entry_type'    => DomainSportType::class,
            'allow_add'     => true,
            'allow_delete'  => true,
            'attr'          => array(
              'class' => 'domain-sports',
              'name'  => 'Domain Sports',
            )
        ))
        // ->add('singleStandings', CheckBoxType::class, array('label' => 'Δείξε μόνο μια βαθμολογία τη φορά', 'mapped' => true, 'required' => false, 'empty_data' => 0))
        ->add('singleStandings')
        ->add('standingsPriority', null, array(
          'attr' => array(
            'placeholder' => 'Priority in Standings'
          )
        ))
        ;
    }
  }

  /**
   * @param OptionsResolver $resolver
   */
  public function configureOptions(OptionsResolver $resolver)
  {
      $resolver->setDefaults(array(
        'data_class' => 'AppBundle\Entity\Sport',
        'is_competition' => false,
        'singleStandings' => false,
      ));
  }
}
