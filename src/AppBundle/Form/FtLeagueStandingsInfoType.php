<?php

namespace AppBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

class FtLeagueStandingsInfoType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('sportId')
            ->add('season')
            ->add('optionId', ChoiceType::class, array(
              'choices'  => array(
                'CL - Champions League'                         => 1,
                'CLQ - Champions League Qualifiers'             => 2,
                'EU - Europa League'                            => 3,
                'EUQ - Europa League Qualifiers'                => 4,
                'PEU - Possible Europa League'                  => 5,
                'REL - Relegation'                              => 6,
                'RELPO - Relegation Playoff'                    => 7,
                'PRO - Promotion'                               => 8,
                'PROPO - Promotion Playoff'                     => 9,
                'CHR - Championship Round'                      => 10,
                'PO - Playoff'                                  => 11,
                'RLR - Relegation Round'                        => 12,
                'AFC - Asian Champions League'                  => 13,
                'NXTR - Next Round'                             => 14,
                'LIBE - Copa Libertadores'                      => 15,
                'SUDA - Copa Sudamericana'                      => 16,
                'EUPO - Europa League Playoff'                  => 17,
                'PNXTR - Possible Next Round'                   => 18,
                'PREL - Possible Relegation'                    => 19,
                'ECL - Europa Conference League'                => 22,
                'ECLQ - Europa Conference League Qualifiers'    => 23,
                'COPALQ - Copa Libertadores Qualifiers'         => 24,
                'CP - Confederation Cup'                        => 25,
                'ACCC - Arab Club Champions Cup'                => 26,
                'PECL - Possible Europa Conference League'      => 27,
                'AFCCLQ - AFC Champions League Qualifiers'      => 28,
                'AFC Asian Cup Qualifiers'                      => 29,
              ),
            ))
            ->add('value')
        ;
    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => 'AppBundle\Entity\FtLeagueStandingsInfo'
        ));
    }
}
