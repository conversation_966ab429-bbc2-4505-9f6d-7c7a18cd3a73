<?php

namespace AppBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;

class SportTranslationType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
          ->add('type', ChoiceType::class, array(
            'label'   => 'Τύπος',
            'choices' => array(
              'για Όνομα'       => 'normal',
              'για Βαθμολογία'  => 'standings',
              'για Προγνωστικά' => 'tips',
              'για Αναλύσεις'   => 'previews',
            )
          ))
          ->add('locale', ChoiceType::class, array(
            'label'   => 'Γλώσσα',
            'choices'  => array(
              'Ελληνικά'  => 'el',
              'Αγγλικά'   => 'en',
              'Ρουμάνικα' => 'ro',
            )
          ))
          ->add('title', null, array(
            'label' => 'Τίτλος'
          ))
          ->add('description', null, array(
            'label' => 'Περιγραφή'
          ))
        ;
    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
          'data_class' => 'AppBundle\Entity\SportTranslation'
        ));
    }

}
