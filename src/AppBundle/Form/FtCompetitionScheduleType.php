<?php

namespace AppBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use AppBundle\Twig\CompetitionExtension;

class FtCompetitionScheduleType extends AbstractType
{
    private $entityManager;
    private $competitionExtension;

    public function __construct(EntityManagerInterface $entityManager, CompetitionExtension $competitionExtension)
    {
        $this->entityManager = $entityManager;
        $this->competitionExtension = $competitionExtension ?: new CompetitionExtension();
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        // Get teams from database
        $teams = $this->entityManager->getRepository('AppBundle:FtCompetitionTeams')->findAll();
        $teamChoices = [];

        foreach ($teams as $team) {
            $teamChoices[$team->getNameGr()] = $team->getId();
        }

        $phaseMap = $this->competitionExtension->getPhaseMap();
        $phaseChoices = [];
        foreach ($phaseMap as $id => $phaseData) {
            $phaseChoices[$phaseData['name']] = $id;
        }

        $builder
            ->add('hTeam', ChoiceType::class, [
                'choices' => $teamChoices
            ])
            ->add('aTeam', ChoiceType::class, [
                'choices' => $teamChoices
            ])
            ->add('matchType', ChoiceType::class, [
                'choices' => \AppBundle\Entity\FtCompetitionSchedule::getMatchTypes(),
                'placeholder' => 'Select Match Type',
                'required' => false
            ])
            ->add('phaseId', ChoiceType::class, [
                'choices' => $phaseChoices,
                'required' => false,
                'placeholder' => 'Select Phase',
                'empty_data' => null
            ])
            ->add('linkBetarades')
            ->add('linkTextBetarades')
            ->add('linkBethome')
            ->add('linkTextBethome')
            ->add('linkPariurix')
            ->add('linkTextPariurix')
            ->add('tvGr')
            ->add('tvRo')
            ->add('matchDatetime')
            ->add('score')
            ->add('homeOdds', NumberType::class, [
                'required' => false,
                'scale' => 2
            ])
            ->add('awayOdds', NumberType::class, [
                'required' => false,
                'scale' => 2
            ])
            ->add('affiliateLink')
            ->add('isHot');
    }

	/**
	 * {@inheritdoc}
	 */
	public function configureOptions(OptionsResolver $resolver)
	{
		$resolver->setDefaults(array(
			'data_class' => 'AppBundle\Entity\FtCompetitionSchedule',
			'more_data' => null
		));
	}

	/**
	 * {@inheritdoc}
	 */
	public function getBlockPrefix()
	{
		return 'appbundle_ftcompetitionschedule';
	}
}
