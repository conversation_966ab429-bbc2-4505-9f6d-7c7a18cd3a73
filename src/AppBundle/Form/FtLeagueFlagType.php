<?php

namespace AppBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;

class FtLeagueFlagType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('leagueId')
            ->add('code')
            ->add('competitionNumberEn')
            ->add('competitionNumberGr')
            ->add('imageName')
            ->add('sport', EntityType::class, array(
              'class' => 'AppBundle:Sport',
              'choice_label' => 'id',
              'placeholder' => 'Επιλέξτε Sport',
              'required' => false,
              'empty_data'  => null,
              'mapped' => true
            ))
        ;
    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => 'AppBundle\Entity\FtLeagueFlag'
        ));
    }
}
