<?php

namespace AppBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

class DomainType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('name')
            ->add('language', EntityType::class, array(
              'class' 				=> 'AppBundle:Language',
              'choice_label' 	=> 'name',
              'placeholder' 	=> 'Επιλέξτε Γλώσσα',
              'required' 			=> true,
              'empty_data'  	=> null,
              'mapped' 				=> true
            ))
            ->add('hostPort')
            ->add('dbHost')
            ->add('dbNodeHost')
            ->add('dbPort')
            ->add('dbName')
            ->add('dbPrefix')
            ->add('dbUsername')
            ->add('dbPassword')
            ->add('postType')
            ->add('categoryId')
            ->add('isActive')
            ->add('syncOnlySports')
            ->add('standingsPrefix')
            ->add('createPosts', ChoiceType::class, array(
                'label'   => 'Create Posts',
                'choices' => array(
                'ΟΧΙ'     => '0',
                'ΝΑΙ' 	  => '1',
                )
            ))
            ->add('protocol', ChoiceType::class, array(
                'label'   => 'Protocol',
                'choices' => array(
                'http'    => '0',
                'https'	  => '1',
                )
            ))
            ->add('nodePortId')
            ->add('isStartAbs')
            ->add('createdAt', DateTimeType::class, array('date_widget' => 'single_text', 'data' => new \DateTime()))
            ->add('updatedAt', DateTimeType::class, array('date_widget' => 'single_text', 'data' => new \DateTime()))
        ;
    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => 'AppBundle\Entity\Domain'
        ));
    }
}
