<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class PreviewsCommand extends ContainerAwareCommand
{
  /*
   * wbs:post:create (creates previews for all sports)
   * wbs:post:create --sport=4 (creates previews for sport 4 only)
   * wbs:post:create --sport=4 --sport=6 (creates previews for sports 4 & 6)
   * wbs:post:create --domain=3 (domain id for which to create previews)
   */
  protected function configure()
  {
    $this
      ->setName('wbs:post:create')
      ->setDescription('Create previews with arguments')
      ->setHelp('Create previews for specific sports')
      ->addOption(
        'domain',
        'd',
        InputOption::VALUE_REQUIRED,
        'Domain for which to create previews'
      )
      ->addOption(
        'sport',
        's',
        InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY,
        'Sports to create previews'
        )
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $domain     = $input->getOption('domain');
    $sports     = $input->getOption('sport');
    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.previews_controller');
    $execute    = $myService->generatePostAction($sports, $domain);

    $output->writeln($execute->getContent());
  }
}
