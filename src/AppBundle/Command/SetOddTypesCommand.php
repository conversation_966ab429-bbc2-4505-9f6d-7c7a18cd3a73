<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class SetOddTypesCommand extends ContainerAwareCommand
{
  /*
   * wbs:oddtypes:setcache
   * set memcached key for OddTypes for specific domain
   * php bin/console wbs:oddtypes:setcache -d 2
   */
  protected function configure()
  {
    $this
      ->setName('wbs:oddtypes:setcache')
      ->setDescription('')
      ->setHelp('')
      ->addOption(
        'domains',
        'd',
        InputOption::VALUE_REQUIRED,
        'Domain for which to set the key',
        null
      );
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
		$domain = $input->getOption('domains');

    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.oddtype_controller');
    $execute    = $myService->setOddTypesCacheAction($domain);

    $output->writeln($execute->getContent());
  }
}
