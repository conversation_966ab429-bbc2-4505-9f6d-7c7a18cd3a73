<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class FtFixtureGetAllForWeatherConditionsCommand extends ContainerAwareCommand
{
	/*
	* wbs:all-matches:get
	* Should run once per day (03:10) to add list of matches in table
	*/
	protected function configure()
	{
		$this
			->setName('wbs:all-matches:get')
			->setDescription('Get all todays matches per domain into table')
			->setHelp('Get all todays matches per domain into table')
		;
	}

	protected function execute(InputInterface $input, OutputInterface $output)
	{
		$myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.ftfixture_controller');
		$execute    = $myService->getAllMatchesAction();

		$output->writeln($execute->getContent());
	}
}
