<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class LeaguesForStandingsCommand extends ContainerAwareCommand
{
  protected function configure()
  {
    $this
      ->setName('wbs:standings:todays')
      ->setDescription('Initialize todays leagues and seasons for standings')
      ->setHelp('Initialize todays leagues and seasons for standings')
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $myService = $this->getApplication()->getKernel()->getContainer()->get('wbs.leaguesforstandings_controller');
    $execute = $myService->getTodaysLeaguesForStandingsAction();

    $output->writeln('initialized todays leagues and seasons for standings');
  }

}
