<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class MigrationCommand extends ContainerAwareCommand
{
  /*
   * wbs:previews:migrate (transfers previews post metas to bet_wbs_previews)
   */
  protected function configure()
  {
    $this
      ->setName('wbs:previews:migrate')
      ->setDescription('Transfer previews post metas to bet_wbs_previews')
      ->setHelp('Transfer previews post metas to bet_wbs_previews')
      ->addArgument(
        'offset',
        InputArgument::REQUIRED,
        'Offset'
      )
      ->addArgument(
        'limit',
        InputArgument::OPTIONAL,
        'Limit'
      )
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $offset     = $input->getArgument('offset');
    $limit      = $input->getArgument('limit') ?: 2000;
    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.migration_controller');
    $execute    = $myService->migrateAction($offset, $limit);

    $output->writeln($execute->getContent());
  }
}
