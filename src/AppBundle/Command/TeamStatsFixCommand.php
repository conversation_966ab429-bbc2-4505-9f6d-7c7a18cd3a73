<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class TeamStatsFixCommand extends ContainerAwareCommand
{
  /*
   * wbs:team:statsfix
   * required sport id parameter, i.e. wbs:team:statsfix -s 304
   */
  protected function configure()
  {
    $this
      ->setName('wbs:team:statsfix')
      ->setDescription('')
      ->setHelp('')
      ->addOption(
        'sport',
        's',
        InputOption::VALUE_REQUIRED,
        'SportId to fix stats',
        null
      )
      ->addOption(
        'domain',
        'd',
        InputOption::VALUE_REQUIRED,
        'Domain to clear cache',
        null
      )
      ->addOption(
        'season',
        'l',
        InputOption::VALUE_REQUIRED,
        'Season of the sport Id',
        null
      );
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $sportId = !empty($input->getOption('sport')) ? $input->getOption('sport') : null;
    $season  = !empty($input->getOption('season')) ? $input->getOption('season') : null;
    $domainId = !empty($input->getOption('domain')) ? $input->getOption('domain') : null;

    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.teamstats_controller');
    $execute    = $myService->fixTeamStatsAction($sportId, $season, $domainId);

    $output->writeln($execute->getContent());
  }
}
