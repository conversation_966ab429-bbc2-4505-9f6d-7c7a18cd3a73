<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class OddsCreateCommand extends ContainerAwareCommand
{
  /*
   * wbs:odds:create (copy ft_fixture_odds to domains bet_odds)
   */
  protected function configure()
  {
    $this
      ->setName('wbs:odds:create')
      ->setDescription('Copy ftFixtureOdds to domains')
      ->setHelp('Copy ftFixtureOdds to domains')
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.ftfixtureodds_controller');
    $execute    = $myService->createAction();

    $output->writeln($execute->getContent());
  }
}
