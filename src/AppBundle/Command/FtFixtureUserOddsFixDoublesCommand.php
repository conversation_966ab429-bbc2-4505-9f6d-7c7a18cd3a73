<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class FtFixtureUserOddsFixDoublesCommand extends ContainerAwareCommand
{
  /*
   * wbs:userodds:fix
   * Fix Daily User Odds
   * php bin/console wbs:userodds:fix
   */
  protected function configure()
  {
    $this
      ->setName('wbs:userodds:fix-doubles')
      ->setDescription('')
      ->setHelp('')
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.ftfixtureuserodds_controller');
    $execute    = $myService->fixFtFixtureUserOddsDoublesAction();

    $output->writeln($execute->getContent());
  }
}
