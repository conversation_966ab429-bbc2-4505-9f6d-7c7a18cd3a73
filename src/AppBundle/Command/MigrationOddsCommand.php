<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class MigrationOddsCommand extends ContainerAwareCommand
{
  /*
   * wbs:odds:migrate (updates preview_id from post_id in DOMAIN bet_odds)
   */
  protected function configure()
  {
    $this
      ->setName('wbs:odds:migrate')
      ->setDescription('Update preview_id from post_id')
      ->setHelp('Update preview_id from post_id')
      ->addArgument(
        'offset',
        InputArgument::REQUIRED,
        'Offset'
      )
      ->addArgument(
        'limit',
        InputArgument::OPTIONAL,
        'Limit'
      )
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $offset     = $input->getArgument('offset');
    $limit      = $input->getArgument('limit') ?: 5000;
    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.migration_controller');
    $execute    = $myService->migrateOddsAction($offset, $limit);

    $output->writeln($execute->getContent());
  }
}
