<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class FtFixtureUserOddsFixCommand extends ContainerAwareCommand
{
  /*
   * wbs:userodds:fix
   * Fix Daily User Odds
   * php bin/console wbs:userodds:fix
   */
  protected function configure()
  {
    $this
      ->setName('wbs:userodds:fix')
      ->setDescription('')
      ->setHelp('')
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.ftfixtureuserodds_controller');
    $execute    = $myService->fixFtFixtureUserOddsAction();

    $output->writeln($execute->getContent());
  }
}
