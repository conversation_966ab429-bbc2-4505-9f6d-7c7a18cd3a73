<?php
// src/AppBundle/Command/FeedParserCommand.php
namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class FtLeagueSeasonSportCommand extends ContainerAwareCommand
{
  protected function configure()
  {
    $this
      ->setName('wbs:generate:season')
      ->setDescription('Generate Seasons with arguments')
      ->setHelp('Generate FtLeagueSeasonSport in our database for each ftLeagueSeason')
      ->addOption(
        'debug',
        'd',
        InputOption::VALUE_REQUIRED,
        'Insert elements in DB but also show DEBUG messages',
        null
      )
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $debug = $input->getOption('debug');

    $myService = $this->getApplication()->getKernel()->getContainer()->get('wbs.ftleagueseasonsport_controller');
    $execute = $myService->generateSeason();

    if ($debug) {
      $output->writeln($execute->getContent());
    }
    $output->writeln('Finished generating sport seasons');
  }

}
