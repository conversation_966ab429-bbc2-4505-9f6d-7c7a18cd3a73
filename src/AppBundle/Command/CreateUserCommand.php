<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputArgument;

class CreateUserCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this
            ->setName('app:create-admin')
            ->setDescription('Creates an admin user')
            ->addArgument('username', InputArgument::REQUIRED, 'The username')
            ->addArgument('password', InputArgument::REQUIRED, 'The password');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $em = $this->getContainer()->get('doctrine')->getManager();
        $userManager = $this->getContainer()->get('fos_user.user_manager');

        $user = $userManager->createUser();
        $user->setUsername($input->getArgument('username'));
        $user->setEmail($input->getArgument('username') . '@example.com');
        $user->setPlainPassword($input->getArgument('password'));
        $user->setEnabled(true);
        $user->setSuperAdmin(true);
        $user->setRoles(['ROLE_ADMIN']);

        $userManager->updateUser($user);

        $output->writeln(sprintf('Created user <comment>%s</comment>', $input->getArgument('username')));
        
        return 0;
    }
}