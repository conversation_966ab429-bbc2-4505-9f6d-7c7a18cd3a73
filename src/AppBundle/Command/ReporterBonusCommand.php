<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ReporterBonusCommand extends ContainerAwareCommand
{
  /*
   * wbs:reporterbonus:set
   * Set Reporter monthly bonuses/points
   * php bin/console wbs:reporterbonus:set
   */
  protected function configure()
  {
    $this
      ->setName('wbs:reporterbonus:set')
      ->setDescription('')
      ->setHelp('')
      ->addOption(
        'month',
        'm',
        InputOption::VALUE_REQUIRED,
        'Month for which to calculate reporter bonus',
        null
      )
      ->addOption(
        'year',
        'y',
        InputOption::VALUE_REQUIRED,
        'Year for which to calculate reporter bonus',
        null
      )
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $month = $input->getOption('month');
    $year = $input->getOption('year');

    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.ftfixtureuserodds_controller');
    $execute    = $myService->setReporterBonusAction($month, $year);

    $output->writeln($execute->getContent());
  }
}
