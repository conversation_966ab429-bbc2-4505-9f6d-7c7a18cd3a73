<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class FtFixtureUserOddsCommand extends ContainerAwareCommand
{
  /*
   * wbs:userodds:get
   * get memcached Daily User Odds for specific domain
   * php bin/console wbs:userodds:get -d 2
   */
  protected function configure()
  {
    $this
      ->setName('wbs:userodds:get')
      ->setDescription('')
      ->setHelp('')
      ->addOption(
        'domains',
        'd',
        InputOption::VALUE_REQUIRED,
        'Domain for which to set the key',
        null
      );
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
		$domain = $input->getOption('domains');

    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.ftfixtureuserodds_controller');
    $execute    = $myService->getFtFixtureUserOddsAction($domain);

    $output->writeln($execute->getContent());
  }
}
