<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class TeamStatsCommand extends ContainerAwareCommand
{
  /*
   * wbs:team:statsinit
   * optional sport id parameter, i.e. wbs:team:statsinit -s 531 -s 304 -s 530
   * in cases where sport id is Apertura/Clausura (meaning, there is no ft_league_season_sport for parent competition A' Bolivias) it's better to pass additional sport_id as parameter
   */
  protected function configure()
  {
    $this
      ->setName('wbs:team:statsinit')
      ->setDescription('')
      ->setHelp('')
      ->addOption(
        'sports',
        's',
        InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY,
        'SportIds to init stats',
        null
      );
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $sportIds = array();
    $sportId  = $input->getOption('sports');
    if (0 < count($sportId)) {
      $sportIds[] = explode(',', $sportId[0]);
    }

    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.teamstats_controller');
    $execute    = $myService->initTeamStatsAction(isset($sportIds[0]) ? $sportIds[0] : null);

    $output->writeln($execute->getContent());
  }
}
