<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class FtFixtureWeatherConditionsCommand extends ContainerAwareCommand
{
	/*
     * wbs:weather-conditions:set
	 * Should run every thirty minutes send weather conditions for domain matches
	 */
	protected function configure()
	{
		$this
			->setName('wbs:weather-conditions:set')
			->setDescription('Set weather conditions per domain match')
			->setHelp('Set weather conditions per domain match');
	}

	protected function execute(InputInterface $input, OutputInterface $output)
	{
		$myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.ftfixture_controller');
		$execute    = $myService->getSetFtFixtureWeatherConditionsAction();

		$output->writeln($execute->getContent());
	}
}
