<?php
// src/AppBundle/Command/FeedParserCommand.php
namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class FeedParserCommand extends ContainerAwareCommand
{
  /*
   * wbs:feed:parse NAME (i.e. wbs:feed:parse standings)
   * wbs:feed:parse NAME --options=VALUE (i.e. wbs:feed:parse standings --options=83 --options=57 OR wbs:feed:parse standings -o 83 -o 57)
   * use --options=all to update all standings and not only today matches
   */
  protected function configure()
  {
    $this
      ->setName('wbs:feed:parse')
      ->setDescription('Parse feed with arguments')
      ->setHelp('Parse specific feed in our database')
      ->addArgument(
        'name',
        InputArgument::REQUIRED,
        'The name of the feed'
      )
      ->addOption(
        'options',
        'o',
        InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY,
        'General options used per feed name',
        null
      )
      ->addOption(
        'silent',
        's',
        InputOption::VALUE_NONE,
        'Do not insert values in database, Debug reasons only',
        null
      )
      ->addOption(
        'sport',
        't',
        InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY,
        'Sport Ids array',
        null
      )
      ->addOption(
        'days',
        'd',
        InputOption::VALUE_REQUIRED,
        'How many days to get fixtures for',
        null
      );
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $argument   = $input->getArgument('name');
    $options    = $input->getOption('options');
    $silent     = $input->getOption('silent');
    $sportIds   = $input->getOption('sport');
    $days       = $input->getOption('days');
    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.feedparser_controller');
    $execute    = $myService->parseFeedAction($argument, $options, $silent, $sportIds, $days);

    // $output->writeln($execute);
  }
}
