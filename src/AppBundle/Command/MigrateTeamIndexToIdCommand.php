<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class MigrateTeamIndexToIdCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this
            ->setName('app:migrate:team-index-to-id')
            ->setDescription('Migrates team index references to team ID references');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $em = $this->getContainer()->get('doctrine')->getManager();
        
        // Get all schedules
        $schedules = $em->getRepository('AppBundle:FtCompetitionSchedule')->findAll();
        
        // Instead of using getTeamIndex(), we'll query the database directly
        $conn = $em->getConnection();
        $sql = "SELECT id, team_index FROM ft_competition_teams WHERE team_index IS NOT NULL";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $teamIndexMap = $stmt->fetchAll(\PDO::FETCH_KEY_PAIR); // team_index => id
        
        $count = 0;
        foreach ($schedules as $schedule) {
            $homeTeamIndex = $schedule->getHTeam();
            $awayTeamIndex = $schedule->getATeam();
            
            // If the current value is a team index (integer)
            if (is_numeric($homeTeamIndex) && isset($teamIndexMap[$homeTeamIndex])) {
                $homeTeam = $em->getRepository('AppBundle:FtCompetitionTeams')->find($teamIndexMap[$homeTeamIndex]);
                if ($homeTeam) {
                    $schedule->setHTeam($homeTeam);
                    $count++;
                }
            }
            
            if (is_numeric($awayTeamIndex) && isset($teamIndexMap[$awayTeamIndex])) {
                $awayTeam = $em->getRepository('AppBundle:FtCompetitionTeams')->find($teamIndexMap[$awayTeamIndex]);
                if ($awayTeam) {
                    $schedule->setATeam($awayTeam);
                    $count++;
                }
            }
        }
        
        $em->flush();
        $output->writeln("Updated $count team references.");
        
        return 0;
    }
}
