<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class PreviewsFinishedCommand extends ContainerAwareCommand
{
  /*
   * wbs:post:finished (updates domain previews with finished matches)
   */
  protected function configure()
  {
    $this
      ->setName('wbs:post:finished')
      ->setDescription('Update domain previews with finished matches')
      ->setHelp('Update domain previews with finished matches')
      // ->addOption(
      //   'sport',
      //   null,
      //   InputOption::VALUE_OPTIONAL | InputOption::VALUE_IS_ARRAY,
      //   'Sports to create previews'
      // )
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    // $options    = $input->getOption('sport');
    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.previews_controller');
    // $execute    = $myService->updateFinishedPostAction($options);
    $execute    = $myService->updateFinishedPostAction();

    $output->writeln($execute->getContent());
  }
}
