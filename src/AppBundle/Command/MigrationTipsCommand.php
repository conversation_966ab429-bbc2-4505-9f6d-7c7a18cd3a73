<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class MigrationTipsCommand extends ContainerAwareCommand
{
  /*
   * wbs:previews:migrate (transfers previews post metas to bet_wbs_previews)
   */
  protected function configure()
  {
    $this
      ->setName('wbs:tips:migrate')
      ->setDescription('Transfer tips post metas to bet_wbs_tips')
      ->setHelp('Transfer tips post metas to bet_wbs_tips')
      ->addArgument(
        'offset',
        InputArgument::REQUIRED,
        'Offset'
      )
      ->addArgument(
        'limit',
        InputArgument::OPTIONAL,
        'Limit'
      )
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $offset     = $input->getArgument('offset');
    $limit      = $input->getArgument('limit') ?: 5000;
    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.migration_controller');
    $execute    = $myService->migrateTipsAction($offset, $limit);

    $output->writeln($execute->getContent());
  }
}
