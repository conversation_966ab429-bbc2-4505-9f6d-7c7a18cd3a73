<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class SetOddTypesMostUsedCommand extends ContainerAwareCommand
{
  /*
   * wbs:oddtypes:setmostused
   */
  protected function configure()
  {
    $this
      ->setName('wbs:oddtypes:setmostused')
      ->setDescription('')
      ->setHelp('')
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.oddtype_controller');
    $execute    = $myService->setOddTypesMostUsedAction();

    $output->writeln($execute->getContent());
  }
}
