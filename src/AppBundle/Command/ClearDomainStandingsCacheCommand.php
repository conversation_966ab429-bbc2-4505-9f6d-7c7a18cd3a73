<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ClearDomainStandingsCacheCommand extends ContainerAwareCommand
{
  /*
   * wbs:standings:clearcache
   * clear cache for 2 sports
   * php bin/console wbs:standings:clearcache -d 2 -s 304,303
   *
   * clear cache for 1 sport
   * php bin/console wbs:standings:clearcache -d 2 -s 304
   */
  protected function configure()
  {
    $this
      ->setName('wbs:standings:clearcache')
      ->setDescription('')
      ->setHelp('')
      ->addOption(
        'sports',
        's',
        InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY,
        'SportIds to clear standings cache',
        null
      )
      ->addOption(
          'domains',
          'd',
          InputOption::VALUE_REQUIRED,
          'Domain for which to clear standings cache',
          null
      );
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
    $sportIds = array();
    $sportId  = $input->getOption('sports');
    if (0 < count($sportId)) {
      $sportIds[] = explode(',', $sportId[0]);
    }

		$domain = $input->getOption('domains');

    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.previews_controller');
    $execute    = $myService->clearDomainStandingsCacheAction(isset($sportIds[0]) ? $sportIds[0] : null, $domain);

    $output->writeln($execute->getContent());
  }
}
