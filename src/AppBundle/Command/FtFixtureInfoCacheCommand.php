<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class FtFixtureInfoCacheCommand extends ContainerAwareCommand
{
  /*
   * wbs:fixtureinfo:set
   * set memcached Fixture Info for specific domain
   * php bin/console wbs:fixtureinfo:set -d 2
   */
  protected function configure()
  {
    $this
      ->setName('wbs:fixtureinfo:set')
      ->setDescription('')
      ->setHelp('')
      ->addOption(
        'domains',
        'd',
        InputOption::VALUE_REQUIRED,
        'Domain for which to set the key',
        null
      );
    ;
  }

  protected function execute(InputInterface $input, OutputInterface $output)
  {
		$domain = $input->getOption('domains');

    $myService  = $this->getApplication()->getKernel()->getContainer()->get('wbs.ftfixture_controller');
    $execute    = $myService->setFixtureInfoCacheAction($domain);

    $output->writeln($execute->getContent());
  }
}
