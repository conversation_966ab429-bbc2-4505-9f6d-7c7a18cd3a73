(function ($) {
    var $body = $('body'),
        currentCities = {},
        countryId = 0,
        countries = {},
        countryUrl = Routing.generate('countries_get'),
        cityName = $('#search-city-name'),
        cityNameSearch = $('#search-city-name'),
        cityNameSearchLbl = $('#lbl__search-city-name');

    $body.addClass('is-loading');

    $.ajax({
        type: 'GET',
        url: countryUrl,
        dataType: 'json',
        success: function(data) {
            countries = data
        },
        error: function(xhr, ajaxOptions, thrownError) {
            $body.removeClass('is-loading');
            alert(xhr.responseText);
        },
        complete: function(req, res) {
            $body.removeClass('is-loading');
        }
    });

    $(document).on('click', '.country-name', function(e) {
        e.preventDefault();
        $body.addClass('is-loading');

        countryId = $(this).data("id");
        countryName = $(this).data("name");

        $('.selected-country-name').html(countryName);
        $('.country-name').hide();
        $('.cities').show(5);

        var citiesUrl = Routing.generate('cities_get', {'countryId': countryId});

        $.ajax({
            type: 'GET',
            url: citiesUrl,
            dataType: 'json',
            success: function(data) {
                currentCities = data
            },
            error: function(xhr, ajaxOptions, thrownError) {
                $body.removeClass('is-loading');
                currentCities = {};
                alert(xhr.responseText);
            },
            complete: function(req, res) {
                $body.removeClass('is-loading');
            }
        });
    });

    $(document).on('keyup', '.country-name', function(e) {
    });

    $('#countries-result.country-name').click(function() {
        countryId = $(this).attr("id");

        var countryDiv = $('#country-' + countryId),
            citiesUrl = $(this).data('city-url');

        $body.addClass('is-loading');

        $.ajax({
            type: 'GET',
            url: citiesUrl,
            dataType: 'json',
            success: function(data) {
                currentCities = data
            },
            error: function(xhr, ajaxOptions, thrownError) {
                $body.removeClass('is-loading');
                currentCities = {};
                alert(xhr.responseText);
            },
            complete: function(req, res) {
                $body.removeClass('is-loading');
            }
        });
    });

    $("#search-city-name").on("keyup", function() {
        var g = $(this).val().toLowerCase(),
            html = '';

        var mpeee = '';
        var citySearchResults = jQuery.grep(currentCities, function(value) {
            $("#cities-result").html('');
            return (value['name'].toLowerCase().indexOf(g) >= 0)
        })

        citySearchResults.forEach(function(value) {
            html += '<div>' + value.name + ' <span>' + value.weatherApiId + '</span></div>';
        })

        $("#cities-result").append(html);
    });

    $("#search-country-name").on("keyup", function() {
        var g = $(this).val().toLowerCase(),
            html = '';
        
        var countrySearchResults = jQuery.grep(countries, function(value) {
            $("#countries-result").html('');
            return (value['nameGr'].toLowerCase().indexOf(g) >= 0)
        })

        countrySearchResults.forEach(function(value) {
            html += '<div id="country-' + value.id + '" class="country-name" data-id="' + value.id + '" data-name="' + value.nameGr + '">' + value.nameGr + '<div id="cities-result-' + value.id + '"></div></div>'
        })

        $("#countries-result").append(html);
    });
})(jQuery);