<?php

namespace AppBundle\Repository;

/**
 * FtLeagueSeasonScorerRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtLeagueSeasonScorerRepository extends \Doctrine\ORM\EntityRepository
{
  public function getLeagueScorers($sportId, $season, $lan) {
    $em = $this->getEntityManager();
    $additionalStandings    = false;
    $additionalAndParentIds = null;
    $singleAdditional       = false;
    $result = null;

    if ($sportId) {
      $sport = $em->getRepository('AppBundle:Sport')->findOneById($sportId);

      if ($sport && $sport->getHasAdditionals()) {
        $additionalStandings = true;
      }

      $singleAdditional = ($sport && $sport->getHasAdditionals() && $sport->getSingleStandings());
    }

    if ($additionalStandings) {
      $additionalAndParentIds = $em->getRepository('AppBundle:Sport')->getAdditionalsWithStandings($sportId);
      if (isset($additionalAndParentIds) && $additionalAndParentIds[0]) {
        $andWhereClause = " lss.sport_id IN ($additionalAndParentIds[0])  AND :sportId = :sportId ";
      }
      else {
          $andWhereClause = " lss.sport_id = :sportId ";
      }
    }
    else {
      $andWhereClause = " lss.sport_id = :sportId ";
    }

    $sql = "
    SELECT lss.sport_id, CASE WHEN (:lan = 'GR') THEN l.name_gr ELSE l.name_en END as leagueName, lss.team_id teamId, lss.player_name playerName, (SELECT name_en FROM ft_team t WHERE t.team_id = lss.team_id) nameEn, lss.goals, lss.penalties, lss.scorer_first scorerFirst
    FROM
      ft_league_season_scorer lss
        INNER JOIN sport s ON s.id = lss.sport_id
        LEFT JOIN ft_league l ON l.l_id = lss.l_id
    WHERE 
        $andWhereClause
    AND lss.season = :season 
    ORDER BY s.standings_priority, lss.l_id, lss.goals DESC LIMIT 10
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":season", $season);
    $stmt->bindValue(":lan", $lan);
    $stmt->execute();
    $result = $stmt->fetchAll();

    // if additionals and parent sport single_standings = 1 then parse league standings and include only the teams from the first sport_id in order list
    if ($singleAdditional && $result) {
      $firstSportId = null;
      foreach ($result as $key => &$single) {
        if (0 === $key) { 
          $firstSportId = $single['sport_id'];
        }
        else {
          if ($firstSportId != $single['sport_id']) {
            unset($result[$key]);
          }
        }
      }
    }

    $em->clear();
    return $result;
  }
}
