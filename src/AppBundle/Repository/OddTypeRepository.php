<?php

namespace AppBundle\Repository;

/**
 * OddTypeRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class OddTypeRepository extends \Doctrine\ORM\EntityRepository
{

	public function getOddTypesMostUsed($dateFrom, $dateTo) {
		$em 		= $this->getEntityManager();
		$result = array();

		$sql = "
		SELECT 
			fuo.odd_type_id id, 
			IF(ot.name IS NULL, 'No Bet', ot.name) name
		FROM 
			ft_fixture_user_odd fuo
				LEFT JOIN odd_type ot ON ot.id = fuo.odd_type_id
		WHERE
				fuo.match_datetime >= :dateFrom
		AND fuo.match_datetime <= :dateTo
		GROUP BY fuo.odd_type_id
		ORDER BY COUNT(fuo.odd_type_id) DESC
		LIMIT 10
		";

		$stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":dateFrom", $dateFrom);
    $stmt->bindValue(":dateTo", $dateTo);
    $stmt->execute();
		$result = $stmt->fetchAll();
		
		$em->clear();

		return $result;
	}

}
