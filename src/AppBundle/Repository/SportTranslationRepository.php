<?php

namespace AppBundle\Repository;

/**
 * SportGedmoTranslationRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class SportTranslationRepository extends \Doctrine\ORM\EntityRepository
{
  // public function findOneByTitle($sportId, $locale = 'en') {
  //   $em = $this->getEntityManager();
  //
  //   $queryMain = "SELECT st.title FROM sport_translations st WHERE st.sport_id = :id AND st.locale = :locale AND st.type = 'normal' ";
  //   $stmt = $em->getConnection()->prepare($queryMain);
  //   $stmt->bindValue(":id", $id);
  //   $stmt->bindValue(":locale", $locale);
  //   $stmt->execute();
  //   $name = $stmt->fetchColumn();
  //
  //   return $name;
  // }
}
