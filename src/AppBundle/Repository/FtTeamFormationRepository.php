<?php

namespace AppBundle\Repository;

use Symfony\Component\Cache\Adapter\FilesystemAdapter;

/**
 * FtTeamFormationRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtTeamFormationRepository extends \Doctrine\ORM\EntityRepository
{

	public function getAllTeamFormations($teamFormationsDir, $environment) {
		$result = array();
		$teamFormationsDir = $teamFormationsDir . '/var/cache/' . $environment . '/team_formations';
		$cache      = new FilesystemAdapter('', 0, $teamFormationsDir);

    $teamFormationCache = $cache->getItem('team.formations');

    if (!$teamFormationCache->isHit()) {
			$em	= $this->getEntityManager();

			$sql = "SELECT id, name FROM ft_team_formation tf ORDER BY tf.name";

			$stmt = $em->getConnection()->prepare($sql);
			$stmt->execute();
			$result = $stmt->fetchAll();

			$teamFormationCache->set($result);
			$cache->save($teamFormationCache);
	
			$em->clear();
		}
		return $teamFormationCache->get();
	}

}