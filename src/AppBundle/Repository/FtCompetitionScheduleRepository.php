<?php

namespace AppBundle\Repository;

use Doctrine\ORM\EntityRepository;

/**
 * FtCompetitionScheduleRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtCompetitionScheduleRepository extends EntityRepository
{
  public function findAllMatches($competitionId)
  {

  $em = $this->getEntityManager();

  $sql = "SELECT f.id, t.team_id as h, t2.team_id as a, f.link_betarades, f.link_bethome, f.link_pariurix, f.link_text_betarades, f.link_text_bethome, f.link_text_pariurix, f.tv_gr, f.tv_ro, f.match_datetime, f.score, f.is_hot, f.phase_id FROM ft_competition_schedule f INNER JOIN ft_team t ON (t.team_id = f.h_team) INNER JOIN ft_team t2 ON (t2.team_id = f.a_team) WHERE f.phase_id != 0 AND f.competition_id = " . $competitionId . " ORDER BY f.phase_id ASC, f.match_datetime ASC";

  $stmt = $em->getConnection()->prepare($sql);
  $stmt->execute();
  $matches = $stmt->fetchAll();

  $em->clear();

  return $matches;
  }

  public function findAllCompetitionMatches()
  {
    $em = $this->getEntityManager();
    $sql = "SELECT f.id, 
            t.id as h, 
            t2.id as a, 
            t.name_gr as h_name, 
            t2.name_gr as a_name, 
            f.link_betarades, 
            f.link_bethome, 
            f.link_pariurix, 
            f.link_text_betarades, 
            f.link_text_bethome, 
            f.link_text_pariurix, 
            f.tv_gr, 
            f.tv_ro, 
            f.match_datetime, 
            f.score, 
            f.is_hot,
            f.home_odds,
            f.away_odds,
            f.affiliate_link,
            f.phase_id,
            f.match_type
            FROM ft_competition_schedule f 
            INNER JOIN ft_competition_teams t ON (t.id = f.h_team) 
            INNER JOIN ft_competition_teams t2 ON (t2.id = f.a_team) 
            ORDER BY f.match_datetime ASC ";
    $stmt = $em->getConnection()->prepare($sql);
    $stmt->execute();
    $matches = $stmt->fetchAll();
    $em->clear();
    return $matches;
  }

  public function findCompetitionMatchesByType($matchType)
  {
    $em = $this->getEntityManager();
    $sql = "SELECT f.id, 
            t.id as h, 
            t2.id as a, 
            t.name_gr as h_name, 
            t2.name_gr as a_name, 
            f.link_betarades, 
            f.link_bethome, 
            f.link_pariurix, 
            f.link_text_betarades, 
            f.link_text_bethome, 
            f.link_text_pariurix, 
            f.tv_gr, 
            f.tv_ro, 
            f.match_datetime, 
            f.score, 
            f.is_hot,
            f.home_odds,
            f.away_odds,
            f.affiliate_link,
            f.phase_id,
            f.match_type
            FROM ft_competition_schedule f 
            INNER JOIN ft_competition_teams t ON (t.id = f.h_team) 
            INNER JOIN ft_competition_teams t2 ON (t2.id = f.a_team) 
            WHERE f.match_type = :matchType
            ORDER BY f.match_datetime ASC ";
    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue('matchType', $matchType);
    $stmt->execute();
    $matches = $stmt->fetchAll();
    $em->clear();
    return $matches;
  }
}

