<?php

namespace AppBundle\Repository;

/**
 * FtLeagueSportRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtLeagueSportRepository extends \Doctrine\ORM\EntityRepository
{
  public function getCompetitionLeagueId($sportId) {
    $em = $this->getEntityManager();

    $queryMain = "SELECT s.l_id FROM ft_league_sport s WHERE s.sport_id = $sportId";
    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->execute();
    $leagueIds = $stmt->fetchColumn();

    $em->clear();
    return $leagueIds;
  }

  public function getSportByLeague($league) {
    $em = $this->getEntityManager();

    $queryMain = "SELECT s.sport_id FROM ft_league_sport s WHERE s.l_id = '$league'";
    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->execute();
    $sport = $stmt->fetchColumn();

    $em->clear();
    return $sport;
  }

  public function getFtLeagueSportsArr() {
    $em = $this->getEntityManager();

    $result = array();

    $queryMain = "SELECT s.sport_id, s.l_id FROM ft_league_sport s";
    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->execute();
    $sports = $stmt->fetchAll();

    if ($sports) {
      foreach ($sports as $sport) {
        $result[$sport['sport_id']] = $sport['l_id'];
      }
    }

    $em->clear();
    return $result;
  }
}
