<?php

namespace AppBundle\Repository;

/**
 * CityRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class CityRepository extends \Doctrine\ORM\EntityRepository
{
    public function getCityByCountryId($countryId)
    {
        $cities = $this->createQueryBuilder('c')
            ->select('cc.id as country_id, c.name, c.weatherApiId')
            ->innerJoin('c.country', 'cc')
            ->where('cc.id = :countryId')
            ->setParameter('countryId', $countryId)
            ->getQuery()
            ->getArrayResult();

        return $cities;
    }
}
