<?php

namespace AppBundle\Repository;

/**
 * CountryRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class CountryRepository extends \Doctrine\ORM\EntityRepository
{

  public function getCountryId($country) {
    $countryId = null;

    $sql ="SELECT id FROM country WHERE c_id = :country";

    $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
    $stmt->bindValue(":country", $country);
    // $stmt->execute(array(), \Doctrine\ORM\Query::HYDRATE_SCALAR);
    $stmt->execute();
    // $countryId = $stmt->fetchAll(\PDO::FETCH_COLUMN);
    $countryId = $stmt->fetchColumn();

    return $countryId;
  }

}
