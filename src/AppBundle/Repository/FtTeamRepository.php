<?php

namespace AppBundle\Repository;

/**
 * FtTeamRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtTeamRepository extends \Doctrine\ORM\EntityRepository
{
	public function getTeamNameByLocale($teamId, $locale, $localeFallback) {
	// public function getTeamNameByLocale($teamId, $locale) {
    $em = $this->getEntityManager();
    $result = '';

		$locale = ('el' === $locale) ? 'gr' : $locale;

		$sql = "SELECT COALESCE(name_$locale, name_$localeFallback) team_name FROM ft_team t WHERE t.team_id = :teamId";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":teamId", $teamId);
    $stmt->execute();
    $result = $stmt->fetchColumn();

    $em->clear();
		return $result;
	}

	public function getGreekTeamNamesForFixtures($fixtureIds) {
		$em = $this->getEntityManager();
		$result = array();

		$sql = "
		SELECT COALESCE(t.name_gr, t.name_en) team_name FROM 
			ft_team t,
			(
			SELECT h_team team_id FROM ft_fixture WHERE f_id IN ($fixtureIds)
			UNION DISTINCT
			SELECT a_team team_id FROM ft_fixture WHERE f_id IN ($fixtureIds)
			) as team_ids
		WHERE 
		team_ids.team_id= t.team_id
		";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->execute();
		$result = $stmt->fetchAll(\PDO::FETCH_COLUMN);
		
		$em->clear();
		return $result;
	}

	public function getTeamMundialData($helper, $teamId) {
		$em = $this->getEntityManager();
		$result = array();
		$previewIds = array();
		// TODO cvyz uncomment before push
		$mundialSportIds = array(712, 713, 714, 715, 716, 717, 718, 719, 720);
		// local system
		// $mundialSportIds = array(618, 619, 620, 622, 623, 624, 625, 626, 627);

		$sql = "
		(SELECT 
			f.f_id, f.sport_id, st.title,
			(SELECT df.preview_id FROM domain_ft_fixture df WHERE df.fixture_id = f.f_id AND df.domain_id = 2) preview_id,
			DATE_FORMAT(f.match_datetime, '%Y-%m-%d') match_date, DATE_FORMAT(f.match_datetime, '%H:%i') match_time, f.ft1, f.ft2, f.h_team, f.a_team,
			f.status,
			COALESCE(t1.name_gr, t1.name_en) home_team_name, COALESCE(t2.name_gr, t2.name_en) away_team_name,
			COALESCE(l.name_gr, l.name_en) league_name
		FROM 
			ft_fixture f 
				LEFT JOIN ft_league l ON l.l_id = f.league
				LEFT JOIN ft_team t1 ON t1.team_id = f.h_team
				LEFT JOIN ft_team t2 ON t2.team_id = f.a_team
				LEFT JOIN sport_translations st ON f.sport_id = st.sport_id AND st.type = 'normal' AND st.locale = 'el'
		WHERE 
				(f.h_team = $teamId OR f.a_team = $teamId) 
		AND f.sport_id IN (".implode(', ', $mundialSportIds).")
		ORDER BY f.match_datetime DESC
		LIMIT 3)
		UNION ALL
		(SELECT 
				f.f_id, f.sport_id, st.title,
				(SELECT df.preview_id FROM domain_ft_fixture df WHERE df.fixture_id = f.f_id AND df.domain_id = 2) preview_id,
				DATE_FORMAT(f.match_datetime, '%Y-%m-%d') match_date, DATE_FORMAT(f.match_datetime, '%H:%i') match_time, f.ft1, f.ft2, f.h_team, f.a_team,
				f.status,
				COALESCE(t1.name_gr, t1.name_en) home_team_name, COALESCE(t2.name_gr, t2.name_en) away_team_name,
				COALESCE(l.name_gr, l.name_en) league_name
			FROM 
				ft_fixture f 
					LEFT JOIN ft_league l ON l.l_id = f.league
					LEFT JOIN ft_team t1 ON t1.team_id = f.h_team
					LEFT JOIN ft_team t2 ON t2.team_id = f.a_team
					LEFT JOIN sport_translations st ON f.sport_id = st.sport_id AND st.type = 'normal' AND st.locale = 'el'
			WHERE 
					(f.h_team = $teamId OR f.a_team = $teamId) 
			AND f.status <> 'NS'
			AND f.sport_id IS NOT NULL
			ORDER BY f.match_datetime DESC
			LIMIT 10)
		";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->execute();
		$result = $stmt->fetchAll();

		if ($result) {
			foreach ($result as $key => $r) {
				// if game is mundial, and has preview_id in betarades, try and get slug if exists, and stadium
				if (in_array((int) $r['sport_id'], $mundialSportIds)) {
					if ($r['preview_id']) {
						$previewIds[$key] = $r['preview_id'];
						// array_push($previewIds, $r['preview_id']);
					}
				}
			}
		}

		// get betarades domain
		if ($previewIds) {
			$domain = $em->getRepository('AppBundle:Domain')->findOneById(2);
			$betaradesConnection = $helper->getDomainConnection($domain);
			
			$beteradesPostData = $this->getBetaradesPostData($betaradesConnection, $domain->getDbPrefix(), implode(',', $previewIds));

			foreach ($previewIds as $key => $value) {
				$postKey = array_search($value, array_column($beteradesPostData, 'id'));
				$result[$key]['slug'] = $beteradesPostData[$postKey]['slug'];
				$result[$key]['stadium'] = $beteradesPostData[$postKey]['stadium'];
			}
		}

		$em->clear();
		return $result;
	}

	public function getTeamExtraData($helper, $teamId, $templateId) {
		$em = $this->getEntityManager();
		$result = array();
		$previewIds = array();
		// TODO cvyz uncomment before push
		if (2 == (int) $templateId) {
			$extraSportIds = array(712, 713, 714, 715, 716, 717, 718, 719, 720);
		} else if (3 == (int) $templateId) {
			//$extraSportIds = array(399, 802, 803);
			$extraSportIds = array(802, 803);
		} else if (4 == (int) $templateId) {
			$extraSportIds = array(251, 805, 806, 807, 808, 809, 810);
		}

		$thisYear = date('Y');
		$sql = "
		(SELECT 
			f.f_id, f.sport_id, st.title,
			(SELECT df.preview_id FROM domain_ft_fixture df WHERE df.fixture_id = f.f_id AND df.domain_id = 2) preview_id,
			DATE_FORMAT(f.match_datetime, '%Y-%m-%d') match_date, DATE_FORMAT(f.match_datetime, '%H:%i') match_time, f.ft1, f.ft2, f.h_team, f.a_team,
			f.status,
			COALESCE(t1.name_gr, t1.name_en) home_team_name, COALESCE(t2.name_gr, t2.name_en) away_team_name,
			COALESCE(l.name_gr, l.name_en) league_name
		FROM 
			ft_fixture f 
				LEFT JOIN ft_league l ON l.l_id = f.league
				LEFT JOIN ft_team t1 ON t1.team_id = f.h_team
				LEFT JOIN ft_team t2 ON t2.team_id = f.a_team
				LEFT JOIN sport_translations st ON f.sport_id = st.sport_id AND st.type = 'normal' AND st.locale = 'el'
		WHERE 
				(f.h_team = $teamId OR f.a_team = $teamId) 
		AND f.sport_id IN (".implode(', ', $extraSportIds).")
		AND f.status <> 'PP'
		AND year(f.match_datetime) = $thisYear
		AND f.match_datetime >= CAST(CURRENT_TIMESTAMP AS DATE)
		ORDER BY f.match_datetime ASC
		LIMIT 3)
		UNION ALL
		(SELECT 
				f.f_id, f.sport_id, st.title,
				(SELECT df.preview_id FROM domain_ft_fixture df WHERE df.fixture_id = f.f_id AND df.domain_id = 2) preview_id,
				DATE_FORMAT(f.match_datetime, '%Y-%m-%d') match_date, DATE_FORMAT(f.match_datetime, '%H:%i') match_time, f.ft1, f.ft2, f.h_team, f.a_team,
				f.status,
				COALESCE(t1.name_gr, t1.name_en) home_team_name, COALESCE(t2.name_gr, t2.name_en) away_team_name,
				COALESCE(l.name_gr, l.name_en) league_name
			FROM 
				ft_fixture f 
					LEFT JOIN ft_league l ON l.l_id = f.league
					LEFT JOIN ft_team t1 ON t1.team_id = f.h_team
					LEFT JOIN ft_team t2 ON t2.team_id = f.a_team
					LEFT JOIN sport_translations st ON f.sport_id = st.sport_id AND st.type = 'normal' AND st.locale = 'el'
			WHERE 
					(f.h_team = $teamId OR f.a_team = $teamId) 
			AND f.status <> 'NS' AND f.status <> 'PP'
			AND f.sport_id IS NOT NULL
			ORDER BY f.match_datetime DESC
			LIMIT 10)
		";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->execute();
		$result = $stmt->fetchAll();

		if ($result) {
			foreach ($result as $key => $r) {
				// if game is mundial, and has preview_id in betarades, try and get slug if exists, and stadium
				if (in_array((int) $r['sport_id'], $extraSportIds)) {
					if ($r['preview_id']) {
						$previewIds[$key] = $r['preview_id'];
						// array_push($previewIds, $r['preview_id']);
					}
				}
			}
		}

		// get betarades domain
		if ($previewIds) {
			$domain = $em->getRepository('AppBundle:Domain')->findOneById(2);
			$betaradesConnection = $helper->getDomainConnection($domain);
			$counter = 0;
			foreach ($previewIds as $preview) {
				$beteradesPostData[$counter] = $this->getBetaradesPostData($betaradesConnection, $domain->getDbPrefix(), $preview);
				$counter++;
			}

			foreach ($previewIds as $key => $value) {
				$postKey = array_search($value, array_column($beteradesPostData, 'id'));
				$result[$key]['slug'] = $beteradesPostData[$postKey]['slug'];
				$result[$key]['stadium'] = $beteradesPostData[$postKey]['stadium'];
			}
		}

		$em->clear();
		return $result;
	}

	private function getBetaradesPostData($conn, $dbPrefix, $previewIds) {
		$sql = "SELECT id, slug, stadium FROM {$dbPrefix}wbs_previews WHERE id = $previewIds";

		$stmt = $conn->prepare($sql);
		$stmt->execute();
		$result = $stmt->fetch();

		return $result;
	}

	public function getTeamsByCountryId($countryId)
	{
		$teams = $this->createQueryBuilder('t')
			->select('t.teamId, COALESCE(t.nameGr, t.nameEn) name')
			->innerJoin('t.country', 'cc')
			->where('cc.id = :countryId')
			->setParameter('countryId', $countryId)
			->getQuery()
			->getArrayResult();

		return $teams;
	}

	public function getAllTeams($offset, $limit)
	{
		$em = $this->getEntityManager();

		$sql = "SELECT team_id as id, name_en as name, 'football' as sportId FROM ft_team ORDER BY team_id ASC LIMIT " . $limit . " OFFSET " . $offset;

		$stmt = $em->getConnection()->prepare($sql);
		$stmt->execute();
		$result = $stmt->fetchAll();

		return $result;
	}
}
