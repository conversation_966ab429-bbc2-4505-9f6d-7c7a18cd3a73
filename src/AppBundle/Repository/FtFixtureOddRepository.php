<?php

namespace AppBundle\Repository;

/**
 * FtFixtureOddRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtFixtureOddRepository extends \Doctrine\ORM\EntityRepository
{
  public function getTodaysOdds($domainId) {
    $em = $this->getEntityManager();

    $date = strtotime('+1 day');
    $dateFrom = date('Y-m-d 06:00:00', time());
    $dateTo   = date('Y-m-d 05:59:59', $date);

    $sql = "
      SELECT dfr.preview_id, fo.bookmaker_id, fo.odd_1, fo.odd_x, fo.odd_2, fo.odd_o25, fo.odd_u25, fo.p_odd_1, fo.p_odd_x, fo.p_odd_2, fo.p_odd_o25, fo.p_odd_u25, fo.match_datetime, fo.last_modified
      FROM
        ft_fixture_odd fo
          INNER JOIN domain_ft_fixture_relationship dfr ON dfr.fixture_id = fo.f_id AND dfr.domain_id = :domainId
      WHERE fo.match_datetime >= :dateFrom AND fo.match_datetime <= :dateTo
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":domainId", $domainId);
    $stmt->bindValue(":dateFrom", $dateFrom);
    $stmt->bindValue(":dateTo", $dateTo);
    $stmt->execute();
    $result = $stmt->fetchAll();

    $em->clear();

    return $result;
  }
}
