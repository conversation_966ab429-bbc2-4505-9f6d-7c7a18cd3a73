<?php

namespace AppBundle\Repository;

/**
 * FtFixtureRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtFixtureRepository extends \Doctrine\ORM\EntityRepository
{

  public function getLeagueMatches($sportInClause, $dateFrom, $dateTo) {
    $em = $this->getEntityManager();
    $result = array();

    $sql = "
      SELECT
        f.f_id, f.sport_id, f.match_datetime, IF(INSTR(f.league, '.'), RIGHT(f.league, LENGTH(f.league) - INSTR(f.league, '.')), null) league_group,
        f.league,
        f.season, f.week, f.status,
        f.h_team,
        f.a_team,
        CONCAT(f.ht1, '-', f.ht2) as ht_score,
        CONCAT(f.ft1, '-', f.ft2) as ft_score,
        CONCAT(f.et1, '-', f.et2) as et_score,
        CONCAT(f.pt1, '-', f.pt2) as pt_score,
        f.events
      FROM
        ft_fixture f
          LEFT JOIN ft_team h ON h.team_id = f.h_team
          LEFT JOIN ft_team a ON a.team_id = f.a_team
      WHERE
          f.match_datetime >= STR_TO_DATE(:dateFrom, '%Y-%m-%d %H:%i:%s')
      AND f.match_datetime <= STR_TO_DATE(:dateTo, '%Y-%m-%d %H:%i:%s')
      AND f.sport_id IN $sportInClause
      ORDER BY f.match_datetime DESC;
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":dateFrom", $dateFrom);
    $stmt->bindValue(":dateTo", $dateTo);
    $stmt->execute();
    $result = $stmt->fetchAll();

    $em->clear();
    return $result;
  }

  public function getFinishedMatchesWithPreviews() {
    $em = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT dfr.fixture_id, dfr.id, lss.id league_season_id, f.sport_id, f.league league_id, dfr.domain_id, dfr.preview_id, f.status, CONCAT(f.ht1, '-', f.ht2) ht_score, CONCAT(f.ft1, '-', f.ft2) ft_score, CONCAT(f.et1, '-', f.et2) et_score, CONCAT(f.pt1, '-', f.pt2) pt_score, events, h_team, a_team, f.season, f.ft1, f.ft2,
    ds.has_standings, s.is_additional, s.parent_id
    FROM
      domain_ft_fixture_relationship dfr
        INNER JOIN ft_fixture f ON f.f_id = dfr.fixture_id
        INNER JOIN sport s ON s.id = f.sport_id
        LEFT JOIN ft_league_season_sport lss ON lss.sport_id = f.sport_id AND lss.season = f.season
        LEFT JOIN domain_sport ds ON dfr.domain_id = ds.domain_id AND ds.sport_id = f.sport_id AND ds.has_standings = 1 AND ds.has_posts = 1
        WHERE f.status = 'F' OR f.status = 'FE' OR f.status = 'FP'
    ORDER BY dfr.fixture_id
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetchAll();

    $final = array();
    foreach ($result as $single) {
      $final[$single['fixture_id']]['fixture_id']       = $single['fixture_id'];
      $final[$single['fixture_id']]['league_season_id'] = $single['league_season_id'];
      $final[$single['fixture_id']]['sport_id']         = $single['sport_id'];
      $final[$single['fixture_id']]['league_id']        = $single['league_id'];
      $final[$single['fixture_id']]['status']           = $single['status'];
      $final[$single['fixture_id']]['ht_score']         = $single['ht_score'];
      $final[$single['fixture_id']]['ft_score']         = $single['ft_score'];
      $final[$single['fixture_id']]['et_score']         = $single['et_score'];
      $final[$single['fixture_id']]['pt_score']         = $single['pt_score'];
      $final[$single['fixture_id']]['events']           = $single['events'];
      $final[$single['fixture_id']]['h_team']           = $single['h_team'];
      $final[$single['fixture_id']]['a_team']           = $single['a_team'];
      $final[$single['fixture_id']]['season']           = $single['season'];
      $final[$single['fixture_id']]['ft1']              = $single['ft1'];
      $final[$single['fixture_id']]['ft2']              = $single['ft2'];
      $final[$single['fixture_id']]['is_additional']    = $single['is_additional'];
      $final[$single['fixture_id']]['parent_id']        = $single['parent_id'];
      $final[$single['fixture_id']]['domains'][$single['domain_id']] = array('id' => $single['id'], 'preview_id' => $single['preview_id'], 'has_standings' => $single['has_standings']);
    }

    $em->clear();
    return $final;
  }

  public function getTeamsInSportSeason($sportId, $season) {
    $em = $this->getEntityManager();
    $result = array();

    $sql = "
      SELECT h_team as team_id FROM ft_fixture WHERE sport_id = :sportId AND season = :season
      UNION DISTINCT
      SELECT a_team FROM ft_fixture WHERE sport_id = :sportId AND season = :season
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":season", $season);
    $stmt->execute();

    $result = $stmt->fetchAll(\PDO::FETCH_COLUMN) ?: array();

    $em->clear();
    return $result;
  }

  public function getTeamLeagueSeasonSportFinished($teamId, $lssId, $fixtureId = null) {
    $em = $this->getEntityManager();
    $result = array();
    $andWhereClause = '';

    // when updating a finished match, only return the finished match stats
    if ($fixtureId) {
      $andWhereClause = " AND f.f_id = $fixtureId ";
    }

    $sql = "
      SELECT
        f.h_team, f.a_team, events, CASE WHEN f.h_team = :teamId THEN 1 ELSE 0 END is_home, f.ft1, f.ft2, f.ht1, f.ht2,
        CASE WHEN f.h_team = :teamId AND f.ft1 > f.ft2 THEN 1 ELSE 0 END h_win,
        CASE WHEN f.h_team = :teamId AND f.ht1 > f.ht2 THEN 1 ELSE 0 END h_win_ht,
        CASE WHEN f.h_team = :teamId AND f.ft1 = f.ft2 THEN 1 ELSE 0 END h_draw,
        CASE WHEN f.h_team = :teamId AND f.ht1 = f.ht2 THEN 1 ELSE 0 END h_draw_ht,
        CASE WHEN f.h_team = :teamId AND f.ft1 < f.ft2 THEN 1 ELSE 0 END h_lose,
        CASE WHEN f.h_team = :teamId AND f.ht1 < f.ht2 THEN 1 ELSE 0 END h_lose_ht,
        CASE WHEN f.h_team = :teamId THEN f.ft1 ELSE 0 END h_goals_for,
        CASE WHEN f.h_team = :teamId THEN f.ft2 ELSE 0 END h_goals_against,
        CASE WHEN f.h_team = :teamId AND f.ft1 = 0 THEN 1 ELSE 0 END h_not_scored,
        CASE WHEN f.h_team = :teamId AND f.ft2 = 0 THEN 1 ELSE 0 END h_not_conceded,
        CASE WHEN f.h_team = :teamId AND f.ht1 + f.ht2 > 1 THEN 1 ELSE 0 END h_over_15_1,
        CASE WHEN f.h_team = :teamId AND (f.ft1 - f.ht1 + f.ft2 - f.ht2) > 1 THEN 1 ELSE 0 END h_over_15_2,
        CASE WHEN f.h_team = :teamId AND f.ft1 + f.ft2 > 1 THEN 1 ELSE 0 END h_over_15,
        CASE WHEN f.h_team = :teamId AND f.ht1 + f.ht2 > 2 THEN 1 ELSE 0 END h_over_25_1,
        CASE WHEN f.h_team = :teamId AND (f.ft1 - f.ht1 + f.ft2 - f.ht2) > 2 THEN 1 ELSE 0 END h_over_25_2,
        CASE WHEN f.h_team = :teamId AND f.ft1 + f.ft2 > 2 THEN 1 ELSE 0 END h_over_25,
        CASE WHEN f.h_team = :teamId AND f.ht1 + f.ht2 > 3 THEN 1 ELSE 0 END h_over_35_1,
        CASE WHEN f.h_team = :teamId AND (f.ft1 - f.ht1 + f.ft2 - f.ht2) > 3 THEN 1 ELSE 0 END h_over_35_2,
        CASE WHEN f.h_team = :teamId AND f.ft1 + f.ft2 > 3 THEN 1 ELSE 0 END h_over_35,
        CASE WHEN f.h_team = :teamId AND f.ht1 + f.ht2 < 2 THEN 1 ELSE 0 END h_under_15_1,
        CASE WHEN f.h_team = :teamId AND (f.ft1 - f.ht1 + f.ft2 - f.ht2) < 2 THEN 1 ELSE 0 END h_under_15_2,
        CASE WHEN f.h_team = :teamId AND f.ft1 + f.ft2 < 2 THEN 1 ELSE 0 END h_under_15,
        CASE WHEN f.h_team = :teamId AND f.ht1 + f.ht2 < 3 THEN 1 ELSE 0 END h_under_25_1,
        CASE WHEN f.h_team = :teamId AND (f.ft1 - f.ht1 + f.ft2 - f.ht2) < 3 THEN 1 ELSE 0 END h_under_25_2,
        CASE WHEN f.h_team = :teamId AND f.ft1 + f.ft2 < 3 THEN 1 ELSE 0 END h_under_25,
        CASE WHEN f.h_team = :teamId AND f.ht1 + f.ht2 < 4 THEN 1 ELSE 0 END h_under_35_1,
        CASE WHEN f.h_team = :teamId AND (f.ft1 - f.ht1 + f.ft2 - f.ht2) < 4 THEN 1 ELSE 0 END h_under_35_2,
        CASE WHEN f.h_team = :teamId AND f.ft1 + f.ft2 < 4 THEN 1 ELSE 0 END h_under_35,
        CASE WHEN f.h_team = :teamId AND f.ft1 > 0 AND f.ft2 > 0 THEN 1 ELSE 0 END h_goal_goal,
        CASE WHEN f.h_team = :teamId AND (f.ft1 = 0 OR f.ft2 = 0) THEN 1 ELSE 0 END h_no_goal,
        CASE WHEN f.h_team = :teamId AND f.ft1 + f.ft2 <= 1 THEN 1 ELSE 0 END h_goals_01,
        CASE WHEN f.h_team = :teamId AND f.ft1 + f.ft2 >= 2 AND f.ft1 + f.ft2 <= 3 THEN 1 ELSE 0 END h_goals_23,
        CASE WHEN f.h_team = :teamId AND f.ft1 + f.ft2 >= 4 AND f.ft1 + f.ft2 <= 6 THEN 1 ELSE 0 END h_goals_46,
        CASE WHEN f.h_team = :teamId AND f.ft1 + f.ft2 >= 7 THEN 1 ELSE 0 END h_goals_7,
        CASE WHEN f.a_team = :teamId AND f.ft1 < f.ft2 THEN 1 ELSE 0 END a_win,
        CASE WHEN f.a_team = :teamId AND f.ht1 < f.ht2 THEN 1 ELSE 0 END a_win_ht,
        CASE WHEN f.a_team = :teamId AND f.ft1 = f.ft2 THEN 1 ELSE 0 END a_draw,
        CASE WHEN f.a_team = :teamId AND f.ht1 = f.ht2 THEN 1 ELSE 0 END a_draw_ht,
        CASE WHEN f.a_team = :teamId AND f.ft1 > f.ft2 THEN 1 ELSE 0 END a_lose,
        CASE WHEN f.a_team = :teamId AND f.ht1 > f.ht2 THEN 1 ELSE 0 END a_lose_ht,
        CASE WHEN f.a_team = :teamId THEN f.ft2 ELSE 0 END a_goals_for,
        CASE WHEN f.a_team = :teamId THEN f.ft1 ELSE 0 END a_goals_against,
        CASE WHEN f.a_team = :teamId AND f.ft2 = 0 THEN 1 ELSE 0 END a_not_scored,
        CASE WHEN f.a_team = :teamId AND f.ft1 = 0 THEN 1 ELSE 0 END a_not_conceded,
        CASE WHEN f.a_team = :teamId AND f.ht1 + f.ht2 > 1 THEN 1 ELSE 0 END a_over_15_1,
        CASE WHEN f.a_team = :teamId AND (f.ft1 - f.ht1 + f.ft2 - f.ht2) > 1 THEN 1 ELSE 0 END a_over_15_2,
        CASE WHEN f.a_team = :teamId AND f.ft1 + f.ft2 > 1 THEN 1 ELSE 0 END a_over_15,
        CASE WHEN f.a_team = :teamId AND f.ht1 + f.ht2 > 2 THEN 1 ELSE 0 END a_over_25_1,
        CASE WHEN f.a_team = :teamId AND (f.ft1 - f.ht1 + f.ft2 - f.ht2) > 2 THEN 1 ELSE 0 END a_over_25_2,
        CASE WHEN f.a_team = :teamId AND f.ft1 + f.ft2 > 2 THEN 1 ELSE 0 END a_over_25,
        CASE WHEN f.a_team = :teamId AND f.ht1 + f.ht2 > 3 THEN 1 ELSE 0 END a_over_35_1,
        CASE WHEN f.a_team = :teamId AND (f.ft1 - f.ht1 + f.ft2 - f.ht2) > 3 THEN 1 ELSE 0 END a_over_35_2,
        CASE WHEN f.a_team = :teamId AND f.ft1 + f.ft2 > 3 THEN 1 ELSE 0 END a_over_35,
        CASE WHEN f.a_team = :teamId AND f.ht1 + f.ht2 < 2 THEN 1 ELSE 0 END a_under_15_1,
        CASE WHEN f.a_team = :teamId AND (f.ft1 - f.ht1 + f.ft2 - f.ht2) < 2 THEN 1 ELSE 0 END a_under_15_2,
        CASE WHEN f.a_team = :teamId AND f.ft1 + f.ft2 < 2 THEN 1 ELSE 0 END a_under_15,
        CASE WHEN f.a_team = :teamId AND f.ht1 + f.ht2 < 3 THEN 1 ELSE 0 END a_under_25_1,
        CASE WHEN f.a_team = :teamId AND (f.ft1 - f.ht1 + f.ft2 - f.ht2) < 3 THEN 1 ELSE 0 END a_under_25_2,
        CASE WHEN f.a_team = :teamId AND f.ft1 + f.ft2 < 3 THEN 1 ELSE 0 END a_under_25,
        CASE WHEN f.a_team = :teamId AND f.ht1 + f.ht2 < 4 THEN 1 ELSE 0 END a_under_35_1,
        CASE WHEN f.a_team = :teamId AND (f.ft1 - f.ht1 + f.ft2 - f.ht2) < 4 THEN 1 ELSE 0 END a_under_35_2,
        CASE WHEN f.a_team = :teamId AND f.ft1 + f.ft2 < 4 THEN 1 ELSE 0 END a_under_35,
        CASE WHEN f.a_team = :teamId AND f.ft1 > 0 AND f.ft2 > 0 THEN 1 ELSE 0 END a_goal_goal,
        CASE WHEN f.a_team = :teamId AND (f.ft1 = 0 OR f.ft2 = 0) THEN 1 ELSE 0 END a_no_goal,
        CASE WHEN f.a_team = :teamId AND f.ft1 + f.ft2 <= 1 THEN 1 ELSE 0 END a_goals_01,
        CASE WHEN f.a_team = :teamId AND f.ft1 + f.ft2 >= 2 AND f.ft1 + f.ft2 <= 3 THEN 1 ELSE 0 END a_goals_23,
        CASE WHEN f.a_team = :teamId AND f.ft1 + f.ft2 >= 4 AND f.ft1 + f.ft2 <= 6 THEN 1 ELSE 0 END a_goals_46,
        CASE WHEN f.a_team = :teamId AND f.ft1 + f.ft2 >= 7 THEN 1 ELSE 0 END a_goals_7
      FROM ft_fixture f
        INNER JOIN ft_league_season_sport lss ON lss.sport_id = f.sport_id AND lss.season = f.season
      WHERE lss.id = :lssId
      AND (f.h_team = :teamId OR f.a_team = :teamId)
      AND f.status IN ('F', 'FE', 'FP')
      $andWhereClause
      ORDER BY f.match_datetime
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":lssId", $lssId);
    $stmt->bindValue(":teamId", $teamId);
    $stmt->execute();
    $result = $stmt->fetchAll();

    $em->clear();
    return $result;
  }

  public function getPreviousNextMatches($sportId, $season, $languageCode, $localeFallback, $translations) {
    $em = $this->getEntityManager();
    $result = array();
    $limitNumber = $this->getNumberOfWeekGames($sportId, $season);

    $sql = "
      SELECT game_type, match_datetime as rowNumber, CONCAT(COALESCE(t1.name_$languageCode, t1.name_$localeFallback), '-', COALESCE(t2.name_$languageCode, t2.name_$localeFallback)) namegr, IF(game_type = 1, CONCAT(ft1, '-', ft2), '') match_result 
      FROM (
      (SELECT 1 game_type, DATE_FORMAT(match_datetime, '%d/%m/%y') match_datetime, f.h_team, f.a_team, f.ft1, f.ft2 FROM ft_fixture f WHERE f.sport_id = :sportId AND f.season = :season AND f.status <> 'NS' ORDER BY f.match_datetime DESC LIMIT $limitNumber)
      UNION ALL
      (SELECT 2, DATE_FORMAT(match_datetime, '%d/%m/%y %H:%i') match_datetime, f.h_team, f.a_team, '-', '-' FROM ft_fixture f WHERE f.sport_id = :sportId AND f.season = :season AND f.status = 'NS' AND f.match_datetime >= NOW() ORDER BY f.match_datetime LIMIT $limitNumber)
      ) tmp
      INNER JOIN ft_team t1 ON t1.team_id = tmp.h_team
      INNER JOIN ft_team t2 ON t2.team_id = tmp.a_team
      ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":season", $season);
    $stmt->execute();

    $previousNext = $stmt->fetchAll() ?: array();

    if ($previousNext) {
      foreach($previousNext as $single) {
        if (1 === (int) $single['game_type']) {
          $result[0]['label'] = $translations[0];
          $result[0]['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'match_result' => $translations[3]);
          $result[0]['data'][] = $single;
        }
        elseif (2 === (int) $single['game_type']) {
          unset($single['match_result']);
          $result[1]['label'] = $translations[4];
          $result[1]['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2]);
          $result[1]['data'][] = $single;
        }
      }
    }

    $em->clear();
    return $result;
  }

  private function getNumberOfWeekGames($sportId, $season) {
    $em = $this->getEntityManager();
    $result = array();
    $limitNumber = 10;

    $sql = "SELECT SUM(1) FROM ft_fixture f WHERE f.sport_id = :sportId AND f.season = :season GROUP BY `week` LIMIT 1";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":season", $season);
    $stmt->execute();

    $limitNumber = $stmt->fetchColumn() ?: 10;

    $em->clear();
    return $limitNumber;
  }

  public function getInfoForDates($dateFrom, $dateTo, $clients) {
    $em         = $this->getEntityManager();
    $info       = array();
    $result     = array();
    $fixture    = array();
    $languages  = null;
    $sqlNames   = '';
    $sqlJoins   = '';
    $sqlSlugs   = '';

    // get all languages for all clients
    if ($clients) {
      try {
        $languages = array_column(json_decode($clients, true), 'locale');
        $languages = array_unique($languages, SORT_REGULAR);

        $domains = array_column(json_decode($clients, true), 'feed_domain_id');
        $domains = array_unique($domains, SORT_REGULAR);

        $clients = json_decode($clients, true);

        if ($clients) {
          foreach ($clients as $client) {
            $feedDomainUrl = 'null';
            $domainName = '';
            if (isset($client['feed_domain_id']) && $client['feed_domain_id']) {
              $feedDomain = $em->getRepository('AppBundle:Domain')->findOneById($client['feed_domain_id']);
              if ($feedDomain) {
                $domainName = $feedDomain->getName();
              }
              $feedDomainUrl = (isset($client['is_https']) && $client['is_https'] === true) ? 'https://' : 'http://';
              $feedDomainUrl .= $domainName;
            }

            $result['clients'][$client['id']] = array('feed_domain_url' => $feedDomainUrl, 'currency' => (isset($client['currency']) && $client['currency'] !== '' ? $client['currency'] : 'EUR'), 'odd_format' => (isset($client['odd_format']) && $client['odd_format'] !== '' ? $client['odd_format'] : 'dec'), 'feed_domain_id' => (isset($client['feed_domain_id']) ? $client['feed_domain_id'] : 'null'), 'locale' => $client['locale']);
          }
        }

        if ($languages) {
          foreach ($languages as $language) {
            $locale = ('el' === $language) ? 'gr' : $language;
            $sqlNames .= ", (SELECT COALESCE(t.name_$locale, t.name_en) FROM ft_team t WHERE t.team_id = f.h_team) h_team_name_" . $language . " ";
            $sqlNames .= ", (SELECT COALESCE(t.name_$locale, t.name_en) FROM ft_team t WHERE t.team_id = f.a_team) a_team_name_" . $language . " ";
          }
        }
      }
      catch (Exception $e) {
        $languages = null;
      }
    }

    $sql = "
      SELECT 
        f.f_id fixture_id, f.sport_id, f.h_team, f.a_team, league
        $sqlNames
      FROM 
        ft_fixture f 
      WHERE 
        f.match_datetime >= STR_TO_DATE(:dateFrom, '%Y-%m-%d %H:%i:%s')
        AND f.match_datetime <= STR_TO_DATE(:dateTo, '%Y-%m-%d %H:%i:%s')
        AND f.sport_id IS NOT NULL
      ORDER BY league
      ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":dateFrom", $dateFrom);
    $stmt->bindValue(":dateTo", $dateTo);
    $stmt->execute();

    $info = $stmt->fetchAll() ?: array();
    if ($info) {
      $sports = array();

      foreach ($info as $single) {
        $home = array();
        $away = array();

        if ($languages) {
          foreach ($languages as $locale) {
            $home[$locale] = $single['h_team_name_'.$locale];
            $away[$locale] = $single['a_team_name_'.$locale];
          }
        }

        $fixture[$single['fixture_id']] = array(
          'hTeamId'   => $single['h_team'], 
          'hTeamName' => $home,
          'aTeamId'   => $single['a_team'], 
          'aTeamName' => $away,
          'league'    => $single['league']
        );
      }
      $result['fixtures'] = $fixture;
    }

    $em->clear();
    return $result;
  }  

    public function getVersusHistory($homeTeamId, $awayTeamId)
    {        
        $em = $this->getEntityManager();
        $result = [];

        $qb = $em->createQueryBuilder();
        $qb->select('f')
        ->from('AppBundle:FtFixture', 'f')
        ->where('f.hTeam = ?1')
        ->andWhere('f.aTeam = ?2')
        ->andWhere('f.status IN ('. "'F'" . ', ' . "'FE'" . ', ' . "'FP'" . ')')
        ->orderBy('f.matchDatetime', 'DESC')
        ->setParameter(1, $homeTeamId)
        ->setParameter(2, $awayTeamId);

        $query = $qb->getQuery();
        $result = $query->getResult();

        $em->clear();
        return $result;
    }

    public function getTeamLastestFiveMatchesForAllCompetitions($teamId)
    {
        $em = $this->getEntityManager();
        $result = [];

        $sql = "
        SELECT 
            h_team, (SELECT name_gr FROM ft_team t WHERE t.team_id = h_team) home_team_name, 
            a_team, (SELECT name_gr FROM ft_team t WHERE t.team_id = a_team) away_team_name,
            match_datetime, ft1, ft2, ht1, ht2, et1, et2, pt1, pt2, status, season, sport_id,
            (SELECT title FROM sport_translations st WHERE st.sport_id = allquery.sport_id AND st.type = 'normal' AND st.locale = 'el') sport_name
        FROM (
            SELECT f.h_team, f.a_team, f.match_datetime, f.ft1, f.ft2, f.ht1, f.ht2, f.et1, f.et2, f.pt1, f.pt2, f.status, f.season, f.sport_id FROM ft_fixture f
            WHERE
                f.h_team = :teamId
            AND f.status IN ('F', 'FE', 'FP')
            UNION 
            SELECT f.h_team, f.a_team, f.match_datetime, f.ft1, f.ft2, f.ht1, f.ht2, f.et1, f.et2, f.pt1, f.pt2, f.status, f.season, f.sport_id FROM ft_fixture f
            WHERE
                f.a_team = :teamId
            AND f.status IN ('F', 'FE', 'FP')
        ) allquery
        ORDER BY match_datetime DESC
        LIMIT 5;
        ";
        
        $stmt = $em->getConnection()->prepare($sql);
        $stmt->bindValue(":teamId", $teamId);
        $stmt->execute();

        $result = $stmt->fetchAll();

        $em->clear();
        return $result;
    }

    //Created at first for euro 2021 teams
    //Skips international friendly matches
    //Thus it's called in RestFtCompetitionScheduleController
    public function getTeamLastTenFinishedMatches($teamId)
    {
      $em = $this->getEntityManager();
      $result = [];

      $sql = "
      SELECT DISTINCT
      f.match_datetime, f.ft1, f.ft2, f.h_team, f.a_team, s.title as competition, t.name_gr as h_name, t2.name_gr as a_name
      FROM ft_fixture f
      INNER JOIN ft_team t ON t.team_id = f.h_team
      INNER JOIN ft_team t2 ON t2.team_id = f.a_team
      LEFT JOIN sport_translations s ON s.sport_id = f.sport_id
      WHERE
      (f.h_team = :teamId OR f.a_team = :teamId)
      AND (f.status = 'F' OR f.status = 'FE' OR f.status = 'FP')
      AND (f.sport_id IS NULL OR (f.sport_id IS NOT NULL AND s.locale = 'el' AND s.type = 'normal'
      AND s.title != 'Διεθνή Φιλικά' AND s.title != 'Φιλικά Συλλόγων'))
      ORDER BY f.match_datetime DESC
      LIMIT 10;
      ";

      $stmt = $em->getConnection()->prepare($sql);
      $stmt->bindValue(":teamId", $teamId);
      $stmt->execute();

      $result = $stmt->fetchAll();

      $em->clear();
      return $result;
    }

    public function getTeamsHistoryLastTenSeasonsFinishedMatches($teamHome, $teamAway, $season)
    {
        $em = $this->getEntityManager();

        $currentSeason = (int)substr($season, 0, 4);
        $tenSeasonsBefore = $currentSeason - 10;

        $sql = "
        SELECT DISTINCT
          IF(((f.h_team = :teamHome AND f.ft1 > f.ft2) OR (f.a_team = :teamHome AND f.ft2 > f.ft1)), 1, 0) won,
          IF(f.ft1 = f.ft2, 1, 0) draw,
          IF(((f.a_team = :teamHome AND f.ft1 > f.ft2) OR (f.h_team = :teamHome AND f.ft2 > f.ft1)), 1, 0) lost,
          f.match_datetime, f.season, f.status, f.sport_id, f.events,
          f.ft1, f.ft2, f.ht1, f.ht2, f.et1, f.et2, f.pt1, f.pt2,
          t1.name_gr as h_name, t2.name_gr as a_name
          FROM ft_fixture f
          LEFT JOIN sport_translations s ON s.sport_id = f.sport_id
          INNER JOIN ft_team t1 ON t1.team_id = f.h_team
          INNER JOIN ft_team t2 ON t2.team_id = f.a_team
          WHERE
          (
          (f.h_team = :teamHome AND f.a_team = :teamAway)
          OR
          (f.a_team = :teamHome AND f.h_team = :teamAway)
          )
          AND (f.status = 'F' OR f.status = 'FE' OR f.status = 'FP')
          AND (f.sport_id IS NULL OR (f.sport_id IS NOT NULL AND s.locale = 'el' AND s.type = 'normal'
          AND s.title != 'Διεθνή Φιλικά' AND s.title != 'Φιλικά Συλλόγων'))
          AND LEFT(f.season, 4) >= :tenSeasonsBefore
          AND f.season != ''
          ORDER BY f.match_datetime DESC
        ";

        $stmt = $em->getConnection()->prepare($sql);
        $stmt->bindValue(":teamHome", $teamHome);
        $stmt->bindValue(":teamAway", $teamAway);
        $stmt->bindValue(":tenSeasonsBefore", $tenSeasonsBefore);
        $stmt->execute();

        $result = $stmt->fetchAll();

//        $won_matches = 0;
//        $lost_matches = 0;
//        $draw_matches = 0;
//        foreach ($result as $single) {
//            if ($single['won'] == 1) {
//                $won_matches++;
//            }
//            elseif ($single['draw'] == 1) {
//                $draw_matches++;
//            }
//            elseif ($single['lost'] == 1) {
//                $lost_matches++;
//            }
//        }

        $em->clear();

        return $result;

//        if ( ($won_matches + $draw_matches + $lost_matches) != 0) {
//            return $won_matches . ' - ' . $draw_matches . ' - ' . $lost_matches;
//        }
//        else {
//            return '';
//        }
    }
}
