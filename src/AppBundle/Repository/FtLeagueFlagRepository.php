<?php

namespace AppBundle\Repository;

/**
 * FtLeagueFlagRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtLeagueFlagRepository extends \Doctrine\ORM\EntityRepository
{
    public function getSportDataToSync() {
        $qb = $this->createQueryBuilder('u');
        $qb->where('u.sport is not null');

        $result = $qb->getQuery()->getResult();

        return $result;
    }

    public function getFlagsWithCompetitions() {
        $em = $this->getEntityManager();

        $query = "
            SELECT
            id, league_id, code, competition_number_en, competition_number_gr, image_name
            FROM ft_league_flag WHERE competition_number_gr IS NOT NULL ORDER BY id ASC";

        $stmt = $em->getConnection()->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll();
    }
}
