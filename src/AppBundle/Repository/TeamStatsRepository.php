<?php

namespace AppBundle\Repository;

// use AppBundle\Repository\DomainFtFixture;

/**
 * TeamStatsRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class TeamStatsRepository extends \Doctrine\ORM\EntityRepository
{

  public function getStats01($teamId1, $teamId2, $sportId, $season) {
    $em = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT
      t.team_id teamId, `rank`,
      overall_pts tPoints,
      (home_w * 3 + home_d * 1) hPoints,
      (away_w * 3 + away_d * 1) aPoints,
      overall_p tPlayed, home_p hPlayed, away_p aPlayed, overall_w tWins, home_w hWins, away_w aWins, overall_d tDraws, home_d hDraws, away_d aDraws, overall_l tLoses, home_l hLoses, away_l aLoses,
      overall_gf tGoalsFor, home_gf hGoalsFor, away_gf aGoalsFor, overall_ga tGoalsAgainst, home_ga hGoalsAgainst, away_ga aGoalsAgainst,
      IF(overall_p=0, 0, ROUND(overall_gf / overall_p, 2)) tGoalsForMV,
      IF(home_p=0, 0, ROUND(home_gf / home_p, 2)) hGoalsForMV,
      IF(away_p=0, 0, ROUND(away_gf / away_p, 2)) aGoalsForMV,
      IF(overall_p=0, 0, ROUND(overall_ga / overall_p, 2)) tGoalsAgainstMV,
      IF(home_p=0, 0, ROUND(home_ga / home_p, 2)) hGoalsAgainstMV,
      IF(away_p=0, 0, ROUND(away_ga / away_p, 2)) aGoalsAgainstMV
    FROM ft_team t LEFT JOIN ft_league_standings ls ON t.team_id = ls.team_id AND sport_id = :sportId AND season = :season
    WHERE (t.team_id = :teamId1 OR t.team_id = :teamId2)
    ORDER BY IF(t.team_id = :teamId1, 1, 0) DESC;
    ";
    // FROM ft_league_standings ls WHERE (team_id = :teamId1 OR team_id = :teamId2) AND sport_id = :sportId AND season = :season
    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":teamId1", $teamId1);
    $stmt->bindValue(":teamId2", $teamId2);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $result = $stmt->fetchAll();

    $em->clear();
    return $result;
  }

  /**
   * Returns the list of latest finished matches of the requested two teams, sport and season
   */
  public function getStats02($teamId1, $teamId2, $sportId, $season, $locale, $localeFallback) {
    $em = $this->getEntityManager();
    $resultH10  = array();
    $resultA10  = array();
    $resultHA10 = array();
    $resultAH10 = array();
    $result     = array();

    $sql = "
    SELECT
      DATE_FORMAT(f.match_datetime, '%d.%m.%y') matchDateTime, CONCAT((SELECT COALESCE(t.name_$locale, t.name_$localeFallback) FROM ft_team t WHERE t.team_id = f.h_team), ' - ', (SELECT COALESCE(t.name_$locale, t.name_$localeFallback) FROM ft_team t WHERE t.team_id = f.a_team)) matchName, CONCAT(f.ht1, '-', f.ht2) htScore, CONCAT(f.ft1, '-', f.ft2) ftScore,
      CASE WHEN f.h_team = :teamId1 AND f.ft1 - f.ft2 > 0 THEN 'w' WHEN f.h_team = :teamId1 AND f.ft1 - f.ft2 = 0 THEN 'd' ELSE 'l' END matchForm,
      CASE WHEN f.ft1 + f.ft2 > 2 THEN 'o' ELSE 'u' END overUnder
    FROM ft_fixture f
    WHERE
        f.h_team = :teamId1
    AND f.status IN ('F', 'FE', 'FP' )
    AND f.sport_id = :sportId
    AND f.season = :season
    ORDER BY f.match_datetime DESC LIMIT 10;
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":teamId1", $teamId1);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $resultH10 = $stmt->fetchAll();

    $sql = "
    SELECT
      DATE_FORMAT(f.match_datetime, '%d.%m.%y') matchDateTime, CONCAT((SELECT COALESCE(t.name_$locale, t.name_$localeFallback) FROM ft_team t WHERE t.team_id = f.h_team), ' - ', (SELECT COALESCE(t.name_$locale, t.name_$localeFallback) FROM ft_team t WHERE t.team_id = f.a_team)) matchName, CONCAT(f.ht1, '-', f.ht2) htScore, CONCAT(f.ft1, '-', f.ft2) ftScore,
      CASE WHEN f.a_team = :teamId2 AND f.ft1 - f.ft2 > 0 THEN 'l' WHEN f.a_team = :teamId2 AND f.ft1 - f.ft2 = 0 THEN 'd' ELSE 'w' END matchForm,
      CASE WHEN f.ft1 + f.ft2 > 2 THEN 'o' ELSE 'u' END overUnder
    FROM ft_fixture f
    WHERE
        f.a_team = :teamId2
    AND f.status IN ('F', 'FE', 'FP' )
    AND f.sport_id = :sportId
    AND f.season = :season
    ORDER BY f.match_datetime DESC LIMIT 10;
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":teamId2", $teamId2);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $resultA10 = $stmt->fetchAll();

    $sql = "
    SELECT
      DATE_FORMAT(f.match_datetime, '%d.%m.%y') matchDateTime, CONCAT((SELECT COALESCE(t.name_$locale, t.name_$localeFallback) FROM ft_team t WHERE t.team_id = f.h_team), ' - ', (SELECT COALESCE(t.name_$locale, t.name_$localeFallback) FROM ft_team t WHERE t.team_id = f.a_team)) matchName, CONCAT(f.ht1, '-', f.ht2) htScore, CONCAT(f.ft1, '-', f.ft2) ftScore,
      CASE WHEN (f.h_team = :teamId1) THEN CASE WHEN f.ft1 - f.ft2 > 0 THEN 'w' WHEN f.ft1 - f.ft2 = 0 THEN 'd' WHEN f.ft1 - f.ft2 < 0 THEN 'l' END
           WHEN (f.a_team = :teamId1) THEN CASE WHEN f.ft1 - f.ft2 > 0 THEN 'l' WHEN f.ft1 - f.ft2 = 0 THEN 'd' WHEN f.ft1 - f.ft2 < 0 THEN 'w' END
      END matchForm,
      CASE WHEN f.ft1 + f.ft2 > 2 THEN 'o' ELSE 'u' END overUnder
    FROM ft_fixture f
    WHERE
        (f.h_team = :teamId1 OR f.a_team = :teamId1)
    AND f.status IN ('F', 'FE', 'FP' )
    AND f.sport_id = :sportId
    AND f.season = :season
    ORDER BY f.match_datetime DESC LIMIT 10;
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":teamId1", $teamId1);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $resultHA10 = $stmt->fetchAll();

    $sql = "
    SELECT
      DATE_FORMAT(f.match_datetime, '%d.%m.%y') matchDateTime, CONCAT((SELECT COALESCE(t.name_$locale, t.name_$localeFallback) FROM ft_team t WHERE t.team_id = f.h_team), ' - ', (SELECT COALESCE(t.name_$locale, t.name_$localeFallback) FROM ft_team t WHERE t.team_id = f.a_team)) matchName, CONCAT(f.ht1, '-', f.ht2) htScore, CONCAT(f.ft1, '-', f.ft2) ftScore,
      CASE WHEN (f.h_team = :teamId2) THEN CASE WHEN f.ft1 - f.ft2 > 0 THEN 'w' WHEN f.ft1 - f.ft2 = 0 THEN 'd' WHEN f.ft1 - f.ft2 < 0 THEN 'l' END
           WHEN (f.a_team = :teamId2) THEN CASE WHEN f.ft1 - f.ft2 > 0 THEN 'l' WHEN f.ft1 - f.ft2 = 0 THEN 'd' WHEN f.ft1 - f.ft2 < 0 THEN 'w' END
      END matchForm,
      CASE WHEN f.ft1 + f.ft2 > 2 THEN 'o' ELSE 'u' END overUnder
    FROM ft_fixture f
    WHERE
        (f.h_team = :teamId2 OR f.a_team = :teamId2)
    AND f.status IN ('F', 'FE', 'FP' )
    AND f.sport_id = :sportId
    AND f.season = :season
    ORDER BY f.match_datetime DESC LIMIT 10;
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":teamId2", $teamId2);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $resultAH10 = $stmt->fetchAll();

    if ($resultH10) $result['home']['h10'] = $resultH10;
    if ($resultA10) $result['away']['a10'] = $resultA10;
    if ($resultHA10) $result['home']['ha10'] = $resultHA10;
    if ($resultAH10) $result['away']['ah10'] = $resultAH10;

    $em->clear();
    return $result;
  }

  public function getStats03($teamId1, $teamId2, $sportId, $season, $homeTeamName, $awayTeamName) {
    $em     = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT
      1 dummy, ts.team_id teamId, CASE WHEN ts.team_id = :teamId1 THEN 0 ELSE 1 END orderBy,
      ts.h_under_15 + ts.a_under_15 + ts.h_over_15 + ts.a_over_15 totalOverUnder, ts.h_over_15 + ts.a_over_15 totalOver, ts.h_under_15 + ts.a_under_15 totalUnder,
      ts.h_under_15 + ts.h_over_15 homeOverUnder, ts.h_over_15 homeOver, ts.h_under_15 homeUnder,
      ts.a_under_15 + ts.a_over_15 awayOverUnder, ts.a_over_15 awayOver, ts.a_under_15 awayUnder
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        (ts.team_id =  :teamId1 OR ts.team_id =  :teamId2)
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      2 dummy, ts.team_id teamId, CASE WHEN ts.team_id = :teamId1 THEN 0 ELSE 1 END orderBy,
      ts.h_under_25 + ts.a_under_25 + ts.h_over_25 + ts.a_over_25 totalOverUnder, ts.h_over_25 + ts.a_over_25 totalOver, ts.h_under_25 + ts.a_under_25 totalUnder,
      ts.h_under_25 + ts.h_over_25 homeOverUnder, ts.h_over_25 homeOver, ts.h_under_25 homeUnder,
      ts.a_under_25 + ts.a_over_25 awayOverUnder, ts.a_over_25 awayOver, ts.a_under_25 awayUnder
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        (ts.team_id =  :teamId1 OR ts.team_id =  :teamId2)
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      3 dummy, ts.team_id teamId, CASE WHEN ts.team_id = :teamId1 THEN 0 ELSE 1 END orderBy,
      ts.h_under_35 + ts.a_under_35 + ts.h_over_35 + ts.a_over_35 totalOverUnder, ts.h_over_35 + ts.a_over_35 totalOver, ts.h_under_35 + ts.a_under_35 totalUnder,
      ts.h_under_35 + ts.h_over_35 homeOverUnder, ts.h_over_35 homeOver, ts.h_under_35 homeUnder,
      ts.a_under_35 + ts.a_over_35 awayOverUnder, ts.a_over_35 awayOver, ts.a_under_35 awayUnder
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        (ts.team_id =  :teamId1 OR ts.team_id =  :teamId2)
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    -- 1st half
    SELECT
      4 dummy, ts.team_id teamId, CASE WHEN ts.team_id = :teamId1 THEN 0 ELSE 1 END orderBy,
      ts.h_under_15_1 + ts.a_under_15_1 + ts.h_over_15_1 + ts.a_over_15_1 totalOverUnder, ts.h_over_15_1 + ts.a_over_15_1 totalOver, ts.h_under_15_1 + ts.a_under_15_1 totalUnder,
      ts.h_under_15_1 + ts.h_over_15_1 homeOverUnder, ts.h_over_15_1 homeOver, ts.h_under_15_1 homeUnder,
      ts.a_under_15_1 + ts.a_over_15_1 awayOverUnder, ts.a_over_15_1 awayOver, ts.a_under_15_1 awayUnder
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        (ts.team_id =  :teamId1 OR ts.team_id =  :teamId2)
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      5 dummy, ts.team_id teamId, CASE WHEN ts.team_id = :teamId1 THEN 0 ELSE 1 END orderBy,
      ts.h_under_25_1 + ts.a_under_25_1 + ts.h_over_25_1 + ts.a_over_25_1 totalOverUnder, ts.h_over_25_1 + ts.a_over_25_1 totalOver, ts.h_under_25_1 + ts.a_under_25_1 totalUnder,
      ts.h_under_25_1 + ts.h_over_25_1 homeOverUnder, ts.h_over_25_1 homeOver, ts.h_under_25_1 homeUnder,
      ts.a_under_25_1 + ts.a_over_25_1 awayOverUnder, ts.a_over_25_1 awayOver, ts.a_under_25_1 awayUnder
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        (ts.team_id =  :teamId1 OR ts.team_id =  :teamId2)
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      6 dummy, ts.team_id teamId, CASE WHEN ts.team_id = :teamId1 THEN 0 ELSE 1 END orderBy,
      ts.h_under_35_1 + ts.a_under_35_1 + ts.h_over_35_1 + ts.a_over_35_1 totalOverUnder, ts.h_over_35_1 + ts.a_over_35_1 totalOver, ts.h_under_35_1 + ts.a_under_35_1 totalUnder,
      ts.h_under_35_1 + ts.h_over_35_1 homeOverUnder, ts.h_over_35_1 homeOver, ts.h_under_35_1 homeUnder,
      ts.a_under_35_1 + ts.a_over_35_1 awayOverUnder, ts.a_over_35_1 awayOver, ts.a_under_35_1 awayUnder
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        (ts.team_id =  :teamId1 OR ts.team_id =  :teamId2)
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    -- 2nd half
    SELECT
      7 dummy, ts.team_id teamId, CASE WHEN ts.team_id = :teamId1 THEN 0 ELSE 1 END orderBy,
      ts.h_under_15_2 + ts.a_under_15_2 + ts.h_over_15_2 + ts.a_over_15_2 totalOverUnder, ts.h_over_15_2 + ts.a_over_15_2 totalOver, ts.h_under_15_2 + ts.a_under_15_2 totalUnder,
      ts.h_under_15_2 + ts.h_over_15_2 homeOverUnder, ts.h_over_15_2 homeOver, ts.h_under_15_2 homeUnder,
      ts.a_under_15_2 + ts.a_over_15_2 awayOverUnder, ts.a_over_15_2 awayOver, ts.a_under_15_2 awayUnder
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        (ts.team_id =  :teamId1 OR ts.team_id =  :teamId2)
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      8 dummy, ts.team_id teamId, CASE WHEN ts.team_id = :teamId1 THEN 0 ELSE 1 END orderBy,
      ts.h_under_25_2 + ts.a_under_25_2 + ts.h_over_25_2 + ts.a_over_25_2 totalOverUnder, ts.h_over_25_2 + ts.a_over_25_2 totalOver, ts.h_under_25_2 + ts.a_under_25_2 totalUnder,
      ts.h_under_25_2 + ts.h_over_25_2 homeOverUnder, ts.h_over_25_2 homeOver, ts.h_under_25_2 homeUnder,
      ts.a_under_25_2 + ts.a_over_25_2 awayOverUnder, ts.a_over_25_2 awayOver, ts.a_under_25_2 awayUnder
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        (ts.team_id =  :teamId1 OR ts.team_id =  :teamId2)
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      9 dummy, ts.team_id teamId, CASE WHEN ts.team_id = :teamId1 THEN 0 ELSE 1 END orderBy,
      ts.h_under_35_2 + ts.a_under_35_2 + ts.h_over_35_2 + ts.a_over_35_2 totalOverUnder, ts.h_over_35_2 + ts.a_over_35_2 totalOver, ts.h_under_35_2 + ts.a_under_35_2 totalUnder,
      ts.h_under_35_2 + ts.h_over_35_2 homeOverUnder, ts.h_over_35_2 homeOver, ts.h_under_35_2 homeUnder,
      ts.a_under_35_2 + ts.a_over_35_2 awayOverUnder, ts.a_over_35_2 awayOver, ts.a_under_35_2 awayUnder
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        (ts.team_id =  :teamId1 OR ts.team_id =  :teamId2)
    AND lss.sport_id = :sportId AND season = :season
    ORDER BY dummy, orderBy
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":teamId1", $teamId1);
    $stmt->bindValue(":teamId2", $teamId2);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $resultAll = $stmt->fetchAll();

    if ($resultAll) {
      foreach($resultAll as $single) {
        switch ($single['dummy']) {
          case "1":
            if ($teamId1 === $single['teamId']) {
              $single['teamName'] = $homeTeamName;
              $result['ou15f'][] = $single;
            }
            else {
              $single['teamName'] = $awayTeamName;
              $result['ou15f'][] = $single;
            }
            break;
          case "2":
            if ($teamId1 === $single['teamId']) {
              $single['teamName'] = $homeTeamName;
              $result['ou25f'][] = $single;
            }
            else {
              $single['teamName'] = $awayTeamName;
              $result['ou25f'][] = $single;
            }
            break;
          case "3":
            if ($teamId1 === $single['teamId']) {
              $single['teamName'] = $homeTeamName;
              $result['ou35f'][] = $single;
            }
            else {
              $single['teamName'] = $awayTeamName;
              $result['ou35f'][] = $single;
            }
            break;
          case "4":
            if ($teamId1 === $single['teamId']) {
              $single['teamName'] = $homeTeamName;
              $result['ou15fh'][] = $single;
            }
            else {
              $single['teamName'] = $awayTeamName;
              $result['ou15fh'][] = $single;
            }
            break;
          case "5":
            if ($teamId1 === $single['teamId']) {
              $single['teamName'] = $homeTeamName;
              $result['ou25fh'][] = $single;
            }
            else {
              $single['teamName'] = $awayTeamName;
              $result['ou25fh'][] = $single;
            }
            break;
          case "6":
            if ($teamId1 === $single['teamId']) {
              $single['teamName'] = $homeTeamName;
              $result['ou35fh'][] = $single;
            }
            else {
              $single['teamName'] = $awayTeamName;
              $result['ou35fh'][] = $single;
            }
            break;
          case "7":
            if ($teamId1 === $single['teamId']) {
              $single['teamName'] = $homeTeamName;
              $result['ou15sh'][] = $single;
            }
            else {
              $single['teamName'] = $awayTeamName;
              $result['ou15sh'][] = $single;
            }
            break;
          case "8":
            if ($teamId1 === $single['teamId']) {
              $single['teamName'] = $homeTeamName;
              $result['ou25sh'][] = $single;
            }
            else {
              $single['teamName'] = $awayTeamName;
              $result['ou25sh'][] = $single;
            }
            break;
          case "9":
            if ($teamId1 === $single['teamId']) {
              $single['teamName'] = $homeTeamName;
              $result['ou35sh'][] = $single;
            }
            else {
              $single['teamName'] = $awayTeamName;
              $result['ou35sh'][] = $single;
            }
            break;
        }
      }
    }

    $em->clear();
    return $result;
  }

  public function getStats04($teamId1, $teamId2, $sportId, $season, $homeTeamName, $awayTeamName) {
    $em     = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT
      1 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      0 orderBy,
      ts.h_played played,
      ts.h_goal_goal goalGoal,
      ts.h_no_goal noGoal,
      ts.h_goals_01 goals01,
      ts.h_goals_23 goals23,
      ts.h_goals_46 goals46,
      ts.h_goals_7 goals7,
      ts.h_wins_ht winsHt,
      ts.h_draws_ht drawsHt,
      ts.h_loses_ht losesHt
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId1
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      1 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      1 orderBy,
      ts.a_played played,
      ts.a_goal_goal goalGoal,
      ts.a_no_goal noGoal,
      ts.a_goals_01 goals01,
      ts.a_goals_23 goals23,
      ts.a_goals_46 goals46,
      ts.a_goals_7 goals7,
      ts.a_loses_ht winsHt,
      ts.a_draws_ht drawsHt,
      ts.a_wins_ht losesHt
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId2
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      2 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      0 orderBy,
      ts.h_played + ts.a_played played,
      ts.h_goal_goal + ts.a_goal_goal goalGoal,
      ts.h_no_goal + ts.a_no_goal noGoal,
      ts.h_goals_01 + ts.a_goals_01 goals01,
      ts.h_goals_23 + ts.a_goals_23 goals23,
      ts.h_goals_46 + ts.a_goals_46 goals46,
      ts.h_goals_7 + ts.a_goals_7 goals7,
      ts.h_wins_ht + ts.a_wins_ht winsHt,
      ts.h_draws_ht + ts.a_draws_ht drawsHt,
      ts.h_loses_ht + ts.a_loses_ht losesHt
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId1
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      2 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      1 orderBy,
      ts.h_played + ts.a_played played,
      ts.h_goal_goal + ts.a_goal_goal goalGoal,
      ts.h_no_goal + ts.a_no_goal noGoal,
      ts.h_goals_01 + ts.a_goals_01 goals01,
      ts.h_goals_23 + ts.a_goals_23 goals23,
      ts.h_goals_46 + ts.a_goals_46 goals46,
      ts.h_goals_7 + ts.a_goals_7 goals7,
      ts.h_loses_ht + ts.a_loses_ht winsHt,
      ts.h_draws_ht + ts.a_draws_ht drawsHt,
      ts.h_wins_ht + ts.a_wins_ht losesHt
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId2
    AND lss.sport_id = :sportId AND season = :season
    ORDER BY dummy, orderBy
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":teamId1", $teamId1);
    $stmt->bindValue(":teamId2", $teamId2);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $resultAll = $stmt->fetchAll();

    if ($resultAll) {
      foreach ($resultAll as $single) {
        switch ($single['dummy']) {
          case "1":
            if ($teamId1 === $single['teamId']) {
              $single['teamName'] = $homeTeamName;
              $result['ha'][] = $single;
            }
            else {
              $single['teamName'] = $awayTeamName;
              $result['ha'][] = $single;
            }
            break;
          case "2":
            if ($teamId1 === $single['teamId']) {
              $single['teamName'] = $homeTeamName;
              $result['t'][] = $single;
            }
            else {
              $single['teamName'] = $awayTeamName;
              $result['t'][] = $single;
            }
            break;
        }
      }
    }

    $em->clear();
    return $result;
  }

  public function getStats05($teamId1, $teamId2, $sportId, $season, $homeTeamName, $awayTeamName) {
    $em     = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT
      1 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      0 orderBy,
      ts.h_played played,
      ts.h_win_win winWin,
      ts.h_draw_win drawWin,
      ts.h_lose_win loseWin,
      ts.h_win_draw winDraw,
      ts.h_draw_draw drawDraw,
      ts.h_lose_draw loseDraw,
      ts.h_win_lose winLose,
      ts.h_draw_lose drawLose,
      ts.h_lose_lose loseLose
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId1
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      1 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      1 orderBy,
      ts.a_played played,
      ts.a_win_win winWin,
      ts.a_draw_win drawWin,
      ts.a_lose_win loseWin,
      ts.a_win_draw winDraw,
      ts.a_draw_draw drawDraw,
      ts.a_lose_draw loseDraw,
      ts.a_win_lose winLose,
      ts.a_draw_lose drawLose,
      ts.a_lose_lose loseLose
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId2
    AND lss.sport_id = :sportId AND season = :season
    ORDER BY dummy, orderBy
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":teamId1", $teamId1);
    $stmt->bindValue(":teamId2", $teamId2);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $resultAll = $stmt->fetchAll();

    if ($resultAll) {
      foreach ($resultAll as $single) {
        switch ($single['dummy']) {
          case "1":
            if ($teamId1 === $single['teamId']) {
              $single['teamName'] = $homeTeamName;
              $result['ha'][] = $single;
            }
            else {
              $single['teamName'] = $awayTeamName;
              $result['ha'][] = $single;
            }
            break;
        }
      }
    }

    $em->clear();
    return $result;
  }

  public function getStats06($teamId1, $teamId2, $sportId, $season) {
    $em     = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT
      1 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      0 orderBy,
      'has' tableType,
      ts.h_goals_for goals,
      ts.h_goals_minute_015 goals015,
      IF(ts.h_goals_for <> 0, ROUND((ts.h_goals_minute_015 * 100 / ts.h_goals_for), 0), 0) goals015p,
      ts.h_goals_minute_1630 goals1630,
      IF(ts.h_goals_for <> 0, ROUND((ts.h_goals_minute_1630 * 100 / ts.h_goals_for), 0), 0) goals1630p,
      ts.h_goals_minute_3145 goals3145,
      IF(ts.h_goals_for <> 0, ROUND((ts.h_goals_minute_3145 * 100 / ts.h_goals_for), 0), 0) goals3145p,
      ts.h_goals_minute_4660 goals4660,
      IF(ts.h_goals_for <> 0, ROUND((ts.h_goals_minute_4660 * 100 / ts.h_goals_for), 0), 0) goals4660p,
      ts.h_goals_minute_6175 goals6175,
      IF(ts.h_goals_for <> 0, ROUND((ts.h_goals_minute_6175 * 100 / ts.h_goals_for), 0), 0) goals6175p,
      ts.h_goals_minute_7690 goals7690,
      IF(ts.h_goals_for <> 0, ROUND((ts.h_goals_minute_7690 * 100 / ts.h_goals_for), 0), 0) goals7690p
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId1
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      1 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      1 orderBy,
      'has' tableType,
      ts.a_goals_for,
      ts.a_goals_minute_015 goals015,
      IF(ts.a_goals_for <> 0, ROUND((ts.a_goals_minute_015 * 100 / ts.a_goals_for), 0), 0) goals015p,
      ts.a_goals_minute_1630 goals1630,
      IF(ts.a_goals_for <> 0, ROUND((ts.a_goals_minute_1630 * 100 / ts.a_goals_for), 0), 0) goals1630p,
      ts.a_goals_minute_3145 goals3145,
      IF(ts.a_goals_for <> 0, ROUND((ts.a_goals_minute_3145 * 100 / ts.a_goals_for), 0), 0) goals3145,
      ts.a_goals_minute_4660 goals4660,
      IF(ts.a_goals_for <> 0, ROUND((ts.a_goals_minute_4660 * 100 / ts.a_goals_for), 0), 0) goals4660,
      ts.a_goals_minute_6175 goals6175,
      IF(ts.a_goals_for <> 0, ROUND((ts.a_goals_minute_6175 * 100 / ts.a_goals_for), 0), 0) goals6175,
      ts.a_goals_minute_7690 goals7690,
      IF(ts.a_goals_for <> 0, ROUND((ts.a_goals_minute_7690 * 100 / ts.a_goals_for), 0), 0) goals7690
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId2
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      2 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      0 orderBy,
      'ts' tableType,
      ts.h_goals_for + ts.a_goals_for goals,
      ts.h_goals_minute_015 + ts.a_goals_minute_015 goals015,
      IF((ts.h_goals_for + ts.a_goals_for) <> 0, ROUND(((ts.h_goals_minute_015 + ts.a_goals_minute_015) * 100 / (ts.h_goals_for + ts.a_goals_for)), 0), 0) goals015p,
      ts.h_goals_minute_1630 + ts.a_goals_minute_1630 goals1630,
      IF((ts.h_goals_for + ts.a_goals_for) <> 0, ROUND(((ts.h_goals_minute_1630 + ts.a_goals_minute_1630) * 100 / (ts.h_goals_for + ts.a_goals_for)), 0), 0) goals1630p,
      ts.h_goals_minute_3145 + ts.a_goals_minute_3145 goals3145,
      IF((ts.h_goals_for + ts.a_goals_for) <> 0, ROUND(((ts.h_goals_minute_3145 + ts.a_goals_minute_3145) * 100 / (ts.h_goals_for + ts.a_goals_for)), 0), 0) goals3145p,
      ts.h_goals_minute_4660 + ts.a_goals_minute_4660 goals4660,
      IF((ts.h_goals_for + ts.a_goals_for) <> 0, ROUND(((ts.h_goals_minute_4660 + ts.a_goals_minute_4660) * 100 / (ts.h_goals_for + ts.a_goals_for)), 0), 0) goals4660p,
      ts.h_goals_minute_6175 + ts.a_goals_minute_6175 goals6175,
      IF((ts.h_goals_for + ts.a_goals_for) <> 0, ROUND(((ts.h_goals_minute_6175 + ts.a_goals_minute_6175) * 100 / (ts.h_goals_for + ts.a_goals_for)), 0), 0) goals6175p,
      ts.h_goals_minute_7690 + ts.a_goals_minute_7690 goals7690,
      IF((ts.h_goals_for + ts.a_goals_for) <> 0, ROUND(((ts.h_goals_minute_7690 + ts.a_goals_minute_7690) * 100 / (ts.h_goals_for + ts.a_goals_for)), 0), 0) goals7690p
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId1
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      2 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      1 orderBy,
      'ts' tableType,
      ts.h_goals_for + ts.a_goals_for goals,
      ts.h_goals_minute_015 + ts.a_goals_minute_015 goals015,
      IF((ts.h_goals_for + ts.a_goals_for) <> 0, ROUND(((ts.h_goals_minute_015 + ts.a_goals_minute_015) * 100 / (ts.h_goals_for + ts.a_goals_for)), 0), 0) goals015p,
      ts.h_goals_minute_1630 + ts.a_goals_minute_1630 goals1630,
      IF((ts.h_goals_for + ts.a_goals_for) <> 0, ROUND(((ts.h_goals_minute_1630 + ts.a_goals_minute_1630) * 100 / (ts.h_goals_for + ts.a_goals_for)), 0), 0) goals1630p,
      ts.h_goals_minute_3145 + ts.a_goals_minute_3145 goals3145,
      IF((ts.h_goals_for + ts.a_goals_for) <> 0, ROUND(((ts.h_goals_minute_3145 + ts.a_goals_minute_3145) * 100 / (ts.h_goals_for + ts.a_goals_for)), 0), 0) goals3145p,
      ts.h_goals_minute_4660 + ts.a_goals_minute_4660 goals4660,
      IF((ts.h_goals_for + ts.a_goals_for) <> 0, ROUND(((ts.h_goals_minute_4660 + ts.a_goals_minute_4660) * 100 / (ts.h_goals_for + ts.a_goals_for)), 0), 0) goals4660p,
      ts.h_goals_minute_6175 + ts.a_goals_minute_6175 goals6175,
      IF((ts.h_goals_for + ts.a_goals_for) <> 0, ROUND(((ts.h_goals_minute_6175 + ts.a_goals_minute_6175) * 100 / (ts.h_goals_for + ts.a_goals_for)), 0), 0) goals6175p,
      ts.h_goals_minute_7690 + ts.a_goals_minute_7690 goals7690,
      IF((ts.h_goals_for + ts.a_goals_for) <> 0, ROUND(((ts.h_goals_minute_7690 + ts.a_goals_minute_7690) * 100 / (ts.h_goals_for + ts.a_goals_for)), 0), 0) goals7690p
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId2
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      3 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      0 orderBy,
      'hac' tableType,
      ts.h_goals_against goals,
      ts.h_goals_minute_015_conceded goals015,
      IF((ts.h_goals_against) <> 0, ROUND(((ts.h_goals_minute_015_conceded) * 100 / (ts.h_goals_against)), 0), 0) goals015p,
      ts.h_goals_minute_1630_conceded goals1630,
      IF((ts.h_goals_against) <> 0, ROUND(((ts.h_goals_minute_1630_conceded) * 100 / (ts.h_goals_against)), 0), 0) goals1630p,
      ts.h_goals_minute_3145_conceded goals3145,
      IF((ts.h_goals_against) <> 0, ROUND(((ts.h_goals_minute_3145_conceded) * 100 / (ts.h_goals_against)), 0), 0) goals3145p,
      ts.h_goals_minute_4660_conceded goals4660,
      IF((ts.h_goals_against) <> 0, ROUND(((ts.h_goals_minute_4660_conceded) * 100 / (ts.h_goals_against)), 0), 0) goals4660p,
      ts.h_goals_minute_6175_conceded goals6175,
      IF((ts.h_goals_against) <> 0, ROUND(((ts.h_goals_minute_6175_conceded) * 100 / (ts.h_goals_against)), 0), 0) goals6175p,
      ts.h_goals_minute_7690_conceded goals7690,
      IF((ts.h_goals_against) <> 0, ROUND(((ts.h_goals_minute_7690_conceded) * 100 / (ts.h_goals_against)), 0), 0) goals7690p
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId1
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      3 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      1 orderBy,
      'hac' tableType,
      ts.a_goals_against,
      ts.a_goals_minute_015_conceded goals015,
      IF((ts.a_goals_against) <> 0, ROUND(((ts.a_goals_minute_015_conceded) * 100 / (ts.a_goals_against)), 0), 0) goals015p,
      ts.a_goals_minute_1630_conceded goals1630,
      IF((ts.a_goals_against) <> 0, ROUND(((ts.a_goals_minute_1630_conceded) * 100 / (ts.a_goals_against)), 0), 0) goals1630p,
      ts.a_goals_minute_3145_conceded goals3145,
      IF((ts.a_goals_against) <> 0, ROUND(((ts.a_goals_minute_3145_conceded) * 100 / (ts.a_goals_against)), 0), 0) goals3145p,
      ts.a_goals_minute_4660_conceded goals4660,
      IF((ts.a_goals_against) <> 0, ROUND(((ts.a_goals_minute_4660_conceded) * 100 / (ts.a_goals_against)), 0), 0) goals4660p,
      ts.a_goals_minute_6175_conceded goals6175,
      IF((ts.a_goals_against) <> 0, ROUND(((ts.a_goals_minute_6175_conceded) * 100 / (ts.a_goals_against)), 0), 0) goals6175p,
      ts.a_goals_minute_7690_conceded goals7690,
      IF((ts.a_goals_against) <> 0, ROUND(((ts.a_goals_minute_7690_conceded) * 100 / (ts.a_goals_against)), 0), 0) goals7690p
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId2
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      4 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      0 orderBy,
      'tc' tableType,
      ts.h_goals_against + ts.a_goals_against goals,
      ts.h_goals_minute_015_conceded + ts.a_goals_minute_015_conceded goals015,
      IF((ts.h_goals_against + ts.a_goals_against) <> 0, ROUND(((ts.h_goals_minute_015_conceded + ts.a_goals_minute_015_conceded) * 100 / (ts.h_goals_against + ts.a_goals_against)), 0), 0) goals015p,
      ts.h_goals_minute_1630_conceded + ts.a_goals_minute_1630_conceded goals1630,
      IF((ts.h_goals_against + ts.a_goals_against) <> 0, ROUND(((ts.h_goals_minute_1630_conceded + ts.a_goals_minute_1630_conceded) * 100 / (ts.h_goals_against + ts.a_goals_against)), 0), 0) goals1630p,
      ts.h_goals_minute_3145_conceded + ts.a_goals_minute_3145_conceded goals3145,
      IF((ts.h_goals_against + ts.a_goals_against) <> 0, ROUND(((ts.h_goals_minute_3145_conceded + ts.a_goals_minute_3145_conceded) * 100 / (ts.h_goals_against + ts.a_goals_against)), 0), 0) goals3145p,
      ts.h_goals_minute_4660_conceded + ts.a_goals_minute_4660_conceded goals4660,
      IF((ts.h_goals_against + ts.a_goals_against) <> 0, ROUND(((ts.h_goals_minute_4660_conceded + ts.a_goals_minute_4660_conceded) * 100 / (ts.h_goals_against + ts.a_goals_against)), 0), 0) goals4660p,
      ts.h_goals_minute_6175_conceded + ts.a_goals_minute_6175_conceded goals6175,
      IF((ts.h_goals_against + ts.a_goals_against) <> 0, ROUND(((ts.h_goals_minute_6175_conceded + ts.a_goals_minute_6175_conceded) * 100 / (ts.h_goals_against + ts.a_goals_against)), 0), 0) goals6175p,
      ts.h_goals_minute_7690_conceded + ts.h_goals_minute_7690_conceded goals7690,
      IF((ts.h_goals_against + ts.a_goals_against) <> 0, ROUND(((ts.h_goals_minute_7690_conceded + ts.a_goals_minute_7690_conceded) * 100 / (ts.h_goals_against + ts.a_goals_against)), 0), 0) goals7690p
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId1
    AND lss.sport_id = :sportId AND season = :season
    UNION ALL
    SELECT
      4 dummy, ts.team_id teamId,
      (SELECT t.name_gr FROM ft_team t WHERE t.team_id = ts.team_id) teamName,
      1 orderBy,
      'tc' tableType,
      ts.h_goals_against + ts.a_goals_against goals,
      ts.h_goals_minute_015_conceded + ts.a_goals_minute_015_conceded goals015,
      IF((ts.h_goals_against + ts.a_goals_against) <> 0, ROUND(((ts.h_goals_minute_015_conceded + ts.a_goals_minute_015_conceded) * 100 / (ts.h_goals_against + ts.a_goals_against)), 0), 0) goals015p,
      ts.h_goals_minute_1630_conceded + ts.a_goals_minute_1630_conceded goals1630,
      IF((ts.h_goals_against + ts.a_goals_against) <> 0, ROUND(((ts.h_goals_minute_1630_conceded + ts.a_goals_minute_1630_conceded) * 100 / (ts.h_goals_against + ts.a_goals_against)), 0), 0) goals1630p,
      ts.h_goals_minute_3145_conceded + ts.a_goals_minute_3145_conceded goals3145,
      IF((ts.h_goals_against + ts.a_goals_against) <> 0, ROUND(((ts.h_goals_minute_3145_conceded + ts.a_goals_minute_3145_conceded) * 100 / (ts.h_goals_against + ts.a_goals_against)), 0), 0) goals3145p,
      ts.h_goals_minute_4660_conceded + ts.a_goals_minute_4660_conceded goals4660,
      IF((ts.h_goals_against + ts.a_goals_against) <> 0, ROUND(((ts.h_goals_minute_4660_conceded + ts.a_goals_minute_4660_conceded) * 100 / (ts.h_goals_against + ts.a_goals_against)), 0), 0) goals4660p,
      ts.h_goals_minute_6175_conceded + ts.a_goals_minute_6175_conceded goals6175,
      IF((ts.h_goals_against + ts.a_goals_against) <> 0, ROUND(((ts.h_goals_minute_6175_conceded + ts.a_goals_minute_6175_conceded) * 100 / (ts.h_goals_against + ts.a_goals_against)), 0), 0) goals6175p,
      ts.h_goals_minute_7690_conceded + ts.h_goals_minute_7690_conceded goals7690,
      IF((ts.h_goals_against + ts.a_goals_against) <> 0, ROUND(((ts.h_goals_minute_7690_conceded + ts.a_goals_minute_7690_conceded) * 100 / (ts.h_goals_against + ts.a_goals_against)), 0), 0) goals7690p
    FROM team_stats ts
      INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        ts.team_id = :teamId2
    AND lss.sport_id = :sportId AND season = :season
    ORDER BY dummy, orderBy
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":teamId1", $teamId1);
    $stmt->bindValue(":teamId2", $teamId2);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $resultAll = $stmt->fetchAll();

    if ($resultAll) {
      foreach ($resultAll as $key => $single) {
        $tableType = $single['tableType'];

        $result[$tableType]['rows']['015']['label']  = "0-15'";
        $result[$tableType]['rows']['1630']['label'] = "16-30'";
        $result[$tableType]['rows']['3145']['label'] = "31-45'";
        $result[$tableType]['rows']['4660']['label'] = "46-60'";
        $result[$tableType]['rows']['6175']['label'] = "61-75'";
        $result[$tableType]['rows']['7690']['label'] = "76-90'";
        if ($teamId1 === $single['teamId']) {
          $result[$tableType]['homeGoals'] = $single['goals'];
          $result[$tableType]['rows']['015']['goals1']    = $single['goals015'].'/'.$single['goals'];
          $result[$tableType]['rows']['015']['goals1p']   = (int) $single['goals015p'];
          $result[$tableType]['rows']['1630']['goals1']   = $single['goals1630'].'/'.$single['goals'];
          $result[$tableType]['rows']['1630']['goals1p']  = (int) $single['goals1630p'];
          $result[$tableType]['rows']['3145']['goals1']   = $single['goals3145'].'/'.$single['goals'];
          $result[$tableType]['rows']['3145']['goals1p']  = (int) $single['goals3145p'];
          $result[$tableType]['rows']['4660']['goals1']   = $single['goals4660'].'/'.$single['goals'];
          $result[$tableType]['rows']['4660']['goals1p']  = (int) $single['goals4660p'];
          $result[$tableType]['rows']['6175']['goals1']   = $single['goals6175'].'/'.$single['goals'];
          $result[$tableType]['rows']['6175']['goals1p']  = (int) $single['goals6175p'];
          $result[$tableType]['rows']['7690']['goals1']   = $single['goals7690'].'/'.$single['goals'];
          $result[$tableType]['rows']['7690']['goals1p']  = (int) $single['goals7690p'];
        }
        else {
          $result[$tableType]['awayGoals'] = $single['goals'];
          $result[$tableType]['rows']['015']['goals2']    = $single['goals015'].'/'.$single['goals'];
          $result[$tableType]['rows']['015']['goals2p']   = (int) $single['goals015p'];
          $result[$tableType]['rows']['1630']['goals2']   = $single['goals1630'].'/'.$single['goals'];
          $result[$tableType]['rows']['1630']['goals2p']  = (int) $single['goals1630p'];
          $result[$tableType]['rows']['3145']['goals2']   = $single['goals3145'].'/'.$single['goals'];
          $result[$tableType]['rows']['3145']['goals2p']  = (int) $single['goals3145p'];
          $result[$tableType]['rows']['4660']['goals2']   = $single['goals4660'].'/'.$single['goals'];
          $result[$tableType]['rows']['4660']['goals2p']  = (int) $single['goals4660p'];
          $result[$tableType]['rows']['6175']['goals2']   = $single['goals6175'].'/'.$single['goals'];
          $result[$tableType]['rows']['6175']['goals2p']  = (int) $single['goals6175p'];
          $result[$tableType]['rows']['7690']['goals2']   = $single['goals7690'].'/'.$single['goals'];
          $result[$tableType]['rows']['7690']['goals2p']  = (int) $single['goals7690p'];
        }
      }
    }

    $em->clear();
    return $result;
  }

  /**
   * Returns the scorers of the requested two teams, sport and season
   */
  public function getStats07($teamId1, $teamId2, $sportId, $season) {
    $em = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT temp.team_id teamId, temp.player_name playerName, temp.goals, temp.scorer_first scorerFirst, temp.penalties, temp.goalsFor,
    IF(temp.goalsFor <> 0, ROUND(temp.goals * 100 / temp.goalsFor, 0), 0) goalsP
    FROM (
    SELECT
      sco.team_id,
      sco.player_name,
      sco.goals,
      sco.scorer_first,
      sco.penalties,
      (SELECT ts.h_goals_for + ts.a_goals_for FROM team_stats ts WHERE ts.league_season_id = lss.id AND ts.team_id = sco.team_id) goalsFor
    FROM
      ft_league_season_scorer sco
        LEFT JOIN ft_league_season_sport lss ON lss.sport_id = sco.sport_id AND lss.season = sco.season
    WHERE
        (sco.team_id = :teamId1 OR sco.team_id = :teamId2)
    AND sco.sport_id = :sportId
    AND sco.season = :season
    ORDER BY goals DESC
    LIMIT 6
    ) temp
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":teamId1", $teamId1);
    $stmt->bindValue(":teamId2", $teamId2);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $result = $stmt->fetchAll();

    $em->clear();
    return $result;
  }

  public function getStats08($teamId1, $teamId2, $sportId, $season, $translations) {
    $em = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT
      0 orderBy,
      ts.team_id teamId,
      ts.h_goals_for_streak homeGoalsForStreak,
      ts.t_goals_for_streak totalGoalsForStreak,
      ts.h_goals_against_streak homeGoalsAgainstStreak,
      ts.t_goals_against_streak totalGoalsAgainstStreak,
      ts.h_not_scored_streak homeNotScoredStreak,
      ts.t_not_scored_streak totalNotScoredStreak,
      ts.h_not_conceded_streak homeNotConcededStreak,
      ts.t_not_conceded_streak totalNotConcededStreak,
      ts.h_win_streak homeWinStreak,
      ts.t_win_streak totalWinStreak,
      ts.h_no_lose_streak homeNoLoseStreak,
      ts.t_no_lose_streak totalNoLoseStreak,
      ts.h_loses_streak homeLosesStreak,
      ts.t_lose_streak totalLosesStreak,
      ts.h_no_win_streak homeNoWinStreak,
      ts.t_no_win_streak totalNoWinStreak
    FROM
      team_stats ts
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        (ts.team_id = :teamId1)
    AND lss.sport_id = :sportId
    AND lss.season = :season
    UNION ALL
    SELECT
      1 orderBy,
      ts.team_id teamId,
      ts.a_goals_for_streak homeGoalsForStreak,
      ts.t_goals_for_streak totalGoalsForStreak,
      ts.a_goals_against_streak homeGoalsAgainstStreak,
      ts.t_goals_against_streak totalGoalsAgainstStreak,
      ts.a_not_scored_streak homeNotScoredStreak,
      ts.t_not_scored_streak totalNotScoredStreak,
      ts.a_not_conceded_streak homeNotConcededStreak,
      ts.t_not_conceded_streak totalNotConcededStreak,
      ts.a_win_streak homeWinStreak,
      ts.t_win_streak totalWinStreak,
      ts.a_no_lose_streak homeNoLoseStreak,
      ts.t_no_lose_streak totalNoLoseStreak,
      ts.a_loses_streak homeLosesStreak,
      ts.t_lose_streak totalLosesStreak,
      ts.a_no_win_streak homeNoWinStreak,
      ts.t_no_win_streak totalNoWinStreak
    FROM
      team_stats ts
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
    WHERE
        (ts.team_id = :teamId2)
    AND lss.sport_id = :sportId
    AND lss.season = :season
    ORDER BY orderBy
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":teamId1", $teamId1);
    $stmt->bindValue(":teamId2", $teamId2);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $resultAll = $stmt->fetchAll();

    if ($resultAll) {
      foreach ($resultAll as $key => $single) {
        $result[0]['label'] = $translations['scoring_in_the_last'];
        $result[1]['label'] = $translations['goals_against'];
        $result[2]['label'] = $translations['failed_to_score'];
        $result[3]['label'] = $translations['clean_sheet'];
        $result[4]['label'] = $translations['winning'];
        $result[5]['label'] = $translations['unbeaten'];
        $result[6]['label'] = $translations['losing'];
        $result[7]['label'] = $translations['winless'];

        if ($teamId1 === $single['teamId']) {
          $result[0]['home']  = (int) $single['homeGoalsForStreak'];
          $result[0]['htotal'] = (int) $single['totalGoalsForStreak'];
          $result[1]['home'] = (int) $single['homeGoalsAgainstStreak'];
          $result[1]['htotal'] = (int) $single['totalGoalsAgainstStreak'];
          $result[2]['home'] = (int) $single['homeNotScoredStreak'];
          $result[2]['htotal'] = (int) $single['totalNotScoredStreak'];
          $result[3]['home'] = (int) $single['homeNotConcededStreak'];
          $result[3]['htotal'] = (int) $single['totalNotConcededStreak'];
          $result[4]['home'] = (int) $single['homeWinStreak'];
          $result[4]['htotal'] = (int) $single['totalWinStreak'];
          $result[5]['home'] = (int) $single['homeNoLoseStreak'];
          $result[5]['htotal'] = (int) $single['totalNoLoseStreak'];
          $result[6]['home'] = (int) $single['homeLosesStreak'];
          $result[6]['htotal'] = (int) $single['totalLosesStreak'];
          $result[7]['home'] = (int) $single['homeNoWinStreak'];
          $result[7]['htotal'] = (int) $single['totalNoWinStreak'];
        }
        elseif ($teamId2 === $single['teamId']) {
          $result[0]['away'] = (int) $single['homeGoalsForStreak'];
          $result[0]['atotal'] = (int) $single['totalGoalsForStreak'];
          $result[1]['away'] = (int) $single['homeGoalsAgainstStreak'];
          $result[1]['atotal'] = (int) $single['totalGoalsAgainstStreak'];
          $result[2]['away'] = (int) $single['homeNotScoredStreak'];
          $result[2]['atotal'] = (int) $single['totalNotScoredStreak'];
          $result[3]['away'] = (int) $single['homeNotConcededStreak'];
          $result[3]['atotal'] = (int) $single['totalNotConcededStreak'];
          $result[4]['away'] = (int) $single['homeWinStreak'];
          $result[4]['atotal'] = (int) $single['totalWinStreak'];
          $result[5]['away'] = (int) $single['homeNoLoseStreak'];
          $result[5]['atotal'] = (int) $single['totalNoLoseStreak'];
          $result[6]['away'] = (int) $single['homeLosesStreak'];
          $result[6]['atotal'] = (int) $single['totalLosesStreak'];
          $result[7]['away'] = (int) $single['homeNoWinStreak'];
          $result[7]['atotal'] = (int) $single['totalNoWinStreak'];
        }
      }
    }

    $em->clear();
    return $result;
  }

  public function getStats09($teamId1, $teamId2, $sportId, $season, $locale, $localeFallback) {
    $em = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT 
      ls.rank,
      ls.team_id teamId,
      ls.overall_pts points,
      ls.overall_p played,
      ls.overall_w wins,
      ls.overall_d draws,
      ls.overall_l loses,
      CONCAT(ls.overall_gf, '-', ls.overall_ga) goals,
      tmp.teamName,
      tmp.matchForm,
      tmp.overUnderForm,
      (SELECT lsi.option_id FROM ft_league_standings_info lsi WHERE FIND_IN_SET(ls.rank, lsi.value) > 0 AND lsi.sport_id = :sportId AND lsi.season = :season) legend,
      (SELECT lso.name_el FROM ft_league_standings_option lso WHERE lso.id = legend) legendValue
    FROM ft_league_standings ls,
    (
    SELECT
      ls.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) teamName,
      SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN tf.h_team = ls.team_id THEN CASE WHEN tf.ft1 - tf.ft2 > 0 THEN 'w' WHEN tf.ft1 - tf.ft2 = 0 THEN 'd' ELSE 'l' END
           WHEN tf.a_team = ls.team_id THEN CASE WHEN tf.ft1 - tf.ft2 > 0 THEN 'l' WHEN tf.ft1 - tf.ft2 = 0 THEN 'd' ELSE 'w' END 
           ELSE '' END ORDER BY tf.match_datetime DESC), ',', 6) matchForm,
      SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN tf.ft1 + tf.ft2 > 2 THEN 'o' WHEN tf.ft1 + tf.ft2 <= 2 THEN 'u' ELSE '' END ORDER BY tf.match_datetime DESC), ',', 6) overUnderForm
    FROM
      ft_league_standings ls
        LEFT JOIN ft_team t ON t.team_id = ls.team_id
        LEFT JOIN (
          SELECT
            f.match_datetime, f.h_team, f.a_team, f.ft1, f.ft2
          FROM ft_fixture f
          WHERE
              f.sport_id = :sportId
          AND f.season = :season
          AND f.status IN ('F', 'FE', 'FP')
        ) tf ON (tf.h_team = ls.team_id OR tf.a_team = ls.team_id)
    WHERE
        ls.sport_id = :sportId
    AND ls.season = :season
    GROUP BY ls.team_id) tmp
    WHERE tmp.team_id = ls.team_id
    AND ls.season = :season
    AND ls.sport_id = :sportId
    ORDER BY `rank`
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $resultAll = $stmt->fetchAll();

    $em->clear();
    return $resultAll;
  }

  public function getStats10($teamId1, $teamId2, $sportId, $season, $locale, $localeFallback) {
    $em = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT temp.orderBy, temp.matchDateTime tempDate, DATE_FORMAT(temp.matchDateTime, '%d.%m.%y') matchDateTime, matchName
    FROM (
    (SELECT
      1 orderBy, f1.match_datetime matchDateTime, CONCAT((SELECT COALESCE(t.name_$locale, t.name_$localeFallback) FROM ft_team t WHERE t.team_id = f1.h_team), ' - ', (SELECT COALESCE(t.name_$locale, t.name_$localeFallback) FROM ft_team t WHERE t.team_id = f1.a_team)) matchName
    FROM
        ft_fixture f1
    WHERE
        f1.match_datetime > (
          SELECT f.match_datetime FROM ft_fixture f WHERE f.h_team = :teamId1 AND f.a_team = :teamId2 AND f.sport_id = :sportId AND f.season = :season ORDER BY f.match_datetime DESC LIMIT 1
          )
    AND (f1.h_team = :teamId1 OR f1.a_team = :teamId1) AND f1.sport_id = :sportId AND f1.season = :season
    LIMIT 6)
    UNION ALL
    (SELECT
      2 orderBy, f2.match_datetime matchDateTime, CONCAT((SELECT COALESCE(t.name_$locale, t.name_$localeFallback) FROM ft_team t WHERE t.team_id = f2.h_team), ' - ', (SELECT COALESCE(t.name_$locale, t.name_$localeFallback) FROM ft_team t WHERE t.team_id = f2.a_team)) matchName
    FROM
      ft_fixture f2
    WHERE
        f2.match_datetime > (
          SELECT f.match_datetime FROM ft_fixture f WHERE f.h_team = :teamId1 AND f.a_team = :teamId2 AND f.sport_id = :sportId AND f.season = :season ORDER BY f.match_datetime DESC LIMIT 1
          )
    AND (f2.h_team = :teamId2 OR f2.a_team = :teamId2) AND f2.sport_id = :sportId AND f2.season = :season
    LIMIT 6)
    ) temp
    ORDER BY orderBy, tempDate
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":teamId1", $teamId1);
    $stmt->bindValue(":teamId2", $teamId2);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $resultAll = $stmt->fetchAll();

    if ($resultAll) {
      $counter = 0;
      foreach ($resultAll as $key => $single) {
        if (1 === (int) $single['orderBy']) {
          $result[$key]['matchDateTime1'] = $single['matchDateTime'];
          $result[$key]['matchName1'] = $single['matchName'];

          $counter++;
        }
        else {
          $result[$key - $counter]['matchDateTime2'] = $single['matchDateTime'];
          $result[$key - $counter]['matchName2'] = $single['matchName'];
        }
      }
    }

    $em->clear();
    return $result;
  }

  public function getStats11($teamId1, $teamId2, $languageCode, $languageCodeFallback, $locale) {
    $em         = $this->getEntityManager();
    $result     = array();
    $resultAll  = null;

    $sql = "
    (SELECT 
    0 homeTeam,
    DATE_FORMAT(f.match_datetime, '%d.%m.%y %H:%i') match_datetime,
    IFNULL(st.title, '') competition_name,
    CASE
      WHEN (f.h_team = :homeTeamId) THEN CASE WHEN f.ft1 - f.ft2 > 0 THEN 'win' WHEN f.ft1 - f.ft2 = 0 THEN 'draw' WHEN f.ft1 - f.ft2 < 0 THEN 'lose' END
      WHEN (f.a_team = :homeTeamId) THEN CASE WHEN f.ft1 - f.ft2 > 0 THEN 'lose' WHEN f.ft1 - f.ft2 = 0 THEN 'draw' WHEN f.ft1 - f.ft2 < 0 THEN 'win' END
    END match_form,
    IF(f.h_team = :homeTeamId, (SELECT COALESCE(t1.name_$languageCode, t1.name_$languageCodeFallback) FROM ft_team t1 WHERE (t1.team_id = f.h_team OR t1.team_id = f.a_team) AND t1.team_id = :homeTeamId), (SELECT COALESCE(t2.name_$languageCode, t2.name_$languageCodeFallback) FROM ft_team t2 WHERE t2.team_id <> :homeTeamId AND (f.h_team = t2.team_id OR f.a_team = t2.team_id))) home_team_name,
    IF(f.a_team = :homeTeamId, (SELECT COALESCE(t1.name_$languageCode, t1.name_$languageCodeFallback) FROM ft_team t1 WHERE (t1.team_id = f.h_team OR t1.team_id = f.a_team) AND t1.team_id = :homeTeamId), (SELECT COALESCE(t2.name_$languageCode, t2.name_$languageCodeFallback) FROM ft_team t2 WHERE t2.team_id <> :homeTeamId AND (f.h_team = t2.team_id OR f.a_team = t2.team_id))) away_team_name,
    f.ht1,
    f.ht2,
    f.ft1,
    f.ft2
  FROM ft_fixture f 
    LEFT JOIN sport s ON s.id = f.sport_id
    LEFT JOIN sport_translations st ON st.sport_id = s.id AND st.type = 'NORMAL' AND st.locale = '$locale'
  WHERE
      (f.h_team = :homeTeamId OR f.a_team = :homeTeamId)
  AND f.status IN ('F', 'FE', 'FP' )
  ORDER BY f.match_datetime DESC
  LIMIT 6
  )
  UNION ALL
  (SELECT 
    1 homeTeam,
    DATE_FORMAT(f.match_datetime, '%d.%m.%y %H:%i') match_datetime,
    IFNULL(st.title, '') competition_name,
    CASE 
      WHEN (f.h_team = :awayTeamId) THEN CASE WHEN f.ft1 - f.ft2 > 0 THEN 'win' WHEN f.ft1 - f.ft2 = 0 THEN 'draw' WHEN f.ft1 - f.ft2 < 0 THEN 'lose' END
      WHEN (f.a_team = :awayTeamId) THEN CASE WHEN f.ft1 - f.ft2 > 0 THEN 'lose' WHEN f.ft1 - f.ft2 = 0 THEN 'draw' WHEN f.ft1 - f.ft2 < 0 THEN 'win' END
    END match_form,
    IF(f.h_team = :awayTeamId, (SELECT COALESCE(t1.name_$languageCode, t1.name_$languageCodeFallback) FROM ft_team t1 WHERE (t1.team_id = f.h_team OR t1.team_id = f.a_team) AND t1.team_id = :awayTeamId), (SELECT COALESCE(t2.name_$languageCode, t2.name_$languageCodeFallback) FROM ft_team t2 WHERE (t2.team_id = f.h_team OR t2.team_id = f.a_team) AND t2.team_id <> :awayTeamId)) home_team_name,
    IF(f.a_team = :awayTeamId, (SELECT COALESCE(t1.name_$languageCode, t1.name_$languageCodeFallback) FROM ft_team t1 WHERE (t1.team_id = f.h_team OR t1.team_id = f.a_team) AND t1.team_id = :awayTeamId), (SELECT COALESCE(t2.name_$languageCode, t2.name_$languageCodeFallback) FROM ft_team t2 WHERE (t2.team_id = f.h_team OR t2.team_id = f.a_team) AND t2.team_id <> :awayTeamId)) away_team_name,
    f.ht1,
    f.ht2,
    f.ft1,
    f.ft2
  FROM ft_fixture f 
    LEFT JOIN sport s ON s.id = f.sport_id
    LEFT JOIN sport_translations st ON st.sport_id = s.id AND st.type = 'NORMAL' AND st.locale = '$locale'
  WHERE
      (f.h_team = :awayTeamId OR f.a_team = :awayTeamId)
  AND f.status IN ('F', 'FE', 'FP' )
  ORDER BY f.match_datetime DESC
  LIMIT 6)
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":homeTeamId", $teamId1);
    $stmt->bindValue(":awayTeamId", $teamId2);
    $stmt->execute();
    $resultAll = $stmt->fetchAll();

    if ($resultAll) {
      foreach ($resultAll as $single) {
        if (0 === (int) $single['homeTeam']) {
          $result['homeTeam'][] = array(
            'matchDateTime'   => $single['match_datetime'],
            'competitionName' => $single['competition_name'],
            'type'            => $single['match_form'],
            'homeTeamName'    => $single['home_team_name'],
            'awayTeamName'    => $single['away_team_name'],
            'ht1'             => $single['ht1'],
            'ht2'             => $single['ht2'],
            'ft1'             => $single['ft1'],
            'ft2'             => $single['ft2']
          );
        }
        else {
          $result['awayTeam'][] = array(
            'matchDateTime'   => $single['match_datetime'],
            'competitionName' => $single['competition_name'],
            'type'            => $single['match_form'],
            'homeTeamName'    => $single['home_team_name'],
            'awayTeamName'    => $single['away_team_name'],
            'ht1'             => $single['ht1'],
            'ht2'             => $single['ht2'],
            'ft1'             => $single['ft1'],
            'ft2'             => $single['ft2']
          );
        }

      }
    }

    $em->clear();
    return $result;
  }

  public function getMostWins($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.h_wins+a_wins wins
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY h_wins+a_wins DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $mostWins = $stmt->fetchAll();

    $result = array();
    if ($mostWins) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'wins' => $translations[3]);
      $result['data'] = $mostWins;
    }

    return $result;
  }

  public function getMostDraws($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.h_draws+a_draws draws
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY h_draws+a_draws DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $mostDraws = $stmt->fetchAll();

    $result = array();
    if ($mostDraws) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'draws' => $translations[3]);
      $result['data'] = $mostDraws;
    }

    return $result;
  }

  public function getMostLoses($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.h_loses+ts.a_loses loses
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY ts.h_loses+ts.a_loses DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $mostLoses = $stmt->fetchAll();

    $result = array();
    if ($mostLoses) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'loses' => $translations[3]);
      $result['data'] = $mostLoses;
    }

    return $result;
  }

  public function getWinStreak($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.t_win_streak winStreak
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY ts.t_win_streak DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $winStreak = $stmt->fetchAll();

    $result = array();
    if ($winStreak) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'winStreak' => '');
      $result['data'] = $winStreak;
    }

    return $result;
  }

  public function getDrawStreak($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.t_draw_streak drawStreak
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY ts.t_draw_streak DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $drawStreak = $stmt->fetchAll();

    $result = array();
    if ($drawStreak) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'drawStreak' => '');
      $result['data'] = $drawStreak;
    }

    return $result;
  }

  public function getLossStreak($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.t_lose_streak lossStreak
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY ts.t_lose_streak DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $lossStreak = $stmt->fetchAll();

    $result = array();
    if ($lossStreak) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'lossStreak' => '');
      $result['data'] = $lossStreak;
    }

    return $result;
  }

  public function getNoWinStreak($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.t_no_win_streak noWinStreak
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY ts.t_no_win_streak DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $noWinStreak = $stmt->fetchAll();

    $result = array();
    if ($noWinStreak) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'noWinStreak' => '');
      $result['data'] = $noWinStreak;
    }

    return $result;
  }

  public function getNoLossStreak($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.t_no_lose_streak noLossStreak
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY ts.t_no_lose_streak DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $noLossStreak = $stmt->fetchAll();

    $result = array();
    if ($noLossStreak) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'noLossStreak' => '');
      $result['data'] = $noLossStreak;
    }

    return $result;
  }

  public function getOver25($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.h_over_25+ts.a_over_25 over25
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY ts.h_over_25+ts.a_over_25 DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $over25 = $stmt->fetchAll();

    $result = array();
    if ($over25) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'over25' => '');
      $result['data'] = $over25;
    }

    return $result;
  }

  public function getUnder25($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.h_under_25+ts.a_under_25 under25
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY ts.h_under_25+ts.a_under_25 DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $under25 = $stmt->fetchAll();

    $result = array();
    if ($under25) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'under25' => '');
      $result['data'] = $under25;
    }

    return $result;
  }

  public function getGoalGoal($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.h_goal_goal+ts.a_goal_goal goalGoal
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY ts.h_goal_goal+ts.a_goal_goal DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $goalGoal = $stmt->fetchAll();

    $result = array();
    if ($goalGoal) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'goalGoal' => '');
      $result['data'] = $goalGoal;
    }

    return $result;
  }

  public function getNoGoal($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.h_no_goal+ts.a_no_goal noGoal
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY ts.h_no_goal+ts.a_no_goal DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $noGoal = $stmt->fetchAll();

    $result = array();
    if ($noGoal) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'noGoal' => '');
      $result['data'] = $noGoal;
    }

    return $result;
  }

  public function getYellowCards($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.t_yellow_cards totalYellowCards
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY ts.t_yellow_cards DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $yellowCards = $stmt->fetchAll();

    $result = array();
    if ($yellowCards) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'totalYellowCards' => '');
      $result['data'] = $yellowCards;
    }

    return $result;
  }

  public function getRedCards($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT (@cnt := @cnt + 1) AS rowNumber, tbl.*
      FROM (
      SELECT ts.team_id, COALESCE(t.name_$locale, t.name_$localeFallback) namegr, ts.t_red_cards totalRedCards
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
      ORDER BY ts.t_red_cards DESC, COALESCE(t.name_$locale, t.name_$localeFallback)) as tbl
      CROSS JOIN (SELECT @cnt := 0) as temp
      ORDER BY rowNumber
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $redCards = $stmt->fetchAll();

    $result = array();
    if ($redCards) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'totalRedCards' => '');
      $result['data'] = $redCards;
    }

    return $result;
  }

  public function getLeagueStats($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $query = "
      SELECT
        SUM(h_played) as totalPlayed,
        SUM(h_wins) as homeWins,
        SUM(h_draws) as draws,
        SUM(h_loses) as awayWins,
        SUM(h_over_25) as over25,
        SUM(h_under_25) as under25,
        SUM(h_goal_goal) as goalGoal,
        SUM(h_no_goal) as noGoal,
        SUM(h_goals_for)+SUM(h_goals_against) as totalGoals,
        SUM(h_goals_for) as totalGoalsHome,
        SUM(h_goals_against) as totalGoalsAway,
        ROUND(SUM(h_goals_for) / SUM(h_played), 1) as meanGoalsHome,
        ROUND(SUM(h_goals_against) / SUM(a_played), 1) as meanGoalsAway
      FROM team_stats ts
        INNER JOIN ft_team t ON ts.team_id = t.team_id
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
      WHERE
        lss.sport_id = :sportId AND lss.season = :season
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":season", $season);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->execute();
    $leagueStats = $stmt->fetchAll();

    $leagueStatsFinal = array();
    if ($leagueStats) {
      if ($leagueStats[0]['totalPlayed'] != null) {
        $leagueStatsFinal['label'] = $translations[0];
        $leagueStatsFinal['fields'] = array('label' => $translations[1], 'value' => '');
        $leagueStatsFinal['data'][] = array('label' => $translations[2], 'value' => $leagueStats[0]['totalPlayed']);
        $leagueStatsFinal['data'][] = array('label' => $translations[3], 'value' => $leagueStats[0]['homeWins']);
        $leagueStatsFinal['data'][] = array('label' => $translations[4], 'value' => $leagueStats[0]['draws']);
        $leagueStatsFinal['data'][] = array('label' => $translations[5], 'value' => $leagueStats[0]['awayWins']);
        $leagueStatsFinal['data'][] = array('label' => 'Over 2.5', 'value' => $leagueStats[0]['over25']);
        $leagueStatsFinal['data'][] = array('label' => 'Under 2.5', 'value' => $leagueStats[0]['under25']);
        $leagueStatsFinal['data'][] = array('label' => 'G/G', 'value' => $leagueStats[0]['goalGoal']);
        $leagueStatsFinal['data'][] = array('label' => 'N/G', 'value' => $leagueStats[0]['noGoal']);
        $leagueStatsFinal['data'][] = array('label' => $translations[6], 'value' => $leagueStats[0]['totalGoals']);
        $leagueStatsFinal['data'][] = array('label' => $translations[7], 'value' => $leagueStats[0]['totalGoalsHome']);
        $leagueStatsFinal['data'][] = array('label' => $translations[8], 'value' => $leagueStats[0]['totalGoalsAway']);
        $leagueStatsFinal['data'][] = array('label' => $translations[9], 'value' => $leagueStats[0]['meanGoalsHome']);
        $leagueStatsFinal['data'][] = array('label' => $translations[10], 'value' => $leagueStats[0]['meanGoalsAway']);
      }
    }

    return $leagueStatsFinal;
  }

  public function getOverUnder($domainId, $locale, $localeFallback) {
    $em = $this->getEntityManager();

    $query = "
    SELECT id, title, slug, image_name, under_25_perc, over_25_perc, season
    FROM (
    SELECT 
      lss.season,
      SUM(ts.h_under_25 + ts.a_under_25) under_25, SUM(ts.h_over_25 + ts.a_over_25) over_25, ts.league_season_id, s.id, dss.slug, 
      COALESCE(st.title, st2.title) title,
      s.image_name,
      SUM(ts.h_under_25 + ts.a_under_25) + SUM(ts.h_over_25 + ts.a_over_25) total,
      ROUND(SUM(ts.h_under_25 + ts.a_under_25)  * 100 / (SUM(ts.h_played + ts.a_played)), 0) under_25_perc,
      ROUND(SUM(ts.h_over_25 + ts.a_over_25) * 100 / (SUM(ts.h_played + ts.a_played)), 0) over_25_perc
    FROM 
      team_stats ts
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
        INNER JOIN sport s ON s.id = lss.sport_id
        INNER JOIN domain_sport_slug dss ON dss.sport_id = s.id AND dss.domain_id = $domainId
        LEFT JOIN sport_translations st ON st.sport_id = s.id AND st.type = 'normal' AND st.locale = '$locale'
        LEFT JOIN sport_translations st2 ON st2.sport_id = s.id AND st2.type = 'normal' AND st2.locale = '$localeFallback'
    WHERE
        lss.id IN (SELECT (SELECT id FROM ft_league_season_sport lss2 WHERE lss2.sport_id = lss.sport_id AND lss2.season = MAX(lss.season)) id FROM ft_league_season_sport lss GROUP BY sport_id)
    AND s.hide_in_stats = 0
    GROUP BY ts.league_season_id, COALESCE(st.title, st2.title), lss.season, dss.slug
    HAVING (SUM(ts.h_played + ts.a_played)) >= 30
    ORDER BY under_25 DESC
    ) tmp
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->execute();
    $overUnder = $stmt->fetchAll();

    $under25  = null;
    $over25   = null;
    $sortArr  = array();
    $yearArr  = array();
    // $imageName = null;
    if ($overUnder) {
      foreach($overUnder as $key1 => $single) {
        $seasonAllYears = explode('-', $single['season']);
        foreach ($single as $key => $value) {
          if (!isset($sortArr[$key])) {
            $sortArr[$key] = array();
          }

          if (in_array(date("Y"), $seasonAllYears)) {
            $sortArr[$key][] = $value;
          }
        }

        if (!in_array(date("Y"), $seasonAllYears)) {
          unset($overUnder[$key1]);
        }
      }
    }

    // reindex $overUnder array due to unset keys
    $under25    = array_values($overUnder);
    $over25     = $under25;
    $sortArrO   = $sortArr;

    $orderBy    = 'under_25_perc';
    $orderByO   = 'over_25_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $under25);
    array_multisort($sortArrO[$orderByO], SORT_DESC, $over25);

    $result = array('under25' => $under25, 'over25' => $over25);

    return $result;
  }

  public function getTeamOverUnder($domainId, $locale, $localeFallback, $languageCode) {
    $em = $this->getEntityManager();
    
    $query = "
    SELECT id, team_name, title, slug, image_name, under_25, over_25, h_under_25_streak, h_over_25_streak, a_under_25_streak, a_over_25_streak, total, h_played, a_played, under_25_streak, over_25_streak, season
    FROM (
    SELECT 
      lss.season,
      SUM(ts.h_under_25 + ts.a_under_25) under_25, SUM(ts.h_under_25_streak) h_under_25_streak, SUM(ts.a_under_25_streak) a_under_25_streak, SUM(ts.t_under_25_streak) under_25_streak,
      SUM(ts.h_over_25 + ts.a_over_25) over_25, SUM(ts.h_over_25_streak) h_over_25_streak, SUM(ts.a_over_25_streak) a_over_25_streak, SUM(ts.t_over_25_streak) over_25_streak,
      SUM(ts.h_played) h_played,
      SUM(ts.a_played) a_played,
      ts.league_season_id, 
      COALESCE(t.name_$languageCode, t.name_$localeFallback) team_name,
      s.id, dss.slug, COALESCE(st.title, st2.title) title, 
      s.image_name,
      SUM(ts.h_played + ts.a_played) total
    FROM 
      team_stats ts
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
        INNER JOIN ft_team t ON t.team_id = ts.team_id
        INNER JOIN sport s ON s.id = lss.sport_id
        INNER JOIN domain_sport_slug dss ON dss.sport_id = s.id AND dss.domain_id = $domainId
        LEFT JOIN sport_translations st ON st.sport_id = s.id AND st.type = 'normal' AND st.locale = '$locale'
        LEFT JOIN sport_translations st2 ON st2.sport_id = s.id AND st2.type = 'normal' AND st2.locale = '$localeFallback'
    WHERE
        lss.id IN (SELECT (SELECT id FROM ft_league_season_sport lss2 WHERE lss2.sport_id = lss.sport_id AND lss2.season = MAX(lss.season)) id FROM ft_league_season_sport lss GROUP BY sport_id)
    AND t.is_national = 0
    AND s.hide_in_stats = 0
    GROUP BY ts.team_id, ts.league_season_id, COALESCE(st.title, st2.title), lss.season, dss.slug
    HAVING (SUM(ts.h_played + ts.a_played)) >= 10
    ) tmp
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->execute();
    $overUnder = $stmt->fetchAll();

    $under25  = null;
    $over25   = null;
    $sortArr  = array();
    $yearArr  = array();
    // $imageName = null;
    if ($overUnder) {
      foreach($overUnder as $key1 => $single) {
        $seasonAllYears = explode('-', $single['season']);
        foreach ($single as $key => $value) {
          if (!isset($sortArr[$key])) {
            $sortArr[$key] = array();
          }

          if (in_array(date("Y"), $seasonAllYears)) {
            $sortArr[$key][] = $value;
          }
        }

        if (!in_array(date("Y"), $seasonAllYears)) {
          unset($overUnder[$key1]);
        }
      }
    }

    // reindex $overUnder array due to unset keys
    $under25        = array_values($overUnder);
    $over25         = $under25;
    $under25HStreak = $under25;
    $over25HStreak  = $under25;
    $under25AStreak = $under25;
    $over25AStreak  = $under25;
    $totalU25Streak = $under25;
    $totalO25Streak = $under25;
    $sortArrO       = $sortArr;

    $orderBy    = 'under_25';
    $orderByO   = 'over_25';
    $orderByS   = 'h_under_25_streak';
    $orderByOS  = 'h_over_25_streak';
    $orderByAS  = 'a_under_25_streak';
    $orderByAOS = 'a_over_25_streak';
    $orderByTS  = 'under_25_streak';
    $orderByTOS = 'over_25_streak';
    array_multisort($sortArr[$orderBy], SORT_DESC, $under25);

    array_multisort($sortArrO[$orderByO], SORT_DESC, $over25);

    array_multisort($sortArr[$orderByS], SORT_DESC, $under25HStreak);
    array_multisort($sortArr[$orderByOS], SORT_DESC, $over25HStreak);
    array_multisort($sortArr[$orderByAS], SORT_DESC, $under25AStreak);
    array_multisort($sortArr[$orderByAOS], SORT_DESC, $over25AStreak);
    array_multisort($sortArr[$orderByTS], SORT_DESC, $totalU25Streak);
    array_multisort($sortArr[$orderByTOS], SORT_DESC, $totalO25Streak);

    $result = array('under25' => $under25, 'over25' => $over25, 'hUnder25Streak' => $under25HStreak, 'hOver25Streak' => $over25HStreak, 'aUnder25Streak' => $under25AStreak, 'aOver25Streak' => $over25AStreak, 'tUnder25Streak' => $totalU25Streak, 'tOver25Streak' => $totalO25Streak);

    return $result;
  }

  /**
   * NoGoal GoalGoal for sport
   */
  public function getNgGg($domainId, $locale, $localeFallback) {
    $em = $this->getEntityManager();

    $query = "
    SELECT id, title, slug, image_name, no_goal_perc, goal_goal_perc, season
    FROM (
    SELECT 
      lss.season,
      SUM(ts.h_no_goal + ts.a_no_goal) no_goal, SUM(ts.h_goal_goal + ts.a_goal_goal) goal_goal, ts.league_season_id, s.id, dss.slug, 
      COALESCE(st.title, st2.title) title,
      s.image_name,
      SUM(ts.h_played + ts.a_played) total,
      ROUND(SUM(ts.h_no_goal + ts.a_no_goal) / (SUM(ts.h_played + ts.a_played)) * 100, 0) no_goal_perc,
      ROUND(SUM(ts.h_goal_goal + ts.a_goal_goal) / (SUM(ts.h_played + ts.a_played)) * 100, 0) goal_goal_perc
    FROM 
      team_stats ts
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
        INNER JOIN sport s ON s.id = lss.sport_id
        INNER JOIN domain_sport_slug dss ON dss.sport_id = s.id AND dss.domain_id = $domainId
        LEFT JOIN sport_translations st ON st.sport_id = s.id AND st.type = 'normal' AND st.locale = '$locale'
        LEFT JOIN sport_translations st2 ON st2.sport_id = s.id AND st2.type = 'normal' AND st2.locale = '$localeFallback'
    WHERE
        lss.id IN (SELECT (SELECT id FROM ft_league_season_sport lss2 WHERE lss2.sport_id = lss.sport_id AND lss2.season = MAX(lss.season)) id FROM ft_league_season_sport lss GROUP BY sport_id)
    AND s.hide_in_stats = 0
    GROUP BY ts.league_season_id, COALESCE(st.title, st2.title), lss.season, dss.slug
    HAVING (SUM(ts.h_played + ts.a_played)) >= 30
    ORDER BY no_goal DESC
    ) tmp
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->execute();
    $ngGg = $stmt->fetchAll();

    $noGoal   = null;
    $goalGoal = null;
    $sortArr  = array();
    $yearArr  = array();
    if ($ngGg) {
      foreach($ngGg as $key1 => $single) {

        $seasonAllYears = explode('-', $single['season']);
        foreach ($single as $key => $value) {
          if (!isset($sortArr[$key])) {
            $sortArr[$key] = array();
          }

          if (in_array(date("Y"), $seasonAllYears)) {
            $sortArr[$key][] = $value;
          }
        }

        if (!in_array(date("Y"), $seasonAllYears)) {
          unset($ngGg[$key1]);
        }
      }
    }

    // reindex $overUnder array due to unset keys
    $noGoal    = array_values($ngGg);
    $goalGoal  = $noGoal;
    $sortArrO  = $sortArr;

    $orderBy    = 'no_goal_perc';
    $orderByO   = 'goal_goal_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $noGoal);
    array_multisort($sortArrO[$orderByO], SORT_DESC, $goalGoal);

    $result = array('noGoal' => $noGoal, 'goalGoal' => $goalGoal);

    return $result;
  }

  public function getTeamNgGg($domainId, $locale, $localeFallback, $languageCode) {
    $em = $this->getEntityManager();

    $query = "
    SELECT id, team_name, title, slug, image_name, no_goal, goal_goal, h_no_goal_streak, h_goal_goal_streak, a_no_goal_streak, a_goal_goal_streak, total, h_played, a_played, no_goal_streak, goal_goal_streak, season
    FROM (
    SELECT 
      lss.season,
      SUM(ts.h_no_goal + ts.a_no_goal) no_goal, SUM(ts.h_no_goal_streak) h_no_goal_streak, SUM(ts.a_no_goal_streak) a_no_goal_streak, SUM(ts.t_no_goal_streak) no_goal_streak,
      SUM(ts.h_goal_goal + ts.a_goal_goal) goal_goal, SUM(ts.h_goal_goal_streak) h_goal_goal_streak, SUM(ts.a_goal_goal_streak) a_goal_goal_streak, SUM(ts.t_goal_goal_streak) goal_goal_streak,
      SUM(ts.h_played) h_played,
      SUM(ts.a_played) a_played,
      ts.league_season_id, 
      COALESCE(t.name_$languageCode, t.name_$localeFallback) team_name,
      s.id, dss.slug, 
      COALESCE(st.title, st2.title) title,
      s.image_name,
      SUM(ts.h_played + ts.a_played) total
    FROM 
      team_stats ts
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
        INNER JOIN ft_team t ON t.team_id = ts.team_id
        INNER JOIN sport s ON s.id = lss.sport_id
        INNER JOIN domain_sport_slug dss ON dss.sport_id = s.id AND dss.domain_id = $domainId
        LEFT JOIN sport_translations st ON st.sport_id = s.id AND st.type = 'normal' AND st.locale = '$locale'
        LEFT JOIN sport_translations st2 ON st2.sport_id = s.id AND st2.type = 'normal' AND st2.locale = '$localeFallback'
    WHERE
        lss.id IN (SELECT (SELECT id FROM ft_league_season_sport lss2 WHERE lss2.sport_id = lss.sport_id AND lss2.season = MAX(lss.season)) id FROM ft_league_season_sport lss GROUP BY sport_id)
    AND t.is_national = 0
    AND s.hide_in_stats = 0
    GROUP BY ts.team_id, ts.league_season_id, COALESCE(st.title, st2.title), lss.season, dss.slug
    HAVING (SUM(ts.h_played + ts.a_played)) >= 10
    ) tmp
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->execute();
    $ngGg = $stmt->fetchAll();

    $noGoal   = null;
    $goalGoal = null;
    $sortArr  = array();
    $yearArr  = array();
    if ($ngGg) {
      foreach($ngGg as $key1 => $single) {
        $seasonAllYears = explode('-', $single['season']);
        foreach ($single as $key => $value) {
          if (!isset($sortArr[$key])) {
            $sortArr[$key] = array();
          }

          if (in_array(date("Y"), $seasonAllYears)) {
            $sortArr[$key][] = $value;
          }
        }

        if (!in_array(date("Y"), $seasonAllYears)) {
          unset($ngGg[$key1]);
        }
      }
    }

    // reindex $ngGg array due to unset keys
    $noGoal           = array_values($ngGg);
    $goalGoal         = $noGoal;
    $noGoalHStreak    = $noGoal;
    $goalGoalHStreak  = $noGoal;
    $noGoalAStreak    = $noGoal;
    $goalGoalAStreak  = $noGoal;
    $totalNGStreak    = $noGoal;
    $totalGGStreak    = $noGoal;
    $sortArrO         = $sortArr;

    $orderBy    = 'no_goal';
    $orderByO   = 'goal_goal';
    $orderByS   = 'h_no_goal_streak';
    $orderByOS  = 'h_goal_goal_streak';
    $orderByAS  = 'a_no_goal_streak';
    $orderByAOS = 'a_goal_goal_streak';
    $orderByTS  = 'no_goal_streak';
    $orderByTOS = 'goal_goal_streak';
    array_multisort($sortArr[$orderBy], SORT_DESC, $noGoal);

    array_multisort($sortArrO[$orderByO], SORT_DESC, $goalGoal);

    array_multisort($sortArr[$orderByS], SORT_DESC, $noGoalHStreak);
    array_multisort($sortArr[$orderByOS], SORT_DESC, $goalGoalHStreak);
    array_multisort($sortArr[$orderByAS], SORT_DESC, $noGoalAStreak);
    array_multisort($sortArr[$orderByAOS], SORT_DESC, $goalGoalAStreak);
    array_multisort($sortArr[$orderByTS], SORT_DESC, $totalNGStreak);
    array_multisort($sortArr[$orderByTOS], SORT_DESC, $totalGGStreak);

    $result = array('noGoal' => $noGoal, 'goalGoal' => $goalGoal, 'hNoGoalStreak' => $noGoalHStreak, 'hGoalGoalStreak' => $goalGoalHStreak, 'aNoGoalStreak' => $noGoalAStreak, 'aGoalGoalStreak' => $goalGoalAStreak, 'tNoGoalStreak' => $totalNGStreak, 'tGoalGoalStreak' => $totalGGStreak);

    return $result;
  }

 /**
  * Teams with most wins/draws/loses home/away/total
  */
  function getTeam1x2($domainId, $locale, $localeFallback, $languageCode) {
    $em = $this->getEntityManager();

    $query = "
    SELECT id, team_name, title, slug, image_name, wins, wins_perc, draws, draws_perc, loses, loses_perc, h_wins, h_wins_perc, h_draws, h_draws_perc, h_loses, h_loses_perc, a_wins, a_wins_perc, a_draws, a_draws_perc, a_loses, a_loses_perc, h_played, a_played, total, season
    FROM (
    SELECT 
      lss.season,
      SUM(ts.h_wins + ts.a_wins) wins, 
      IF(SUM(ts.h_played + ts.a_played) > 0, ROUND(SUM(ts.h_wins + ts.a_wins) * 100 / SUM(ts.h_played + ts.a_played), 2), 0) wins_perc,
      SUM(ts.h_draws + ts.a_draws) draws, 
      IF(SUM(ts.h_played + ts.a_played) > 0, ROUND(SUM(ts.h_draws + ts.a_draws) * 100 / SUM(ts.h_played + ts.a_played), 2), 0) draws_perc,
      SUM(ts.h_loses + ts.a_loses) loses,
      IF(SUM(ts.h_played + ts.a_played) > 0, ROUND(SUM(ts.h_loses + ts.a_loses) * 100 / SUM(ts.h_played + ts.a_played), 2), 0) loses_perc,
      SUM(ts.h_wins) h_wins,
      IF(SUM(ts.h_played) > 0, ROUND(SUM(ts.h_wins) * 100 / SUM(ts.h_played), 2), 0) h_wins_perc,
      SUM(ts.a_wins) a_wins, 
      IF(SUM(ts.a_played) > 0, ROUND(SUM(ts.a_wins) * 100 / SUM(ts.a_played), 2), 0) a_wins_perc,
      SUM(ts.h_draws) h_draws, 
      IF(SUM(ts.h_played) > 0, ROUND(SUM(ts.h_draws) * 100 / SUM(ts.h_played), 2), 0) h_draws_perc,
      SUM(ts.a_draws) a_draws,
      IF(SUM(ts.a_played) > 0, ROUND(SUM(ts.a_draws) * 100 / SUM(ts.a_played), 2), 0) a_draws_perc,
      SUM(ts.h_loses) h_loses,
      IF(SUM(ts.h_played) > 0, ROUND(SUM(ts.h_loses) * 100 / SUM(ts.h_played), 2), 0) h_loses_perc,
      SUM(ts.a_loses) a_loses,
      IF(SUM(ts.a_played) > 0, ROUND(SUM(ts.a_loses) * 100 / SUM(ts.a_played), 2), 0) a_loses_perc,
      SUM(ts.h_played) h_played,
      SUM(ts.a_played) a_played,
      SUM(ts.h_played + ts.a_played) total,
      ts.league_season_id, 
      COALESCE(t.name_$languageCode, t.name_$localeFallback) team_name,
      s.id, dss.slug, COALESCE(st.title, st2.title) title, s.image_name
    FROM 
      team_stats ts
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
        INNER JOIN ft_team t ON t.team_id = ts.team_id
        INNER JOIN sport s ON s.id = lss.sport_id
        INNER JOIN domain_sport_slug dss ON dss.sport_id = s.id AND dss.domain_id = $domainId
        LEFT JOIN sport_translations st ON st.sport_id = s.id AND st.type = 'normal' AND st.locale = '$locale'
        LEFT JOIN sport_translations st2 ON st2.sport_id = s.id AND st2.type = 'normal' AND st2.locale = '$localeFallback'
    WHERE
        lss.id IN (SELECT (SELECT id FROM ft_league_season_sport lss2 WHERE lss2.sport_id = lss.sport_id AND lss2.season = MAX(lss.season)) id FROM ft_league_season_sport lss GROUP BY sport_id)
    AND t.is_national = 0
    AND s.hide_in_stats = 0
    GROUP BY ts.team_id, ts.league_season_id, COALESCE(st.title, st2.title), lss.season, dss.slug
    HAVING SUM(ts.h_played + ts.a_played) >= 10
    ) tmp
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->execute();
    $team1x2 = $stmt->fetchAll();

    $wins       = null;
    $draws      = null;
    $loses      = null;
    $hWins      = null;
    $hDraws     = null;
    $hLoses     = null;
    $aWins      = null;
    $aDraws     = null;
    $aLoses     = null;
    $sortArr    = array();
    $yearArr    = array();
    if ($team1x2) {
      foreach($team1x2 as $key1 => $single) {
        $seasonAllYears = explode('-', $single['season']);
        foreach ($single as $key => $value) {
          if (!isset($sortArr[$key])) {
            $sortArr[$key] = array();
          }

          if (in_array(date("Y"), $seasonAllYears)) {
            $sortArr[$key][] = $value;
          }
        }

        if (!in_array(date("Y"), $seasonAllYears)) {
          unset($team1x2[$key1]);
        }
      }
    }

    // reindex $team1x2 array due to unset keys
    $wins     = array_values($team1x2);
    $draws    = $wins;
    $loses    = $wins;
    $hWins    = $wins;
    $hDraws   = $wins;
    $hLoses   = $wins;
    $aWins    = $wins;
    $aDraws   = $wins;
    $aLoses   = $wins;
    // $sortArrO = $sortArr;

    $orderBy = 'wins_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $wins);
    $orderBy = 'draws_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $draws);
    $orderBy = 'loses_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $loses);
	  $orderBy = 'h_wins_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $hWins);
	  $orderBy = 'h_draws_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $hDraws);
	  $orderBy = 'h_loses_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $hLoses);
	  $orderBy = 'a_wins_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $aWins);
	  $orderBy = 'a_draws_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $aDraws);
	  $orderBy = 'a_loses_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $aLoses);

    $result = array('wins' => $wins, 'draws' => $draws, 'loses' => $loses, 'hWins' => $hWins, 'hDraws' => $hDraws, 'hLoses' => $hLoses, 'aWins' => $aWins, 'aDraws' => $aDraws, 'aLoses' => $aLoses);

    return $result;
  }

  function getTeamNumberOfGoals($domainId, $locale, $localeFallback, $languageCode) {
    $em = $this->getEntityManager();

    $query = "
    SELECT id, team_name, title, slug, image_name, goals_01, goals_01_perc, goals_23, goals_23_perc, goals_46, goals_46_perc, goals_7, goals_7_perc, season, h_played, a_played, total
    FROM (
    SELECT 
      lss.season,
      SUM(ts.h_goals_01 + ts.a_goals_01) goals_01, 
      SUM(ts.h_goals_23 + ts.a_goals_23) goals_23, 
      SUM(ts.h_goals_46 + ts.a_goals_46) goals_46, 
      SUM(ts.h_goals_7 + ts.a_goals_7) goals_7, 
      COALESCE(t.name_$languageCode, t.name_$localeFallback) team_name,
      ts.league_season_id, s.id, dss.slug, COALESCE(st.title, st2.title) title, s.image_name,
      SUM(ts.h_played) h_played,
      SUM(ts.a_played) a_played,
      SUM(ts.h_played + ts.a_played) total,
      ROUND(SUM(ts.h_goals_01 + ts.a_goals_01)  * 100 / (SUM(ts.h_played + ts.a_played)), 0) goals_01_perc,
      ROUND(SUM(ts.h_goals_23 + ts.a_goals_23) * 100 / (SUM(ts.h_played + ts.a_played)), 0) goals_23_perc,
      ROUND(SUM(ts.h_goals_46 + ts.a_goals_46) * 100 / (SUM(ts.h_played + ts.a_played)), 0) goals_46_perc,
      ROUND(SUM(ts.h_goals_7 + ts.a_goals_7) * 100 / (SUM(ts.h_played + ts.a_played)), 0) goals_7_perc
    FROM 
      team_stats ts
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
        INNER JOIN ft_team t ON t.team_id = ts.team_id
        INNER JOIN sport s ON s.id = lss.sport_id
        INNER JOIN domain_sport_slug dss ON dss.sport_id = s.id AND dss.domain_id = $domainId
        LEFT JOIN sport_translations st ON st.sport_id = s.id AND st.type = 'normal' AND st.locale = '$locale'
        LEFT JOIN sport_translations st2 ON st2.sport_id = s.id AND st2.type = 'normal' AND st2.locale = '$localeFallback'
    WHERE
        lss.id IN (SELECT (SELECT id FROM ft_league_season_sport lss2 WHERE lss2.sport_id = lss.sport_id AND lss2.season = MAX(lss.season)) id FROM ft_league_season_sport lss GROUP BY sport_id)
    AND t.is_national = 0
    AND s.hide_in_stats = 0
    GROUP BY ts.team_id, ts.league_season_id, COALESCE(st.title, st2.title), lss.season, dss.slug
    HAVING (SUM(ts.h_played + ts.a_played)) >= 10
    ) tmp
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->execute();
    $numberOfGoals = $stmt->fetchAll();

    $goals01    = null;
    $goals23    = null;
    $goals46    = null;
    $goals7     = null;
    $sortArr    = array();
    $yearArr    = array();
    if ($numberOfGoals) {
      foreach($numberOfGoals as $key1 => $single) {
        $seasonAllYears = explode('-', $single['season']);
        foreach ($single as $key => $value) {
          if (!isset($sortArr[$key])) {
            $sortArr[$key] = array();
          }

          if (in_array(date("Y"), $seasonAllYears)) {
            $sortArr[$key][] = $value;
          }
        }

        if (!in_array(date("Y"), $seasonAllYears)) {
          unset($numberOfGoals[$key1]);
        }
      }
    }

    // reindex $numberOfGoals array due to unset keys
    $goals01 = array_values($numberOfGoals);
    $goals23 = $goals01;
    $goals46 = $goals01;
    $goals7  = $goals01;

    $orderBy = 'goals_01_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $goals01);
    $orderBy = 'goals_23_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $goals23);
    $orderBy = 'goals_46_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $goals46);
	  $orderBy = 'goals_7_perc';
    array_multisort($sortArr[$orderBy], SORT_DESC, $goals7);

    $result = array('goals01' => $goals01, 'goals23' => $goals23, 'goals46' => $goals46, 'goals7' => $goals7);

    return $result;
  }

  function getTeamStreak($domainId, $locale, $localeFallback, $languageCode) {
    $em = $this->getEntityManager();

    $query = "
    SELECT id, team_name, title, slug, image_name, t_win_streak, t_draw_streak, t_lose_streak, t_no_win_streak, t_no_draw_streak, t_no_lose_streak, season
    FROM (
    SELECT 
      lss.season,
      SUM(ts.t_win_streak) t_win_streak,
      SUM(ts.t_draw_streak) t_draw_streak,
      SUM(ts.t_lose_streak) t_lose_streak,
      SUM(ts.t_no_win_streak) t_no_win_streak,
      SUM(ts.t_no_draw_streak) t_no_draw_streak,
      SUM(ts.t_no_lose_streak) t_no_lose_streak,
      COALESCE(t.name_$languageCode, t.name_$localeFallback) team_name,
      ts.league_season_id, s.id, dss.slug, COALESCE(st.title, st2.title) title, s.image_name
    FROM 
      team_stats ts
        INNER JOIN ft_league_season_sport lss ON lss.id = ts.league_season_id
        INNER JOIN ft_team t ON t.team_id = ts.team_id
        INNER JOIN sport s ON s.id = lss.sport_id
        INNER JOIN domain_sport_slug dss ON dss.sport_id = s.id AND dss.domain_id = $domainId
        LEFT JOIN sport_translations st ON st.sport_id = s.id AND st.type = 'normal' AND st.locale = '$locale'
        LEFT JOIN sport_translations st2 ON st2.sport_id = s.id AND st2.type = 'normal' AND st2.locale = '$localeFallback'
    WHERE
        lss.id IN (SELECT (SELECT id FROM ft_league_season_sport lss2 WHERE lss2.sport_id = lss.sport_id AND lss2.season = MAX(lss.season)) id FROM ft_league_season_sport lss GROUP BY sport_id)
    AND t.is_national = 0
    AND s.hide_in_stats = 0
    GROUP BY ts.team_id, ts.league_season_id, COALESCE(st.title, st2.title), lss.season, dss.slug
    HAVING (SUM(ts.h_played + ts.a_played)) >= 10
    ) tmp
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->execute();
    $streak = $stmt->fetchAll();

    $win        = null;
    $draw       = null;
    $lose       = null;
    $noWin      = null;
    $noDraw     = array();
    $noLose     = array();
    if ($streak) {
      foreach($streak as $key1 => $single) {
        $seasonAllYears = explode('-', $single['season']);
        foreach ($single as $key => $value) {
          if (!isset($sortArr[$key])) {
            $sortArr[$key] = array();
          }

          if (in_array(date("Y"), $seasonAllYears)) {
            $sortArr[$key][] = $value;
          }
        }

        if (!in_array(date("Y"), $seasonAllYears)) {
          unset($streak[$key1]);
        }
      }
    }

    // reindex $streak array due to unset keys
    $win    = array_values($streak);
    $draw   = $win;
    $lose   = $win;
    $noWin  = $win;
    $noDraw = $win;
    $noLose = $win;

    $orderBy = 't_win_streak';
    array_multisort($sortArr[$orderBy], SORT_DESC, $win);
    $orderBy = 't_draw_streak';
    array_multisort($sortArr[$orderBy], SORT_DESC, $draw);
    $orderBy = 't_lose_streak';
    array_multisort($sortArr[$orderBy], SORT_DESC, $lose);
	  $orderBy = 't_no_win_streak';
    array_multisort($sortArr[$orderBy], SORT_DESC, $noWin);
	  $orderBy = 't_no_draw_streak';
    array_multisort($sortArr[$orderBy], SORT_DESC, $noDraw);
	  $orderBy = 't_no_lose_streak';
    array_multisort($sortArr[$orderBy], SORT_DESC, $noLose);

    $result = array('win' => $win, 'draw' => $draw, 'lose' => $lose, 'noWin' => $noWin, 'noDraw' => $noDraw, 'noLose' => $noLose);

    return $result;
  }

  function getCompetitionStats($domainId, $locale, $localeFallback, $footballPriority) {
    $em = $this->getEntityManager();

    $query = "
    SELECT 
      tmp.id, COALESCE(st.title, st2.title) regiontitle, tmp.title, tmp.image_name, tmp.slug, tmp.season,
      IF(total > 0, ROUND(wins * 100 / total, 0), 0) wins_perc,
      IF(total > 0, ROUND(draws * 100 / total, 0), 0) draws_perc,
      IF(total > 0, ROUND(loses * 100 / total, 0), 0) loses_perc,
      IF(total > 0, ROUND(goals / total, 2), 0) goals_mv,
      IF(total > 0, ROUND(ng * 100 / total, 0), 0) ng_perc,
      IF(total > 0, ROUND(gg * 100 / total, 0), 0) gg_perc,
      IF(total > 0, ROUND(under * 100 / total, 0), 0) under_perc,
      IF(total > 0, ROUND(`over` * 100 / total, 0), 0) over_perc
    FROM (
    SELECT
      COUNT(1) total,
      f.sport_id, f.season,
      SUM(IF(ft1 > ft2, 1, 0)) wins,
      SUM(IF(ft1 = ft2, 1, 0)) draws,
      SUM(IF(ft1 < ft2, 1, 0)) loses,
      SUM(ft1+ft2) goals,
      SUM(IF((ft1 = 0 OR ft2 = 0), 1, 0)) ng,
      SUM(IF((ft1 > 0 AND ft2 > 0), 1, 0)) gg,
      SUM(IF((ft1 + ft2) <= 2, 1, 0)) under,
      SUM(IF((ft1 + ft2) > 2, 1, 0)) `over`,
      s.id, s.parent_id , COALESCE(st.title, st2.title) title, s.image_name,
      CASE WHEN s.is_additional = 1 THEN (SELECT slug FROM domain_sport_slug dss1 WHERE dss1.sport_id = s.parent_id AND dss1.domain_id = $domainId) ELSE dss.slug END slug
    FROM
      ft_fixture f,
      (SELECT (SELECT id FROM ft_league_season_sport lss2 WHERE lss2.sport_id = lss.sport_id AND lss2.season = MAX(lss.season)) id, lss.sport_id, MAX(lss.season) season FROM ft_league_season_sport lss GROUP BY sport_id) ss
        INNER JOIN sport s ON s.id = ss.sport_id
        INNER JOIN domain_sport_slug dss ON dss.sport_id = s.id AND dss.domain_id = $domainId
        LEFT JOIN sport_translations st ON st.sport_id = s.id AND st.type = 'normal' AND st.locale = '$locale'
        LEFT JOIN sport_translations st2 ON st2.sport_id = s.id AND st2.type = 'normal' AND st2.locale = '$localeFallback'
    WHERE
        f.status IN ('F', 'FE', 'FP')
    AND ss.sport_id = f.sport_id
    AND ss.season = f.season
    AND s.hide_in_stats = 0
    GROUP BY f.sport_id, f.season, COALESCE(st.title, st2.title), dss.slug
    HAVING total >= 30
    ) tmp
      INNER JOIN sport r ON r.id = tmp.parent_id
      LEFT JOIN sport_translations st ON st.sport_id = r.id AND st.type = 'normal' AND st.locale = '$locale'
      LEFT JOIN sport_translations st2 ON st2.sport_id = r.id AND st2.type = 'normal' AND st2.locale = '$localeFallback'
    -- ORDER BY FIELD(tmp.id, $footballPriority), COALESCE(st.title, st2.title)
    ORDER BY COALESCE(st.title, st2.title)
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->execute();
    $result = $stmt->fetchAll();

    if ($result) {
      foreach($result as $key1 => $single) {
        $seasonAllYears = explode('-', $single['season']);

        if (!in_array(date("Y"), $seasonAllYears)) {
          unset($result[$key1]);
        }
      }
    }

    return $result;
  }

  function getTodaysMatchesStats($domainId, $footballCb) {
    $em = $this->getEntityManager();

    $previewIds = array();
    $competitions = null;
    $final = array();

    if ($footballCb) {
      // $competitions = json_decode(json_decode($footballCb), true);
      $competitions = json_decode($footballCb, true);
    }
    $imageName = null;
    if ($competitions) {
      foreach ($competitions as $key => $competition) {

        $domainSportSlug = $em->getRepository('AppBundle:DomainSportSlug')->findOneBy(array('slug' => $competition['slug'], 'domain' => $domainId));

        if (isset($competition['matches']) && $competition['matches']) {
          foreach ($competition['matches'] as $key1 => $match) {
            $team1Stats = null;
            $team2Stats = null;

            // for each preview find fixture and get team stats for season/sport
            $fixture = $em->getRepository('AppBundle:DomainFtFixture')->findOneBy(array('previewId' => $match['preview_id'], 'domainId' => $domainId));

            if ($fixture) {
              $matchStats = $this->getMatchStats($fixture->getFixtureId(), $fixture->getHomeTeamId(), $fixture->getAwayTeamId());
              if ($matchStats) {
                if (isset($matchStats[0])) $team1Stats = $matchStats[0];
                if (isset($matchStats[1])) $team2Stats = $matchStats[1];

                $final[$key]['competition'] = array('name' => $competition['name'], 'slug' => $competition['slug'], 'image' => $competition['comp_image_name'], 'region' => $competition['region_title'], 'region_slug' => $competition['region_slug']);
                $final[$key]['matches'][] = array('preview_slug' => $match['preview_slug'], 'home' => array('name' => $match['h_team_name'], 'id' => $match['h_team_id'], 'stats' => $team1Stats), 'away' => array('name' => $match['a_team_name'], 'id' => $match['a_team_id'], 'stats' => $team2Stats));
              }
            }
          }
        }
      }
    }

    return $final;
  }

  private function getMatchStats($fixtureId, $hTeamId, $aTeamId) {
    $em = $this->getEntityManager();

    $sql = 
    "
    SELECT
      IF(ls.overall_pts IS NULL, '-', ls.overall_pts) overall_pts,
      IF(ls.rank IS NULL, '-', ls.rank) `rank`,
      ts.h_played + ts.a_played total_played,
      ts.h_wins + ts.a_wins total_wins,
      ts.h_draws + ts.a_draws total_draws,
      ts.h_loses + ts.a_loses total_loses,
      ts.h_under_25 + ts.a_under_25 total_under,
      ts.h_over_25 + ts.a_over_25 total_over,
      ts.h_goals_01 + ts.a_goals_01 total_goals_01,
      ts.h_goals_23 + ts.a_goals_23 total_goals_23,
      ts.h_goals_46 + ts.a_goals_46 total_goals_46,
      ts.h_goals_7 + ts.a_goals_7 total_goals_7,
      ts.h_no_goal + ts.a_no_goal total_no_goal,
      ts.h_goal_goal + ts.a_goal_goal total_goal_goal
    FROM
      team_stats ts,
      ft_fixture f
        INNER JOIN ft_league_season_sport lss ON lss.sport_id = f.sport_id AND lss.season = f.season
        LEFT JOIN ft_league_standings ls ON ls.sport_id = f.sport_id AND ls.season = f.season AND ls.team_id = :hTeamId
    WHERE
        ts.team_id = :hTeamId
    AND f.f_id = :fixtureId
    AND lss.id = ts.league_season_id
    UNION ALL
    SELECT
      IF(ls.overall_pts IS NULL, '-', ls.overall_pts) overall_pts,
      IF(ls.rank IS NULL, '-', ls.rank) `rank`,
      ts.h_played + ts.a_played total_played,
      ts.h_wins + ts.a_wins total_wins,
      ts.h_draws + ts.a_draws total_draws,
      ts.h_loses + ts.a_loses total_loses,
      ts.h_under_25 + ts.a_under_25 total_under,
      ts.h_over_25 + ts.a_over_25 total_over,
      ts.h_goals_01 + ts.a_goals_01 total_goals_01,
      ts.h_goals_23 + ts.a_goals_23 total_goals_23,
      ts.h_goals_46 + ts.a_goals_46 total_goals_46,
      ts.h_goals_7 + ts.a_goals_7 total_goals_7,
      ts.h_no_goal + ts.a_no_goal total_no_goal,
      ts.h_goal_goal + ts.a_goal_goal total_goal_goal
    FROM
      team_stats ts,
      ft_fixture f
        INNER JOIN ft_league_season_sport lss ON lss.sport_id = f.sport_id AND lss.season = f.season
        LEFT JOIN ft_league_standings ls ON ls.sport_id = f.sport_id AND ls.season = f.season AND ls.team_id = :aTeamId
    WHERE
        ts.team_id = :aTeamId
    AND f.f_id = :fixtureId
    AND lss.id = ts.league_season_id
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":fixtureId", $fixtureId);
    $stmt->bindValue(":hTeamId", $hTeamId);
    $stmt->bindValue(":aTeamId", $aTeamId);
    $stmt->execute();
    $result = $stmt->fetchAll();

    $em->clear();
    return $result;
  }

  public function getTeamForm($sportId, $season, $lan, $localeFallback, $translations) {
    $em = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT 
      ls.rank rowNumber,
      ls.team_id teamId,
      tmp.teamName namegr,
      tmp.matchForm,
      tmp.overUnderForm,
      tmp.goalForm
    FROM ft_league_standings ls,
    (
    SELECT
      ls.team_id, COALESCE(t.name_$lan, t.name_$localeFallback) teamName,
      SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN tf.h_team = ls.team_id THEN CASE WHEN tf.ft1 - tf.ft2 > 0 THEN 'w' WHEN tf.ft1 - tf.ft2 = 0 THEN 'd' ELSE 'l' END
           WHEN tf.a_team = ls.team_id THEN CASE WHEN tf.ft1 - tf.ft2 > 0 THEN 'l' WHEN tf.ft1 - tf.ft2 = 0 THEN 'd' ELSE 'w' END END ORDER BY tf.match_datetime DESC), ',', 6) matchForm,
      SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN tf.ft1 + tf.ft2 > 2 THEN 'o' ELSE 'u' END ORDER BY tf.match_datetime DESC), ',', 6) overUnderForm,
      SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN tf.ft1 > 0 AND tf.ft2 > 0 THEN 'g' ELSE 'n' END ORDER BY tf.match_datetime DESC), ',', 6) goalForm
    FROM
      ft_league_standings ls
        LEFT JOIN ft_team t ON t.team_id = ls.team_id
        LEFT JOIN (
          SELECT
            f.match_datetime, f.h_team, f.a_team, f.ft1, f.ft2
          FROM ft_fixture f
          WHERE
              f.sport_id = :sportId
          AND f.season = :season
          AND f.status IN ('F', 'FE', 'FP')
        ) tf ON (tf.h_team = ls.team_id OR tf.a_team = ls.team_id)
    WHERE
        ls.sport_id = :sportId
    AND ls.season = :season
    GROUP BY ls.team_id) tmp
    WHERE tmp.team_id = ls.team_id
    AND ls.season = :season
    AND ls.sport_id = :sportId
    ORDER BY `rank`
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $teamForm = $stmt->fetchAll();

    if ($teamForm) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'matchForm' => $translations[3], 'overUnderForm' => 'OVER/UNDER', 'goalForm' => 'GOAL/NO GOAL');
      $result['data'] = $teamForm;
    }

    $em->clear();
    return $result;
  }

  public function getTeamTotalStats($sportId, $season, $lan, $localeFallback, $translations) {
    $em = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT 
      ls.rank rowNumber,
      ls.team_id teamId,
      COALESCE(t.name_$lan, t.name_$localeFallback) namegr,
      ls.overall_pts points,
      ls.overall_p played,
      ls.overall_w wins,
      ls.overall_d draws,
      ls.overall_l loses,
      ts.h_over_25 + ts.a_over_25 over25,
      ts.h_under_25 + ts.a_under_25 under25,
      ts.h_goal_goal + ts.a_goal_goal goalGoal,
      ts.h_no_goal + ts.a_no_goal noGoal,
      ts.h_goals_01 + ts.a_goals_01 goals01,
      ts.h_goals_23 + ts.a_goals_23 goals23,
      ts.h_goals_46 + ts.a_goals_46 goals46,
      ts.h_goals_7 + ts.a_goals_7 goals7
    FROM ft_league_standings ls
      LEFT JOIN ft_team t ON t.team_id = ls.team_id
      LEFT JOIN ft_league_season_sport lss ON lss.sport_id = :sportId AND lss.season = :season
      LEFT JOIN team_stats ts ON ts.team_id = ls.team_id AND ts.league_season_id = lss.id
    WHERE 
        ls.season = :season
    AND ls.sport_id = :sportId
    ORDER BY `rank`
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->bindValue(":season", $season);
    $stmt->execute();
    $teamTotalStats = $stmt->fetchAll();

    if ($teamTotalStats) {
      $result['label'] = $translations[0];
      $result['fields'] = array('rowNumber' => $translations[1], 'namegr' => $translations[2], 'points' => $translations[3], 'played' => $translations[4], 'wins' => $translations[5], 'draws' => $translations[6], 'loses' => $translations[7], 'over25' => 'O', 'under25' => 'U', 'goalGoal' => 'GG', 'noGoal' => 'NG', 'goals01' => '0-1', 'goals23' => '2-3', 'goals46' => '4-6', 'goals7' => '7+');
      $result['data'] = $teamTotalStats;
    }

    $em->clear();
    return $result;
  }

}