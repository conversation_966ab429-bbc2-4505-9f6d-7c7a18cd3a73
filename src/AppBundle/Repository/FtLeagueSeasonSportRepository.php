<?php

namespace AppBundle\Repository;

/**
 * FtLeagueSeasonSportRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtLeagueSeasonSportRepository extends \Doctrine\ORM\EntityRepository
{
  public function getLatestSportSeason($sportId) {
    $em = $this->getEntityManager();
    $sportIds = array();

    // use additionals for getting Latest Sport Season
    // για competition όπως Α' Χιλής, Α' Χιλής Apertura, Α' Χιλής Clausura
    // η A' Χιλής δεν θα έχει ποτέ ft_league_season_sport (παρά μόνο τα additional της)
    $additionals = $em->getRepository('AppBundle:Sport')->getAdditionalCompetitions($sportId);

    if ($additionals) {
      foreach ($additionals as $single) {
        array_push($sportIds, $single['id']);
      }
      array_push($sportIds, $sportId);
    }
    else {
      array_push($sportIds, $sportId);
    }
    // dump($sportId);
    $sportIdsFixed = implode(',', $sportIds);

    $query = "SELECT ftlss.id, ftlss.season FROM ft_league_season_sport ftlss WHERE ftlss.sport_id IN ($sportIdsFixed) ORDER BY ftlss.season DESC LIMIT 1";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->execute();
    $sportSeason = $stmt->fetch();

    if ($sportSeason) {
      $sportSeason['id'] = intval($sportSeason['id']);
    }

    return $sportSeason;
  }

  public function getLatestSportSeasons($sportId, $limit = 1) {
    $em = $this->getEntityManager();
    $sportIds = [];
    $result = [];

    // use additionals for getting Latest Sport Season
    // για competition όπως Α' Χιλής, Α' Χιλής Apertura, Α' Χιλής Clausura
    // η A' Χιλής δεν θα έχει ποτέ ft_league_season_sport (παρά μόνο τα additional της)
    $additionals = $em->getRepository('AppBundle:Sport')->getAdditionalCompetitions($sportId);

    if ($additionals) {
      foreach ($additionals as $single) {
        array_push($sportIds, $single['id']);
      }
      array_push($sportIds, $sportId);
    }
    else {
      array_push($sportIds, $sportId);
    }

    $sportIdsFixed = implode(',', $sportIds);

    $query = "SELECT ftlss.season FROM ft_league_season_sport ftlss WHERE ftlss.sport_id IN ($sportIdsFixed) ORDER BY ftlss.season DESC LIMIT $limit";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->execute();
    $sportSeasons = $stmt->fetchAll();

    if ($sportSeasons) {
      foreach ($sportSeasons as $sportSeason) {
        $result[$sportSeason['season']] = $sportSeason['season'];
      }
    }

    return $result;
  }

  public function getSportLeagueSeason($leagueSeasonSportId) {
    $em = $this->getEntityManager();
    $result = null;
    $whereClause = '';

    if (1 < count($leagueSeasonSportId)) {
      $arrToCommaString = implode(',', $leagueSeasonSportId);
      $whereClause = "lss.id IN ($arrToCommaString) ";
    }
    else {
      $whereClause = "lss.id = $leagueSeasonSportId[0] ";
    }

    $query = "
      SELECT ls.sport_id, ls.l_id league_id, lss.season, d.id domain_id
      FROM ft_league_season_sport lss INNER JOIN ft_league_sport ls ON ls.sport_id = lss.sport_id 
        LEFT JOIN domain_sport ds ON ds.sport_id = ls.sport_id
        LEFT JOIN domain d ON d.id = ds.domain_id
      WHERE 
          d.is_active = 1 
      AND ds.has_standings = 1
      AND $whereClause
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->execute();
    $lss = $stmt->fetchAll();

    if ($lss) {
      foreach ($lss as $key => $value) {
        if ($value['sport_id'] && $value['league_id'] && $value['season']) {
          $result[$value['sport_id']]['league_id'] = $value['league_id'];
          $result[$value['sport_id']]['sport_id'] = $value['sport_id'];
          $result[$value['sport_id']]['season'] = $value['season'];
          $result[$value['sport_id']]['domain'][] = $value['domain_id'];

          // $result[$key]['sport_id']   = $value['sport_id'];
          // $result[$key]['league_id']  = $value['league_id'];
          // $result[$key]['season']     = $value['season'];
          // $result[$key]['domain'][]     = $value['domain_id'];
        }
      }
    }

    return $result;
  }
}
