<?php

namespace AppBundle\Repository;

/**
 * FootballFieldRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FootballFieldRepository extends \Doctrine\ORM\EntityRepository
{
	public function getFootballFieldsByCountryId($countryId, $country)
    {
        try {
            $teams = $this->createQueryBuilder('ff')
                ->select('ff.id, ff.name')
                ->innerJoin('AppBundle:City', 'cc', 'WITH', 'cc.weatherApiId = ff.weatherApiId')
                ->where('cc.country = :countryId')
                ->setParameter('countryId', $country)
                ->getQuery()
                ->getArrayResult();
        } catch (\Exception $e) {
            echo '<pre>';
            var_dump($e->getMessage());
            echo '</pre>';
        }

        return $teams;
    }
}
