<?php

namespace AppBundle\Repository;

/**
 * FtLeagueSeasonRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtLeagueSeasonRepository extends \Doctrine\ORM\EntityRepository
{
  public function getLatestLeagueSeason($leagueId) {
    $em = $this->getEntityManager();

    $query = "SELECT season FROM ft_league_season WHERE l_id LIKE '$leagueId%' ORDER BY season DESC LIMIT 1";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->execute();
    return $stmt->fetchColumn();
  }
}
