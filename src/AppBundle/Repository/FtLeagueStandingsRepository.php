<?php

namespace AppBundle\Repository;

/**
 * FtLeagueStandingsRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtLeagueStandingsRepository extends \Doctrine\ORM\EntityRepository
{

  public function getLeagueStandings($sportId, $season, $languageCode, $locale, $localeFallback) {
    $em                     = $this->getEntityManager();
    $additionalStandings    = false;
    $additionalAndParentIds = null;
    $singleAdditional       = false;
    $result                 = null;
    $firstSportId           = null;

    if ($sportId) {
      $sport = $em->getRepository('AppBundle:Sport')->findOneById($sportId);

      if ($sport && $sport->getHasAdditionals()) {
        $additionalStandings = true;
      }

      $singleAdditional = ($sport && $sport->getHasAdditionals() && $sport->getSingleStandings());
    }

    if ($additionalStandings) {
      $additionalAndParentIds = $em->getRepository('AppBundle:Sport')->getAdditionalsWithStandings($sportId);
      if (isset($additionalAndParentIds) && $additionalAndParentIds[0]) {
        $andWhereClause = " sport_id IN ($additionalAndParentIds[0]) AND :sportId = :sportId ";
      }
      else {
        $andWhereClause = ' 1 = 1  AND :sportId = :sportId ';
      }
    }
    else {
      $andWhereClause = " sport_id = :sportId ";
    }

    $sql = "
    SELECT
      REPLACE(COALESCE(l.name_$languageCode, l.name_$localeFallback), 'ΕΛΛΑΔΑ : SUPERLEAGUE', 'ΕΛΛΑΔΑ : STOIXIMAN SUPERLEAGUE') as leagueName,
      COALESCE(t.name_$languageCode, t.name_$localeFallback) as teamName,
      fls.sport_id,
      fls.team_id, fls.rank, fls.overall_p, fls.overall_pts, fls.overall_w, fls.overall_d, fls.overall_l, fls.overall_gf, fls.overall_ga,
      CONCAT(fls.overall_gf, ':', fls.overall_ga) goals,
      CAST(fls.overall_gf AS SIGNED) - CAST(fls.overall_ga AS SIGNED) goalDifference,
      fls.home_p, fls.home_w, fls.home_d, fls.home_l, fls.home_gf, fls.home_ga, CONCAT(fls.home_gf, ':', fls.home_ga) homeGoals,
      fls.away_p, fls.away_w, fls.away_d, fls.away_l, fls.away_gf, fls.away_ga, CONCAT(fls.away_gf, ':', fls.away_ga) awayGoals,
      fls.penalty_pts, fls.penalty_hgf, fls.penalty_hga, fls.penalty_agf, fls.penalty_aga
    FROM
      ft_league_standings fls
      INNER JOIN sport s ON s.id = fls.sport_id
      LEFT JOIN ft_team t ON t.team_id = fls.team_id
      LEFT JOIN ft_league l ON l.l_id = fls.l_id
    WHERE
      $andWhereClause
    AND season = :season
    ORDER BY s.standings_priority, fls.l_id, `rank`
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->bindValue (":season", $season);
    $stmt->execute();
    $leagueStandings = $stmt->fetchAll();

    // if additionals and parent sport single_standings = 1 then parse league standings and include only the teams from the first sport_id in order list
    if ($singleAdditional && $leagueStandings) {
      foreach ($leagueStandings as $key => &$single) {
        if (0 === $key) { 
          $firstSportId = $single['sport_id'];
        }
        else {
          if ($firstSportId != $single['sport_id']) {
            unset($leagueStandings[$key]);
          }
        }
      }
    }

    if (isset($additionalAndParentIds) && $additionalAndParentIds[0]) {
      $leagueStandingsInfo = ($firstSportId) ? $this->getLeagueStandingsInfo($firstSportId, $season, $locale, $localeFallback, true) : $this->getLeagueStandingsInfo($additionalAndParentIds[0], $season, $locale, $localeFallback, true);
    }
    else {
      $leagueStandingsInfo = $this->getLeagueStandingsInfo($sportId, $season, $locale, $localeFallback);
    }

    $em->clear();
    return $result = array('standings' => $leagueStandings, 'info' => $leagueStandingsInfo);
  }

  public function getLeagueStandingsInfo($sportId, $season, $locale, $localeFallback, $hasAdditionals = false) {
    $em             = $this->getEntityManager();
    $result         = null;
    $andWhereClause = '';

    if ($hasAdditionals) {
      $andWhereClause = " lsi.sport_id IN ($sportId) ";
    }
    else {
      $andWhereClause = " lsi.sport_id = :sportId ";
    }

    $sql = "
      SELECT lsi.sport_id, option_id, (SELECT COALESCE(lso.name_$locale, lso.name_$localeFallback) FROM ft_league_standings_option lso WHERE lso.id = lsi.option_id) optionName, value FROM ft_league_standings_info lsi WHERE $andWhereClause AND lsi.season = :season ORDER BY lsi.sport_id
    ";

    // cannot prepare a statement with string as integer values, so inject the $sportId in the $andWhereClause
    // and only bind :sportId if there are no additionals
    $stmt = $em->getConnection()->prepare($sql);
    if (!$hasAdditionals) {
      $stmt->bindValue (":sportId", $sportId);
    }
    $stmt->bindValue (":season", $season);
    $stmt->execute();
    $result = $stmt->fetchAll();

    $em->clear();
    return $result;
  }

  public function getLeagueStandingsHalves($sportId, $season, $languageCode, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();
    $result = null;
    
    $sql = "
    (SELECT
      '".$translations[0]."' leagueName, tmp.sport_id, tmp.teamId team_id, tmp.teamName, (@cnt1 := @cnt1 + 1) AS `rank`, tmp.overall_pts, tmp.overall_p, tmp.overall_w, tmp.overall_d, tmp.overall_l, tmp.overall_gf, tmp.overall_ga, CONCAT(tmp.overall_gf, ':', tmp.overall_ga) goals, CAST(tmp.overall_gf AS SIGNED) - CAST(tmp.overall_ga AS SIGNED) goalDifference, tmp.home_w, tmp.home_d, tmp.home_l, tmp.home_gf, tmp.home_ga, CONCAT(tmp.home_gf, ':', tmp.home_ga) homeGoals, tmp.away_w, tmp.away_d, tmp.away_l, tmp.away_gf, tmp.away_ga, CONCAT(tmp.away_gf, ':', tmp.away_ga) awayGoals FROM (
    SELECT 
      fls.team_id teamId,
      fls.sport_id,
      COALESCE(t.name_$languageCode, t.name_$localeFallback) as teamName,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF(f.ht1 > f.ht2, 3, IF(f.ht1 = f.ht2, 1, 0))
        WHEN f.a_team = fls.team_id THEN IF(f.ht1 > f.ht2, 0, IF(f.ht1 = f.ht2, 1, 3))
      END) overall_pts, 
      COUNT(1) overall_p,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF(f.ht1 > f.ht2, 1, 0)
        WHEN f.a_team = fls.team_id THEN IF(f.ht1 < f.ht2, 1, 0)
      END) overall_w,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF(f.ht1 = f.ht2, 1, 0)
        WHEN f.a_team = fls.team_id THEN IF(f.ht1 = f.ht2, 1, 0)
      END) overall_d,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF(f.ht1 < f.ht2, 1, 0)
        WHEN f.a_team = fls.team_id THEN IF(f.ht1 > f.ht2, 1, 0)
      END) overall_l,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN f.ht1
        WHEN f.a_team = fls.team_id THEN f.ht2
      END) overall_gf,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN f.ht2
        WHEN f.a_team = fls.team_id THEN f.ht1
      END) overall_ga,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF(f.ht1 > f.ht2, 1, 0)
        ELSE 0
      END) home_w,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF(f.ht1 = f.ht2, 1, 0)
        ELSE 0
      END) home_d,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF(f.ht1 < f.ht2, 1, 0)
        ELSE 0
      END) home_l,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN f.ht1
        ELSE 0
      END) home_gf,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN f.ht2
        ELSE 0
      END) home_ga,
      SUM(CASE 
        WHEN f.a_team = fls.team_id THEN IF(f.ht1 < f.ht2, 1, 0)
        ELSE 0
      END) away_w,
      SUM(CASE 
        WHEN f.a_team = fls.team_id THEN IF(f.ht1 = f.ht2, 1, 0)
        ELSE 0
      END) away_d,
      SUM(CASE 
        WHEN f.a_team = fls.team_id THEN IF(f.ht1 > f.ht2, 1, 0)
        ELSE 0
      END) away_l,
      SUM(CASE 
        WHEN f.a_team = fls.team_id THEN f.ht2
        ELSE 0
      END) away_gf,
      SUM(CASE 
        WHEN f.a_team = fls.team_id THEN f.ht1
        ELSE 0
      END) away_ga
    FROM 
      ft_league_standings fls
        INNER JOIN ft_fixture f ON f.sport_id = fls.sport_id AND f.season = fls.season AND (f.h_team = fls.team_id OR f.a_team = fls.team_id) AND f.status IN ('F', 'FE', 'FP')
        INNER JOIN ft_team t ON t.team_id = fls.team_id
    WHERE 
        fls.sport_id = :sportId
    AND fls.season = :season
    GROUP BY fls.team_id
    ORDER BY overall_pts DESC
    ) tmp
      CROSS JOIN (SELECT @cnt1 := 0) as temp)
    UNION ALL
    (SELECT
      '".$translations[1]."' leagueName, tmp.sport_id, tmp.teamId, tmp.teamName, (@cnt2 := @cnt2 + 1) AS `rank`, tmp.overall_pts, tmp.overall_p, tmp.overall_w, tmp.overall_d, tmp.overall_l, tmp.overall_gf, tmp.overall_ga, CONCAT(tmp.overall_gf, ':', tmp.overall_ga) goals, CAST(tmp.overall_gf AS SIGNED) - CAST(tmp.overall_ga AS SIGNED) goalDifference, tmp.home_w, tmp.home_d, tmp.home_l, tmp.home_gf, tmp.home_ga, CONCAT(tmp.home_gf, ':', tmp.home_ga) homeGoals, tmp.away_w, tmp.away_d, tmp.away_l, tmp.away_gf, tmp.away_ga, CONCAT(tmp.away_gf, ':', tmp.away_ga) awayGoals FROM (
    SELECT 
      fls.team_id teamId,
      fls.sport_id,
      COALESCE(t.name_$languageCode, t.name_$localeFallback) as teamName,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF((f.ft1 - f.ht1) > (f.ft2 - f.ht2), 3, IF((f.ft1 - f.ht1) = (f.ft2 - f.ht2), 1, 0))
        WHEN f.a_team = fls.team_id THEN IF((f.ft1 - f.ht1) > (f.ft2 - f.ht2), 0, IF((f.ft1 - f.ht1) = (f.ft2 - f.ht2), 1, 3))
      END) overall_pts, 
      COUNT(1) overall_p,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF((f.ft1 - f.ht1) > (f.ft2 - f.ht2), 1, 0)
        WHEN f.a_team = fls.team_id THEN IF((f.ft1 - f.ht1) < (f.ft2 - f.ht2), 1, 0)
      END) overall_w,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF((f.ft1 - f.ht1) = (f.ft2 - f.ht2), 1, 0)
        WHEN f.a_team = fls.team_id THEN IF((f.ft1 - f.ht1) = (f.ft2 - f.ht2), 1, 0)
      END) overall_d,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF((f.ft1 - f.ht1) < (f.ft2 - f.ht2), 1, 0)
        WHEN f.a_team = fls.team_id THEN IF((f.ft1 - f.ht1) > (f.ft2 - f.ht2), 1, 0)
      END) overall_l,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN f.ft1 - f.ht1
        WHEN f.a_team = fls.team_id THEN f.ft2 - f.ht2
      END) overall_gf,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN f.ft2 - f.ht2
        WHEN f.a_team = fls.team_id THEN f.ft1 - f.ht1
      END) overall_ga,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF((f.ft1 - f.ht1) > (f.ft2 - f.ht2), 1, 0)
        ELSE 0
      END) home_w,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF((f.ft1 - f.ht1) = (f.ft2 - f.ht2), 1, 0)
        ELSE 0
      END) home_d,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN IF((f.ft1 - f.ht1) < (f.ft2 - f.ht2), 1, 0)
        ELSE 0
      END) home_l,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN f.ft1 - f.ht1
        ELSE 0
      END) home_gf,
      SUM(CASE 
        WHEN f.h_team = fls.team_id THEN f.ft2 - f.ht2
        ELSE 0
      END) home_ga,
      SUM(CASE 
        WHEN f.a_team = fls.team_id THEN IF((f.ft1 - f.ht1) < (f.ft2 - f.ht2), 1, 0)
        ELSE 0
      END) away_w,
      SUM(CASE 
        WHEN f.a_team = fls.team_id THEN IF((f.ft1 - f.ht1) = (f.ft2 - f.ht2), 1, 0)
        ELSE 0
      END) away_d,
      SUM(CASE 
        WHEN f.a_team = fls.team_id THEN IF((f.ft1 - f.ht1) > (f.ft2 - f.ht2), 1, 0)
        ELSE 0
      END) away_l,
      SUM(CASE 
        WHEN f.a_team = fls.team_id THEN f.ft2 - f.ht2
        ELSE 0
      END) away_gf,
      SUM(CASE 
        WHEN f.a_team = fls.team_id THEN f.ft1 - f.ht1
        ELSE 0
      END) away_ga
    FROM 
      ft_league_standings fls
        INNER JOIN ft_fixture f ON f.sport_id = fls.sport_id AND f.season = fls.season AND (f.h_team = fls.team_id OR f.a_team = fls.team_id) AND f.status IN ('F', 'FE', 'FP')
        INNER JOIN ft_team t ON t.team_id = fls.team_id
    WHERE 
        fls.sport_id = :sportId
    AND fls.season = :season
    GROUP BY fls.team_id
    ORDER BY overall_pts DESC
    ) tmp
      CROSS JOIN (SELECT @cnt2 := 0) as temp)
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->bindValue (":season", $season);
    $stmt->execute();
    $leagueStandings = $stmt->fetchAll();

    if ($leagueStandings) {
      foreach ($leagueStandings as $key => &$single) {
        if (0 === $key) { 
          $firstSportId = $single['sport_id'];
        }
        else {
          if ($firstSportId != $single['sport_id']) {
            unset($leagueStandings[$key]);
          }
        }
      }
    }

    $em->clear();
    // return $result;
    return $result = array('standings' => $leagueStandings);
  }

 /**
  * Competition Teams with most 11, 1X, 12, X1, XX, X2, 21, 2X, 22 home/away
  */
  function getTeam1x2HalfFinal($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $sortHome11 = [];
    $sortHome1X = [];
    $sortHome12 = [];
    $sortHomeX1 = [];
    $sortHomeXX = [];
    $sortHomeX2 = [];
    $sortHome21 = [];
    $sortHome2X = [];
    $sortHome22 = [];
    $sortAway11 = [];
    $sortAway1X = [];
    $sortAway12 = [];
    $sortAwayX1 = [];
    $sortAwayXX = [];
    $sortAwayX2 = [];
    $sortAway21 = [];
    $sortAway2X = [];
    $sortAway22 = [];
    $home11 = [];
    $home1X = [];
    $home12 = [];
    $homeX1 = [];
    $homeXX = [];
    $homeX2 = [];
    $home21 = [];
    $home2X = [];
    $home22 = [];
    $away11 = [];
    $away1X = [];
    $away12 = [];
    $awayX1 = [];
    $awayXX = [];
    $awayX2 = [];
    $away21 = [];
    $away2X = [];
    $away22 = [];

    $query = "
    SELECT
      fls.team_id teamId,
      COALESCE(t.name_$locale, t.name_$localeFallback) as teamName,
      SUM(CASE WHEN fls.team_id = f.h_team THEN 1 ELSE 0 END) homePlayed,
      SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 > ht2, IF(ft1 > ft2, 1, 0), 0) ELSE 0 END) home11,
      SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 > ht2, IF(ft1 = ft2, 1, 0), 0) ELSE 0 END) home1X,
      SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 > ht2, IF(ft1 < ft2, 1, 0), 0) ELSE 0 END) home12,
      SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 = ht2, IF(ft1 > ft2, 1, 0), 0) ELSE 0 END) homeX1,
      SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 = ht2, IF(ft1 = ft2, 1, 0), 0) ELSE 0 END) homeXX,
      SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 = ht2, IF(ft1 < ft2, 1, 0), 0) ELSE 0 END) homeX2,
      SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 < ht2, IF(ft1 > ft2, 1, 0), 0) ELSE 0 END) home21,
      SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 < ht2, IF(ft1 = ft2, 1, 0), 0) ELSE 0 END) home2X,
      SUM(CASE WHEN fls.team_id = f.h_team THEN IF(ht1 < ht2, IF(ft1 < ft2, 1, 0), 0) ELSE 0 END) home22,
      SUM(CASE WHEN fls.team_id = f.a_team THEN 1 ELSE 0 END) awayPlayed,
      SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 > ht2, IF(ft1 > ft2, 1, 0), 0) ELSE 0 END) away11,
      SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 > ht2, IF(ft1 = ft2, 1, 0), 0) ELSE 0 END) away1X,
      SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 > ht2, IF(ft1 < ft2, 1, 0), 0) ELSE 0 END) away12,
      SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 = ht2, IF(ft1 > ft2, 1, 0), 0) ELSE 0 END) awayX1,
      SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 = ht2, IF(ft1 = ft2, 1, 0), 0) ELSE 0 END) awayXX,
      SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 = ht2, IF(ft1 < ft2, 1, 0), 0) ELSE 0 END) awayX2,
      SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 < ht2, IF(ft1 > ft2, 1, 0), 0) ELSE 0 END) away21,
      SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 < ht2, IF(ft1 = ft2, 1, 0), 0) ELSE 0 END) away2X,
      SUM(CASE WHEN fls.team_id = f.a_team THEN IF(ht1 < ht2, IF(ft1 < ft2, 1, 0), 0) ELSE 0 END) away22
    FROM 
      ft_league_standings fls
        INNER JOIN ft_fixture f ON f.sport_id = fls.sport_id AND f.season = fls.season AND (f.h_team = fls.team_id OR f.a_team = fls.team_id) AND f.status IN ('F', 'FE', 'FP')
        INNER JOIN ft_team t ON t.team_id = fls.team_id
    WHERE 
        fls.sport_id = :sportId
    AND fls.season = :season
    GROUP BY fls.team_id
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->bindValue (":season", $season);
    $stmt->execute();
    $data = $stmt->fetchAll();

    if ($data) {
      foreach ($data as $key => $single) {
        $home11Perc = ($single['homePlayed'] > 0) ? round($single['home11'] * 100 / $single['homePlayed'], 0).'%' : '-';
        $home1XPerc = ($single['homePlayed'] > 0) ? round($single['home1X'] * 100 / $single['homePlayed'], 0).'%' : '-';
        $home12Perc = ($single['homePlayed'] > 0) ? round($single['home12'] * 100 / $single['homePlayed'], 0).'%' : '-';
        $homeX1Perc = ($single['homePlayed'] > 0) ? round($single['homeX1'] * 100 / $single['homePlayed'], 0).'%' : '-';
        $homeXXPerc = ($single['homePlayed'] > 0) ? round($single['homeXX'] * 100 / $single['homePlayed'], 0).'%' : '-';
        $homeX2Perc = ($single['homePlayed'] > 0) ? round($single['homeX2'] * 100 / $single['homePlayed'], 0).'%' : '-';
        $home21Perc = ($single['homePlayed'] > 0) ? round($single['home21'] * 100 / $single['homePlayed'], 0).'%' : '-';
        $home2XPerc = ($single['homePlayed'] > 0) ? round($single['home2X'] * 100 / $single['homePlayed'], 0).'%' : '-';
        $home22Perc = ($single['homePlayed'] > 0) ? round($single['home22'] * 100 / $single['homePlayed'], 0).'%' : '-';
        $home11[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'home11' => $single['home11'].'/'.$single['homePlayed'], 'percentage' => $home11Perc);
        $home1X[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'home1X' => $single['home1X'].'/'.$single['homePlayed'], 'percentage' => $home1XPerc);
        $home12[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'home12' => $single['home12'].'/'.$single['homePlayed'], 'percentage' => $home12Perc);
        $homeX1[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'homeX1' => $single['homeX1'].'/'.$single['homePlayed'], 'percentage' => $homeX1Perc);
        $homeXX[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'homeXX' => $single['homeXX'].'/'.$single['homePlayed'], 'percentage' => $homeXXPerc);
        $homeX2[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'homeX2' => $single['homeX2'].'/'.$single['homePlayed'], 'percentage' => $homeX2Perc);
        $home21[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'home21' => $single['home21'].'/'.$single['homePlayed'], 'percentage' => $home21Perc);
        $home2X[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'home2X' => $single['home2X'].'/'.$single['homePlayed'], 'percentage' => $home2XPerc);
        $home22[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'home22' => $single['home22'].'/'.$single['homePlayed'], 'percentage' => $home22Perc);
        $sortHome11[$key] = $single['home11'];
        $sortHome1X[$key] = $single['home1X'];
        $sortHome12[$key] = $single['home12'];
        $sortHomeX1[$key] = $single['homeX1'];
        $sortHomeXX[$key] = $single['homeXX'];
        $sortHomeX2[$key] = $single['homeX2'];
        $sortHome21[$key] = $single['home21'];
        $sortHome2X[$key] = $single['home2X'];
        $sortHome22[$key] = $single['home22'];

        $away11Perc = ($single['awayPlayed'] > 0) ? round($single['away11'] * 100 / $single['awayPlayed'], 0).'%' : '-';
        $away1XPerc = ($single['awayPlayed'] > 0) ? round($single['away1X'] * 100 / $single['awayPlayed'], 0).'%' : '-';
        $away12Perc = ($single['awayPlayed'] > 0) ? round($single['away12'] * 100 / $single['awayPlayed'], 0).'%' : '-';
        $awayX1Perc = ($single['awayPlayed'] > 0) ? round($single['awayX1'] * 100 / $single['awayPlayed'], 0).'%' : '-';
        $awayXXPerc = ($single['awayPlayed'] > 0) ? round($single['awayXX'] * 100 / $single['awayPlayed'], 0).'%' : '-';
        $awayX2Perc = ($single['awayPlayed'] > 0) ? round($single['awayX2'] * 100 / $single['awayPlayed'], 0).'%' : '-';
        $away21Perc = ($single['awayPlayed'] > 0) ? round($single['away21'] * 100 / $single['awayPlayed'], 0).'%' : '-';
        $away2XPerc = ($single['awayPlayed'] > 0) ? round($single['away2X'] * 100 / $single['awayPlayed'], 0).'%' : '-';
        $away22Perc = ($single['awayPlayed'] > 0) ? round($single['away22'] * 100 / $single['awayPlayed'], 0).'%' : '-';
        $away11[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'away11' => $single['away11'].'/'.$single['awayPlayed'], 'percentage' => $away11Perc);
        $away1X[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'away1X' => $single['away1X'].'/'.$single['awayPlayed'], 'percentage' => $away1XPerc);
        $away12[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'away12' => $single['away12'].'/'.$single['awayPlayed'], 'percentage' => $away12Perc);
        $awayX1[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'awayX1' => $single['awayX1'].'/'.$single['awayPlayed'], 'percentage' => $awayX1Perc);
        $awayXX[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'awayXX' => $single['awayXX'].'/'.$single['awayPlayed'], 'percentage' => $awayXXPerc);
        $awayX2[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'awayX2' => $single['awayX2'].'/'.$single['awayPlayed'], 'percentage' => $awayX2Perc);
        $away21[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'away21' => $single['away21'].'/'.$single['awayPlayed'], 'percentage' => $away21Perc);
        $away2X[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'away2X' => $single['away2X'].'/'.$single['awayPlayed'], 'percentage' => $away2XPerc);
        $away22[] = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'away22' => $single['away22'].'/'.$single['awayPlayed'], 'percentage' => $away22Perc);
        $sortAway11[$key] = $single['away11'];
        $sortAway1X[$key] = $single['away1X'];
        $sortAway12[$key] = $single['away12'];
        $sortAwayX1[$key] = $single['awayX1'];
        $sortAwayXX[$key] = $single['awayXX'];
        $sortAwayX2[$key] = $single['awayX2'];
        $sortAway21[$key] = $single['away21'];
        $sortAway2X[$key] = $single['away2X'];
        $sortAway22[$key] = $single['away22'];
      }
    }

    array_multisort($sortHome11, SORT_DESC, $home11);
    array_multisort($sortHome1X, SORT_DESC, $home1X);
    array_multisort($sortHome12, SORT_DESC, $home12);
    array_multisort($sortHomeX1, SORT_DESC, $homeX1);
    array_multisort($sortHomeXX, SORT_DESC, $homeXX);
    array_multisort($sortHomeX2, SORT_DESC, $homeX2);
    array_multisort($sortHome21, SORT_DESC, $home21);
    array_multisort($sortHome2X, SORT_DESC, $home2X);
    array_multisort($sortHome22, SORT_DESC, $home22);
    array_multisort($sortAway11, SORT_DESC, $away11);
    array_multisort($sortAway1X, SORT_DESC, $away1X);
    array_multisort($sortAway12, SORT_DESC, $away12);
    array_multisort($sortAwayX1, SORT_DESC, $awayX1);
    array_multisort($sortAwayXX, SORT_DESC, $awayXX);
    array_multisort($sortAwayX2, SORT_DESC, $awayX2);
    array_multisort($sortAway21, SORT_DESC, $away21);
    array_multisort($sortAway2X, SORT_DESC, $away2X);
    array_multisort($sortAway22, SORT_DESC, $away22);

    $result = array(
      'home11' => array('label' => '1/1 '.$translations[0], 'fields' => array('namegr' => $translations[2], 'home11' => '', 'percentage' => '%'), 'data' => array_slice($home11, 0, 5)), 
      'home1X' => array('label' => '1/X '.$translations[0], 'fields' => array('namegr' => $translations[2], 'home1X' => '', 'percentage' => '%'), 'data' => array_slice($home1X, 0, 5)), 
      'home12' => array('label' => '1/2 '.$translations[0], 'fields' => array('namegr' => $translations[2], 'home12' => '', 'percentage' => '%'), 'data' => array_slice($home12, 0, 5)), 
      'homeX1' => array('label' => 'X/1 '.$translations[0], 'fields' => array('namegr' => $translations[2], 'homeX1' => '', 'percentage' => '%'), 'data' => array_slice($homeX1, 0, 5)), 
      'homeXX' => array('label' => 'X/X '.$translations[0], 'fields' => array('namegr' => $translations[2], 'homeXX' => '', 'percentage' => '%'), 'data' => array_slice($homeXX, 0, 5)), 
      'homeX2' => array('label' => 'X/2 '.$translations[0], 'fields' => array('namegr' => $translations[2], 'homeX2' => '', 'percentage' => '%'), 'data' => array_slice($homeX2, 0, 5)), 
      'home21' => array('label' => '2/1 '.$translations[0], 'fields' => array('namegr' => $translations[2], 'home21' => '', 'percentage' => '%'), 'data' => array_slice($home21, 0, 5)), 
      'home2X' => array('label' => '2/X '.$translations[0], 'fields' => array('namegr' => $translations[2], 'home2X' => '', 'percentage' => '%'), 'data' => array_slice($home2X, 0, 5)), 
      'home22' => array('label' => '2/2 '.$translations[0], 'fields' => array('namegr' => $translations[2], 'home22' => '', 'percentage' => '%'), 'data' => array_slice($home22, 0, 5)),
      'away11' => array('label' => '1/1 '.$translations[1], 'fields' => array('namegr' => $translations[2], 'away11' => '', 'percentage' => '%'), 'data' => array_slice($away11, 0, 5)), 
      'away1X' => array('label' => '1/X '.$translations[1], 'fields' => array('namegr' => $translations[2], 'away1X' => '', 'percentage' => '%'), 'data' => array_slice($away1X, 0, 5)), 
      'away12' => array('label' => '1/2 '.$translations[1], 'fields' => array('namegr' => $translations[2], 'away12' => '', 'percentage' => '%'), 'data' => array_slice($away12, 0, 5)), 
      'awayX1' => array('label' => 'X/1 '.$translations[1], 'fields' => array('namegr' => $translations[2], 'awayX1' => '', 'percentage' => '%'), 'data' => array_slice($awayX1, 0, 5)), 
      'awayXX' => array('label' => 'X/X '.$translations[1], 'fields' => array('namegr' => $translations[2], 'awayXX' => '', 'percentage' => '%'), 'data' => array_slice($awayXX, 0, 5)), 
      'awayX2' => array('label' => 'X/2 '.$translations[1], 'fields' => array('namegr' => $translations[2], 'awayX2' => '', 'percentage' => '%'), 'data' => array_slice($awayX2, 0, 5)), 
      'away21' => array('label' => '2/1 '.$translations[1], 'fields' => array('namegr' => $translations[2], 'away21' => '', 'percentage' => '%'), 'data' => array_slice($away21, 0, 5)), 
      'away2X' => array('label' => '2/X '.$translations[1], 'fields' => array('namegr' => $translations[2], 'away2X' => '', 'percentage' => '%'), 'data' => array_slice($away2X, 0, 5)), 
      'away22' => array('label' => '2/2 '.$translations[1], 'fields' => array('namegr' => $translations[2], 'away22' => '', 'percentage' => '%'), 'data' => array_slice($away22, 0, 5)),
    );

    return $result;
  }

 /**
  * Competition Teams with goals 0-1, 0-2, 2-3, 3+, 4-6, 7+
  */
  function getTeamGoals($sportId, $season, $locale, $localeFallback, $translations) {
    $em = $this->getEntityManager();

    $sortGoals01 = [];
    $sortGoals02 = [];
    $sortGoals23 = [];
    $sortGoals3 = [];
    $sortGoals46 = [];
    $sortGoals7 = [];
    $goals01 = [];
    $goals02 = [];
    $goals3 = [];
    $goals23 = [];
    $goals46 = [];
    $goals7 = [];


    $query = "
    SELECT
      fls.team_id teamId,
      COALESCE(t.name_$locale, t.name_$localeFallback) as teamName,
      COUNT(1) totalPlayed,
      SUM(CASE WHEN (fls.team_id = f.h_team OR fls.team_id = f.a_team) THEN IF(ft1 + ft2 <= 1, 1, 0) ELSE 0 END) goals01,
      SUM(CASE WHEN (fls.team_id = f.h_team OR fls.team_id = f.a_team) THEN IF(ft1 + ft2 <= 2, 1, 0) ELSE 0 END) goals02,
      SUM(CASE WHEN (fls.team_id = f.h_team OR fls.team_id = f.a_team) THEN IF((ft1 + ft2 >= 2) AND (ft1 + ft2 <= 3), 1, 0) ELSE 0 END) goals23,
      SUM(CASE WHEN (fls.team_id = f.h_team OR fls.team_id = f.a_team) THEN IF(ft1 + ft2 >= 3, 1, 0) ELSE 0 END) goals3,
      SUM(CASE WHEN (fls.team_id = f.h_team OR fls.team_id = f.a_team) THEN IF((ft1 + ft2 >= 4) AND (ft1 + ft2 <= 6), 1, 0) ELSE 0 END) goals46,
      SUM(CASE WHEN (fls.team_id = f.h_team OR fls.team_id = f.a_team) THEN IF(ft1 + ft2 >= 7, 1, 0) ELSE 0 END) goals7
    FROM 
      ft_league_standings fls
        INNER JOIN ft_fixture f ON f.sport_id = fls.sport_id AND f.season = fls.season AND (f.h_team = fls.team_id OR f.a_team = fls.team_id) AND f.status IN ('F', 'FE', 'FP')
        INNER JOIN ft_team t ON t.team_id = fls.team_id
    WHERE 
        fls.sport_id = :sportId
    AND fls.season = :season
    GROUP BY fls.team_id
    ";

    $stmt = $em->getConnection()->prepare($query);
    $stmt->bindValue (":sportId", $sportId);
    $stmt->bindValue (":season", $season);
    $stmt->execute();
    $data = $stmt->fetchAll();

    if ($data) {
      foreach ($data as $key => $single) {
        $goals01Perc  = ($single['totalPlayed'] > 0) ? round($single['goals01'] * 100 / $single['totalPlayed'], 0).'%' : '-';
        $goals02Perc  = ($single['totalPlayed'] > 0) ? round($single['goals02'] * 100 / $single['totalPlayed'], 0).'%' : '-';
        $goals23Perc  = ($single['totalPlayed'] > 0) ? round($single['goals23'] * 100 / $single['totalPlayed'], 0).'%' : '-';
        $goals3Perc   = ($single['totalPlayed'] > 0) ? round($single['goals3'] * 100 / $single['totalPlayed'], 0).'%' : '-';
        $goals46Perc  = ($single['totalPlayed'] > 0) ? round($single['goals46'] * 100 / $single['totalPlayed'], 0).'%' : '-';
        $goals7Perc   = ($single['totalPlayed'] > 0) ? round($single['goals7'] * 100 / $single['totalPlayed'], 0).'%' : '-';

        $goals01[]  = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'goals01' => $single['goals01'].'/'.$single['totalPlayed'], 'percentage' => $goals01Perc);
        $goals02[]  = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'goals02' => $single['goals02'].'/'.$single['totalPlayed'], 'percentage' => $goals02Perc);
        $goals23[]  = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'goals23' => $single['goals23'].'/'.$single['totalPlayed'], 'percentage' => $goals23Perc);
        $goals3[]   = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'goals3' => $single['goals3'].'/'.$single['totalPlayed'], 'percentage' => $goals3Perc);
        $goals46[]  = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'goals46' => $single['goals46'].'/'.$single['totalPlayed'], 'percentage' => $goals46Perc);
        $goals7[]   = array('teamId' => $single['teamId'], 'namegr' => $single['teamName'], 'goals7' => $single['goals7'].'/'.$single['totalPlayed'], 'percentage' => $goals7Perc);

        $sortGoals01[$key] = $single['goals01'];
        $sortGoals02[$key] = $single['goals02'];
        $sortGoals23[$key] = $single['goals23'];
        $sortGoals3[$key] = $single['goals3'];
        $sortGoals46[$key] = $single['goals46'];
        $sortGoals7[$key] = $single['goals7'];
      }
    }

    array_multisort($sortGoals01, SORT_DESC, $goals01);
    array_multisort($sortGoals02, SORT_DESC, $goals02);
    array_multisort($sortGoals23, SORT_DESC, $goals23);
    array_multisort($sortGoals3, SORT_DESC, $goals3);
    array_multisort($sortGoals46, SORT_DESC, $goals46);
    array_multisort($sortGoals7, SORT_DESC, $goals7);

    $result = array(
      'goals01' => array('label' => '0-1 '.$translations[0], 'fields' => array('namegr' => $translations[1], 'goals01' => '', 'percentage' => '%'), 'data' => isset($goals01) ? array_slice($goals01, 0, 10) : null), 
      'goals02' => array('label' => '0-2 '.$translations[0].' (Under)', 'fields' => array('namegr' => $translations[1], 'goals02' => '', 'percentage' => '%'), 'data' => isset($goals02) ? array_slice($goals02, 0, 10) : null), 
      'goals23' => array('label' => '2-3 '.$translations[0], 'fields' => array('namegr' => $translations[1], 'goals23' => '', 'percentage' => '%'), 'data' => isset($goals23) ? array_slice($goals23, 0, 10) : null), 
      'goals3'  => array('label' => '3+ '.$translations[0].' (over)', 'fields' => array('namegr' => 'ΟΜΑΔΑ', 'goals3' => '', 'percentage' => '%'), 'data' => isset($goals3) ? array_slice($goals3, 0, 10) : null), 
      'goals46' => array('label' => '4-6 '.$translations[0], 'fields' => array('namegr' => $translations[1], 'goals46' => '', 'percentage' => '%'), 'data' => isset($goals46) ? array_slice($goals46, 0, 10) : null), 
      'goals7'  => array('label' => '7+ '.$translations[0], 'fields' => array('namegr' => $translations[1], 'goals7' => '', 'percentage' => '%'), 'data' => isset($goals7) ? array_slice($goals7, 0, 10) : null), 
    );

    return $result;
  }

    public function findEuroTeams()
    {
        $em = $this->getEntityManager();

        $sql = "(select '1' as group_field, '' as flag, t.team_id, t.name_gr, t.name_ro, f.rank, f.overall_pts as pts, f.overall_p as gp, CONCAT(f.overall_gf,':',f.overall_ga) as goals from ft_league_standings f inner join ft_team t on f.team_id = t.team_id where f.sport_id = 844 AND f.season = 2024 order by f.rank asc)
        UNION ALL
        (select '2' as group_field, '' as flag, t.team_id, t.name_gr, t.name_ro, f.rank, f.overall_pts as pts, f.overall_p as gp, CONCAT(f.overall_gf,':',f.overall_ga) as goals from ft_league_standings f inner join ft_team t on f.team_id = t.team_id where f.sport_id = 845 AND f.season = 2024 order by f.rank asc)
        UNION ALL
        (select '3' as group_field, '' as flag, t.team_id, t.name_gr, t.name_ro, f.rank, f.overall_pts as pts, f.overall_p as gp, CONCAT(f.overall_gf,':',f.overall_ga) as goals from ft_league_standings f inner join ft_team t on f.team_id = t.team_id where f.sport_id = 846 AND f.season = 2024 order by f.rank asc)
        UNION ALL
        (select '4' as group_field, '' as flag, t.team_id, t.name_gr, t.name_ro, f.rank, f.overall_pts as pts, f.overall_p as gp, CONCAT(f.overall_gf,':',f.overall_ga) as goals from ft_league_standings f inner join ft_team t on f.team_id = t.team_id where f.sport_id = 847 AND f.season = 2024 order by f.rank asc)
        UNION ALL
        (select '5' as group_field, '' as flag, t.team_id, t.name_gr, t.name_ro, f.rank, f.overall_pts as pts, f.overall_p as gp, CONCAT(f.overall_gf,':',f.overall_ga) as goals from ft_league_standings f inner join ft_team t on f.team_id = t.team_id where f.sport_id = 848 AND f.season = 2024 order by f.rank asc)
        UNION ALL
        (select '6' as group_field, '' as flag, t.team_id, t.name_gr, t.name_ro, f.rank, f.overall_pts as pts, f.overall_p as gp, CONCAT(f.overall_gf,':',f.overall_ga) as goals from ft_league_standings f inner join ft_team t on f.team_id = t.team_id where f.sport_id = 849 AND f.season = 2024 order by f.rank asc)
        ORDER BY group_field ASC, `rank` ASC";

        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute();
        $leagueStandings = $stmt->fetchAll();

        $em->clear();

        return $leagueStandings;
    }

    public function findCopaAmericaTeams()
    {
        $em = $this->getEntityManager();

        $sql = "(select '1' as group_field, '' as flag, t.team_id, t.name_gr, t.name_ro, f.rank, f.overall_pts as pts, f.overall_p as gp, CONCAT(f.overall_gf,':',f.overall_ga) as goals from ft_league_standings f inner join ft_team t on f.team_id = t.team_id where f.sport_id = 802 AND f.season = 2024 order by f.rank asc)
        UNION ALL
        (select '2' as group_field, '' as flag, t.team_id, t.name_gr, t.name_ro, f.rank, f.overall_pts as pts, f.overall_p as gp, CONCAT(f.overall_gf,':',f.overall_ga) as goals from ft_league_standings f inner join ft_team t on f.team_id = t.team_id where f.sport_id = 803 AND f.season = 2024 order by f.rank asc)
         UNION ALL
        (select '3' as group_field, '' as flag, t.team_id, t.name_gr, t.name_ro, f.rank, f.overall_pts as pts, f.overall_p as gp, CONCAT(f.overall_gf,':',f.overall_ga) as goals from ft_league_standings f inner join ft_team t on f.team_id = t.team_id where f.sport_id = 804 AND f.season = 2024 order by f.rank asc)
        UNION ALL
        (select '4' as group_field, '' as flag, t.team_id, t.name_gr, t.name_ro, f.rank, f.overall_pts as pts, f.overall_p as gp, CONCAT(f.overall_gf,':',f.overall_ga) as goals from ft_league_standings f inner join ft_team t on f.team_id = t.team_id where f.sport_id = 1175 AND f.season = 2024 order by f.rank asc)
        ORDER BY group_field ASC, `rank` ASC";

        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute();
        $leagueStandings = $stmt->fetchAll();

        $em->clear();

        return $leagueStandings;
    }

  public function findCustomCompetitionTeams(): array
  {
    $em = $this->getEntityManager();
    $sql  = " SELECT
                t.id,
                t.name_gr,
                t.flag,
                t.team_url_gr AS url,
                COALESCE(t.pts,  ls.overall_pts)   AS pts,
                COALESCE(t.gp,   ls.overall_p)      AS gp,
                COALESCE(t.goals, CONCAT(ls.overall_gf, ':', ls.overall_ga)) AS goals
              FROM ft_competition_teams t
              LEFT JOIN ft_league_season ls_info
                ON ls_info.id = t.league_season_id
              LEFT JOIN ft_league_standings ls
                ON ls.team_id = t.team_id
               AND ls.l_id     = ls_info.l_id
               AND ls.season   = ls_info.season
              ORDER BY
                t.team_index IS NULL,
                t.team_index ASC,
                COALESCE(t.pts, ls.overall_pts) DESC;
                  ";
    $stmt = $em->getConnection()->prepare($sql);
    $stmt->execute();
    $customCompetitionTeams = $stmt->fetchAll();
    $em->clear();
    return $customCompetitionTeams;
  }

}
