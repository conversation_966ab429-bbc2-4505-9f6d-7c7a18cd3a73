<?php

namespace AppBundle\Repository;

use Doctrine\ORM\EntityRepository;
use Gedmo\Tree\Entity\Repository\NestedTreeRepository;

/**
 * SportRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class SportRepository extends NestedTreeRepository
{

  public function findTranslation($id) {
    $em = $this->getEntityManager();

    $queryMain = "SELECT st.* FROM sport_translations st WHERE st.sport_id = :id ";
    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->bindValue(":id", $id);
    $stmt->execute();
    $sports = $stmt->fetchAll();

    return $sports;
  }

  public function findTranslationByTypeAndLocale($sportId, $type = 'normal', $locale = 'el') {
    $em = $this->getEntityManager();

    $queryMain = "SELECT st.title, st.description FROM sport_translations st WHERE st.sport_id = :id AND st.type = :type AND st.locale = :locale ";
    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->bindValue(":id", $sportId);
    $stmt->bindValue(":type", $type);
    $stmt->bindValue(":locale", $locale);
    $stmt->execute();
    $translation = $stmt->fetchAll();
    
    return $translation;
  }

  public function findRestOfTranslationsByLocale($sportId, $locale = 'el') {
    $em = $this->getEntityManager();

    $queryMain = "SELECT st.type, st.title, st.description FROM sport_translations st WHERE st.sport_id = :id AND st.locale = :locale AND st.type <> 'normal' ";
    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->bindValue(":id", $sportId);
    $stmt->bindValue(":locale", $locale);
    $stmt->execute();
    $translation = $stmt->fetchAll();

    return $translation;
  }

  public function getAllCompetitionIds() {
    $em = $this->getEntityManager();

    $queryMain = "SELECT s.id FROM sport s WHERE is_competition = true";
    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->execute();
    $sports = $stmt->fetchAll();

    foreach ($sports as &$sport) {
      // var_dump($sport['id']);
      $additional = $this->getAdditionalCompetitions($sport['id']);
      if ($additional) {
        $sport['additional'] = array('id' => $additional);
      }
      else {
        $sport['additional'] = array();
      }
    }

    return $sports;
  }

  public function getAdditionalCompetitions($competitionId) {
    $em = $this->getEntityManager();

    $queryMain = "SELECT s.id FROM sport s WHERE is_additional = true AND parent_id = $competitionId";
    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->execute();
    $sports = $stmt->fetchAll();

    return $sports;
  }

  public function getAdditionalsWithStandings($competitionId) {
    $em = $this->getEntityManager();

    // $queryMain = "SELECT s.id FROM sport s WHERE is_additional = true AND parent_id = $competitionId";
    $queryMain = "
    SELECT GROUP_CONCAT(DISTINCT s.id) FROM sport s LEFT JOIN domain_sport ds ON ds.sport_id = s.id WHERE ((is_additional = true AND parent_id = $competitionId) OR s.id = $competitionId) AND ds.has_standings = 1
    ";
    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->execute();
    // $additionals = $stmt->fetchAll();
    $additionals = $stmt->fetchAll(\PDO::FETCH_COLUMN) ?: array();

    return $additionals;
  }

  private function hasAdditionalCompetitions($competitionId) {

  }

  public function findByIdThenReturnArray($sport) {
    $result = array();

    if ($sport) {
      $result['id'] = $sport->getId();
      $result['lft'] = $sport->getLft();
      $result['lvl'] = $sport->getLvl();
      $result['rgt'] = $sport->getRgt();
      $result['slug'] = $sport->getSlug();
      $result['isSport'] = $sport->getIsSport();
      $result['isRegion'] = $sport->getIsRegion();
      $result['isCompetition'] = $sport->getIsCompetition();
      $result['isAdditional'] = $sport->getIsAdditional();
      $result['createdAt'] = $sport->getCreatedAt()->format('Y-m-d H:i:s');
      $result['updatedAt'] = $sport->getUpdatedAt()->format('Y-m-d H:i:s');
    }

    // $query = "SELECT s.id, s.lft, s.lvl, s.rgt, s.slug, s.is_sport as isSport, s.is_region as isRegion, s.is_competition as isCompetition, s.is_additional as isAdditional, s.created_at as createdAt, s.updated_at as updatedAt FROM sport s WHERE s.id = $id";
    // $stmt = $em->getConnection()->prepare($query);
    // $stmt->execute();
    // $sport = $stmt->fetchAll();
    //
    // if ($sport) {
    //   $result = $sport;
    // }

    return $result;
  }

  public function getAllDomainCompetitions($domain) {
    $em = $this->getEntityManager();
    $result = array();

    $domainId = $domain->getId();

    $query = $em->createQuery("SELECT s, ls.lId FROM AppBundle\Entity\Sport s JOIN s.domainSports ds JOIN ds.domain d JOIN s.ftLeagueSport ls WHERE d.id = $domainId AND d.isActive = true AND (s.isCompetition = true OR s.isAdditional = true) AND ds.hasPosts = true");
    $competitions = $query->getResult();


    // $query = "SELECT s.id, s.lft, s.lvl, s.rgt, s.slug, s.is_sport as isSport, s.is_region as isRegion, s.is_competition as isCompetition, s.is_additional as isAdditional, s.created_at as createdAt, s.updated_at as updatedAt FROM sport s WHERE s.id = $id";
    // $stmt = $em->getConnection()->prepare($query);
    // $stmt->execute();
    // $sport = $stmt->fetchAll();

    if ($competitions) {
      $result = $competitions;
    }

    $em->clear();
    return $result;
  }

  public function getSportsForPreviews() {
    $em = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT DISTINCT ds.sport_id
    FROM domain_sport ds INNER JOIN domain d ON d.id = ds.domain_id
    WHERE ds.has_posts = 1
    AND d.is_active = 1
    ORDER BY ds.sport_id
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetchAll(\PDO::FETCH_COLUMN) ?: array();

    $em->clear();
    return $result;
  }

  public function getActiveDomainCompetitions() {
    $em = $this->getEntityManager();
    $result = array();

    $sql = "
    SELECT  ls.l_id, ls.sport_id, ds.has_standings, ds.domain_id
    FROM domain_sport ds INNER JOIN ft_league_sport ls ON ds.sport_id = ls.sport_id INNER JOIN domain d ON d.id = ds.domain_id
    WHERE ds.has_posts = 1
    AND d.is_active = 1
    ORDER BY ls.sport_id
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->execute();
    $domainCompetitions = $stmt->fetchAll();

    if ($domainCompetitions) {
      foreach ($domainCompetitions as $domainCompetition) {
        // $result[$domainCompetition['l_id']]['sportId'] = $domainCompetition['sport_id'];
        $result[$domainCompetition['sport_id']]['domains'][]  = $domainCompetition['domain_id'];
      }
    }

    $em->clear();
    return $result;
  }

  public function getSportDataToSync($sportId, $locale) {
    $em = $this->getEntityManager();
    $result = array();

    $queryMain = "
    SELECT s.id, s.parent_id, s.slug, s.is_sport, s.is_region, s.is_competition, s.is_additional, s.tips_date_from, s.tips_date_to, st.title, st.description
    FROM sport s
    LEFT JOIN sport_translations st ON st.sport_id = s.id  AND st.locale = :locale AND st.type = 'normal'
    WHERE s.id = :id
    ";

    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->bindValue(":id", $sportId);
    $stmt->bindValue(":locale", $locale);
    $stmt->execute();
    $sportsData = $stmt->fetchAll();

    if ($sportsData) {
      $result = $sportsData;
    }

    $em->clear();
    return $result;
  }

  public function getAllSportIds() {
    $em = $this->getEntityManager();
    $result = array();

    $queryMain = "SELECT s.id FROM sport s";

    $stmt = $em->getConnection()->prepare($queryMain);
    $stmt->execute();
    $sportIds = $stmt->fetchAll(\PDO::FETCH_COLUMN);

    if ($sportIds) {
      $result = $sportIds;
    }

    $em->clear();
    return $result;
  }

  public function getIsFootball($sportId) {
    try {
      $em = $this->getEntityManager();
      $sport = null;
      // $isFootball = false;

      if (!$sportId) return 0;

      $sql = "SELECT parent_id, slug FROM sport WHERE id = $sportId";

      $stmt = $em->getConnection()->prepare($sql);
      $stmt->execute();
      $sport = $stmt->fetchAll();

      if ($sport) {
        if (isset($sport[0]) && $sport[0]['parent_id'] != 0 && $sport[0]['parent_id'] != null) {
          return $this->getIsFootball($sport[0]['parent_id']);
        }
        else {
          if (isset($sport[0]) && $sport[0]['slug'] === 'podosfairo') {
            $isFootball = 1;
            return $isFootball;
          }
          else {
            $isFootball = 0;
            return $isFootball;
          }
        }
      }
      else {
        return 0;
      }

    }
    catch (\Exception $e) {
      echo '<pre>';
      var_dump($e->getMessage());
      echo '</pre>';
      die();
    }
    return $isFootball;
  }

  /**
   * Get all football competitions connected to domain
   * Ignore has_posts = 1, and fetch all
   * @param withStandings, if true get only domain sports with has_standings = 1
   */
  public function getFootballCompetitions($domainId, $withStandings = null, $locale = "el") {
    $em             = $this->getEntityManager();
    $result         = array();
    $andWhereClause = '';

    if ($withStandings) {
      $andWhereClause = " AND ds.has_standings = 1 ";
    }
    $sql = "
    SELECT 
      (SELECT title FROM sport_translations st WHERE st.sport_id = IF(s.is_additional = 1, s2.id, s1.id) AND st.type = 'normal' AND st.locale = :locale) parent_sport,
      ds.sport_id, st.title
    FROM 
      domain_sport ds 
        INNER JOIN domain d ON d.id = ds.domain_id
        INNER JOIN sport s ON s.id = ds.sport_id
        INNER JOIN sport_translations st ON st.sport_id = s.id AND st.type = 'normal' AND st.locale = 'el'
        LEFT JOIN sport s1 ON s1.id = s.parent_id
        LEFT JOIN sport s2 ON s2.id = s1.parent_id
    WHERE
        d.id = :domainId
        $andWhereClause
    -- AND ds.has_posts = 1
    ORDER BY parent_sport, st.title
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":domainId", $domainId);
    $stmt->bindValue(":locale", $locale);
    $stmt->execute();
    $result = $stmt->fetchAll();

    $em->clear();
    return $result;    
  }

  /**
   * Get sport league season info for standings tool
   * @param sportId
   */
  public function getSportLeagueSeasonInfo($sportId) {
    $em             = $this->getEntityManager();
    $result         = array();

    $sql = "
    SELECT ls.l_id league_id, lss.season, lss.id 
    FROM 
      ft_league_sport ls 
        INNER JOIN ft_league_season_sport lss ON ls.sport_id = lss.sport_id 
    WHERE 
        ls.sport_id = :sportId
    ORDER BY season DESC 
    LIMIT 1;
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->execute();
    // $result = $stmt->fetchAll();
    $result = $stmt->fetch();

    $em->clear();
    return $result;    
  }
}
