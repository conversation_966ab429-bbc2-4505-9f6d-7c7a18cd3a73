<?php

namespace AppBundle\Repository;

/**
 * DomainSportRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class DomainSportRepository extends \Doctrine\ORM\EntityRepository
{
  public function getLeagueAndSeasonForStandings() {
    $em = $this->getEntityManager();
    $result = array();
    $lss = array();

    $sql = "
    SELECT sport_id, l_id league_id, season, domain_id FROM (
      SELECT DISTINCT ds.sport_id, l.l_id, (SELECT lss.season FROM ft_league_season_sport lss WHERE lss.sport_id = ds.sport_id ORDER BY lss.season DESC LIMIT 1) season, d.id domain_id
      FROM domain_sport ds
      INNER JOIN ft_league_sport ls ON ls.sport_id = ds.sport_id
      INNER JOIN ft_league l ON l.l_id = ls.l_id 
      LEFT JOIN domain d ON d.id = ds.domain_id
      WHERE ds.has_standings = 1
      AND d.is_active = 1
    ) tmp
    WHERE tmp.season IS NOT NULL
    ORDER BY sport_id, domain_id
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->execute();
    $lss = $stmt->fetchAll();

    if ($lss) {
      foreach ($lss as $key => $value) {
        if ($value['sport_id'] && $value['league_id'] && $value['season']) {
          $result[$value['sport_id']]['league_id'] = $value['league_id'];
          $result[$value['sport_id']]['sport_id'] = $value['sport_id'];
          $result[$value['sport_id']]['season'] = $value['season'];
          $result[$value['sport_id']]['domain'][] = $value['domain_id'];

          // $result[$key]['sport_id']   = $value['sport_id'];
          // $result[$key]['league_id']  = $value['league_id'];
          // $result[$key]['season']     = $value['season'];
          // $result[$key]['domain'][]     = $value['domain_id'];
        }
      }
    }

    return $result;
  }

  // used for sending available competitions with standings to betwidgets admin server
  // including competition number and competition slug, based on domainId request (from ft_league_flag which is used for betfair app and kouponi app)
  public function getAvailableCompetitionsWithStandings($locale, $domainId) {
    $em = $this->getEntityManager();
    $result = array();
    $lss = array();

    $competitionNumberField = $locale === "el" ? "competition_number_gr" : "competition_number_en";
    $domainSlugFields = ($domainId) ? ", CONCAT(d.standings_prefix, dss.slug) slug" : "";
    $domainSlugJoin = ($domainId) ? "    LEFT JOIN domain_sport_slug dss ON dss.domain_id = $domainId AND dss.sport_id = ds.sport_id LEFT JOIN domain d ON d.id = $domainId" : "";

    $sql = "
    SELECT DISTINCT ds.sport_id id, stc.title name, str.title countryName, s.image_name flag, $competitionNumberField competitionNumber $domainSlugFields
    FROM 
      domain_sport ds
        INNER JOIN sport s ON s.id = ds.sport_id AND s.is_additional = 0
        LEFT JOIN sport r ON s.parent_id = r.id
        LEFT JOIN sport_translations stc ON stc.sport_id = s.id AND stc.type = 'normal'
        LEFT JOIN sport_translations str ON str.sport_id = r.id AND str.type = 'normal' AND str.locale = stc.locale
        LEFT JOIN ft_league_flag lf ON lf.sport_id = ds.sport_id
        $domainSlugJoin
    WHERE 
        ds.has_standings = 1
    AND s.image_name IS NOT NULL
    AND stc.locale = '$locale'
    ORDER BY str.title, stc.title
    ";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->execute();
    $lss = $stmt->fetchAll();
    
    if ($lss) {
      $result = $lss;
    //   foreach ($lss as $key => $value) {
    //     if ($value['sport_id'] && $value['league_id'] && $value['season']) {
    //       $result[$value['sport_id']]['league_id'] = $value['league_id'];
    //       $result[$value['sport_id']]['sport_id'] = $value['sport_id'];
    //       $result[$value['sport_id']]['season'] = $value['season'];
    //       $result[$value['sport_id']]['domain'][] = $value['domain_id'];

    //       // $result[$key]['sport_id']   = $value['sport_id'];
    //       // $result[$key]['league_id']  = $value['league_id'];
    //       // $result[$key]['season']     = $value['season'];
    //       // $result[$key]['domain'][]     = $value['domain_id'];
    //     }
    //   }
    }

    $em->close();

    return $result;    
  }
}
