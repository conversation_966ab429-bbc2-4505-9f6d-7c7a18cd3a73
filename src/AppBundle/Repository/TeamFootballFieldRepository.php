<?php

namespace AppBundle\Repository;

/**
 * TeamFootballFieldRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class TeamFootballFieldRepository extends \Doctrine\ORM\EntityRepository
{
    public function getFootballFieldsByTeamId($team, $teamId)
    {
        try {
            $footballFields = $this->createQueryBuilder('tff')
                ->select('ff.id')
                ->innerJoin('AppBundle:FootballField', 'ff', 'WITH', 'ff.id = tff.footballField')
                ->innerJoin('AppBundle:FtTeam', 't', 'WITH', 't.id = tff.team')
                ->where('t.teamId = :teamId')
                ->setParameter('teamId', (int) $teamId)
                ->getQuery()
                ->getArrayResult();
        } catch (\Exception $e) {
            // silent exception
        }

        return $footballFields;
    }

    public function getDefaultFootballFieldByTeamId($teamId)
    {
        $footballField = null;

        try {
            $footballField = $this->createQueryBuilder('tff')
                ->select('ff')
                ->innerJoin('AppBundle:FootballField', 'ff', 'WITH', 'ff.id = tff.footballField')
                ->innerJoin('AppBundle:FtTeam', 't', 'WITH', 't.id = tff.team')
                ->where('t.teamId = :teamId')
                ->andWhere('tff.isDefault = true')
                ->setParameter('teamId', $teamId)
                ->getQuery()
                ->getOneOrNullResult();
        } catch (\Exception $e) {
            // silent exception
        }

        return $footballField;
    }
}
