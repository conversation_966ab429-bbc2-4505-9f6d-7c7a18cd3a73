<?php

namespace AppBundle\Repository;

/**
 * FtLeagueRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtLeagueRepository extends \Doctrine\ORM\EntityRepository
{
	public function getLeagueNameByLocale($sportId, $locale, $localeFallback) {
    $em = $this->getEntityManager();
    $result = '';

	$locale = ('el' === $locale) ? 'gr' : $locale;

	$sql = "SELECT REPLACE(REPLACE(COALESCE(name_$locale, name_$localeFallback), 'ΕΛΛΑΔΑ : SUPERLEAGUE', 'ΕΛΛΑΔΑ : STOIXIMAN SUPERLEAGUE'), 'ΕΛΛΑΔΑ : STOIXIMAN SUPERLEAGUE 2', 'ΕΛΛΑΔΑ : SUPERLEAGUE 2') league_name FROM ft_league l INNER JOIN ft_league_sport ls ON l.l_id = ls.l_id WHERE ls.sport_id = :sportId";

    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue(":sportId", $sportId);
    $stmt->execute();
    $result = $stmt->fetchColumn();

    $em->clear();
		return $result;
	}
}
