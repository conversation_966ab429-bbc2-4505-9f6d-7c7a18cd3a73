<?php

namespace AppBundle\Repository;

/**
 * FtFixtureRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtFixtureUserOddRepository extends \Doctrine\ORM\EntityRepository
{

	public function getUsersReport($type, $dateFrom, $dateTo, $optionId) {
    $em = $this->getEntityManager();
		$result = array();
		
		if ('null' == $optionId) {
			// users query
			$sql = "
			SELECT
				author_id, author_name,
				COUNT(1) total,
				SUM(IF(odd_result = 0, 1, 0)) won,
				SUM(IF(odd_result = 1, 1, 0)) void,
				SUM(IF(odd_result = 2, 1, 0)) lost,
				SUM(CASE WHEN odd_type_id = 255 THEN 1 ELSE 0 END) nobet,
				ROUND(SUM(CASE 
					WHEN odd_result IS NULL THEN 0
					WHEN odd_result = 0 THEN (odd_value * 100 ) - 100
					WHEN odd_result = 1 THEN 0 
					ELSE -100 END), 0) profit,
					IF(
						(SUM(IF(odd_result = 0, 1, 0)) + SUM(IF(odd_result = 2, 1, 0)))>0, 
						ROUND(SUM(IF(odd_result = 0, 1, 0)) / (SUM(IF(odd_result = 0, 1, 0)) + SUM(IF(odd_result = 2, 1, 0))) * 100, 2), 0) winperc
			FROM 
				ft_fixture_user_odd fuo
					INNER JOIN ft_fixture f ON (f.f_id = CONVERT(CAST(fuo.fixture_id AS CHAR(32)) USING utf8))
			WHERE 
					f.match_datetime >= :dateFrom
			AND f.match_datetime <= :dateTo
			AND author_id NOT IN (1, 1317, 1255, 5320, 7502, 9556, 10388, 10524)
			GROUP BY author_id, author_name
			ORDER BY profit DESC
			";

			$stmt = $em->getConnection()->prepare($sql);
			$stmt->bindValue(":dateFrom", $dateFrom);
			$stmt->bindValue(":dateTo", $dateTo);
			$stmt->execute();
			$result = $stmt->fetchAll();
		}
		else {
			$header = array();
			$sportsHeader = array();
			$analytics = array();

			// user specific query
			// user header
			$sql = "
			SELECT
				author_id, author_name,
				(SELECT GROUP_CONCAT(CASE 
				WHEN rb.points = 12 AND rb.bet_profit >= 0 THEN CONCAT('GoldGreen', '-', month_no)
				WHEN rb.points = 12 AND rb.bet_profit < 0 THEN CONCAT('GoldRed', '-', month_no)
				WHEN rb.points = 10 AND rb.bet_profit >= 0 THEN CONCAT('SilverGreen', '-', month_no)
				WHEN rb.points = 10 AND rb.bet_profit < 0 THEN CONCAT('SilverRed', '-', month_no)
				WHEN rb.bet_profit >= 0 THEN CONCAT('BronzeGreen', '-', month_no)
				END ORDER BY rb.points DESC, rb.bet_profit DESC) FROM reporter_bonus rb WHERE rb.author_id = :authorId AND rb.year_no = YEAR(:dateFrom)) badges,
				COUNT(1) total,
				SUM(IF(odd_result = 0, 1, 0)) won,
				SUM(IF(odd_result = 1, 1, 0)) void,
				SUM(IF(odd_result = 2, 1, 0)) lost,
				SUM(CASE WHEN odd_type_id = 255 THEN 1 ELSE 0 END) nobet,
				ROUND(SUM(CASE 
					WHEN odd_result IS NULL THEN 0
					WHEN odd_result = 0 THEN (odd_value * 100 ) - 100
					WHEN odd_result = 1 THEN 0 
					ELSE -100 END), 0) profit,
					IF(
						(SUM(IF(odd_result = 0, 1, 0)) + SUM(IF(odd_result = 2, 1, 0)))>0, 
						ROUND(SUM(IF(odd_result = 0, 1, 0)) / (SUM(IF(odd_result = 0, 1, 0)) + SUM(IF(odd_result = 2, 1, 0))) * 100, 2), 0) winperc
			FROM 
				ft_fixture_user_odd fuo
				INNER JOIN ft_fixture f ON (f.f_id = CONVERT(CAST(fuo.fixture_id AS CHAR(32)) USING utf8))
			WHERE 
					f.match_datetime >= :dateFrom
			AND f.match_datetime <= :dateTo
			AND author_id = :authorId
			GROUP BY author_id, author_name
			ORDER BY profit DESC
			";

			$stmt = $em->getConnection()->prepare($sql);
			$stmt->bindValue(":dateFrom", $dateFrom);
			$stmt->bindValue(":dateTo", $dateTo);
			$stmt->bindValue(":authorId", $optionId);
			$stmt->execute();
			$header = $stmt->fetchAll();

			// sports header
			$sql = "
			SELECT
				fuo.sport_id sport_id, (SELECT title FROM sport_translations st WHERE st.sport_id = fuo.sport_id AND type = 'normal' AND locale = 'el') sport_name,
				COUNT(1) total,
				SUM(IF(fuo.odd_result = 0, 1, 0)) won,
				SUM(IF(fuo.odd_result = 1, 1, 0)) void,
				SUM(IF(fuo.odd_result = 2, 1, 0)) lost,
				SUM(CASE WHEN fuo.odd_type_id = 255 THEN 1 ELSE 0 END) nobet,
				ROUND(SUM(CASE 
					WHEN fuo.odd_result IS NULL THEN 0
					WHEN fuo.odd_result = 0 THEN (fuo.odd_value * 100 ) - 100
					WHEN fuo.odd_result = 1 THEN 0 
					ELSE -100 END), 0) profit,
				IF((SUM(IF(fuo.odd_result = 0, 1, 0)) + SUM(IF(fuo.odd_result = 2, 1, 0)))>0, ROUND(SUM(IF(fuo.odd_result = 0, 1, 0)) / (SUM(IF(fuo.odd_result = 0, 1, 0)) + SUM(IF(fuo.odd_result = 2, 1, 0))) * 100, 2), 0) winperc
			FROM 
				ft_fixture_user_odd fuo
					INNER JOIN ft_fixture f ON (f.f_id = CONVERT(CAST(fuo.fixture_id AS CHAR(32)) USING utf8))
			WHERE 
					f.match_datetime >= :dateFrom
			AND f.match_datetime <= :dateTo
			AND fuo.author_id = :authorId
			GROUP BY fuo.sport_id
			ORDER BY profit DESC
			";

			$stmt = $em->getConnection()->prepare($sql);
			$stmt->bindValue(":dateFrom", $dateFrom);
			$stmt->bindValue(":dateTo", $dateTo);
			$stmt->bindValue(":authorId", $optionId);
			$stmt->execute();
			$sportsHeader = $stmt->fetchAll();

			// user analytics
			$sql = "
			SELECT 
				(SELECT COALESCE(name_gr, name_en) FROM ft_team WHERE team_id = f.h_team) h_name,
				(SELECT COALESCE(name_gr, name_en) FROM ft_team WHERE team_id = f.a_team) a_name,
				f.match_datetime,
				IF(ot.name IS NULL, 'No Bet', ot.name) name,
				fuo.odd_value,
				fuo.odd_result,
				f.ht1, f.ht2, f.ft1, f.ft2
			FROM
				ft_fixture_user_odd fuo
					LEFT JOIN odd_type ot ON ot.id = fuo.odd_type_id
					INNER JOIN ft_fixture f ON (f.f_id = CONVERT(CAST(fuo.fixture_id AS CHAR(32)) USING utf8))
			WHERE 
					f.match_datetime >= :dateFrom
			AND f.match_datetime <= :dateTo
			AND fuo.author_id = :authorId
			ORDER BY f.match_datetime DESC
			";

			$stmt = $em->getConnection()->prepare($sql);
			$stmt->bindValue(":dateFrom", $dateFrom);
			$stmt->bindValue(":dateTo", $dateTo);
			$stmt->bindValue(":authorId", $optionId);
			$stmt->execute();
			$analytics = $stmt->fetchAll();

			if ($header) $result['header'] = $header;
			if ($sportsHeader) $result['sportsHeader'] = $sportsHeader;
			if ($analytics) $result['analytics'] = $analytics;
		}


    $em->clear();
    return $result;		
	}
	
	public function getSportsReport($type, $dateFrom, $dateTo, $optionId) {
    $em = $this->getEntityManager();
    $result = array();

		if ('null' == $optionId) {
			// sports query
			$sql = "
			SELECT
				fuo.sport_id sport_id, (SELECT title FROM sport_translations st WHERE st.sport_id = fuo.sport_id AND type = 'normal' AND locale = 'el') sport_name,
				COUNT(1) total,
				SUM(IF(fuo.odd_result = 0, 1, 0)) won,
				SUM(IF(fuo.odd_result = 1, 1, 0)) void,
				SUM(IF(fuo.odd_result = 2, 1, 0)) lost,
				SUM(CASE WHEN fuo.odd_type_id = 255 THEN 1 ELSE 0 END) nobet,
				ROUND(SUM(CASE 
					WHEN fuo.odd_result IS NULL THEN 0
					WHEN fuo.odd_result = 0 THEN (fuo.odd_value * 100 ) - 100
					WHEN fuo.odd_result = 1 THEN 0 
					ELSE -100 END), 0) profit,
				IF((SUM(IF(fuo.odd_result = 0, 1, 0)) + SUM(IF(fuo.odd_result = 2, 1, 0)))>0, ROUND(SUM(IF(fuo.odd_result = 0, 1, 0)) /(SUM(IF(fuo.odd_result = 0, 1, 0)) + SUM(IF(fuo.odd_result = 2, 1, 0))) * 100, 2), 0) winperc
			FROM 
				ft_fixture_user_odd fuo
					INNER JOIN ft_fixture f ON (f.f_id = CONVERT(CAST(fuo.fixture_id AS CHAR(32)) USING utf8))
			WHERE 
					f.match_datetime >= :dateFrom
			AND f.match_datetime <= :dateTo
			AND fuo.author_id NOT IN (1, 1317, 1255, 5320, 7502, 9556, 10388, 10524)
			GROUP BY fuo.sport_id
			ORDER BY profit DESC
			";

			$stmt = $em->getConnection()->prepare($sql);
			$stmt->bindValue(":dateFrom", $dateFrom);
			$stmt->bindValue(":dateTo", $dateTo);
			$stmt->execute();
			$result = $stmt->fetchAll();
		}
		else {
			// sport specific query
			$header = array();
			$usersHeader = array();
			$analytics = array();

			// sport header
			$sql = "
			SELECT
				fuo.sport_id sport_id, (SELECT title FROM sport_translations st WHERE st.sport_id = fuo.sport_id AND type = 'normal' AND locale = 'el') sport_name,
				COUNT(1) total,
				SUM(IF(fuo.odd_result = 0, 1, 0)) won,
				SUM(IF(fuo.odd_result = 1, 1, 0)) void,
				SUM(IF(fuo.odd_result = 2, 1, 0)) lost,
				SUM(CASE WHEN fuo.odd_type_id = 255 THEN 1 ELSE 0 END) nobet,
				ROUND(SUM(CASE 
					WHEN fuo.odd_result IS NULL THEN 0
					WHEN fuo.odd_result = 0 THEN (fuo.odd_value * 100 ) - 100
					WHEN fuo.odd_result = 1 THEN 0 
					ELSE -100 END), 0) profit,
				IF((SUM(IF(fuo.odd_result = 0, 1, 0)) + SUM(IF(fuo.odd_result = 2, 1, 0)))>0, ROUND(SUM(IF(fuo.odd_result = 0, 1, 0)) / (SUM(IF(fuo.odd_result = 0, 1, 0)) + SUM(IF(fuo.odd_result = 2, 1, 0))) * 100, 2), 0) winperc
			FROM 
				ft_fixture_user_odd fuo
					INNER JOIN ft_fixture f ON (f.f_id = CONVERT(CAST(fuo.fixture_id AS CHAR(32)) USING utf8))
			WHERE 
					f.match_datetime >= :dateFrom
			AND f.match_datetime <= :dateTo
			AND fuo.author_id NOT IN (1, 1317, 1255, 5320, 7502, 9556, 10388, 10524)
			AND fuo.sport_id = :sportId
			GROUP BY fuo.sport_id
			ORDER BY profit DESC
			";
			
			$stmt = $em->getConnection()->prepare($sql);
			$stmt->bindValue(":dateFrom", $dateFrom);
			$stmt->bindValue(":dateTo", $dateTo);
			$stmt->bindValue(":sportId", $optionId);
			$stmt->execute();
			$header = $stmt->fetchAll();

			// user header
			$sql = "
			SELECT
				author_id, author_name,
				COUNT(1) total,
				SUM(IF(fuo.odd_result = 0, 1, 0)) won,
				SUM(IF(fuo.odd_result = 1, 1, 0)) void,
				SUM(IF(fuo.odd_result = 2, 1, 0)) lost,
				SUM(CASE WHEN fuo.odd_type_id = 255 THEN 1 ELSE 0 END) nobet,
				ROUND(SUM(CASE 
					WHEN fuo.odd_result IS NULL THEN 0
					WHEN fuo.odd_result = 0 THEN (fuo.odd_value * 100 ) - 100
					WHEN fuo.odd_result = 1 THEN 0 
					ELSE -100 END), 0) profit,
				IF((SUM(IF(fuo.odd_result = 0, 1, 0)) + SUM(IF(fuo.odd_result = 2, 1, 0)))>0, ROUND(SUM(IF(fuo.odd_result = 0, 1, 0)) / (SUM(IF(fuo.odd_result = 0, 1, 0)) + SUM(IF(fuo.odd_result = 2, 1, 0))) * 100, 2), 0) winperc
			FROM 
				ft_fixture_user_odd fuo
					INNER JOIN ft_fixture f ON (f.f_id = CONVERT(CAST(fuo.fixture_id AS CHAR(32)) USING utf8))
			WHERE 
					f.match_datetime >= :dateFrom
			AND f.match_datetime <= :dateTo
			AND fuo.author_id NOT IN (1, 1317, 1255, 5320, 7502, 9556, 10388, 10524)
			AND fuo.sport_id = :sportId
			GROUP BY fuo.author_id, fuo.author_name
			ORDER BY profit DESC
			";
						
			$stmt = $em->getConnection()->prepare($sql);
			$stmt->bindValue(":dateFrom", $dateFrom);
			$stmt->bindValue(":dateTo", $dateTo);
			$stmt->bindValue(":sportId", $optionId);
			$stmt->execute();
			$usersHeader = $stmt->fetchAll();

			// sport analytics
			$sql = "
			SELECT 
				(SELECT COALESCE(name_gr, name_en) FROM ft_team WHERE team_id = f.h_team) h_name,
				(SELECT COALESCE(name_gr, name_en) FROM ft_team WHERE team_id = f.a_team) a_name,
				f.match_datetime,
				IF(ot.name IS NULL, 'No Bet', ot.name) name,
				fuo.odd_value,
				fuo.odd_result,
				fuo.author_id,
				fuo.author_name,
				f.ht1, f.ht2, f.ft1, f.ft2
			FROM
				ft_fixture_user_odd fuo
					LEFT JOIN odd_type ot ON ot.id = fuo.odd_type_id
					INNER JOIN ft_fixture f ON (f.f_id = CONVERT(CAST(fuo.fixture_id AS CHAR(32)) USING utf8))
			WHERE 
					f.match_datetime >= :dateFrom
			AND f.match_datetime <= :dateTo
			AND fuo.author_id NOT IN (1, 1317, 1255, 5320, 7502, 9556, 10388, 10524)
			AND fuo.sport_id = :sportId
			ORDER BY f.match_datetime DESC
			";
			
			$stmt = $em->getConnection()->prepare($sql);
			$stmt->bindValue(":dateFrom", $dateFrom);
			$stmt->bindValue(":dateTo", $dateTo);
			$stmt->bindValue(":sportId", $optionId);
			$stmt->execute();
			$analytics = $stmt->fetchAll();

			if ($header) $result['header'] = $header;
			if ($usersHeader) $result['usersHeader'] = $usersHeader;
			if ($analytics) $result['analytics'] = $analytics;
		}

    $em->clear();
    return $result;
	}

	public function getTop3Users($dateFrom, $dateTo) {
		$em = $this->getEntityManager();
		$result = array();

		$sql = "
		SELECT
			fuo.author_id, fuo.author_name,
			ROUND(SUM(CASE 
				WHEN fuo.odd_result IS NULL THEN 0
				WHEN fuo.odd_result = 0 THEN (fuo.odd_value * 100 ) - 100
				WHEN fuo.odd_result = 1 THEN 0 
				ELSE -100 END), 0) profit
		FROM 
			ft_fixture_user_odd fuo
				INNER JOIN ft_fixture f ON (f.f_id = CONVERT(CAST(fuo.fixture_id AS CHAR(32)) USING utf8))
		WHERE 
				f.match_datetime >= :dateFrom
		AND f.match_datetime <= :dateTo
		AND fuo.author_id NOT IN (1, 1317, 1255, 5320, 7502, 9556, 10388, 10524)
		GROUP BY fuo.author_id, fuo.author_name
		ORDER BY profit DESC
		LIMIT 3
		";

		$stmt = $em->getConnection()->prepare($sql);
		$stmt->bindValue(":dateFrom", $dateFrom);
		$stmt->bindValue(":dateTo", $dateTo);
		$stmt->execute();
		$result = $stmt->fetchAll();

    $em->clear();
    return $result;
	}
	
	public function getSportsThisMonth($type, $dateFrom, $dateTo) {
		$em = $this->getEntityManager();
		$result = array();

		$orderType = ('top' === (string) $type) ? 'DESC' : 'ASC';

		$sql = "
		SELECT
			fuo.sport_id sport_id, (SELECT title FROM sport_translations st WHERE st.sport_id = fuo.sport_id AND type = 'normal' AND locale = 'el') sport_name,
			ROUND(SUM(CASE 
				WHEN fuo.odd_result IS NULL THEN 0
				WHEN fuo.odd_result = 0 THEN (fuo.odd_value * 100 ) - 100
				WHEN fuo.odd_result = 1 THEN 0 
				ELSE -100 END), 0) profit
		FROM 
			ft_fixture_user_odd fuo
				INNER JOIN ft_fixture f ON (f.f_id = CONVERT(CAST(fuo.fixture_id AS CHAR(32)) USING utf8))
		WHERE 
				f.match_datetime >= :dateFrom
		AND f.match_datetime <= :dateTo
		AND fuo.author_id NOT IN (1, 1317, 1255, 5320, 7502, 9556, 10388, 10524)
		GROUP BY fuo.sport_id
		ORDER BY profit $orderType
		LIMIT 5
		";

		$stmt = $em->getConnection()->prepare($sql);
		$stmt->bindValue(":dateFrom", $dateFrom);
		$stmt->bindValue(":dateTo", $dateTo);
		$stmt->execute();
		$result = $stmt->fetchAll();

    $em->clear();
    return $result;
	}

	
	public function getMonthlyUserOdds($dateFrom, $dateTo) {
		$em = $this->getEntityManager();
		$result = array();

		$sql = "
		SELECT 
			@rownum:=@rownum+1 tmp,
			DATE_FORMAT(:dateFrom, '%c') month_no,
			DATE_FORMAT(:dateFrom, '%Y') year_no,
			CASE 
				WHEN (@rownum = 1) THEN 12
				WHEN (@rownum = 2) THEN 10
				WHEN (@rownum = 3) THEN 8
				WHEN (@rownum = 4) THEN 6
				WHEN (@rownum = 5) THEN 4
				WHEN (@rownum = 6) THEN 2
				WHEN (@rownum >= 7) THEN 1
			END points,
		bonus.* FROM (
		SELECT
			fuo.author_id, fuo.author_name,
			COUNT(1) bet_count,
			ROUND(SUM(CASE 
				WHEN fuo.odd_result IS NULL THEN 0
				WHEN fuo.odd_result = 0 THEN (fuo.odd_value * 100 ) - 100
				WHEN fuo.odd_result = 1 THEN 0 
				ELSE -100 END), 0) bet_profit
		FROM 
			ft_fixture_user_odd fuo
				INNER JOIN ft_fixture f ON (f.f_id = CONVERT(CAST(fuo.fixture_id AS CHAR(32)) USING utf8)),
			(SELECT @rownum:=0) r
		WHERE 
				f.match_datetime >= :dateFrom
		AND f.match_datetime <= :dateTo
		AND fuo.author_id NOT IN (1, 1317, 1255, 5320, 7502, 9556, 10388, 10524)
		GROUP BY fuo.author_id, fuo.author_name
		ORDER BY bet_profit DESC
		) bonus,
		(SELECT @rownum:=0) r
		";

		$stmt = $em->getConnection()->prepare($sql);
		$stmt->bindValue(":dateFrom", $dateFrom);
		$stmt->bindValue(":dateTo", $dateTo);
		$stmt->execute();
		$result = $stmt->fetchAll();

    $em->clear();
    return $result;
	}

	public function findReporterMatchesLast45Days()
	{
		$em = $this->getEntityManager();

		$sql = "SELECT 
				f.id, f.fixture_id, f.match_datetime, f.odd_type_id, f.odd_value, f.author_name, f.odd_result
				FROM ft_fixture_user_odd f 
				WHERE f.match_datetime >= ( CURDATE() - INTERVAL 45 DAY ) 
				ORDER BY f.match_datetime DESC";

		$stmt = $em->getConnection()->prepare($sql);
		$stmt->execute();
		$ftFixtureUserOdds = $stmt->fetchAll();

		$em->clear();

		return $ftFixtureUserOdds;
	}
}