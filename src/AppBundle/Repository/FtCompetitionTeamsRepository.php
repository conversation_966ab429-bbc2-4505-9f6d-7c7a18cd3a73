
<?php

namespace AppBundle\Repository;

use Doctrine\ORM\EntityRepository;

/**
 * FtCompetitionTeamsRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtCompetitionTeamsRepository extends \Doctrine\ORM\EntityRepository
{
	public function findAllTeams()
	{

		$em = $this->getEntityManager();

		$sql = "SELECT id, name_gr FROM ft_competition_teams ORDER BY name_gr ASC";

		$stmt = $em->getConnection()->prepare($sql);
		$stmt->execute();
		$teams = $stmt->fetchAll();

		$em->clear();

		return $teams;
	}

	public function findOneTeam($team_id)
	{
		$em = $this->getEntityManager();

		$sql = "SELECT id, name_gr FROM ft_competition_teams WHERE id = " . $team_id;

		$stmt = $em->getConnection()->prepare($sql);
		$stmt->execute();
		$team = $stmt->fetchAll();

		$em->clear();

		return $team;
	}


  /**
   * Returns an array of [ 'Label' => id ] for ft_league_season,
   * e.g. [ 'EURO.AA — 2024' => 11800673, … ]
   *
   * @return array
   */
  public function getLeagueSeasonChoices(): array
  {
    $em   = $this->getEntityManager();
    $conn = $em->getConnection();

    $sql  = "
             SELECT
          id,
          CONCAT(l_id, ' — ', season) AS label
        FROM ft_league_season
        WHERE season >= YEAR(CURDATE())
        ORDER BY season DESC, l_id ASC
        ";

    $rows = $conn->fetchAll($sql);

    $choices = [];
    foreach ($rows as $row) {
      $choices[$row['label']] = $row['id'];
    }

    return $choices;
  }

  /**
   * Find teams that are participating in matches of a specific match type
   */
  public function findTeamsByMatchType($matchType)
  {
    $em = $this->getEntityManager();
    
    $sql = "SELECT DISTINCT t.* 
            FROM ft_competition_teams t
            INNER JOIN ft_competition_schedule s ON (s.h_team = t.id OR s.a_team = t.id)
            WHERE s.match_type = :matchType
            ORDER BY 
              t.team_index IS NULL,
              t.team_index ASC,
              t.pts DESC";
    
    $stmt = $em->getConnection()->prepare($sql);
    $stmt->bindValue('matchType', $matchType);
    $stmt->execute();
    return $stmt->fetchAll();
  }
}
