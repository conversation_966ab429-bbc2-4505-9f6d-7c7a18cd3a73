<?php

namespace AppBundle\Repository;

/**
 * FtFixtureTeamDataRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FtFixtureTeamDataRepository extends \Doctrine\ORM\EntityRepository
{

	public function getLineups($fixedLineup) {
		$result = array();

		// if ($hLineup)

		if ($fixedLineup) {
			$em = $this->getEntityManager();

			$sql = "SELECT id, name_el title, name_el, name_en FROM ft_team_player WHERE id IN ($fixedLineup) ORDER BY FIELD(id, $fixedLineup)";

			$stmt = $em->getConnection()->prepare($sql);
			$stmt->execute();
			$result = $stmt->fetchAll();

			$em->clear();
		}

		return $result;
	}

	public function getPreviousFixtureTeamData($teamId, $fixtureId, $isHome = true) {
		$em = $this->getEntityManager();
		$result = array();

		// $whereClause = ($isHome ? ' d.hId = ' : ' d.aId = ') . $teamId;
		$whereClause = " ( d.hId = " . $teamId . " OR d.aId = " . $teamId . " ) ";

		$result = $em->createQuery("SELECT d FROM AppBundle:FtFixtureTeamData d WHERE $whereClause AND d.fixtureId <> $fixtureId ORDER BY d.createdAt DESC")
		->setMaxResults(1)
		->getResult();

		$em->clear();
		return $result;
	}

}
