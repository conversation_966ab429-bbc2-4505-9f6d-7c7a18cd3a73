<?php

namespace AppBundle\Repository;

/**
 * ReporterBonusRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class ReporterBonusRepository extends \Doctrine\ORM\EntityRepository
{

	public function getMonthlyBonus($year) {
		$em = $this->getEntityManager();
		$result = array();

		$sql = "
		SELECT
			@rownum:=@rownum+1 `rank`,
			rb.author_id, rb.author_name,
			rb.points,
			rb.bet_count,
			rb.bet_profit,
			rb.month_01,
			rb.month_02,
			rb.month_03,
			rb.month_04,
			rb.month_05,
			rb.month_06,
			rb.month_07,
			rb.month_08,
			rb.month_09,
			rb.month_10,
			rb.month_11,
			rb.month_12
		FROM (
		SELECT
			author_id, author_name,
			SUM(points) points,
			SUM(bet_count) bet_count,
			SUM(bet_profit) bet_profit,
			SUM(IF(month_no= 1, points, 0)) month_01,
			SUM(IF(month_no= 2, points, 0)) month_02,
			SUM(IF(month_no= 3, points, 0)) month_03,
			SUM(IF(month_no= 4, points, 0)) month_04,
			SUM(IF(month_no= 5, points, 0)) month_05,
			SUM(IF(month_no= 6, points, 0)) month_06,
			SUM(IF(month_no= 7, points, 0)) month_07,
			SUM(IF(month_no= 8, points, 0)) month_08,
			SUM(IF(month_no= 9, points, 0)) month_09,
			SUM(IF(month_no= 10, points, 0)) month_10,
			SUM(IF(month_no= 11, points, 0)) month_11,
			SUM(IF(month_no= 12, points, 0)) month_12
		FROM reporter_bonus
		WHERE
				year_no = :year
		GROUP BY author_id, author_name
		ORDER BY points DESC, bet_profit DESC) rb,
		(SELECT @rownum:=0) r
		";

		$stmt = $em->getConnection()->prepare($sql);
		$stmt->bindValue(":year", $year);
		$stmt->execute();
		$result = $stmt->fetchAll();

    $em->clear();
    return $result;
	}

}