<?php

namespace AppBundle\Menu;

use Knp\Menu\FactoryInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;

class Builder implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    public function mainMenu(FactoryInterface $factory, array $options)
    {
        $menu = $factory->createItem('root');
        $menu->setChildrenAttribute('class', 'bg-blue dker');
        $menu->setChildrenAttribute('id', 'menu');

        $menu->addChild('MENU', array());
        $menu['MENU']->setAttribute('class', 'nav-header');

        $menu->addChild('Sports', array('route' => 'app_sport_index'));
        $menu->addChild('Domains', array('route' => 'domain_index'));
        $menu->addChild('Languages', array('route' => 'language_index'));
        $menu->addChild('Odd Types', array('route' => 'oddtype_index'));
        $menu->addChild('Team Formations', array('route' => 'ftteamformation_index'));
        $menu->addChild('Team - Football Field Relation', array('route' => 'team_football_field_index'));
        $menu['Team - Football Field Relation']->addChild('Football Fields', array('route' => 'football_field_index'));
        $menu->addChild('Standings Info', array('route' => 'ftleaguestandingsinfo_index'));
        $menu->addChild('Standings Options', array('route' => 'ftleaguestandingsoption_index'));
        $menu->addChild('Competition Schedule', array('route' => 'ftcompetitionschedule_index'));
        $menu->addChild('Competition Teams', array('route' => 'ftcompetitionteams_index'));
        $menu->addChild('Tools', array());
        $menu['Tools']->addChild('Flags', array('route' => 'ftleagueflag_index'));
        $menu['Tools']->addChild('Sync', array('route' => 'sportsync_index'));
        $menu['Tools']->addChild('Checker', array('route' => 'checker_index'));
        $menu['Tools']->addChild('Standings', array('route' => 'tools_standings_index'));
        $menu['Tools']->addChild('Find City', array('route' => 'tools_city_find_index'));
        $menu['Tools']->addChild('Generate Posts', array('route' => 'tools_generate_posts_index'));

        return $menu;
    }
}
