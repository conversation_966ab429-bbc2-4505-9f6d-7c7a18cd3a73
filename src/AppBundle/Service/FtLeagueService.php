<?php

namespace AppBundle\Service;

use Symfony\Component\DomCrawler\Crawler;
use Psr\Log\LoggerInterface;

use AppBundle\Service\GlobalHelperService;

class FtLeagueService {

  protected $entityManager;
  protected $parser;
  protected $countryRepository;

  public function __construct($entityManager, $parser, $countryRepository) {
    $this->entityManager = $entityManager;
    $this->parser = $parser;
    $this->countryRepository = $countryRepository;

    $this->helper = new GlobalHelperService($this->entityManager, $this->parser);
  }

  public function batchInsert($username, $password, $lan, $fixturesAll = null, $translator, $options) {
    $em = $this->entityManager;

    $url = "http://xml.globalscore.com/xml.php?un=".$username."&pass=".$password."&sport=s&tp=leagues&lan=".$lan;

    $crawler = new Crawler();
    try {
      $crawler->addXmlContent(file_get_contents($url));
    }
    catch (\Exception $e){
      $logger = $this->parser;
      $logger->critical($e->getMessage());
    }

    $leagueArr = $crawler->filterXPath('//league')->each(function ($node, $i) {
      $arr = $node->extract(array('id', 'name', 'country'))[0];

      return $arr;
    });

    $sql = "insert into ft_league (l_id, name_$lan, country_id, created_at) VALUES ";

    $hasRecords = 0;
    foreach ($leagueArr as $i => $value) {
      $hasRecords++;

      $dateTime = $this->helper->getCurrentTimestampDate();

      $countryId = $this->countryRepository->getCountryId($value[2]);
      if (!$countryId) continue;
      
      $sql .= " ('$value[0]', '$value[1]', $countryId, '$dateTime') ";
      $sql .= (count($leagueArr) - 1  === $i) ? " ON DUPLICATE KEY UPDATE name_$lan = VALUES(name_$lan), updated_at = '$dateTime' ;" : ",";
    }

    if ($hasRecords > 0) {
      try {
        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute();
        $logger = $this->parser;
        $logger->info("Updated $hasRecords LEAGUE records for $lan");
      }
      catch (\Exception $e) {
        $logger = $this->parser;
        $logger->critical($e->getMessage());
      }
    }

    $em->clear();
  }
}
