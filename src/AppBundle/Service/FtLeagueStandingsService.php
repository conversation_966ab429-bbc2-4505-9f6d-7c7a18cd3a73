<?php

namespace AppBundle\Service;

use Symfony\Component\DomCrawler\Crawler;
use Psr\Log\LoggerInterface;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;

use AppBundle\Service\GlobalHelperService;

class FtLeagueStandingsService {

  protected $entityManager;
  protected $parser;
  protected $domainSportRepository;
  protected $container;
  protected $helper;
  protected $environment;
  protected $projectDir;

  public function __construct($entityManager, $parser, $domainSportRepository, $container, $environment, $projectDir) {
    $this->entityManager = $entityManager;
    $this->parser = $parser;
    $this->domainSportRepository = $domainSportRepository;
    $this->container = $container;
    $this->environment = $environment;
    $this->projectDir = $projectDir;

    $this->helper = new GlobalHelperService($this->entityManager, null, $this->parser);
  }

  public function batchInsert($username, $password, $lan = null, $fixturesAll = null, $translator, $options) {
    $em     = $this->entityManager;
    $todaysLeagues = $this->projectDir . '/var/cache/' . $this->environment . '/todays_leagues';
    $cache = new FilesystemAdapter('', 0, $todaysLeagues);
    $sportsClearCache = array();
    $betaradesDomain = null;
    $outputMsg = '';

    // get all competitions and current season for all domains that have standings flag
    // and there are no matches today 00:00:00 - 23:59:59
    // if there are matches today, wbs:post:finished will calculate stanindgs for compeition + season
    // $sports = $this->domainSportRepository->getLeagueAndSeasonForStandings();

    if ($options) {
      // update league standings for all domain sports with has_standings = 1
      if (isset($options[0]) && 'all' === $options[0]) {
        $leaguesCache = $this->domainSportRepository->getLeagueAndSeasonForStandings();
      }
      else {
        $leaguesCache = $em->getRepository('AppBundle:FtLeagueSeasonSport')->getSportLeagueSeason($options);
      }
    }
    else {
      $leaguesCache = $cache->getItem('standings.todays_leagues')->get();
    }
    // echo '<pre>';
    // print_r($leaguesCache);
    // echo '</pre>';

    if ($leaguesCache) {
      // get domain for which to delete memcached keys after updating standings
      // $domainName = $this->container->hasParameter('domain.name') ? $this->container->getParameter('domain.name') : null;
      // if ($domainName) {
      //   $domain = $em->getRepository('AppBundle:Domain')->findOneBy(array('name' => $domainName));
      //   $betaradesDomain = $domain;
      // }

      foreach ($leaguesCache as $sport) {
        // if ($sport['league_id'] != 'GRE1') continue;

        // if at least one is empty, continue (sport_id, league_id, season)
        if (!($sport['league_id'] && $sport['season'] && $sport['sport_id'])) continue;

        // gather list of sportIds and seasons to clear their DOMAIN memcache
        if (!in_array($sport['sport_id'], $sportsClearCache)) {
          // if is_additional then clear cache for parent sport
          $sportObj = $em->getRepository('AppBundle:Sport')->findOneById((int) $sport['sport_id']);
          if ($sportObj && true === $sportObj->getIsAdditional()) {
            $sportsClearCache[(int) $sportObj->getParent()->getId()]['sport_id'] = (int) $sportObj->getParent()->getId();
            if (isset($sport['domain'])) {
              $sportsClearCache[(int) $sportObj->getParent()->getId()]['domain'] = $sport['domain'];
              // foreach ($sport['domain'] as $tmp) {
              //   $sportsClearCache[(int) $sportObj->getParent()->getId()]['domain'][] = $tmp;
              // }
            }
          }
          else {
            $sportsClearCache[(int) $sport['sport_id']]['sport_id'] = (int) $sport['sport_id'];
            if (isset($sport['domain'])) {
              foreach ($sport['domain'] as $tmp) {
                $sportsClearCache[(int) $sport['sport_id']]['domain'][] = $tmp;
              }
            }
          }
        }

        // if ($sport['league_id'] != 'GRE1') continue;
        $url = "http://xml.globalscore.com/xml.php?un=".$username."&pass=".$password."&sport=s&tp=leagueTables&lid=".$sport['league_id']."&ssn=".$sport['season'];

        $crawler = new Crawler();
        try {
          $fileContent = file_get_contents($url);
          // fix not well-formed xml
          $fileContent = str_replace('&', '&amp;', $fileContent);

          $crawler->addXmlContent($fileContent);
        }
        catch (\Exception $e) {
          $logger = $this->parser;
          $logger->critical($e->getMessage());
        }

        $standingsArr = $crawler->filterXPath('//team')->each(function ($node, $i) use ($sport) {
          $arr['team_id']   = $node->extract(array('id'))[0];
          $arr['rank']      = $node->extract(array('rank'))[0];
          $arr['cnf']       = $node->extract(array('cnf'))[0];
          $arr['sport_id']  = $sport['sport_id'];
          $arr['league_id'] = $sport['league_id'];
          $arr['season']    = $sport['season'];

          $homeArr = $node->filterXPath('//home')->each(function ($node1, $i1) {
            $result['home_p']   = isset($node1->extract(array('hp'))[0]) ? (int) $node1->extract(array('hp'))[0] : 0;
            $result['home_w']   = isset($node1->extract(array('hw'))[0]) ? (int) $node1->extract(array('hw'))[0] : 0;
            $result['home_d']   = isset($node1->extract(array('hd'))[0]) ? (int) $node1->extract(array('hd'))[0] : 0;
            $result['home_l']   = isset($node1->extract(array('hl'))[0]) ? (int) $node1->extract(array('hl'))[0] : 0;
            $result['home_gf']  = isset($node1->extract(array('hgf'))[0]) ? (int) $node1->extract(array('hgf'))[0] : 0;
            $result['home_ga']  = isset($node1->extract(array('hga'))[0]) ? (int) $node1->extract(array('hga'))[0] : 0;

            return $result;
          });

          $awayArr = $node->filterXPath('//away')->each(function ($node2, $i2) {
            $result['away_p']   = isset($node2->extract(array('ap'))[0]) ? (int) $node2->extract(array('ap'))[0] : 0;
            $result['away_w']   = isset($node2->extract(array('aw'))[0]) ? (int) $node2->extract(array('aw'))[0] : 0;
            $result['away_d']   = isset($node2->extract(array('ad'))[0]) ? (int) $node2->extract(array('ad'))[0] : 0;
            $result['away_l']   = isset($node2->extract(array('al'))[0]) ? (int) $node2->extract(array('al'))[0] : 0;
            $result['away_gf']  = isset($node2->extract(array('agf'))[0]) ? (int) $node2->extract(array('agf'))[0] : 0;
            $result['away_ga']  = isset($node2->extract(array('aga'))[0]) ? (int) $node2->extract(array('aga'))[0] : 0;

            return $result;
          });

          $overallArr = $node->filterXPath('//overall')->each(function ($node3, $i3) {
            $result['overall_p']    = isset($node3->extract(array('op'))[0]) ? (int) $node3->extract(array('op'))[0] : 0;
            $result['overall_w']    = isset($node3->extract(array('ow'))[0]) ? (int) $node3->extract(array('ow'))[0] : 0;
            $result['overall_d']    = isset($node3->extract(array('od'))[0]) ? (int) $node3->extract(array('od'))[0] : 0;
            $result['overall_l']    = isset($node3->extract(array('ol'))[0]) ? (int) $node3->extract(array('ol'))[0] : 0;
            $result['overall_gf']   = isset($node3->extract(array('ogf'))[0]) ? (int) $node3->extract(array('ogf'))[0] : 0;
            $result['overall_ga']   = isset($node3->extract(array('oga'))[0]) ? (int) $node3->extract(array('oga'))[0] : 0;
            $result['overall_pts']  = isset($node3->extract(array('pts'))[0]) ? (int) $node3->extract(array('pts'))[0] : 0;

            return $result;
          });

          $penaltyArr = $node->filterXPath('//penalty')->each(function ($node4, $i4) {
            $result['penalty_pts'] = isset($node4->extract(array('pts'))[0]) ? (int) $node4->extract(array('pts'))[0] : 0;
            $result['penalty_hgf'] = isset($node4->extract(array('hgf'))[0]) ? (int) $node4->extract(array('hgf'))[0] : 0;
            $result['penalty_hga'] = isset($node4->extract(array('hga'))[0]) ? (int) $node4->extract(array('hga'))[0] : 0;
            $result['penalty_agf'] = isset($node4->extract(array('agf'))[0]) ? (int) $node4->extract(array('agf'))[0] : 0;
            $result['penalty_aga'] = isset($node4->extract(array('aga'))[0]) ? (int) $node4->extract(array('aga'))[0] : 0;

            return $result;
          });

          $arr['home']    = $homeArr;
          $arr['away']    = $awayArr;
          $arr['overall'] = $overallArr;
          $arr['penalty'] = $penaltyArr;
          return $arr;
        });

        $sql = "insert into ft_league_standings
        (sport_id, l_id, season, team_id, `rank`, cnf, overall_p, overall_w, overall_d, overall_l, overall_gf, overall_ga, overall_pts, home_p, home_w, home_d, home_l, home_gf, home_ga,
        away_p, away_w, away_d, away_l, away_gf, away_ga, penalty_pts, penalty_hgf, penalty_hga, penalty_agf, penalty_aga, created_at) VALUES ";

        $hasRecords = 0;
        foreach ($standingsArr as $i => $value) {
          $hasRecords++;

          $dateTime = $this->helper->getCurrentTimestampDate();

          $sql .= " (
            ".$value['sport_id'].",
            '".$value['league_id']."',
            '".$value['season']."',
            ".$value['team_id'].",
            ".$value['rank'].",
            '".$value['cnf']."',
            ".$value['overall'][0]['overall_p'].",
            ".$value['overall'][0]['overall_w'].",
            ".$value['overall'][0]['overall_d'].",
            ".$value['overall'][0]['overall_l'].",
            ".$value['overall'][0]['overall_gf'].",
            ".$value['overall'][0]['overall_ga'].",
            ".$value['overall'][0]['overall_pts'].",
            ".$value['home'][0]['home_p'].",
            ".$value['home'][0]['home_w'].",
            ".$value['home'][0]['home_d'].",
            ".$value['home'][0]['home_l'].",
            ".$value['home'][0]['home_gf'].",
            ".$value['home'][0]['home_ga'].",
            ".$value['away'][0]['away_p'].",
            ".$value['away'][0]['away_w'].",
            ".$value['away'][0]['away_d'].",
            ".$value['away'][0]['away_l'].",
            ".$value['away'][0]['away_gf'].",
            ".$value['away'][0]['away_ga'].",
            ".$value['penalty'][0]['penalty_pts'].",
            ".$value['penalty'][0]['penalty_hgf'].",
            ".$value['penalty'][0]['penalty_hga'].",
            ".$value['penalty'][0]['penalty_agf'].",
            ".$value['penalty'][0]['penalty_aga'].",
            '$dateTime') ";
          $sql .= (count($standingsArr) - 1  === $i) ? " ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id), `rank` = VALUES(`rank`), overall_p = VALUES(overall_p), overall_w = VALUES(overall_w), overall_d = VALUES(overall_d), overall_l = VALUES(overall_l),
          overall_gf = VALUES(overall_gf), overall_ga = VALUES(overall_ga), overall_pts = VALUES(overall_pts), home_p = VALUES(home_p), home_w = VALUES(home_w), home_d = VALUES(home_d), home_l = VALUES(home_l), home_gf = VALUES(home_gf), home_ga = VALUES(home_ga),
          away_p = VALUES(away_p), away_w = VALUES(away_w), away_d = VALUES(away_d), away_l = VALUES(away_l), away_gf = VALUES(away_gf), away_ga = VALUES(away_ga),
          penalty_pts = VALUES(penalty_pts), penalty_hgf = VALUES(penalty_hgf), penalty_hga = VALUES(penalty_hga), penalty_agf = VALUES(penalty_agf), penalty_aga = VALUES(penalty_aga), updated_at = '$dateTime' ;" : ",";
        }

        if ($hasRecords > 0) {
          try {
            $stmt = $em->getConnection()->prepare($sql);
            $stmt->execute();
            $logger = $this->parser;
            $logger->info("Updated $hasRecords LEAGUE records for {$value['sport_id']} and {$value['league_id']} ");
          }
          catch (\Exception $e) {
            $logger = $this->parser;
            $logger->critical($e->getMessage());
          }
        }

        // Do the Scorers
        $scorersArr = $crawler->filterXPath('//scorer')->each(function ($node, $i) use ($sport) {
          $arr['player_name']   = $node->extract(array('player_name'))[0];
          $arr['team_id']       = $node->extract(array('team'))[0];
          $arr['goals']         = $node->extract(array('goals'))[0];
          $arr['penalties']     = $node->extract(array('penalties'))[0];
          $arr['scorer_first']  = $node->extract(array('scorer1st'))[0];
          $arr['sport_id']      = $sport['sport_id'];
          $arr['league_id']     = $sport['league_id'];
          $arr['season']        = $sport['season'];

          return $arr;
        });

        $sql = "insert into ft_league_season_scorer (sport_id, l_id, season, team_id, player_name, goals, penalties, scorer_first, created_at) VALUES ";

        $hasScorers = 0;
        foreach ($scorersArr as $i => $value) {
          $hasScorers++;

          $dateTime = $this->helper->getCurrentTimestampDate();

          $sql .= " (
            ".$value['sport_id'].",
            '".$value['league_id']."',
            '".$value['season']."',
            ".$value['team_id'].",
            '".$value['player_name']."',
            ".$value['goals'].",
            ".$value['penalties'].",
            ".$value['scorer_first'].",
            '$dateTime') ";
          $sql .= (count($scorersArr) - 1  === $i) ? " ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id), team_id = VALUES(team_id), goals = VALUES(goals), penalties = VALUES(penalties), scorer_first = VALUES(scorer_first), updated_at = '$dateTime' ;" : ",";
        }

        if ($hasScorers > 0) {
          try {
            $stmt = $em->getConnection()->prepare($sql);
            $stmt->execute();
            $logger = $this->parser;
            $logger->info("Updated $hasScorers Scorer records for $lan");
          }
          catch (\Exception $e) {
            $logger = $this->parser;
            $logger->critical($e->getMessage());
          }
        }

        $outputMsg .= 'Updated standings for [sport_id]='.$sport['sport_id'].' [league]='.$sport['league_id'].' [season]='.$sport['season'].PHP_EOL;

      } // foreach $sports

      echo $outputMsg;

      // clear memcached key for each
      if ($sportsClearCache) {
        $this->helper->clearStandingsMemcache($em, $sportsClearCache, $translator, $this->projectDir, $this->environment);
      }
    }

    $em->clear();

    return $outputMsg;
  }
}
