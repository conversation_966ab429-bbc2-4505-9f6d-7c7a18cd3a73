<?php

namespace AppBundle\Service;

use Symfony\Component\Cache\Adapter\FilesystemAdapter;

class GlobalHelperService {

  private $defaultLanguageCode;

  private $entityManager;
  private $parser;
  private $cf;
  private $locale;

  public function __construct($entityManager = null, $cf = null, $parser = null) {
    $this->locale = \Locale::getDefault();
    if (is_array($entityManager)) {
      $this->entityManager = $entityManager[0];
      $this->cf = $entityManager[1];
      $this->parser = $entityManager[2];
    }
    else {
      $this->entityManager = $entityManager;
      $this->cf = $cf;
      // for some reason when service is called by another service, connection factory does not exist
      $this->parser = $parser;
    }
  }

  public function getLanguageCode($lan) {
    $result = ($lan == 1) ? 'el' : 'en';

    return $result;
  }

  public function getDefaultLanguageCode() {
    return $defaultLanguageCode = array(
      'el',
      'en'
    );
  }

  public function getLanguageByLocale($locale) {
    if ('el' === $locale) {
      $languageCode = 'Gr';
    }
    else {
      $languageCode = ucwords($locale);
    }

    return $languageCode;
  }

  public function getCurrentTimestampDate($dateTime = null) {
    if (!$dateTime) $dateTime = new \DateTime();

    return $dateTime->format('Y-m-d H:i:s');
  }

  public function sluggify($name) {
  //		setlocale(LC_CTYPE, 'el_GR.utf8');
  //		$Name = iconv('UTF-8', 'ASCII//TRANSLIT', $Name);
      $name = iconv('UTF-8', 'utf-8//TRANSLIT', $name);

      $greek    = array('α','ά','Ά','Α','β','Β','γ', 'Γ', 'δ','Δ','ε','έ','Ε','Έ','ζ','Ζ','η','ή','Η','Ή','θ','Θ','ι','ί','ϊ','ΐ','Ι','Ί', 'Ϊ', 'κ','Κ','λ','Λ','μ','Μ','ν','Ν','ξ','Ξ','ο','ό','Ο','Ό','π','Π','ρ','Ρ','σ','ς', 'Σ','τ','Τ','υ','ύ','ϋ','Υ','Ύ','Ϋ','φ','Φ','χ','Χ','ψ','Ψ','ω','ώ','Ω','Ώ',' ',"'","'",',');
      $english  = array('a', 'a','A','A','v','V','g','G','d','D','e','e','E','E','z','Z','i','i','I','h','th','Th', 'i','i','i','i','I','I','i','k','K','l','L','m','M','n','N','x','X','o','o','O','O','p','P' ,'r','R','s','s','S','t','T','u','u','u','U','U','U','f','F','x','X','ps','Ps','o','o','O','O','-','-','-','-');
      $string   = str_replace($greek, $english, $name);
      // remove multiple continuous dashes with a single dash
      $string   = preg_replace('/-+/', '-', $string);
      return strtolower($string);
  }

  public function doTranslation($entity, $translations) {

    foreach ($translations as $key => $value) {
      if ($value) {
        foreach($value as $key1 => $value1) {
          if ($value1) {
            // $entity->addTranslation(new AppBundle\Entity\SportGedmoTranslation('', '', '', ''))
            $methodStr = 'set'.ucwords($key1);
            $entity->translate($key)->$methodStr($value1);
            // echo 'get'.ucwords($key1);
          }
        }
      }
    }

    return $entity;
  }

  public function getTranslationTitle($translations, $locale = 'en') {
    if ($translations) {
      foreach ($translations as $translation) {
        if ($locale === $translation->getLocale()) {
          return $translation->getTitle();
        }
      }
    }

    return null;
  }

  public function getDomainConnection($domain, $logger = null) {
    if (!isset($logger)) $logger = $this->parser;
    try {
      $wp_db_host     = $domain->getDbHost();
      $wp_db_port     = $domain->getDbPort();
      $wp_db_name     = $domain->getDbName();
      $wp_db_username = $domain->getDbUsername();
      $wp_db_password = $domain->getDbPassword();

      if (!$wp_db_port) {
          $wp_db_port = 3306;
      }

      if ('************' === $wp_db_host) return null; //$wp_db_host = '127.0.0.1';
      // get connection for domain

      $mysqlStr = 'mysql:host='.$wp_db_host.';dbname='.$wp_db_name.';port='.$wp_db_port.';charset=UTF8';
      $connection = $this->cf->createConnection(
        // array('pdo' => new \PDO("mysql:host=$wp_db_host;dbname=$wp_db_name;port=$wp_db_port;charset=UTF8", $wp_db_username, $wp_db_password))
        array('pdo' => new \PDO($mysqlStr, $wp_db_username, $wp_db_password))
        // array('pdo' => new \PDO('mysql:host=************;dbname=tipst3r_db;port=3306','tipst3ruser','cjTJBtW2KeMrM6ZB'))
      );
    }
    catch (\Exception $e) {
      $logger->critical('error getting domain connection ' . $wp_db_host . ' - ' . $e->getMessage());
      return null;
    }

    return $connection;
  }

  public function startTimer() {
    $time = -microtime(true);

    return $time;
  }

  public function printTimer($time) {
    $time += microtime(true);
    return PHP_EOL.' total time:: ' . sprintf('%f', $time);
  }

  public function getSportsInClause($leagues) {
    $result = null;

    if ($leagues) {
      $result = '('.implode(array_keys($leagues), ',').')';
    }

    return $result;
  }

  public function curlRequest($url, $isPost = false, $params = null, $origin = null) {
    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_HEADER, true);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_POST, $isPost);
    curl_setopt($curl, CURLOPT_HEADER,'Content-Type: application/x-www-form-urlencoded');
    if ($origin) {
      curl_setopt($curl, CURLOPT_HTTPHEADER, array("Origin: $origin"));
    }

    if ($params) {
      $postParams['sportIds'] = $params;
      $postParams['params']   = $params;
      curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($postParams));
    }

    // curl_setopt($curl, CURLOPT_POSTFIELDS, $field_string);
    $json_response = curl_exec($curl);

    $status = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if (!(($status >= 200) && ($status < 300))) {
        throw new \Exception("[ERROR] Curl Request error: call to URL $url failed with status $status, response $json_response, curl_error " . curl_error($curl) . ", curl_errno " . curl_errno($curl) . "\n");
    }

    curl_close($curl);

    return $json_response;
  }

  public function clearStandingsMemcache($em, $sports, $translator, $projectDir, $environment) {
    $logger = $this->parser;
    $locale = array('el', 'en');
    $standingsCacheDir = $projectDir . '/var/cache/' . $environment . '/standings_cache';
    //echo $standingsCacheDir;
    $cache      = new FilesystemAdapter('', 0, $standingsCacheDir);
    $standingsData = '';

    $domainIds = array();
    $domainSports = array();

    if ($sports) {
      foreach ($sports as $sport) {
        foreach ($locale as $l) {
          $cacheKey = "standings-cache-".$sport['sport_id']."-".$l;
          //echo 'clearing ' . $cacheKey . ' -- ';
          $standingsData = $this->cacheStandings($translator, $sport['sport_id'], $l, null, 1, null, null);
          $cache->deleteItem($cacheKey);
          
          $standingsCache = $cache->getItem($cacheKey);
          $standingsCache->set($standingsData);
          $standingsCache->expiresAfter(60*60*24*7); // one week expiration
          $cache->save($standingsCache);
        }

        if ($sport['domain']) {
          foreach ($sport['domain'] as $domainId) {
            $domainSports[$domainId][] = $sport['sport_id'];
          }
        }
      }
    }

    $domainIds = array_keys($domainSports);
    if ($domainIds) {
      foreach ($domainIds as $domainId) {
        $domain = $em->getRepository('AppBundle:Domain')->findOneById($domainId);
        if ($domain) {
          $protocol = (true === $domain->getProtocol() ? 'https' : 'http');
          $nodePort = $domain->getNodePortId();
          $hostName = $domain->getDbNodeHost();
        }

        // once everything is generated, make the curl call per domain
        $domainSportIds = implode('-', $domainSports[$domainId]);
        $url = $protocol."://".$hostName.":".$nodePort."/delete-standings-cache/".$domainSportIds;
        if ($domainId != 8) { // exclude bethome, there is no node port
          //echo 'clearing standings cache url ' . $url;
          $this->curlRequest($url, false, null);
        }
      }
    }

    // delete cache for NEW STANDINGS
    // must remove/clear code above which works for the old standings system
    if (!empty($domainSportIds)) {
      $protocol = 'https';
      $nodePort = 2083;
      $hostName = 'nodejs.betwidgets.com';
      $url = $protocol."://".$hostName.":".$nodePort."/delete-standings-cache/".$domainSportIds;
      //echo 'clearing standings cache url ' . $url;
      $this->curlRequest($url, false, null);
    }

    return true;
  }

  public function getDomainUrl($domain) {
    $protocol   = ($domain->getProtocol() ? 'https' : 'http');
    $domainUrl  = $protocol.'://'.$domain->getName().'/';
    
    return $domainUrl;
  }

  public function cacheStandings($translator, $sportId, $locale, $season = null, $fetchStats = 0, $teamId1 = null, $teamId2 = null) {
    $em = $this->entityManager;
    $languageCode   = strtolower($this->getLanguageByLocale($locale));

    //$localeFallback = $this->locale;
    $localeFallback = 'en';

    if ( !$season || $season === 'null' ) {
      $seasonSport = $em->getRepository('AppBundle:FtLeagueSeasonSport')->getLatestSportSeason($sportId);
      if ($seasonSport) {
        $season = $seasonSport['season'];
      }
    }

    // $sportId is always for the main competition, NOT for the additionals
    // $sportId can be a-mexico, a-kuprou, champions-league, superleague but NOT a-mexico-clausura
    $leagueStandings = $em->getRepository('AppBundle:FtLeagueStandings')->getLeagueStandings($sportId, $season, $languageCode, $locale, $localeFallback);

    $data = $this->fixStandingsV2($translator, $locale, $leagueStandings, $teamId1, $teamId2);

    $leagueScorers = $em->getRepository('AppBundle:FtLeagueSeasonScorer')->getLeagueScorers($sportId, $season, $languageCode, $localeFallback);
    if ($leagueScorers) {
      $tmpLeagueName = '';
      $leagueNames = [];
      foreach ($leagueScorers as $single) {
        if (!in_array($tmpLeagueName, $leagueNames)) {
          $leagueNames[] = $single['leagueName'];
        }

        $data['scorers'][$single['leagueName']]['data'][] = array(
          'playerName'  => $single['playerName'],
          'teamId'      => $single['teamId'],
          'nameEn'      => $single['nameEn'],
          'goals'       => $single['goals'],
          'penalties'   => $single['penalties'],
          'scorerFirst' => $single['scorerFirst']
        );
      }

      foreach ($leagueNames as $leagueName) {
        $data['scorers'][$leagueName]['fields'] = array(
          'teamId'      => '',
          'playerName'  => $translator->trans('PLAYER'),
          'nameEn'      => $translator->trans('TEAM'),
          'goals'       => $translator->trans('GOALS'),
          'penalties'   => $translator->trans('PENALTY GOALS'),
          'scorerFirst' => $translator->trans('FIRST GOALS'),
        );
      }
    }

    $translations = array($translator->trans('1st Half Standings'), $translator->trans('2nd Half Standings'));
    $leagueStandingsHalves = $em->getRepository('AppBundle:FtLeagueStandings')->getLeagueStandingsHalves($sportId, $season, $languageCode, $locale, $localeFallback, $translations);

    $dataHalves = $this->fixStandingsV2($translator, $locale, $leagueStandingsHalves, null, null);
    if ($dataHalves && isset($dataHalves['standings'])) $data['halves'] = $dataHalves['standings'];

    $translations = array($translator->trans('Home'), $translator->trans('Away'), $translator->trans('TEAM'));
    $dataHalfFinal = $em->getRepository('AppBundle:FtLeagueStandings')->getTeam1x2HalfFinal($sportId, $season, $languageCode, $localeFallback, $translations);

    if ($dataHalfFinal) {
      // home
      $data['home11'] = $dataHalfFinal['home11'];
      $data['home1X'] = $dataHalfFinal['home1X'];
      $data['home12'] = $dataHalfFinal['home12'];
      $data['homeX1'] = $dataHalfFinal['homeX1'];
      $data['homeXX'] = $dataHalfFinal['homeXX'];
      $data['homeX2'] = $dataHalfFinal['homeX2'];
      $data['home21'] = $dataHalfFinal['home21'];
      $data['home2X'] = $dataHalfFinal['home2X'];
      $data['home22'] = $dataHalfFinal['home22'];
      // away
      $data['away11'] = $dataHalfFinal['away11'];
      $data['away1X'] = $dataHalfFinal['away1X'];
      $data['away12'] = $dataHalfFinal['away12'];
      $data['awayX1'] = $dataHalfFinal['awayX1'];
      $data['awayXX'] = $dataHalfFinal['awayXX'];
      $data['awayX2'] = $dataHalfFinal['awayX2'];
      $data['away21'] = $dataHalfFinal['away21'];
      $data['away2X'] = $dataHalfFinal['away2X'];
      $data['away22'] = $dataHalfFinal['away22'];
    }

    $translations = array($translator->trans('Goals'), $translator->trans('TEAM'));
    $dataGoals = $em->getRepository('AppBundle:FtLeagueStandings')->getTeamGoals($sportId, $season, $languageCode, $localeFallback, $translations);
    if ($dataGoals) {
      $data['goals01']  = $dataGoals['goals01'];
      $data['goals02']  = $dataGoals['goals02'];
      $data['goals23']  = $dataGoals['goals23'];
      $data['goals3']   = $dataGoals['goals3'];
      $data['goals46']  = $dataGoals['goals46'];
      $data['goals7']   = $dataGoals['goals7'];
    }

    // fetch stats
    if (1 === (int) $fetchStats) {
      // 'UEFA', 'CHL', 'WCQEU'
      $leagueCups = array(310, 309, 317);
      $finalScorers = array();

      if (in_array($sportId, $leagueCups)) {
        $teamForm        = array();
        $teamTotalStats  = array();
        $previousNext    = array();
        $mostWins        = array();
        $mostDraws       = array();
        $mostLoses       = array();
        $mostWinsStreak  = array();
        $mostDrawsStreak = array();
        $mostLossStreak  = array();
        $noWinStreak     = array();
        $noLossStreak    = array();
        $mostOver25      = array();
        $mostUnder25     = array();
        $mostGoalGoal    = array();
        $mostNoGoal      = array();
        $yellowCards     = array();
        $redCards        = array();
        $leagueStats     = array();
      }
      else {
        $translations = array($translator->trans('Team Form'), $translator->trans('#'), $translator->trans('TEAM'), $translator->trans('W-D-L'));
        $teamForm     = $em->getRepository('AppBundle:TeamStats')->getTeamForm($sportId, $season, $languageCode, $localeFallback, $translations);

        $translations   = array($translator->trans('Stats Analytics'), $translator->trans('#'), $translator->trans('TEAM'), $translator->trans('Pts'), $translator->trans('M'), $translator->trans('W'), $translator->trans('D'), $translator->trans('L'));
        $teamTotalStats = $em->getRepository('AppBundle:TeamStats')->getTeamTotalStats($sportId, $season, $languageCode, $localeFallback, $translations);

        $translations = array($translator->trans('Previous Matches'), $translator->trans('DATE'), $translator->trans('MATCH'), $translator->trans('RESULT'), $translator->trans('Next Matches'));
        $previousNext = $em->getRepository('AppBundle:FtFixture')->getPreviousNextMatches($sportId, $season, $languageCode, $localeFallback, $translations);

        $translations = array($translator->trans('Most Wins'), $translator->trans('#'), $translator->trans('TEAM'), $translator->trans('W'));
        $mostWins     = $em->getRepository('AppBundle:TeamStats')->getMostWins($sportId, $season, $languageCode, $localeFallback, $translations);

        $translations = array($translator->trans('Most Draws'), $translator->trans('#'), $translator->trans('TEAM'), $translator->trans('D'));
        $mostDraws    = $em->getRepository('AppBundle:TeamStats')->getMostDraws($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Most Loses'), $translator->trans('#'), $translator->trans('TEAM'), $translator->trans('L'));
        $mostLoses    = $em->getRepository('AppBundle:TeamStats')->getMostLoses($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations   = array($translator->trans('Win Streak'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostWinsStreak = $em->getRepository('AppBundle:TeamStats')->getWinStreak($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations     = array($translator->trans('Draw Streak'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostDrawsStreak  = $em->getRepository('AppBundle:TeamStats')->getDrawStreak($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations   = array($translator->trans('Lose Streak'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostLossStreak = $em->getRepository('AppBundle:TeamStats')->getLossStreak($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('No Win Streak'), $translator->trans('#'), $translator->trans('TEAM'));
        $noWinStreak  = $em->getRepository('AppBundle:TeamStats')->getNoWinStreak($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('No Loss Streak'), $translator->trans('#'), $translator->trans('TEAM'));
        $noLossStreak = $em->getRepository('AppBundle:TeamStats')->getNoLossStreak($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Most Over'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostOver25   = $em->getRepository('AppBundle:TeamStats')->getOver25($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Most Under'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostUnder25  = $em->getRepository('AppBundle:TeamStats')->getUnder25($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Most Goal Goal'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostGoalGoal = $em->getRepository('AppBundle:TeamStats')->getGoalGoal($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Most No Goal'), $translator->trans('#'), $translator->trans('TEAM'));
        $mostNoGoal   = $em->getRepository('AppBundle:TeamStats')->getNoGoal($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Yellow Cards'), $translator->trans('#'), $translator->trans('TEAM'));
        $yellowCards  = $em->getRepository('AppBundle:TeamStats')->getYellowCards($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array($translator->trans('Red Cards'), $translator->trans('#'), $translator->trans('TEAM'));
        $redCards     = $em->getRepository('AppBundle:TeamStats')->getRedCards($sportId, $season, strtolower($languageCode), $localeFallback, $translations);

        $translations = array(
          $translator->trans('Competition Statistics'), 
          $translator->trans('Statistics'), 
          $translator->trans('Total Matches'), 
          $translator->trans('Home Wins'), 
          $translator->trans('Draws'), 
          $translator->trans('Away Wins'), 
          $translator->trans('Total Goals'), 
          $translator->trans('Total Goals') . ' ' . $translator->trans('Home'), 
          $translator->trans('Total Goals') . ' ' . $translator->trans('Away'), 
          $translator->trans('M.V. Goals Home'), 
          $translator->trans('M.V. Goals Away')
        );

        $leagueStats      = $em->getRepository('AppBundle:TeamStats')->getLeagueStats($sportId, $season, $languageCode, $localeFallback, $translations);
      }

      $data['teamForm']         = $teamForm;
      $data['teamTotalStats']   = $teamTotalStats;
      $data['previousMatches']  = isset($previousNext[0]) ? $previousNext[0] : null;
      $data['nextMatches']      = isset($previousNext[1]) ? $previousNext[1] : null;
      $data['mostWins']         = $mostWins;
      $data['mostDraws']        = $mostDraws;
      $data['mostLoses']        = $mostLoses;
      $data['mostWinsStreak']   = $mostWinsStreak;
      $data['mostDrawsStreak']  = $mostDrawsStreak;
      $data['mostLossStreak']   = $mostLossStreak;
      $data['noWinStreak']      = $noWinStreak;
      $data['noLossStreak']     = $noLossStreak;
      $data['mostOver25']       = $mostOver25;
      $data['mostUnder25']      = $mostUnder25;
      $data['mostGoalGoal']     = $mostGoalGoal;
      $data['mostNoGoal']       = $mostNoGoal;
      $data['yellowCards']      = $yellowCards;
      $data['redCards']         = $redCards;
      $data['leagueStats']      = $leagueStats;
    }

    $em->close();

    return $data;
  }

  private function fixStandingsV2($translator, $locale, $data, $teamId1, $teamId2) {
    // $translator = $this->theTranslator;
    $translator->setLocale($locale);

    $result = null;
    $homeAwayFilter = ((int) $teamId1 && (int) $teamId2);

    if ($data) {
      foreach ($data['standings'] as $single) {
        $result['standings'][$single['leagueName']]['fields'] = array(
          'group1' => array(
            'columns' => 4,
            'label'   => '',
            'sub' => array(
              'rank'        => $translator->trans('#'),
              'teamId'      => '',
              'teamName'    => $translator->trans('TEAM'),
              'overall_pts' => $translator->trans('PTS')
            )
          ),
          'group2' => array(
            'columns' => 6,
            'label'   => $translator->trans('TOTAL'),
            'sub'     => array(
              'overall_p'       => $translator->trans('MP'),
              'overall_w'       => $translator->trans('W'),
              'overall_d'       => $translator->trans('D'),
              'overall_l'       => $translator->trans('L'),
              'goals'           => $translator->trans('G'),
              'goalDifference'  => $translator->trans('GD')
            )
          ),
          'group3' => array(
            'columns' => 4,
            'label'   => $translator->trans('HOME'),
            'sub'     => array(
              'home_w'    => $translator->trans('W'),
              'home_d'    => $translator->trans('D'),
              'home_l'    => $translator->trans('L'),
              'homeGoals' => $translator->trans('G')
            )
          ),
          'group4' => array(
            'columns' => 4,
            'label'   => $translator->trans('AWAY'),
            'sub'     => array(
              'away_w'    => $translator->trans('W'),
              'away_d'    => $translator->trans('D'),
              'away_l'    => $translator->trans('L'),
              'awayGoals' => $translator->trans('G')
            )
          )
        );

        if ($homeAwayFilter) {
          if (($teamId1 != $single['team_id']) && ($teamId2 != $single['team_id'])) continue;
        }

        $dataInfo = isset($data['info']) ? $data['info'] : null;

        $teamInfo = $this->checkRankAndInfo($single['sport_id'], $single['rank'], $dataInfo);
        if ($teamInfo) {
          $result['standings'][$single['leagueName']]['data'][] = array(
            'teamId'      => $single['team_id'],
            'teamName'    => $single['teamName'],
            'rank'        => $single['rank'],
            'overall_p'   => $single['overall_p'],
            'overall_pts' => $single['overall_pts'],
            'overall_w'   => $single['overall_w'],
            'overall_d'   => $single['overall_d'],
            'overall_l'   => $single['overall_l'],
            'overall_gf'  => $single['overall_gf'],
            'overall_ga'  => $single['overall_ga'],
            'goals'       => $single['goals'],
            'goalDifference' => $single['goalDifference'],
            'home_p'      => $single['home_p'],
            'home_w'      => $single['home_w'],
            'home_d'      => $single['home_d'],
            'home_l'      => $single['home_l'],
            'home_gf'     => $single['home_gf'],
            'home_ga'     => $single['home_ga'],
            'homeGoals'   => $single['homeGoals'],
            'away_p'      => $single['away_p'],
            'away_w'      => $single['away_w'],
            'away_d'      => $single['away_d'],
            'away_l'      => $single['away_l'],
            'away_gf'     => $single['away_gf'],
            'away_ga'     => $single['away_ga'],
            'awayGoals'   => $single['awayGoals'],
            'penalty_pts' => $single['penalty_pts'],
            'penalty_hgf' => $single['penalty_hgf'],
            'penalty_hga' => $single['penalty_hga'],
            'penalty_agf' => $single['penalty_agf'],
            'penalty_aga' => $single['penalty_aga'],
            'info'        => $teamInfo
          );
        }
        else {
          $result['standings'][$single['leagueName']]['data'][] = array(
            'teamId'      => $single['team_id'],
            'teamName'    => $single['teamName'],
            'rank'        => $single['rank'],
            'overall_p'   => $single['overall_p'],
            'overall_pts' => $single['overall_pts'],
            'overall_w'   => $single['overall_w'],
            'overall_d'   => $single['overall_d'],
            'overall_l'   => $single['overall_l'],
            'overall_gf'  => $single['overall_gf'],
            'overall_ga'  => $single['overall_ga'],
            'goals'       => $single['goals'],
            'goalDifference' => $single['goalDifference'],
            'home_p'      => isset($single['home_p']) ? $single['home_p'] : 0,
            'home_w'      => $single['home_w'],
            'home_d'      => $single['home_d'],
            'home_l'      => $single['home_l'],
            'home_gf'     => $single['home_gf'],
            'home_ga'     => $single['home_ga'],
            'homeGoals'   => $single['homeGoals'],
            'away_p'      => isset($single['away_p']) ? $single['away_p'] : 0,
            'away_w'      => $single['away_w'],
            'away_d'      => $single['away_d'],
            'away_l'      => $single['away_l'],
            'away_gf'     => $single['away_gf'],
            'away_ga'     => $single['away_ga'],
            'awayGoals'   => $single['awayGoals'],
            'penalty_pts' => isset($single['penalty_pts']) ? $single['penalty_pts'] : 0,
            'penalty_hgf' => isset($single['penalty_hgf']) ? $single['penalty_hgf'] : 0,
            'penalty_hga' => isset($single['penalty_hga']) ? $single['penalty_hga'] : 0,
            'penalty_agf' => isset($single['penalty_agf']) ? $single['penalty_agf'] : 0,
            'penalty_aga' => isset($single['penalty_aga']) ? $single['penalty_aga'] : 0
          );
        }
      }
    }

    return $result;
  }

  private function checkRankAndInfo($sportId, $rank, $info) {
    $result = null;

    if ($info) {
      foreach ($info as $single) {

        if ($single['sport_id'] != $sportId) continue;

        $values = explode(',', str_replace(' ', '', $single['value']));
        if (in_array($rank, $values)) {
          $result = array('id' => $single['option_id'], 'value' => $single['optionName']);
        }
      }
    }

    return $result;
  }

}
