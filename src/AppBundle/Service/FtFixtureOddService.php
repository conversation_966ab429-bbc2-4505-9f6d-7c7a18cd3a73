<?php

namespace AppBundle\Service;

use Symfony\Component\DomCrawler\Crawler;
use Psr\Log\LoggerInterface;

use AppBundle\Service\GlobalHelperService;

class FtFixtureOddService {

  protected $entityManager;
  protected $parser;

  const BOOKMAKERS = 'stoiximan,bet365,betshop,bwin,interwetten,pamestoixima,fonbet,novibet,betsson,winmasters,netbet';
  private $bookRel = array('stoiximan' => 19320, 'bet365' => 20081, 'betshop' => 20247, 'bwin' => 20085, 'interwetten' => 20348, 'pamestoixima' => 20600, 'fonbet' => 286790, 'novibet' => 19247, 'betsson' => 318228, 'winmasters' => 20272, 'netbet' => 20381);

  public function __construct($entityManager, $parser) {
    $this->entityManager = $entityManager;
    $this->parser = $parser;

    $this->helper = new GlobalHelperService();
  }

  public function batchInsert($username, $password, $lan = null, $fixturesAll = null, $translator, $options) {
    date_default_timezone_set("Europe/Athens");
    $em = $this->entityManager;

    $currentDate = date('Y-m-d');

    try {
      $url = "http://odds.globalscore.com/gpr/cms/xml/basic.php?username=$username&date=$currentDate&b=".self::BOOKMAKERS;

      $crawler = new Crawler();
      try {
        $fileContent = file_get_contents($url);
        // fix not well-formed xml
        $fileContent = str_replace('&', '&amp;', $fileContent);

        $crawler->addXmlContent($fileContent);
      }
      catch (\Exception $e){
        $logger = $this->parser;
        $logger->critical($e->getMessage());
      }

      $fixturesArr = $crawler->filterXPath('//fixture')->each(function ($node, $i) {
        $arr['id'] = (int) $node->extract('id')[0];
        $arr['matchDatetime'] = $node->extract('datetime')[0];

        $node->filterXPath('//odds')->each(function($odd, $i) use (&$arr) {
          $oddType = $odd->extract('type');

          $odd->filterXPath('//book')->each(function($book, $i) use (&$arr, $oddType) {
            $bookId = $this->bookRel[$book->extract('name')[0]];

            $arr['odds'][$bookId]['last_modified'] = $book->extract('upd')[0];
            if (1 === (int) $oddType[0]) {
              $arr['odds'][$bookId]['odd_1'] = $book->extract('odd_1')[0];
              $arr['odds'][$bookId]['odd_x'] = $book->extract('odd_X')[0];
              $arr['odds'][$bookId]['odd_2'] = $book->extract('odd_2')[0];
            }
            if (2 === (int) $oddType[0]) {
              $arr['odds'][$bookId]['odd_o25'] = $book->extract('odd_o25')[0];
              $arr['odds'][$bookId]['odd_u25'] = $book->extract('odd_u25')[0];
            }
          });
        });

        return $arr;
      });

      if ($fixturesArr) {

        $fixtureCounter = count($fixturesArr) - 1;

        foreach ($fixturesArr as $key => $fixture) {
          if (isset($fixture['odds'])) {
            foreach ($fixture['odds'] as $bookKey => $single) {
              // get current row if exists for fixture and bookmaker
              $prev = $em->getRepository('AppBundle:FtFixtureOdd')->findOneBy(array('fId' => $fixture['id'], 'bookmakerId' => $bookKey));

              $dateTime = \DateTime::createFromFormat('Y-m-d H:i:s', $this->helper->getCurrentTimestampDate());

              if ($prev) {
                // if date modified different than previous do UPDATES
                if (\DateTime::createFromFormat('Y-m-d-H-i-s', $single['last_modified']) != $prev->getLastModified()) {
                  // set previous values
                  $prev->setPOdd1($prev->getOdd1());
                  $prev->setPOddX($prev->getOddX());
                  $prev->setPOdd2($prev->getOdd2());
                  $prev->setPOddO25($prev->getOddO25());
                  $prev->setPOddU25($prev->getOddU25());
                  // set current values
                  $prev->setOdd1($single['odd_1']);
                  $prev->setOddX($single['odd_x']);
                  $prev->setOdd2($single['odd_2']);
                  $prev->setOddO25($single['odd_o25']);
                  $prev->setOddU25($single['odd_u25']);
                  $prev->setBookmakerId($bookKey);
                  $prev->setLastModified(\DateTime::createFromFormat('Y-m-d-H-i-s', $single['last_modified']));
                  $prev->setMatchDatetime(\DateTime::createFromFormat('Y-m-d-H-i', $fixture['matchDatetime']));
                  $prev->setUpdatedAt($dateTime);

                  try {
                    $em->persist($prev);
                    $em->flush();
                  }
                  catch (\Exception $e) {
                    echo '<pre>';
                    var_dump($e->getMessage());
                    echo '</pre>';
                  }
                }
              }
              else {
                // inserting new odd
                // if feed is sending crap matchdatetime ignore it and continue
                if (!\DateTime::createFromFormat('Y-m-d-H-i', $fixture['matchDatetime'])) {
                  continue;
                }
                $newOdd = new \AppBundle\Entity\FtFixtureOdd;
                $newOdd->setOdd1($single['odd_1']);
                $newOdd->setOddX($single['odd_x']);
                $newOdd->setOdd2($single['odd_2']);
                $newOdd->setOddO25($single['odd_o25']);
                $newOdd->setOddU25($single['odd_u25']);
                $newOdd->setBookmakerId($bookKey);
                $newOdd->setLastModified(\DateTime::createFromFormat('Y-m-d-H-i-s', $single['last_modified']));
                $newOdd->setMatchDatetime(\DateTime::createFromFormat('Y-m-d-H-i', $fixture['matchDatetime']));
                $newOdd->setFId($fixture['id']);

                try {
                  $em->persist($newOdd);
                  $em->flush();
                }
                catch (\Exception $e){
                  echo '<pre>';
                  var_dump($e->getMessage());
                  echo '</pre>';
                }
              }
            }
          }
        }
      }
    }
    catch (\Exception $e) {
      echo '<pre>';
      var_dump($e->getMessage());
      echo '</pre>';
    }

    $em->clear();
    return;
  }
}
