<?php

namespace AppBundle\Service;

use Symfony\Component\DomCrawler\Crawler;
use Psr\Log\LoggerInterface;

use AppBundle\Service\GlobalHelperService;

class FtTeamService {

  protected $entityManager;
  protected $parser;

  // protected $continents = array("AFRIC", "EUROP", "OCEAN", "ASIA", "AMERI", "AMERS", "AMERN");

  public function __construct($entityManager, $parser) {
    $this->entityManager = $entityManager;
    $this->parser = $parser;

    $this->helper = new GlobalHelperService();
  }

  public function batchInsert($username, $password, $lan = null, $fixturesAll = null, $translator, $options) {
    date_default_timezone_set("Europe/Athens");
    $em = $this->entityManager;

    $settingsSql = "SELECT value FROM ft_settings WHERE option_name = 'country_id_$lan' LIMIT 1";
    $stmt = $em->getConnection()->prepare($settingsSql);
    $stmt->execute();
    $countryId = $stmt->fetchColumn();

    try {
      $sql = "SELECT id, c_id FROM country WHERE id = (SELECT MIN(id) FROM country WHERE id > $countryId)";
      $stmt = $em->getConnection()->prepare($sql);
      $stmt->execute();
      $country = $stmt->fetchAll();

      $newValue = 0;
      if (isset($country[0])) {
        $newValue = $country[0]['id'];

        $url = "http://xml.globalscore.com/xml.php?un=".$username."&pass=".$password."&sport=s&tp=teams&cid=".$country[0]['c_id']."&lan=".$lan;

        $crawler = new Crawler();
        try {
          $xmlFile = file_get_contents($url);
          $xmlFileFixed = str_replace("&", "&amp;", $xmlFile);
          $crawler->addXmlContent($xmlFileFixed);
        }
        catch (\Exception $e){
          $logger = $this->parser;
          $logger->critical($e->getMessage());
        }

        $arr = array();
        $basis = $crawler->filterXPath('//team');
        $teamsArr = $basis->each(function ($node, $i) {
          $arr = array();
          if ($node->filter('team')->count() > 0) {
            $arr['teamId']      = $node->filter('team')->attr('id');
            $arr['teamName']    = str_replace("&amp;", "&", $node->filter('team')->attr('name'));
            $arr['isNational']  = $node->filter('team')->attr('nat');
          }

          return $arr;
        });

        if ($teamsArr) {

          $insertSql = "
          INSERT INTO ft_team (country_id, is_national, name_$lan, team_id, created_at) VALUES
          ";

          $hasRecords = 0;
          $teamCounter = count($teamsArr) - 1;

          foreach ($teamsArr as $i => $team) {
            $hasRecords++;

            $dateTime = $this->helper->getCurrentTimestampDate();
            $insertSql .= "(".$country[0]['id'].", ".$team['isNational'].", '".$team['teamName']."', ".$team['teamId'].", '$dateTime' ) ";
            $insertSql .= ($teamCounter  === $i) ? " ON DUPLICATE KEY UPDATE name_$lan = VALUES(name_$lan), updated_at = '$dateTime';" : ",";
          }

          if ($hasRecords > 0) {
            try {
              $stmt = $em->getConnection()->prepare($insertSql);
              $stmt->execute();
              $logger = $this->parser;
              $logger->info("Updated $hasRecords Team records for $lan");
            }
            catch (\Exception $e) {
              $logger = $this->parser;
              $logger->critical($e->getMessage());
            }
          }
        }
      }

      $settingsUpdateSql = "UPDATE ft_settings SET value = $newValue WHERE option_name = 'country_id_$lan'";
      $stmt = $em->getConnection()->prepare($settingsUpdateSql);
      $stmt->execute();
    }
    catch (\Exception $e) {
      echo '<pre>';
      var_dump($e->getMessage());
      echo '</pre>';
    }

    $em->clear();
    return;
  }
}
