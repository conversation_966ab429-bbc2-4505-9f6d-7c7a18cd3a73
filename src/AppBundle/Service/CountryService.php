<?php

namespace AppBundle\Service;

use Symfony\Component\DomCrawler\Crawler;
use Psr\Log\LoggerInterface;

use AppBundle\Service\GlobalHelperService;

class CountryService {

  protected $entityManager;
  protected $parser;
  protected $helper;

  public function __construct($servicesArr) {
    $this->entityManager = $servicesArr[0];
    $this->parser = $servicesArr[1];
    $this->helper = new GlobalHelperService($this->entityManager, $this->parser);
  }

  public function batchInsert($username, $password, $lan, $fixturesAll = null, $translator, $options) {
    date_default_timezone_set("Europe/Athens");
    $em = $this->entityManager;

    $url = "http://xml.globalscore.com/xml.php?un=".$username."&pass=".$password."&sport=s&tp=countries&lan=".$lan;

    $crawler = new Crawler();
    try {
      $crawler->addXmlContent(file_get_contents($url));
    }
    catch (\Exception $e){
      $logger = $this->parser;
      $logger->critical($e->getMessage());
    }

    $countryArr = $crawler->filterXPath('//country')->each(function ($node, $i) {
      $arr = $node->extract(array('id', 'name', 'code', 'isContinent', 'continent'))[0];

      return $arr;
    });


    $sql = "insert into country (c_id, name_$lan, is_continent, continent, code, created_at) VALUES ";

    $hasRecords = 0;
    foreach ($countryArr as $i => $value) {
      $hasRecords++;
      $dateTime = $this->helper->getCurrentTimestampDate();

      $sql .= " ('$value[0]', '$value[1]', '$value[3]', '$value[4]', '$value[2]', '$dateTime') ";
      $sql .= (count($countryArr) - 1  === $i) ? " ON DUPLICATE KEY UPDATE name_$lan = VALUES(name_$lan), updated_at = '$dateTime' ;" : ",";
    }

    if ($hasRecords > 0) {
      try {
        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute();
        $logger = $this->parser;
        $logger->info("Updated $hasRecords COUNTRY records for $lan");
      }
      catch (\Exception $e) {
        $logger = $this->parser;
        $logger->critical($e->getMessage());
      }
    }

    $em->clear();
  }
}
