<?php

namespace AppBundle\Service;

use Symfony\Component\DomCrawler\Crawler;
use AppBundle\Service\GlobalHelperService;

class FtFixtureService {

  // protected $entityManager;
  // protected $parser;

  const TIME_DIFF     = 2;
  const FUTURE_CHANGE = 5;

  public function __construct($entityManager, $cf = null, $parser) {
    $this->entityManager = $entityManager;
    $this->cf = $cf;
    $this->parser = $parser;

    $this->helper = new GlobalHelperService();
  }

  public function batchInsert($username, $password, $lan = null, $fixturesAll, $translator, $options, $silent = null, $sportIds = null, $days = null) {
    if (in_array('history', $options)) {
      $this->batchInsertHistory($username, $password, $lan, $fixturesAll, $translator, $options, $silent, $sportIds, $days);
    }

    date_default_timezone_set("Europe/Athens");
    $em                 = $this->entityManager;
    $ftLeagueSportsArr  = $em->getRepository('AppBundle:FtLeagueSport')->getFtLeagueSportsArr();
    $silentArr          = array();
    // $mundialSportIds    = array(618, 619, 620, 622, 623, 624, 625, 626, 627);
    $mundialSportIds    = array(712, 713, 714, 715, 716, 717, 718, 719, 720);
    $hasMundialMatches  = false;
    $mundialMatches     = array();

    // $dateFrom = "2016-10-17-17-00";
    // $dateTo = "2016-10-17-23-59";

    if ($fixturesAll) {
      // $settingsSql = "SELECT f_interval FROM ft_settings LIMIT 1";
      $settingsSql = "SELECT value FROM ft_settings WHERE option_name = 'date_interval' LIMIT 1";
      $stmt = $em->getConnection()->prepare($settingsSql);
      $stmt->execute();
      $interval = $stmt->fetchColumn();

      $newInterval = $interval + self::FUTURE_CHANGE;
      if ($newInterval > 200) { /// Reset future days counter
        $newInterval = -45;
      	// $newInterval = -500;
      }

      $settingsUpdateSql = "UPDATE ft_settings SET value = $newInterval WHERE option_name = 'date_interval' ";
      $stmt = $em->getConnection()->prepare($settingsUpdateSql);
      $stmt->execute();

      $dateFrom = date("Y-m-d-H-i", mktime(0, 0, 0, date("m"), date("d") + $interval, date("Y")));
      $dateTo   = date("Y-m-d-H-i", mktime(23, 59, 59, date("m"), date("d") + $interval + self::FUTURE_CHANGE, date("Y")));

      // $dateFrom = date("Y-m-d-H-i", mktime(0, 0, 0, 9, 17, 2016));
      // $dateTo   = date("Y-m-d-H-i", mktime(23, 59, 59, 9, 17, 2016));

      // print_r($dateFrom);
      // echo '1'.PHP_EOL;
      // print_r($dateTo);
      // echo PHP_EOL;
    }
    else {
      if ($silent) {
        $dateFrom = date("Y-m-d-H-i", mktime(7, 0, 0, date("m"), date("d") + (int) $days - 1, date("Y")));
        $dateTo   = date("Y-m-d-H-i", mktime(6, 59, 59, date("m"), date("d") + (int) $days, date("Y")));
      }
      else {
        $dateFrom = date("Y-m-d-H-i", mktime(0, 0, 0, date("m"), date("d") - 2, date("Y")));
        $dateTo   = date("Y-m-d-H-i", mktime(23, 59, 59, date("m"), date("d") + 1, date("Y")));
      }

      // print_r($dateFrom);
      // echo '2'.PHP_EOL;
      // print_r($dateTo);
      // echo PHP_EOL;
    }

    $url = "http://xml.globalscore.com/xml.php?un=".$username."&pass=".$password."&sport=s&tp=fixtures&sbtp=date&from=".$dateFrom."&to=".$dateTo."&lan=en";

    $crawler = new Crawler();
    try {
      $crawler->addXmlContent(file_get_contents($url));
      //In case we have timeout
      //$crawler->addXmlContent($this->curlGetContent($url));
    }
    catch (\Exception $e){
      if (!isset($logger)) $logger = $this->parser;
      $logger->critical($e->getMessage());
    }

    $fixturesArr = $crawler->filterXPath('//game')->each(function ($node, $i) {
      $arr = $node->extract(array('id', 'date', 'time', 'week', 'league_id', 'minute', 'gamestatus', 'season', 'upd'))[0];

      $teamsArr = $node->filterXPath('//teams')->each(function ($node1, $i1) {
        $hTeam      = $node1->filter('hometeam')->attr('id');
        $hTeamName  = $node1->filter('hometeam')->attr('name');
        $aTeam      = $node1->filter('awayteam')->attr('id');
        $aTeamName  = $node1->filter('awayteam')->attr('name');

        $teamsArr = array('hTeam' => $hTeam, 'aTeam' => $aTeam, 'hTeamName' => $hTeamName, 'aTeamName' => $aTeamName);

        return $teamsArr;
      });

      $goalsArr = $node->filterXPath('//score')->each(function ($node1, $i1) {
        $result = $node1->extract(array('goal1', 'goal2', 'ht_goal1', 'ht_goal2', 'et_goal1', 'et_goal2', 'pt_goal1', 'pt_goal2'))[0];

        return $result;
      });

      $eventsArr = $node->filterXPath('//events')->each(function ($node1, $i1) {
        $matchEvents = $node1->filterXPath('//event')->each(function ($node2, $i2) {
          $matchEvent = $node2->extract(array('player', 'type', 'team', 'minute'))[0];

          return $matchEvent;
        });

        $tempArr = array_map(function($el){ return $el[0].'##'.$el[1].'##'.$el[2].'##'.$el[3]; }, $matchEvents);
        $fixedArr = implode('##', $tempArr);

        return $fixedArr;
      });

      $arr[] = $teamsArr[0]['hTeam'];
      $arr[] = $teamsArr[0]['aTeam'];

      // ft1, ft2, ht1, ht2, et1, et2, pt1, pt2
      $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][0] : 0;
      $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][1] : 0;
      $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][2] : 0;
      $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][3] : 0;
      $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][4] : 0;
      $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][5] : 0;
      $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][6] : 0;
      $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][7] : 0;
      $arr[] = (isset($eventsArr[0])) ? strval($eventsArr[0]) : '';

      // add team names for silent version
      $arr[] = $teamsArr[0]['hTeamName'];
      $arr[] = $teamsArr[0]['aTeamName'];

      return $arr;
    });

    // print_r($fixturesArr);

    $sql = "insert into ft_fixture (f_id, match_datetime, h_team, a_team, league, season, status, minute, ft1, ft2, ht1, ht2, et1, et2, pt1, pt2, events, week, updated, created_at, sport_id) VALUES ";

    // $sqlMundial = "INSERT INTO fixture (`home_team_id`, `home_team_name`, `away_team_id`, `away_team_name`, `match_datetime`, `ht1`, `ht2`, `ft1`, `ft2`, `et1`, `et2`, `pt1`, `pt2`, `status`, `fixture_id`, `phase_name`, `match_value`, `is_evaluated`, `created_at`) VALUES ";
    $sqlMundial = "INSERT INTO fixture (`home_team_id`, `home_team_name`, `away_team_id`, `away_team_name`, `match_datetime`, `ht1`, `ht2`, `ft1`, `ft2`, `et1`, `et2`, `pt1`, `pt2`, `status`, `fixture_id`, `phase_name`, `match_value`, `created_at`) VALUES ";
    $mundialPhase = array();
    $mundialPhase['AA'] = array('name' => 'Α\' Όμιλος', 'value' => 100);
    $mundialPhase['AB'] = array('name' => 'Β\' Όμιλος', 'value' => 100);
    $mundialPhase['AC'] = array('name' => 'Γ\' Όμιλος', 'value' => 100);
    $mundialPhase['AD'] = array('name' => 'Δ\' Όμιλος', 'value' => 100);
    $mundialPhase['AE'] = array('name' => 'Ε\' Όμιλος', 'value' => 100);
    $mundialPhase['AF'] = array('name' => 'ΣΤ\' Όμιλος', 'value' => 100);
    $mundialPhase['AG'] = array('name' => 'Ζ\' Όμιλος', 'value' => 100);
    $mundialPhase['AH'] = array('name' => 'Η\' Όμιλος', 'value' => 100);
    $mundialPhase['5'] = array('name' => 'Φάση των 16', 'value' => 200);
    $mundialPhase['6'] = array('name' => 'Προημιτελικά', 'value' => 300);
    $mundialPhase['7'] = array('name' => 'Ημιτελικά', 'value' => 400);
    $mundialPhase['8'] = array('name' => 'Μικρός Τελικός', 'value' => 400);
    $mundialPhase['9'] = array('name' => 'Τελικός', 'value' => 500);

    // if (!$teamsArr[0]['hTeam'])
    if (!isset($logger)) $logger = $this->parser;

    $hasRecords = 0;
    $fixtureCounter = count($fixturesArr) - 1;

    foreach ($fixturesArr as $i => $value) {
      // do not record games with empty team data
      if (!$value[9] || !$value[10] || !$value[20] || !$value[21]) continue;

      $hasRecords++;

      $dateTime = $this->helper->getCurrentTimestampDate();

      $dateToks = explode("-", $value[1]);
      $timeToks = explode(":", $value[2]);

      if (!isset($timeToks[1])) {
        $timeToks[1] = 0;
      }

      $matchDateTime = new \DateTime();
      $matchDateTime->setTimestamp(mktime($timeToks[0] + self::TIME_DIFF, $timeToks[1], 0, $dateToks[1], $dateToks[2], $dateToks[0]));
      $matchDateTimeStr = $matchDateTime->format('Y-m-d H:i:s');

      $updatedArr = explode("-", $value[8]);

      $updated = $updatedArr[0].'/'.$updatedArr[1].'/'.$updatedArr[2].' '.$updatedArr[3].':'.$updatedArr[4];

      // escape quotes  MISSING last 2 values, why the substr???
      // $value[19] = htmlentities(substr($value[19], 0, -2), ENT_QUOTES);
      $value[19] = htmlentities($value[19], ENT_QUOTES);

      $sportId = null;
      try {
        $leagueExplodedArr = explode('.', $value[4]);

        // first search if league id ($value[4]) exists in $ftLeagueSportsArr and use that sport_id
        // if it doesn't exist then search for the exploded league id
        // i.e 
        // 1. if game is MEX1A apertura, it exists in $ftLeagueSportsArr as an additional sport so, return MEX1A sport_id
        // 2. if game is GRE1.AA, it exists in $ftLeagueSportsArr as an additional sport so return GRE1.AA sport_id
        $sport_id = array_search($value[4], $ftLeagueSportsArr);

        if (!$sport_id) {
          $leagueExploded = $value[4];
          if (count($leagueExplodedArr) > 1) {
            $leagueExploded = $leagueExplodedArr[0];
          }
          $sport_id = array_search($leagueExploded, $ftLeagueSportsArr);

          if (!$sport_id) $sport_id = null;
        }
        // var_dump($sport_id);
        // echo PHP_EOL;
      }
      catch (\Exception $e) {
        die($e->getMessage());
      }
      $sportIdArr = ( !empty($sportIds) && strpos($sportIds, ',') ) ? explode(',', $sportIds) : array($sportIds);
      if (in_array($sport_id, $sportIdArr)) {
        $silentArr[] = array(
          'fixtureId'     => $value[0],
          'matchDateTime' => $matchDateTimeStr,
          'homeTeamId'    => $value[9],
          'awayTeamId'    => $value[10],
          'homeTeamName'  => $value[20],
          'awayTeamName'  => $value[21],
          'league'        => $value[4],
          'season'        => $value[7],
          'sportId'       => var_export($sport_id, true)
        );
      }

      // this is a world cup match
      // prepare the sql to insert/update match in mundial2018 server (tispter league)
      if (in_array($sport_id, $mundialSportIds)) {
        $hasMundialMatches = true;
        // f_id, match_datetime, h_team, a_team, league, season, status, minute, ft1, ft2, ht1, ht2, et1, et2, pt1, pt2, events, week, updated, created_at, sport_id
        // home_team_id, home_team_name, away_team_id, away_team_name, match_datetime, ht1, ht2, ft1, ft2, et1, et2, pt1, pt2, status, fixture_id, phase_name, match_value, created_at

        $homeTeam = $em->getRepository('AppBundle:FtTeam')->findOneBy(array('teamId' => $value[9]));
        if ($homeTeam) {
          $homeTeamName = $homeTeam->getNameGr();
        }

        $awayTeam = $em->getRepository('AppBundle:FtTeam')->findOneBy(array('teamId' => $value[10]));
        if ($awayTeam) {
          $awayTeamName = $awayTeam->getNameGr();
        }

        $phaseKey = str_replace('WOCUP.', '', $value[4]);

        $phaseName = str_replace("'", "\'", $mundialPhase[$phaseKey]['name']);
        $phaseValue = $mundialPhase[$phaseKey]['value'];

        $mundialMatches[] = array(
          'home_team_id'    => $value[9],
          'home_team_name'  => $homeTeamName,
          'away_team_id'    => $value[10],
          'away_team_name'  => $awayTeamName,
          'match_datetime'  => $matchDateTimeStr,
          'ht1'             => $value[13],
          'ht2'             => $value[14],
          'ft1'             => $value[11],
          'ft2'             => $value[12],
          'et1'             => $value[15],
          'et2'             => $value[16],
          'pt1'             => $value[17],
          'pt2'             => $value[18],
          'status'          => $value[6],
          'fixture_id'      => $value[0],
          'phase_name'      => $phaseName,
          'phase_value'     => $phaseValue,
          // 'is_evaluated'    => 2,
          'created_at'      => $dateTime
        );
      } // Mundial match

      $sql .= "
      ('$value[0]',
      '$matchDateTimeStr',
      $value[9],
      $value[10],
      '$value[4]',
      '$value[7]',
      '$value[6]',
      '$value[5]',
      $value[11],
      $value[12],
      $value[13],
      $value[14],
      $value[15],
      $value[16],
      $value[17],
      $value[18],
      '$value[19]',
      $value[3],
      '$updated',
      '$dateTime',
      ".var_export($sport_id, true).") ";
      $sql .= ($fixtureCounter  === $i) ? " ON DUPLICATE KEY UPDATE sport_id = VALUES(sport_id), status = VALUES(status), minute = VALUES(minute), h_team = VALUES(h_team), a_team = VALUES(a_team), season = VALUES(season), ft1 = VALUES(ft1), ft2 = VALUES(ft2), ht1 = VALUES(ht1), ht2 = VALUES(ht2), et1 = VALUES(et1), et2 = VALUES(et2), pt1 = VALUES(pt1), pt2 = VALUES(pt2), events = VALUES(events), match_datetime = VALUES(match_datetime), week = VALUES(week), updated = VALUES(updated), updated_at = '$dateTime', sport_id = VALUES(sport_id);" : ",";
    } // foreach ($fixturesArr

    if ($hasRecords > 0 && !$silent) {
      try {
        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute();
        if (!isset($logger)) $logger = $this->parser;
        $logger->info("Updated $hasRecords FIXTURE records for $lan");

        // Mundial
        if ($hasMundialMatches) {
          $mundialDomain = $em->getRepository('AppBundle:Domain')->findOneBy(array('name' => 'mundial2018'));

          // $mundialDomain = $em->getRepository('AppBundle:Domain')->findOneById(9);
          if ($mundialDomain) {
            // $logger->info('Running helper '.$this->helper->getCurrentTimestampDate());
            try {
              $wp_db_host     = $mundialDomain->getDbHost();
              $wp_db_port     = $mundialDomain->getDbPort();
              $wp_db_name     = $mundialDomain->getDbName();
              $wp_db_username = $mundialDomain->getDbUsername();
              $wp_db_password = $mundialDomain->getDbPassword();
        
              if (!$wp_db_port) {
                  $wp_db_port = 3306;
              }

              $mysqlStr = 'mysql:host='.$wp_db_host.';dbname='.$wp_db_name.';port='.$wp_db_port.';charset=UTF8';
              // $logger->critical('connection '.$mysqlStr. '---'.$wp_db_username.'----'.$wp_db_password);
              $mundialConnection = $this->cf->createConnection(
                array('pdo' => new \PDO($mysqlStr, $wp_db_username, $wp_db_password))
                // array('pdo' => new \PDO("mysql:host=$wp_db_host;dbname=tipst3r_db;port=3306","tipst3ruser","cjTJBtW2KeMrM6ZB"))
              );

              if ($mundialMatches) {
                $mundialMatchesCounter = count($mundialMatches) - 1;
                foreach ($mundialMatches as $i => $mm) {
                  // $sqlMundial .= "(".$mm['home_team_id'].", '".$mm['home_team_name']."', ".$mm['away_team_id'].", '".$mm['away_team_name']."', '".$mm['match_datetime']."', ".$mm['ht1'].", ".$mm['ht2'].", ".$mm['ft1'].", ".$mm['ft2'].", ".$mm['et1'].", ".$mm['et2'].", ".$mm['pt1'].", ".$mm['pt2'].", '".$mm['status']."', ".$mm['fixture_id'].", '".$mm['phase_name']."', ".$mm['phase_value'].", ".$mm['is_evaluated'].", '".$mm['created_at']."')";
                  $sqlMundial .= "(".$mm['home_team_id'].", '".$mm['home_team_name']."', ".$mm['away_team_id'].", '".$mm['away_team_name']."', '".$mm['match_datetime']."', ".$mm['ht1'].", ".$mm['ht2'].", ".$mm['ft1'].", ".$mm['ft2'].", ".$mm['et1'].", ".$mm['et2'].", ".$mm['pt1'].", ".$mm['pt2'].", '".$mm['status']."', ".$mm['fixture_id'].", '".$mm['phase_name']."', ".$mm['phase_value'].", '".$mm['created_at']."')";
                  $dateTime = $this->helper->getCurrentTimestampDate();
                  $sqlMundial .= ($mundialMatchesCounter === $i) ? " ON DUPLICATE KEY UPDATE `status` = VALUES(`status`), `home_team_id` = VALUES(`home_team_id`), `home_team_name` = VALUES(`home_team_name`), `away_team_id` = VALUES(`away_team_id`), `away_team_name` = VALUES(`away_team_name`), `ft1` = VALUES(`ft1`), `ft2` = VALUES(`ft2`), `ht1` = VALUES(`ht1`), `ht2` = VALUES(`ht2`), `et1` = VALUES(`et1`), `et2` = VALUES(`et2`), `pt1` = VALUES(`pt1`), `pt2` = VALUES(`pt2`), `match_datetime` = VALUES(`match_datetime`), `updated_at` = '$dateTime';" : ",";
                }
              }
              $mundialStmt = $mundialConnection->prepare($sqlMundial);
              $mundialStmt->execute();
            }
            catch (\Exception $err) {
              $logger->critical('SUPER ERROR ' . $err->getMessage());
            }
          }
        }
      }
      catch (\Exception $e) {
        // $logger = $this->parser;
        if (!isset($logger)) $logger = $this->parser;
        $logger->critical('error here my man'.$e->getMessage());
        // $logger->critical($e->getMessage());
      }

      $em->clear();
    }

    if ($hasRecords > 0 && $silent) {
      return $silentArr;
    }
  }

  private function batchInsertHistory($username, $password, $lan = null, $fixturesAll, $translator, $options, $silent = null, $sportIds = null, $days = null)
  {
    date_default_timezone_set("Europe/Athens");
    $em                 = $this->entityManager;
    $ftLeagueSportsArr  = $em->getRepository('AppBundle:FtLeagueSport')->getFtLeagueSportsArr();

    if ($ftLeagueSportsArr) {
      foreach ($ftLeagueSportsArr as $sportId => $league) {
        $leagueSeasons = $em->getRepository('AppBundle:FtLeagueSeason')->createQueryBuilder('o')
          ->where('o.lId LIKE :league')
          ->setParameter('league', "%$league%")
          ->orderBy('o.season', 'ASC')
          ->getQuery()
          ->getResult();

        if ($leagueSeasons) {
          foreach($leagueSeasons as $leagueSeason) {
            // $validated = $this->validateLeagueSeason($leagueSeason->getSeason());
            // if (!$validated) {
            //   continue;
            // }

            $url = "";

            $url = "http://xml.globalscore.com/xml.php?un=".$username."&pass=".$password."&sport=s&tp=fixtures&sbtp=history&lid=".$league."&period=".$leagueSeason->getSeason()."&lan=en";

            $crawler = new Crawler();
            try {
              $crawler->addXmlContent(file_get_contents($url));
            } catch (\Exception $e) {
              if (!isset($logger)) $logger = $this->parser;
              $logger->critical($e->getMessage());
            }

            $fixturesArr = $crawler->filterXPath('//game')->each(function ($node, $i) {
              $arr = $node->extract(array('id', 'date', 'time', 'week', 'league_id', 'minute', 'gamestatus', 'season', 'upd'))[0];
        
              $teamsArr = $node->filterXPath('//teams')->each(function ($node1, $i1) {
                $hTeam      = $node1->filter('hometeam')->attr('id');
                $hTeamName  = $node1->filter('hometeam')->attr('name');
                $aTeam      = $node1->filter('awayteam')->attr('id');
                $aTeamName  = $node1->filter('awayteam')->attr('name');
        
                $teamsArr = array('hTeam' => $hTeam, 'aTeam' => $aTeam, 'hTeamName' => $hTeamName, 'aTeamName' => $aTeamName);
        
                return $teamsArr;
              });
        
              $goalsArr = $node->filterXPath('//score')->each(function ($node1, $i1) {
                $result = $node1->extract(array('goal1', 'goal2', 'ht_goal1', 'ht_goal2', 'et_goal1', 'et_goal2', 'pt_goal1', 'pt_goal2'))[0];
        
                return $result;
              });
        
              $eventsArr = $node->filterXPath('//events')->each(function ($node1, $i1) {
                $matchEvents = $node1->filterXPath('//event')->each(function ($node2, $i2) {
                  $matchEvent = $node2->extract(array('player', 'type', 'team', 'minute'))[0];
        
                  return $matchEvent;
                });
        
                $tempArr = array_map(function($el){ return $el[0].'##'.$el[1].'##'.$el[2].'##'.$el[3]; }, $matchEvents);
                $fixedArr = implode('##', $tempArr);
        
                return $fixedArr;
              });
        
              $arr[] = $teamsArr[0]['hTeam'];
              $arr[] = $teamsArr[0]['aTeam'];
        
              // ft1, ft2, ht1, ht2, et1, et2, pt1, pt2
              $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][0] : 0;
              $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][1] : 0;
              $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][2] : 0;
              $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][3] : 0;
              $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][4] : 0;
              $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][5] : 0;
              $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][6] : 0;
              $arr[] = (isset($goalsArr[0])) ? (int) $goalsArr[0][7] : 0;
              $arr[] = (isset($eventsArr[0])) ? strval($eventsArr[0]) : '';
        
              // add team names for silent version
              $arr[] = $teamsArr[0]['hTeamName'];
              $arr[] = $teamsArr[0]['aTeamName'];
        
              return $arr;
            });

            if (!$fixturesArr) {
              continue;
            }
            
            $sql = "insert into ft_fixture (f_id, match_datetime, h_team, a_team, league, season, status, minute, ft1, ft2, ht1, ht2, et1, et2, pt1, pt2, events, week, updated, created_at, sport_id) VALUES ";

            if (!isset($logger)) $logger = $this->parser;

            $hasRecords = 0;
            $fixtureCounter = count($fixturesArr) - 1;
        
            foreach ($fixturesArr as $i => $value) {
              // do not record games with empty team data
              if (!$value[9] || !$value[10] || !$value[20] || !$value[21]) continue;
        
              $hasRecords++;
        
              $dateTime = $this->helper->getCurrentTimestampDate();
        
              $dateToks = explode("-", $value[1]);
              $timeToks = explode(":", $value[2]);
        
              if (!isset($timeToks[1])) {
                $timeToks[1] = 0;
              }
        
              $matchDateTime = new \DateTime();
              $matchDateTime->setTimestamp(mktime($timeToks[0] + self::TIME_DIFF, $timeToks[1], 0, $dateToks[1], $dateToks[2], $dateToks[0]));
              $matchDateTimeStr = $matchDateTime->format('Y-m-d H:i:s');
        
              $updatedArr = explode("-", $value[8]);
        
              $updated = $updatedArr[0].'/'.$updatedArr[1].'/'.$updatedArr[2].' '.$updatedArr[3].':'.$updatedArr[4];
        
              // escape quotes  MISSING last 2 values, why the substr???
              // $value[19] = htmlentities(substr($value[19], 0, -2), ENT_QUOTES);
              $value[19] = htmlentities($value[19], ENT_QUOTES);
        
              $sportId = null;
              try {
                $leagueExplodedArr = explode('.', $value[4]);
        
                // first search if league id ($value[4]) exists in $ftLeagueSportsArr and use that sport_id
                // if it doesn't exist then search for the exploded league id
                // i.e 
                // 1. if game is MEX1A apertura, it exists in $ftLeagueSportsArr as an additional sport so, return MEX1A sport_id
                // 2. if game is GRE1.AA, it exists in $ftLeagueSportsArr as an additional sport so return GRE1.AA sport_id
                $sport_id = array_search($value[4], $ftLeagueSportsArr);
        
                if (!$sport_id) {
                  $leagueExploded = $value[4];
                  if (count($leagueExplodedArr) > 1) {
                    $leagueExploded = $leagueExplodedArr[0];
                  }
                  $sport_id = array_search($leagueExploded, $ftLeagueSportsArr);
        
                  if (!$sport_id) $sport_id = null;
                }
                // var_dump($sport_id);
                // echo PHP_EOL;
              }
              catch (\Exception $e) {
                die($e->getMessage());
              }
        
              $sportIdArr = (strpos($sportIds, ',')) ? explode(',', $sportIds) : array($sportIds);
              if (in_array($sport_id, $sportIdArr)) {
                $silentArr[] = array(
                  'fixtureId'     => $value[0],
                  'matchDateTime' => $matchDateTimeStr,
                  'homeTeamId'    => $value[9],
                  'awayTeamId'    => $value[10],
                  'homeTeamName'  => $value[20],
                  'awayTeamName'  => $value[21],
                  'league'        => $value[4],
                  'season'        => $value[7],
                  'sportId'       => var_export($sport_id, true)
                );
              }

              $sql .= "
              ('$value[0]',
              '$matchDateTimeStr',
              $value[9],
              $value[10],
              '$value[4]',
              '$value[7]',
              '$value[6]',
              '$value[5]',
              $value[11],
              $value[12],
              $value[13],
              $value[14],
              $value[15],
              $value[16],
              $value[17],
              $value[18],
              '$value[19]',
              $value[3],
              '$updated',
              '$dateTime',
              ".var_export($sport_id, true).") ";
              $sql .= ($fixtureCounter  === $i) ? " ON DUPLICATE KEY UPDATE sport_id = VALUES(sport_id), status = VALUES(status), minute = VALUES(minute), h_team = VALUES(h_team), a_team = VALUES(a_team), season = VALUES(season), ft1 = VALUES(ft1), ft2 = VALUES(ft2), ht1 = VALUES(ht1), ht2 = VALUES(ht2), et1 = VALUES(et1), et2 = VALUES(et2), pt1 = VALUES(pt1), pt2 = VALUES(pt2), events = VALUES(events), match_datetime = VALUES(match_datetime), week = VALUES(week), updated = VALUES(updated), updated_at = '$dateTime', sport_id = VALUES(sport_id);" : ",";
            } // foreach ($fixturesArr
        
            if ($hasRecords > 0 && !$silent) {
              try {
                $stmt = $em->getConnection()->prepare($sql);
                $stmt->execute();
                if (!isset($logger)) $logger = $this->parser;
                $logger->info("Updated $hasRecords FIXTURE records for $lan");
        
                // Mundial
                if ($hasMundialMatches) {
                  $mundialDomain = $em->getRepository('AppBundle:Domain')->findOneBy(array('name' => 'mundial2018'));
        
                  // $mundialDomain = $em->getRepository('AppBundle:Domain')->findOneById(9);
                  if ($mundialDomain) {
                    // $logger->info('Running helper '.$this->helper->getCurrentTimestampDate());
                    try {
                      $wp_db_host     = $mundialDomain->getDbHost();
                      $wp_db_port     = $mundialDomain->getDbPort();
                      $wp_db_name     = $mundialDomain->getDbName();
                      $wp_db_username = $mundialDomain->getDbUsername();
                      $wp_db_password = $mundialDomain->getDbPassword();
                
                      if (!$wp_db_port) {
                          $wp_db_port = 3306;
                      }
        
                      $mysqlStr = 'mysql:host='.$wp_db_host.';dbname='.$wp_db_name.';port='.$wp_db_port.';charset=UTF8';
                      // $logger->critical('connection '.$mysqlStr. '---'.$wp_db_username.'----'.$wp_db_password);
                      $mundialConnection = $this->cf->createConnection(
                        array('pdo' => new \PDO($mysqlStr, $wp_db_username, $wp_db_password))
                        // array('pdo' => new \PDO("mysql:host=$wp_db_host;dbname=tipst3r_db;port=3306","tipst3ruser","cjTJBtW2KeMrM6ZB"))
                      );
        
                      if ($mundialMatches) {
                        $mundialMatchesCounter = count($mundialMatches) - 1;
                        foreach ($mundialMatches as $i => $mm) {
                          // $sqlMundial .= "(".$mm['home_team_id'].", '".$mm['home_team_name']."', ".$mm['away_team_id'].", '".$mm['away_team_name']."', '".$mm['match_datetime']."', ".$mm['ht1'].", ".$mm['ht2'].", ".$mm['ft1'].", ".$mm['ft2'].", ".$mm['et1'].", ".$mm['et2'].", ".$mm['pt1'].", ".$mm['pt2'].", '".$mm['status']."', ".$mm['fixture_id'].", '".$mm['phase_name']."', ".$mm['phase_value'].", ".$mm['is_evaluated'].", '".$mm['created_at']."')";
                          $sqlMundial .= "(".$mm['home_team_id'].", '".$mm['home_team_name']."', ".$mm['away_team_id'].", '".$mm['away_team_name']."', '".$mm['match_datetime']."', ".$mm['ht1'].", ".$mm['ht2'].", ".$mm['ft1'].", ".$mm['ft2'].", ".$mm['et1'].", ".$mm['et2'].", ".$mm['pt1'].", ".$mm['pt2'].", '".$mm['status']."', ".$mm['fixture_id'].", '".$mm['phase_name']."', ".$mm['phase_value'].", '".$mm['created_at']."')";
                          $dateTime = $this->helper->getCurrentTimestampDate();
                          $sqlMundial .= ($mundialMatchesCounter === $i) ? " ON DUPLICATE KEY UPDATE `status` = VALUES(`status`), `home_team_id` = VALUES(`home_team_id`), `home_team_name` = VALUES(`home_team_name`), `away_team_id` = VALUES(`away_team_id`), `away_team_name` = VALUES(`away_team_name`), `ft1` = VALUES(`ft1`), `ft2` = VALUES(`ft2`), `ht1` = VALUES(`ht1`), `ht2` = VALUES(`ht2`), `et1` = VALUES(`et1`), `et2` = VALUES(`et2`), `pt1` = VALUES(`pt1`), `pt2` = VALUES(`pt2`), `match_datetime` = VALUES(`match_datetime`), `updated_at` = '$dateTime';" : ",";
                        }
                      }
                      $mundialStmt = $mundialConnection->prepare($sqlMundial);
                      $mundialStmt->execute();
                    }
                    catch (\Exception $err) {
                      $logger->critical('SUPER ERROR ' . $err->getMessage());
                    }
                  }
                }
              }
              catch (\Exception $e) {
                // $logger = $this->parser;
                if (!isset($logger)) $logger = $this->parser;
                $logger->critical('error here my man'.$e->getMessage());
                // $logger->critical($e->getMessage());
              }
        
              $em->clear();
            }
        
            if ($hasRecords > 0 && $silent) {
              return $silentArr;
            }
          }
        }
      }
    }

    $em->clear();
  }

  private function validateLeagueSeason($season)
  {
    $seasonValues = explode('-', $season);

    if (count($seasonValues) > 1) {
      $minYear = (int) $seasonValues[0];
      $maxYear = (int) $seasonValues[1];

      if ($minYear >= 2016 && $maxYear >= 2017) {
        return false;
      }
    } else {
      $minYear = (int) $seasonValues[0];
      if ($minYear >= 2016) {
        return false;
      }
    }

    return true;
  }

  public function checkFixtureIdExistsElseUpdate($em, $fixtureId, $matchDateTimeStr, $homeId, $awayId, $conn, $domain)
  {
      $stmt = $em->getConnection()->prepare("SELECT f_id FROM ft_fixture WHERE f_id = ?");
      $stmt->bindValue(1, $fixtureId);
      $stmt->execute();
      $existing = $stmt->fetchColumn();
      if (!$existing) {
          $fallbackStmt = $em->getConnection()->prepare("
                    SELECT f_id FROM ft_fixture 
                    WHERE match_datetime = ? AND h_team = ? AND a_team = ?
                ");
          $fallbackStmt->execute([$matchDateTimeStr, $homeId, $awayId]);
          $fallbackResult = $fallbackStmt->fetchColumn();
          if ($fallbackResult) {
              // Update fixture with new f_id and other values
              $em->getConnection()->update('ft_fixture', [
                  'f_id' => $fixtureId,
                  'updated_at' => (new \DateTime())->format('Y-m-d H:i:s'),
              ], ['f_id' => $fallbackResult]);
              return false;
          }
      }

      return true;
  }

    public function checkFixtureIdExistsElseUpdateOnWbsPreview($em, $fixtureId, $matchDateTimeStr, $homeId, $awayId, $conn, $domain)
    {
        try {
            $sqlUpdate = "
                UPDATE "."bet_wbs_previews SET
                fixture_id = '".$fixtureId."',
                updated_at = '".date('Y-m-d H:i:s', time())."'
                WHERE h_team_id = ".$homeId." AND a_team_id = ".$awayId." AND match_date_time = '".$matchDateTimeStr."'
                ";
            $stmt = $conn->prepare($sqlUpdate);
            $stmt->execute();
        }
        catch (\Exception $e){
            if (!isset($logger)) $logger = $this->parser;
            $logger->critical($e->getMessage());
            return false;
        }

        return true;
    }

  // private function curlGetContent($url)
  // {
  //   $ch = curl_init($url);
  //   curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
  //   curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
  //   curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
  //   curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
  //   $data = curl_exec($ch);
  //   curl_close($ch);
  //   return $data;
  // }
}
