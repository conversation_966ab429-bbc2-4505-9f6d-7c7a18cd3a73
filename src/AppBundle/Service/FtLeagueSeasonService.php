<?php

namespace AppBundle\Service;

use Symfony\Component\DomCrawler\Crawler;
use Psr\Log\LoggerInterface;

use AppBundle\Service\GlobalHelperService;

class FtLeagueSeasonService {

  protected $entityManager;
  protected $parser;

  public function __construct($entityManager, $parser) {
    $this->entityManager = $entityManager;
    $this->parser = $parser;

    $this->helper = new GlobalHelperService($this->entityManager, $this->parser);
  }

  public function batchInsert($username, $password, $lan = null, $fixturesAll = null, $translator, $options) {
    $em = $this->entityManager;

    $url = "http://xml.globalscore.com/xml.php?un=".$username."&pass=".$password."&sport=s&tp=leagueSeasons";

    $crawler = new Crawler();
    try {
      $crawler->addXmlContent(file_get_contents($url));
    }
    catch (\Exception $e){
      $logger = $this->parser;
      $logger->critical($e->getMessage());
    }

    $leagueSeasonsArr = $crawler->filterXPath('//leagueSeason')->each(function ($node, $i) {
      $arr = $node->extract(array('league', 'season', 'name'))[0];

      return $arr;
    });

    $sql = "insert into ft_league_season (l_id, season, name_en, created_at) VALUES ";

    $hasRecords = 0;
    foreach ($leagueSeasonsArr as $i => $value) {
      $hasRecords++;

      $dateTime = $this->helper->getCurrentTimestampDate();

      $sql .= " ('$value[0]', '$value[1]', '$value[2]', '$dateTime') ";
      $sql .= (count($leagueSeasonsArr) - 1  === $i) ? " ON DUPLICATE KEY UPDATE name_en = VALUES(name_en), updated_at = '$dateTime' ;" : ",";
    }

    if ($hasRecords > 0) {
      try {
        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute();
        $logger = $this->parser;
        $logger->info("Updated $hasRecords LEAGUE SEASON records for $lan");
      }
      catch (\Exception $e) {
        $logger = $this->parser;
        $logger->critical($e->getMessage());
      }
    }

    $em->clear();
  }
}
