<?php

namespace AppBundle\Service;

class TeamStatsService {

  private $entityManager;
  private $parser;
  private $cf;

  public function __construct($entityManager = null) {
    if (is_array($entityManager)) {
      $this->entityManager = $entityManager[0];
      $this->cf = $entityManager[1];
      $this->parser = $entityManager[2];
    }
  }

  public function initTeamStats($teamId, $lssId) {
    return 'lala';
  }

  public function calculateTeamStats($teamId, $lssId, $teamStats, $fixtureId = null) {
    $em     = $this->entityManager;

    // if ((3307 != $teamId) && (794 != $teamId)) return;

    // edw 8a prepei na trexei mono gia to finished ft_fixture OXI gia ola apo thn arxh
    $teamResults = $em->getRepository('AppBundle:FtFixture')->getTeamLeagueSeasonSportFinished($teamId, $lssId, $fixtureId);

    $tPlayed          = count($teamResults);
    $hPlayed          = $teamStats ? $teamStats->getHPlayed() : 0;
    $hWins            = $teamStats ? $teamStats->getHWins() : 0;
    $hWinsHt          = $teamStats ? $teamStats->getHWinsHt() : 0;
    $hDraws           = $teamStats ? $teamStats->getHDraws() : 0;
    $hDrawsHt         = $teamStats ? $teamStats->getHDrawsHt() : 0;
    $hLoses           = $teamStats ? $teamStats->getHLoses() : 0;
    $hLosesHt         = $teamStats ? $teamStats->getHLosesHt() : 0;
    $hGoalsFor        = $teamStats ? $teamStats->getHGoalsFor() : 0;
    $hGoalsAgainst    = $teamStats ? $teamStats->getHGoalsAgainst() : 0;
    $hNotScored       = $teamStats ? $teamStats->getHNotScored() : 0;
    $hNotConceded     = $teamStats ? $teamStats->getHNotConceded() : 0;
    $hOver151         = $teamStats ? $teamStats->getHOver151() : 0;
    $hOver152         = $teamStats ? $teamStats->getHOver152() : 0;
    $hOver15          = $teamStats ? $teamStats->getHOver15() : 0;
    $hOver251         = $teamStats ? $teamStats->getHOver251() : 0;
    $hOver252         = $teamStats ? $teamStats->getHOver252() : 0;
    $hOver25          = $teamStats ? $teamStats->getHOver25() : 0;
    $hOver351         = $teamStats ? $teamStats->getHOver351() : 0;
    $hOver352         = $teamStats ? $teamStats->getHOver352() : 0;
    $hOver35          = $teamStats ? $teamStats->getHOver35() : 0;
    $hUnder15         = $teamStats ? $teamStats->getHUnder15() : 0;
    $hUnder151        = $teamStats ? $teamStats->getHUnder151() : 0;
    $hUnder152        = $teamStats ? $teamStats->getHUnder152() : 0;
    $hUnder25         = $teamStats ? $teamStats->getHUnder25() : 0;
    $hUnder251        = $teamStats ? $teamStats->getHUnder251() : 0;
    $hUnder252        = $teamStats ? $teamStats->getHUnder252() : 0;
    $hUnder35         = $teamStats ? $teamStats->getHUnder35() : 0;
    $hUnder351        = $teamStats ? $teamStats->getHUnder351() : 0;
    $hUnder352        = $teamStats ? $teamStats->getHUnder352() : 0;
    $hGoalGoal        = $teamStats ? $teamStats->getHGoalGoal() : 0;
    $hNoGoal          = $teamStats ? $teamStats->getHNoGoal() : 0;
    $hGoals01         = $teamStats ? $teamStats->getHGoals01() : 0;
    $hGoals23         = $teamStats ? $teamStats->getHGoals23() : 0;
    $hGoals46         = $teamStats ? $teamStats->getHGoals46() : 0;
    $hGoals7          = $teamStats ? $teamStats->getHGoals7() : 0;
    $hWinWin          = $teamStats ? $teamStats->getHWinWin() : 0;
    $hDrawWin         = $teamStats ? $teamStats->getHDrawWin() : 0;
    $hLoseWin         = $teamStats ? $teamStats->getHLoseWin() : 0;
    $hWinDraw         = $teamStats ? $teamStats->getHWinDraw() : 0;
    $hDrawDraw        = $teamStats ? $teamStats->getHDrawDraw() : 0;
    $hLoseDraw        = $teamStats ? $teamStats->getHLoseDraw() : 0;
    $hWinLose         = $teamStats ? $teamStats->getHWinLose() : 0;
    $hDrawLose        = $teamStats ? $teamStats->getHDrawLose() : 0;
    $hLoseLose        = $teamStats ? $teamStats->getHLoseLose() : 0;

    $aPlayed          = $teamStats ? $teamStats->getAPlayed() : 0;
    $aWins            = $teamStats ? $teamStats->getAWins() : 0;
    $aWinsHt          = $teamStats ? $teamStats->getAWinsHt() : 0;
    $aDraws           = $teamStats ? $teamStats->getADraws() : 0;
    $aDrawsHt         = $teamStats ? $teamStats->getADrawsHt() : 0;
    $aLoses           = $teamStats ? $teamStats->getALoses() : 0;
    $aLosesHt         = $teamStats ? $teamStats->getALosesHt() : 0;
    $aGoalsFor        = $teamStats ? $teamStats->getAGoalsFor() : 0;
    $aGoalsAgainst    = $teamStats ? $teamStats->getAGoalsAgainst() : 0;
    $aNotScored       = $teamStats ? $teamStats->getANotScored() : 0;
    $aNotConceded     = $teamStats ? $teamStats->getANotConceded() : 0;
    $aOver151         = $teamStats ? $teamStats->getAOver151() : 0;
    $aOver152         = $teamStats ? $teamStats->getAOver152() : 0;
    $aOver15          = $teamStats ? $teamStats->getAOver15() : 0;
    $aOver251         = $teamStats ? $teamStats->getAOver251() : 0;
    $aOver252         = $teamStats ? $teamStats->getAOver252() : 0;
    $aOver25          = $teamStats ? $teamStats->getAOver25() : 0;
    $aOver351         = $teamStats ? $teamStats->getAOver351() : 0;
    $aOver352         = $teamStats ? $teamStats->getAOver352() : 0;
    $aOver35          = $teamStats ? $teamStats->getAOver35() : 0;
    $aUnder15         = $teamStats ? $teamStats->getAUnder15() : 0;
    $aUnder151        = $teamStats ? $teamStats->getAUnder151() : 0;
    $aUnder152        = $teamStats ? $teamStats->getAUnder152() : 0;
    $aUnder25         = $teamStats ? $teamStats->getAUnder25() : 0;
    $aUnder251        = $teamStats ? $teamStats->getAUnder251() : 0;
    $aUnder252        = $teamStats ? $teamStats->getAUnder252() : 0;
    $aUnder35         = $teamStats ? $teamStats->getAUnder35() : 0;
    $aUnder351        = $teamStats ? $teamStats->getAUnder351() : 0;
    $aUnder352        = $teamStats ? $teamStats->getAUnder352() : 0;
    $aGoalGoal        = $teamStats ? $teamStats->getAGoalGoal() : 0;
    $aNoGoal          = $teamStats ? $teamStats->getANoGoal() : 0;
    $aGoals01         = $teamStats ? $teamStats->getAGoals01() : 0;
    $aGoals23         = $teamStats ? $teamStats->getAGoals23() : 0;
    $aGoals46         = $teamStats ? $teamStats->getAGoals46() : 0;
    $aGoals7          = $teamStats ? $teamStats->getAGoals7() : 0;
    $aWinWin          = $teamStats ? $teamStats->getAWinWin() : 0;
    $aDrawWin         = $teamStats ? $teamStats->getADrawWin() : 0;
    $aLoseWin         = $teamStats ? $teamStats->getALoseWin() : 0;
    $aWinDraw         = $teamStats ? $teamStats->getAWinDraw() : 0;
    $aDrawDraw        = $teamStats ? $teamStats->getADrawDraw() : 0;
    $aLoseDraw        = $teamStats ? $teamStats->getALoseDraw() : 0;
    $aWinLose         = $teamStats ? $teamStats->getAWinLose() : 0;
    $aDrawLose        = $teamStats ? $teamStats->getADrawLose() : 0;
    $aLoseLose        = $teamStats ? $teamStats->getALoseLose() : 0;

    $hWinStreak       = $teamStats ? $teamStats->getHWinStreak() : 0;
    $hDrawStreak      = $teamStats ? $teamStats->getHDrawStreak() : 0;
    $hLoseStreak      = $teamStats ? $teamStats->getHLoseStreak() : 0;
    $hNoWinStreak     = $teamStats ? $teamStats->getHNoWinStreak() : 0;
    $hNoDrawStreak    = $teamStats ? $teamStats->getHNoDrawStreak() : 0;
    $hNoLoseStreak    = $teamStats ? $teamStats->getHNoLoseStreak() : 0;
    $hGoalsForStreak  = $teamStats ? $teamStats->getHGoalsForStreak() : 0;
    $hNotScoredStreak = $teamStats ? $teamStats->getHNotScoredStreak() : 0;
    $hGoalsAgainstStreak  = $teamStats ? $teamStats->getHGoalsAgainstStreak() : 0;
    $hNotConcededStreak   = $teamStats ? $teamStats->getHNotConcededStreak() : 0;
    $hOver25Streak    = $teamStats ? $teamStats->getHOver25Streak() : 0;
    $hUnder25Streak   = $teamStats ? $teamStats->getHUnder25Streak() : 0;
    $hGoalGoalStreak  = $teamStats ? $teamStats->getHGoalGoalStreak() : 0;
    $hNoGoalStreak    = $teamStats ? $teamStats->getHNoGoalStreak() : 0;

    $hGoalsMinute015Conceded  = $teamStats ? $teamStats->getHGoalsMinute015Conceded() : 0;
    $hGoalsMinute015          = $teamStats ? $teamStats->getHGoalsMinute015() : 0;
    $hGoalsMinute1630Conceded = $teamStats ? $teamStats->getHGoalsMinute1630Conceded() : 0;
    $hGoalsMinute1630         = $teamStats ? $teamStats->getHGoalsMinute1630() : 0;
    $hGoalsMinute3145Conceded = $teamStats ? $teamStats->getHGoalsMinute3145Conceded() : 0;
    $hGoalsMinute3145         = $teamStats ? $teamStats->getHGoalsMinute3145() : 0;
    $hGoalsMinute4660Conceded = $teamStats ? $teamStats->getHGoalsMinute4660Conceded() : 0;
    $hGoalsMinute4660         = $teamStats ? $teamStats->getHGoalsMinute4660() : 0;
    $hGoalsMinute6175Conceded = $teamStats ? $teamStats->getHGoalsMinute6175Conceded() : 0;
    $hGoalsMinute6175         = $teamStats ? $teamStats->getHGoalsMinute6175() : 0;
    $hGoalsMinute7690Conceded = $teamStats ? $teamStats->getHGoalsMinute7690Conceded() : 0;
    $hGoalsMinute7690         = $teamStats ? $teamStats->getHGoalsMinute7690() : 0;

    $aWinStreak       = $teamStats ? $teamStats->getAWinStreak() : 0;
    $aDrawStreak      = $teamStats ? $teamStats->getADrawStreak() : 0;
    $aLoseStreak      = $teamStats ? $teamStats->getALoseStreak() : 0;
    $aNoWinStreak     = $teamStats ? $teamStats->getANoWinStreak() : 0;
    $aNoDrawStreak    = $teamStats ? $teamStats->getANoDrawStreak() : 0;
    $aNoLoseStreak    = $teamStats ? $teamStats->getANoLoseStreak() : 0;
    $aGoalsForStreak  = $teamStats ? $teamStats->getAGoalsForStreak() : 0;
    $aNotScoredStreak = $teamStats ? $teamStats->getANotScoredStreak() : 0;
    $aGoalsAgainstStreak  = $teamStats ? $teamStats->getAGoalsAgainstStreak() : 0;
    $aNotConcededStreak   = $teamStats ? $teamStats->getANotConcededStreak() : 0;
    $aOver25Streak    = $teamStats ? $teamStats->getAOver25Streak() : 0;
    $aUnder25Streak   = $teamStats ? $teamStats->getAUnder25Streak() : 0;
    $aGoalGoalStreak  = $teamStats ? $teamStats->getAGoalGoalStreak() : 0;
    $aNoGoalStreak    = $teamStats ? $teamStats->getANoGoalStreak() : 0;

    $aGoalsMinute015Conceded  = $teamStats ? $teamStats->getAGoalsMinute015Conceded() : 0;
    $aGoalsMinute015          = $teamStats ? $teamStats->getAGoalsMinute015() : 0;
    $aGoalsMinute1630Conceded = $teamStats ? $teamStats->getAGoalsMinute1630Conceded() : 0;
    $aGoalsMinute1630         = $teamStats ? $teamStats->getAGoalsMinute1630() : 0;
    $aGoalsMinute3145Conceded = $teamStats ? $teamStats->getAGoalsMinute3145Conceded() : 0;
    $aGoalsMinute3145         = $teamStats ? $teamStats->getAGoalsMinute3145() : 0;
    $aGoalsMinute4660Conceded = $teamStats ? $teamStats->getAGoalsMinute4660Conceded() : 0;
    $aGoalsMinute4660         = $teamStats ? $teamStats->getAGoalsMinute4660() : 0;
    $aGoalsMinute6175Conceded = $teamStats ? $teamStats->getAGoalsMinute6175Conceded() : 0;
    $aGoalsMinute6175         = $teamStats ? $teamStats->getAGoalsMinute6175() : 0;
    $aGoalsMinute7690Conceded = $teamStats ? $teamStats->getAGoalsMinute7690Conceded() : 0;
    $aGoalsMinute7690         = $teamStats ? $teamStats->getAGoalsMinute7690() : 0;

    $tYellowCards     = $teamStats ? $teamStats->getTYellowCards() : 0;
    $tRedCards        = $teamStats ? $teamStats->getTRedCards() : 0;

    $tWinStreak       = $teamStats ? $teamStats->getTWinStreak() : 0;
    $tDrawStreak      = $teamStats ? $teamStats->getTDrawStreak() : 0;
    $tLoseStreak      = $teamStats ? $teamStats->getTLoseStreak() : 0;
    $tNoWinStreak     = $teamStats ? $teamStats->getTNoWinStreak() : 0;
    $tNoDrawStreak    = $teamStats ? $teamStats->getTNoDrawStreak() : 0;
    $tNoLoseStreak    = $teamStats ? $teamStats->getTNoLoseStreak() : 0;
    $tGoalsForStreak  = $teamStats ? $teamStats->getTGoalsForStreak() : 0;
    $tNotScoredStreak = $teamStats ? $teamStats->getTNotScoredStreak() : 0;
    $tGoalsAgainstStreak = $teamStats ? $teamStats->getTGoalsAgainstStreak() : 0;
    $tNotConcededStreak = $teamStats ? $teamStats->getTNotConcededStreak() : 0;
    $tOver25Streak    = $teamStats ? $teamStats->getTOver25Streak() : 0;
    $tUnder25Streak   = $teamStats ? $teamStats->getTUnder25Streak() : 0;
    $tGoalGoalStreak  = $teamStats ? $teamStats->getTGoalGoalStreak() : 0;
    $tNoGoalStreak    = $teamStats ? $teamStats->getTNoGoalStreak() : 0;

    foreach($teamResults as $result) {
      $teamStats = $em->getRepository('AppBundle:TeamStats')->findOneBy(array('leagueSeasonId' => $lssId, 'teamId' => $teamId));

      (1 === (int) $result['is_home']) ? $hPlayed++ : $aPlayed++;

      // home
      $hWins          = (int) $hWins + (int) $result['h_win'];
      $hWinsHt        = (int) $hWinsHt + (int) $result['h_win_ht'];
      $hDraws         = (int) $hDraws + (int) $result['h_draw'];
      $hDrawsHt       = (int) $hDrawsHt + (int) $result['h_draw_ht'];
      $hLoses         = (int) $hLoses + (int) $result['h_lose'];
      $hLosesHt       = (int) $hLosesHt + (int) $result['h_lose_ht'];
      $hGoalsFor      = (int) $hGoalsFor + (int) $result['h_goals_for'];
      $hGoalsAgainst  = (int) $hGoalsAgainst + (int) $result['h_goals_against'];
      $hNotScored     = (int) $hNotScored + (int) $result['h_not_scored'];
      $hNotConceded   = (int) $hNotConceded + (int) $result['h_not_conceded'];
      $hOver151       = (int) $hOver151 + (int) $result['h_over_15_1'];
      $hOver152       = (int) $hOver152 + (int) $result['h_over_15_2'];
      $hOver15        = (int) $hOver15 + (int) $result['h_over_15'];
      $hOver251       = (int) $hOver251 + (int) $result['h_over_25_1'];
      $hOver252       = (int) $hOver252 + (int) $result['h_over_25_2'];
      $hOver25        = (int) $hOver25 + (int) $result['h_over_25'];
      $hOver351       = (int) $hOver351 + (int) $result['h_over_35_1'];
      $hOver352       = (int) $hOver352 + (int) $result['h_over_35_2'];
      $hOver35        = (int) $hOver35 + (int) $result['h_over_35'];
      $hUnder151      = (int) $hUnder151 + (int) $result['h_under_15_1'];
      $hUnder152      = (int) $hUnder152 + (int) $result['h_under_15_2'];
      $hUnder15       = (int) $hUnder15 + (int) $result['h_under_15'];
      $hUnder251      = (int) $hUnder251 + (int) $result['h_under_25_1'];
      $hUnder252      = (int) $hUnder252 + (int) $result['h_under_25_2'];
      $hUnder25       = (int) $hUnder25 + (int) $result['h_under_25'];
      $hUnder351      = (int) $hUnder351 + (int) $result['h_under_35_1'];
      $hUnder352      = (int) $hUnder352 + (int) $result['h_under_35_2'];
      $hUnder35       = (int) $hUnder35 + (int) $result['h_under_35'];
      $hGoalGoal      = (int) $hGoalGoal + (int) $result['h_goal_goal'];
      $hNoGoal        = (int) $hNoGoal + (int) $result['h_no_goal'];
      $hGoals01       = (int) $hGoals01 + (int) $result['h_goals_01'];
      $hGoals23       = (int) $hGoals23 + (int) $result['h_goals_23'];
      $hGoals46       = (int) $hGoals46 + (int) $result['h_goals_46'];
      $hGoals7        = (int) $hGoals7 + (int) $result['h_goals_7'];

      // away
      $aWins          = (int) $aWins + (int) $result['a_win'];
      $aWinsHt        = (int) $aWinsHt + (int) $result['a_win_ht'];
      $aDraws         = (int) $aDraws + (int) $result['a_draw'];
      $aDrawsHt       = (int) $aDrawsHt + (int) $result['a_draw_ht'];
      $aLoses         = (int) $aLoses + (int) $result['a_lose'];
      $aLosesHt       = (int) $aLosesHt + (int) $result['a_lose_ht'];
      $aGoalsFor      = (int) $aGoalsFor + (int) $result['a_goals_for'];
      $aGoalsAgainst  = (int) $aGoalsAgainst + (int) $result['a_goals_against'];
      $aNotScored     = (int) $aNotScored + (int) $result['a_not_scored'];
      $aNotConceded   = (int) $aNotConceded + (int) $result['a_not_conceded'];
      $aOver151       = (int) $aOver151 + (int) $result['a_over_15_1'];
      $aOver152       = (int) $aOver152 + (int) $result['a_over_15_2'];
      $aOver15        = (int) $aOver15 + (int) $result['a_over_15'];
      $aOver251       = (int) $aOver251 + (int) $result['a_over_25_1'];
      $aOver252       = (int) $aOver252 + (int) $result['a_over_25_2'];
      $aOver25        = (int) $aOver25 + (int) $result['a_over_25'];
      $aOver351       = (int) $aOver351 + (int) $result['a_over_35_1'];
      $aOver352       = (int) $aOver352 + (int) $result['a_over_35_2'];
      $aOver35        = (int) $aOver35 + (int) $result['a_over_35'];
      $aUnder151      = (int) $aUnder151 + (int) $result['a_under_15_1'];
      $aUnder152      = (int) $aUnder152 + (int) $result['a_under_15_2'];
      $aUnder15       = (int) $aUnder15 + (int) $result['a_under_15'];
      $aUnder251      = (int) $aUnder251 + (int) $result['a_under_25_1'];
      $aUnder252      = (int) $aUnder252 + (int) $result['a_under_25_2'];
      $aUnder25       = (int) $aUnder25 + (int) $result['a_under_25'];
      $aUnder351      = (int) $aUnder351 + (int) $result['a_under_35_1'];
      $aUnder352      = (int) $aUnder352 + (int) $result['a_under_35_2'];
      $aUnder35       = (int) $aUnder35 + (int) $result['a_under_35'];
      $aGoalGoal      = (int) $aGoalGoal + (int) $result['a_goal_goal'];
      $aNoGoal        = (int) $aNoGoal + (int) $result['a_no_goal'];
      $aGoals01       = (int) $aGoals01 + (int) $result['a_goals_01'];
      $aGoals23       = (int) $aGoals23 + (int) $result['a_goals_23'];
      $aGoals46       = (int) $aGoals46 + (int) $result['a_goals_46'];
      $aGoals7        = (int) $aGoals7 + (int) $result['a_goals_7'];

      ($result['h_win'] && $result['h_win_ht']) ? $hWinWin++ : '';
      ($result['h_win'] && $result['h_draw_ht']) ? $hDrawWin++ : '';
      ($result['h_win'] && $result['h_lose_ht']) ? $hLoseWin++ : '';
      ($result['h_draw'] && $result['h_win_ht']) ? $hWinDraw++ : '';
      ($result['h_draw'] && $result['h_draw_ht']) ? $hDrawDraw++ : '';
      ($result['h_draw'] && $result['h_lose_ht']) ? $hLoseDraw++ : '';
      ($result['h_lose'] && $result['h_win_ht']) ? $hWinLose++ : '';
      ($result['h_lose'] && $result['h_draw_ht']) ? $hDrawLose++ : '';
      ($result['h_lose'] && $result['h_lose_ht']) ? $hLoseLose++ : '';

      ($result['a_win'] && $result['a_win_ht']) ? $aWinWin++ : '';
      ($result['a_win'] && $result['a_draw_ht']) ? $aDrawWin++ : '';
      ($result['a_win'] && $result['a_lose_ht']) ? $aLoseWin++ : '';
      ($result['a_draw'] && $result['a_win_ht']) ? $aWinDraw++ : '';
      ($result['a_draw'] && $result['a_draw_ht']) ? $aDrawDraw++ : '';
      ($result['a_draw'] && $result['a_lose_ht']) ? $aLoseDraw++ : '';
      ($result['a_lose'] && $result['a_win_ht']) ? $aWinLose++ : '';
      ($result['a_lose'] && $result['a_draw_ht']) ? $aDrawLose++ : '';
      ($result['a_lose'] && $result['a_lose_ht']) ? $aLoseLose++ : '';

      // streaks
      if (1 === (int) $result['is_home']) {
        if (1 === (int) $result['h_win']) {
          $hWinStreak++;
          $hDrawStreak  = 0;
          $hLoseStreak  = 0;

          $hNoWinStreak = 0;
          $hNoDrawStreak++;
          $hNoLoseStreak++;

          $tWinStreak++;
          $tDrawStreak = 0;
          $tLoseStreak = 0;

          $tNoWinStreak = 0;
          $tNoDrawStreak++;
          $tNoLoseStreak++;
        }
        elseif (1 === (int) $result['h_draw']) {
          $hWinStreak   = 0;
          $hDrawStreak++;
          $hLoseStreak  = 0;

          $hNoWinStreak++;
          $hNoDrawStreak = 0;
          $hNoLoseStreak++;

          $tWinStreak  = 0;
          $tDrawStreak++;
          $tLoseStreak = 0;

          $tNoWinStreak++;
          $tNoDrawStreak = 0;
          $tNoLoseStreak++;
        }
        elseif (1 === (int) $result['h_lose']) {
          $hWinStreak   = 0;
          $hDrawStreak  = 0;
          $hLoseStreak++;

          $hNoWinStreak++;
          $hNoDrawStreak++;
          $hNoLoseStreak = 0;

          $tWinStreak  = 0;
          $tDrawStreak = 0;
          $tLoseStreak++;

          $tNoWinStreak++;
          $tNoDrawStreak++;
          $tNoLoseStreak = 0;
        }

        // goals_for is a number, not a boolean
        if (1 <= (int) $result['h_goals_for']) {
          $hGoalsForStreak++;
          $hNotScoredStreak = 0;

          $tGoalsForStreak++;
          $tNotScoredStreak = 0;
        }

        // not_scored is a boolean with values 0/1
        if (1 === (int) $result['h_not_scored']) {
          $hGoalsForStreak = 0;
          $hNotScoredStreak++;

          $tGoalsForStreak = 0;
          $tNotScoredStreak++;
        }

        if (1 <= (int) $result['h_goals_against']) {
          $hGoalsAgainstStreak++;
          $hNotConcededStreak = 0;

          $tGoalsAgainstStreak++;
          $tNotConcededStreak = 0;
        }

        if (1 === (int) $result['h_not_conceded']) {
          $hGoalsAgainstStreak = 0;
          $hNotConcededStreak++;

          $tGoalsAgainstStreak = 0;
          $tNotConcededStreak++;
        }

        if (1 === (int) $result['h_over_25']) {
          $hOver25Streak++;
          $hUnder25Streak = 0;

          $tOver25Streak++;
          $tUnder25Streak = 0;
        }

        if (1 === (int) $result['h_under_25']) {
          $hOver25Streak = 0;
          $hUnder25Streak++;

          $tOver25Streak = 0;
          $tUnder25Streak++;
        }

        if (1 === (int) $result['h_goal_goal']) {
          $hGoalGoalStreak++;
          $hNoGoalStreak = 0;

          $tGoalGoalStreak++;
          $tNoGoalStreak = 0;
        }

        if (1 === (int) $result['h_no_goal']) {
          $hGoalGoalStreak = 0;
          $hNoGoalStreak++;

          $tGoalGoalStreak = 0;
          $tNoGoalStreak++;
        }

      } // if $results['is_home']
      // away match
      else {
        if (1 === (int) $result['a_win']) {
          $aWinStreak++;
          $aDrawStreak = 0;
          $aLoseStreak = 0;

          $aNoWinStreak = 0;
          $aNoDrawStreak++;
          $aNoLoseStreak++;

          $tWinStreak++;
          $tDrawStreak = 0;
          $tLoseStreak = 0;

          $tNoWinStreak = 0;
          $tNoDrawStreak++;
          $tNoLoseStreak++;
        }
        elseif (1 === (int) $result['a_draw']) {
          $aWinStreak = 0;
          $aDrawStreak++;
          $aLoseStreak  = 0;

          $aNoWinStreak++;
          $aNoDrawStreak = 0;
          $aNoLoseStreak++;

          $tWinStreak  = 0;
          $tDrawStreak++;
          $tLoseStreak = 0;

          $tNoWinStreak++;
          $tNoDrawStreak = 0;
          $tNoLoseStreak++;
        }
        elseif (1 === (int) $result['a_lose']) {
          $aWinStreak = 0;
          $aDrawStreak = 0;
          $aLoseStreak++;

          $aNoWinStreak++;
          $aNoDrawStreak++;
          $aNoLoseStreak = 0;

          $tWinStreak  = 0;
          $tDrawStreak = 0;
          $tLoseStreak++;

          $tNoWinStreak++;
          $tNoDrawStreak++;
          $tNoLoseStreak = 0;
        }

        if (1 <= (int) $result['a_goals_for']) {
          $aGoalsForStreak++;
          $aNotScoredStreak = 0;

          $tGoalsForStreak++;
          $tNotScoredStreak = 0;
        }

        // not_scored is a boolean with values 0/1
        if (1 === (int) $result['a_not_scored']) {
          $aGoalsForStreak = 0;
          $aNotScoredStreak++;

          $tGoalsForStreak = 0;
          $tNotScoredStreak++;
        }

        if (1 <= (int) $result['a_goals_against']) {
          $aGoalsAgainstStreak++;
          $aNotConcededStreak = 0;

          $tGoalsAgainstStreak++;
          $tNotConcededStreak = 0;
        }

        if (1 === (int) $result['a_not_conceded']) {
          $aGoalsAgainstStreak = 0;
          $aNotConcededStreak++;

          $tGoalsAgainstStreak = 0;
          $tNotConcededStreak++;
        }

        if (1 === (int) $result['a_over_25']) {
          $aOver25Streak++;
          $aUnder25Streak = 0;

          $tOver25Streak++;
          $tUnder25Streak = 0;
        }

        if (1 === (int) $result['a_under_25']) {
          $aOver25Streak = 0;
          $aUnder25Streak++;

          $tOver25Streak = 0;
          $tUnder25Streak++;
        }

        if (1 === (int) $result['a_goal_goal']) {
          $aGoalGoalStreak++;
          $aNoGoalStreak = 0;

          $tGoalGoalStreak++;
          $tNoGoalStreak = 0;
        }

        if (1 === (int) $result['a_no_goal']) {
          $aGoalGoalStreak = 0;
          $aNoGoalStreak++;

          $tGoalGoalStreak = 0;
          $tNoGoalStreak++;
        }
      } // else away match


      $html = explode('##', $result['events']);

      for ($i = 0; $i <= count($html) - 1; $i++) {
        // Parse event
        if ($i % 4 == 0) {
          $playerName = $html[$i];
        }
        elseif ($i % 4 == 1) {
          $eventType = $html[$i];
        }
        elseif ($i % 4 == 2) {
          $team = $html[$i];
        }
        elseif ($i % 4 == 3) {
          $minute = (int) $html[$i];
          // Calculate
          if ($eventType == 'goal' || $eventType == 'og' || $eventType == 'pen') {
            if ($team == 1) {
              if ($minute <= 15) {
                if (1 === (int) $result['is_home']) {
                  $hGoalsMinute015++;
                }
                else {
                  $hGoalsMinute015Conceded++;
                }
              }
               elseif ($minute >= 16 && $minute <= 30) {
                if (1 === (int) $result['is_home']) {
                  $hGoalsMinute1630++;
                }
                else {
                  $hGoalsMinute1630Conceded++;
                }
              }
               elseif ($minute >= 31 && $minute <= 45) {
                if (1 === (int) $result['is_home']) {
                  $hGoalsMinute3145++;
                }
                else {
                  $hGoalsMinute3145Conceded++;
                }
              }
               elseif ($minute >= 46 && $minute <= 60) {
                if (1 === (int) $result['is_home']) {
                  $hGoalsMinute4660++;
                }
                else {
                  $hGoalsMinute4660Conceded++;
                }
              }
               elseif ($minute >= 61 && $minute <= 75) {
                if (1 === (int) $result['is_home']) {
                  $hGoalsMinute6175++;
                }
                else {
                  $hGoalsMinute6175Conceded++;
                }
              }
               elseif ($minute >= 76) {
                if (1 === (int) $result['is_home']) {
                  $hGoalsMinute7690++;
                }
                else {
                  $hGoalsMinute7690Conceded++;
                }
              }
            }
            else {
              if ($minute <= 15) {
                if (1 === (int) $result['is_home']) {
                  $aGoalsMinute015Conceded++;
                }
                else {
                  $aGoalsMinute015++;
                }
              }
               elseif ($minute >= 16 && $minute <= 30) {
                if (1 === (int) $result['is_home']) {
                  $aGoalsMinute1630Conceded++;
                }
                else {
                  $aGoalsMinute1630++;
                }
              }
               elseif ($minute >= 31 && $minute <= 45) {
                if (1 === (int) $result['is_home']) {
                  $aGoalsMinute3145Conceded++;
                }
                else {
                  $aGoalsMinute3145++;
                }
              }
               elseif ($minute >= 46 && $minute <= 60) {
                if (1 === (int) $result['is_home']) {
                  $aGoalsMinute4660Conceded++;
                }
                else {
                  $aGoalsMinute4660++;
                }
              }
               elseif ($minute >= 61 && $minute <= 75) {
                if (1 === (int) $result['is_home']) {
                  $aGoalsMinute6175Conceded++;
                }
                else {
                  $aGoalsMinute6175++;
                }
              }
               elseif ($minute >= 76) {
                if (1 === (int) $result['is_home']) {
                  $aGoalsMinute7690Conceded++;
                }
                else {
                  $aGoalsMinute7690++;
                }
              }
            }
          } // if eventType goal og pen
          elseif ($eventType === 'yc') {
            if (1 === (int) $team) {
              if (1 === (int) $result['is_home']) {
                $tYellowCards++;
              }
            }
            elseif (2 === (int) $team) {
              if (0 === (int) $result['is_home']) {
                $tYellowCards++;
              }
            }
          }
          elseif ($eventType === 'rc') {
            if (1 === (int) $team) {
              if (1 === (int) $result['is_home']) {
                $tRedCards++;
              }
            }
            elseif (2 === (int) $team) {
              if (0 === (int) $result['is_home']) {
                $tRedCards++;
              }
            }
          }
          elseif ($eventType === 'yelc2') {
            if (1 === (int) $team) {
              if (1 === (int) $result['is_home']) {
                $tYellowCards++;
                $tRedCards++;
              }
            }
            elseif (2 === (int) $team) {
              if (0 === (int) $result['is_home']) {
                $tYellowCards++;
                $tRedCards++;
              }
            }
          }

        } // $i % 3
      } // for $events $html

      try {
        // home
        $teamStats->setHPlayed($hPlayed);
        $teamStats->setHWins($hWins);
        $teamStats->setHWinsHt($hWinsHt);
        $teamStats->setHWinStreak($hWinStreak);
        $teamStats->setHNoWinStreak($hNoWinStreak);
        $teamStats->setHDraws($hDraws);
        $teamStats->setHDrawsHt($hDrawsHt);
        $teamStats->setHDrawStreak($hDrawStreak);
        $teamStats->setHNoDrawStreak($hNoDrawStreak);
        $teamStats->setHLoses($hLoses);
        $teamStats->setHLosesHt($hLosesHt);
        $teamStats->setHLoseStreak($hLoseStreak);
        $teamStats->setHNoLoseStreak($hNoLoseStreak);
        $teamStats->setHGoalsForStreak($hGoalsForStreak);
        $teamStats->setHGoalsAgainstStreak($hGoalsAgainstStreak);
        $teamStats->setHNotScoredStreak($hNotScoredStreak);
        $teamStats->setHNotConcededStreak($hNotConcededStreak);
        $teamStats->setHGoalsFor($hGoalsFor);
        $teamStats->setHGoalsAgainst($hGoalsAgainst);
        $teamStats->setHNotScored($hNotScored);
        $teamStats->setHNotConceded($hNotConceded);
        $teamStats->setHOver15($hOver15);
        $teamStats->setHOver151($hOver151);
        $teamStats->setHOver152($hOver152);
        $teamStats->setHUnder15($hUnder15);
        $teamStats->setHUnder151($hUnder151);
        $teamStats->setHUnder152($hUnder152);
        $teamStats->setHOver25($hOver25);
        $teamStats->setHOver251($hOver251);
        $teamStats->setHOver252($hOver252);
        $teamStats->setHUnder25($hUnder25);
        $teamStats->setHUnder251($hUnder251);
        $teamStats->setHUnder252($hUnder252);
        $teamStats->setHOver35($hOver35);
        $teamStats->setHOver351($hOver351);
        $teamStats->setHOver352($hOver352);
        $teamStats->setHUnder35($hUnder35);
        $teamStats->setHUnder351($hUnder351);
        $teamStats->setHUnder352($hUnder352);
        $teamStats->setHOver25Streak($hOver25Streak);
        $teamStats->setHUnder25Streak($hUnder25Streak);
        $teamStats->setHGoalGoal($hGoalGoal);
        $teamStats->setHGoalGoalStreak($hGoalGoalStreak);
        $teamStats->setHNoGoal($hNoGoal);
        $teamStats->setHNoGoalStreak($hNoGoalStreak);
        $teamStats->setHGoals01($hGoals01);
        $teamStats->setHGoals23($hGoals23);
        $teamStats->setHGoals46($hGoals46);
        $teamStats->setHGoals7($hGoals7);
        $teamStats->setHGoalsMinute015($hGoalsMinute015);
        $teamStats->setHGoalsMinute1630($hGoalsMinute1630);
        $teamStats->setHGoalsMinute3145($hGoalsMinute3145);
        $teamStats->setHGoalsMinute4660($hGoalsMinute4660);
        $teamStats->setHGoalsMinute6175($hGoalsMinute6175);
        $teamStats->setHGoalsMinute7690($hGoalsMinute7690);
        $teamStats->setHGoalsMinute015Conceded($hGoalsMinute015Conceded);
        $teamStats->setHGoalsMinute1630Conceded($hGoalsMinute1630Conceded);
        $teamStats->setHGoalsMinute3145Conceded($hGoalsMinute3145Conceded);
        $teamStats->setHGoalsMinute4660Conceded($hGoalsMinute4660Conceded);
        $teamStats->setHGoalsMinute6175Conceded($hGoalsMinute6175Conceded);
        $teamStats->setHGoalsMinute7690Conceded($hGoalsMinute7690Conceded);
        $teamStats->setHWinWin($hWinWin);
        $teamStats->setHDrawWin($hDrawWin);
        $teamStats->setHLoseWin($hLoseWin);
        $teamStats->setHWinDraw($hWinDraw);
        $teamStats->setHDrawDraw($hDrawDraw);
        $teamStats->setHLoseDraw($hLoseDraw);
        $teamStats->setHWinLose($hWinLose);
        $teamStats->setHDrawLose($hDrawLose);
        $teamStats->setHLoseLose($hLoseLose);

        // away
        $teamStats->setAPlayed($aPlayed);
        $teamStats->setAWins($aWins);
        $teamStats->setAWinsHt($aWinsHt);
        $teamStats->setAWinStreak($aWinStreak);
        $teamStats->setANoWinStreak($aNoWinStreak);
        $teamStats->setADraws($aDraws);
        $teamStats->setADrawsHt($aDrawsHt);
        $teamStats->setADrawStreak($aDrawStreak);
        $teamStats->setANoDrawStreak($aNoDrawStreak);
        $teamStats->setALoses($aLoses);
        $teamStats->setALosesHt($aLosesHt);
        $teamStats->setALoseStreak($aLoseStreak);
        $teamStats->setANoLoseStreak($aNoLoseStreak);
        $teamStats->setAGoalsForStreak($aGoalsForStreak);
        $teamStats->setAGoalsAgainstStreak($aGoalsAgainstStreak);
        $teamStats->setANotScoredStreak($aNotScoredStreak);
        $teamStats->setANotConcededStreak($aNotConcededStreak);
        $teamStats->setAGoalsFor($aGoalsFor);
        $teamStats->setAGoalsAgainst($aGoalsAgainst);
        $teamStats->setANotScored($aNotScored);
        $teamStats->setANotConceded($aNotConceded);
        $teamStats->setAOver15($aOver15);
        $teamStats->setAOver151($aOver151);
        $teamStats->setAOver152($aOver152);
        $teamStats->setAUnder15($aUnder15);
        $teamStats->setAUnder151($aUnder151);
        $teamStats->setAUnder152($aUnder152);
        $teamStats->setAOver25($aOver25);
        $teamStats->setAOver251($aOver251);
        $teamStats->setAOver252($aOver252);
        $teamStats->setAUnder25($aUnder25);
        $teamStats->setAUnder251($aUnder251);
        $teamStats->setAUnder252($aUnder252);
        $teamStats->setAOver35($aOver35);
        $teamStats->setAOver351($aOver351);
        $teamStats->setAOver352($aOver352);
        $teamStats->setAUnder35($aUnder35);
        $teamStats->setAUnder351($aUnder351);
        $teamStats->setAUnder352($aUnder352);
        $teamStats->setAOver25Streak($aOver25Streak);
        $teamStats->setAUnder25Streak($aUnder25Streak);
        $teamStats->setAGoalGoal($aGoalGoal);
        $teamStats->setAGoalGoalStreak($aGoalGoalStreak);
        $teamStats->setANoGoal($aNoGoal);
        $teamStats->setANoGoalStreak($aNoGoalStreak);
        $teamStats->setAGoals01($aGoals01);
        $teamStats->setAGoals23($aGoals23);
        $teamStats->setAGoals46($aGoals46);
        $teamStats->setAGoals7($aGoals7);
        $teamStats->setAGoalsMinute015($aGoalsMinute015);
        $teamStats->setAGoalsMinute1630($aGoalsMinute1630);
        $teamStats->setAGoalsMinute3145($aGoalsMinute3145);
        $teamStats->setAGoalsMinute4660($aGoalsMinute4660);
        $teamStats->setAGoalsMinute6175($aGoalsMinute6175);
        $teamStats->setAGoalsMinute7690($aGoalsMinute7690);
        $teamStats->setAGoalsMinute015Conceded($aGoalsMinute015Conceded);
        $teamStats->setAGoalsMinute1630Conceded($aGoalsMinute1630Conceded);
        $teamStats->setAGoalsMinute3145Conceded($aGoalsMinute3145Conceded);
        $teamStats->setAGoalsMinute4660Conceded($aGoalsMinute4660Conceded);
        $teamStats->setAGoalsMinute6175Conceded($aGoalsMinute6175Conceded);
        $teamStats->setAGoalsMinute7690Conceded($aGoalsMinute7690Conceded);
        $teamStats->setAWinWin($aWinWin);
        $teamStats->setADrawWin($aDrawWin);
        $teamStats->setALoseWin($aLoseWin);
        $teamStats->setAWinDraw($aWinDraw);
        $teamStats->setADrawDraw($aDrawDraw);
        $teamStats->setALoseDraw($aLoseDraw);
        $teamStats->setAWinLose($aWinLose);
        $teamStats->setADrawLose($aDrawLose);
        $teamStats->setALoseLose($aLoseLose);

        // total
        $teamStats->setTPlayed($tPlayed);
        $teamStats->setTYellowCards($tYellowCards);
        $teamStats->setTRedCards($tRedCards);
        $teamStats->setTWinStreak($tWinStreak);
        $teamStats->setTDrawStreak($tDrawStreak);
        $teamStats->setTLoseStreak($tLoseStreak);
        $teamStats->setTNoWinStreak($tNoWinStreak);
        $teamStats->setTNoDrawStreak($tNoDrawStreak);
        $teamStats->setTNoLoseStreak($tNoLoseStreak);
        $teamStats->setTGoalsForStreak($tGoalsForStreak);
        $teamStats->setTNotScoredStreak($tNotScoredStreak);
        $teamStats->setTGoalsAgainstStreak($tGoalsAgainstStreak);
        $teamStats->setTNotConcededStreak($tNotConcededStreak);
        $teamStats->setTOver25Streak($tOver25Streak);
        $teamStats->setTUnder25Streak($tUnder25Streak);
        $teamStats->setTGoalGoalStreak($tGoalGoalStreak);
        $teamStats->setTNoGoalStreak($tNoGoalStreak);

        $em->persist($teamStats);
        $em->flush();
      } catch (\Exception $e) {
        echo '<pre>';
        var_dump($e->getMessage());
        echo '</pre>';
      }

    } // foreach $teamResults

    // get all fixtures for team and league season sport
    return 'doing the calculations';
  }

  //This function calculates the most streaks in a season, not the current streaks
  public function fixStreakTeamStats($teamId, $sportId, $sportSeason, $lssId) {
    $em = $this->entityManager;

    try {
      $matchOneByOneQuery = "
      SELECT ft_team.team_id, ft_team.name_gr, ft_fixture.ft1, ft_fixture.ft2, ft_fixture.h_team, ft_fixture.a_team
      FROM ft_league_standings
        INNER JOIN ft_fixture ON ft_fixture.sport_id = ft_league_standings.sport_id AND ft_fixture.season = ft_league_standings.season AND (ft_fixture.h_team = ft_league_standings.team_id OR ft_fixture.a_team = ft_league_standings.team_id) AND ft_fixture.status IN ('F', 'FE', 'FP')
        INNER JOIN ft_team ON ft_team.team_id = ft_league_standings.team_id
      WHERE
        ft_league_standings.sport_id = $sportId
      AND ft_league_standings.season = '$sportSeason'
      AND ft_team.team_id = " . $teamId . " ORDER BY ft_fixture.match_datetime asc
      ";
      $stmt = $em->getConnection()->prepare($matchOneByOneQuery);
      $stmt->execute();
      $matchOneByOneResult = $stmt->fetchAll();

      $t_win_streak_current = 0;
      $t_win_streak_final = 0;
      $t_loss_streak_current = 0;
      $t_loss_streak_final = 0;
      $t_draw_streak_current = 0;
      $t_draw_streak_final = 0;
      foreach ($matchOneByOneResult as $match) {
        if ($match['h_team'] == $match['team_id'] && $match['ft1'] > $match['ft2']) {
          $t_win_streak_current++;
          $t_draw_streak_current = 0;
          $t_loss_streak_current = 0;
          if ($t_win_streak_final < $t_win_streak_current) {
            $t_win_streak_final =  $t_win_streak_current;
          }
        }
        elseif ($match['a_team'] == $match['team_id'] && $match['ft2'] > $match['ft1']) {
          $t_win_streak_current++;
          $t_draw_streak_current = 0;
          $t_loss_streak_current = 0;
          if ($t_win_streak_final < $t_win_streak_current) {
            $t_win_streak_final =  $t_win_streak_current;
          }
        }
        elseif (($match['a_team'] == $match['team_id'] || $match['h_team'] == $match['team_id']) && $match['ft1'] == $match['ft2']) {
          $t_draw_streak_current++;
          $t_win_streak_current = 0;
          $t_loss_streak_current = 0;
          if ($t_draw_streak_final < $t_draw_streak_current) {
            $t_draw_streak_final =  $t_draw_streak_current;
          }
        }
        elseif ($match['h_team'] == $match['team_id'] && $match['ft1'] < $match['ft2']) {
          $t_loss_streak_current++;
          $t_draw_streak_current = 0;
          $t_win_streak_current = 0;
          if ($t_loss_streak_final < $t_loss_streak_current) {
            $t_loss_streak_final =  $t_loss_streak_current;
          }
        }
        elseif ($match['a_team'] == $match['team_id'] && $match['ft2'] < $match['ft1']) {
          $t_loss_streak_current++;
          $t_draw_streak_current = 0;
          $t_win_streak_current = 0;
          if ($t_loss_streak_final < $t_loss_streak_current) {
            $t_loss_streak_final =  $t_loss_streak_current;
          }
        }
      }
      $sqlUpdateStreakStats = "
      UPDATE team_stats SET
        t_win_streak = " . $t_win_streak_final . ",
        t_lose_streak = " . $t_loss_streak_final . ",
        t_draw_streak = " . $t_draw_streak_final . "
        WHERE league_season_id = " . $lssId . " AND team_id = " . $teamId;
        $stmt = $em->getConnection()->prepare($sqlUpdateStreakStats);
        $stmt->execute();
    } catch (\Exception $e) {
      echo '<pre>';
      var_dump($e->getMessage());
      echo '</pre>';
    }

  }

}
