<?php

namespace AppBundle\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class CompetitionExtension extends AbstractExtension
{
    private $phaseMap = [
        1 => ['name' => 'Group A', 'slug' => '1'],
        2 => ['name' => 'Group B', 'slug' => '2'],
        3 => ['name' => 'Group C', 'slug' => '3'],
        4 => ['name' => 'Group D', 'slug' => '4'],
        5 => ['name' => 'Group E', 'slug' => '5'],
        6 => ['name' => 'Group F', 'slug' => '6'],
        7 => ['name' => 'Group G', 'slug' => '7'],
        8 => ['name' => 'Group H', 'slug' => '8'],
        9 => ['name' => 'Best 16 Phase', 'slug' => 'k-16'],
        10 => ['name' => 'Best 8 Phase', 'slug' => 'k-8'],
        11 => ['name' => 'Semi-Finals', 'slug' => 'semifinals'],
        12 => ['name' => 'Third Place Match', 'slug' => 'third-place-match'],
        13 => ['name' => 'Finals', 'slug' => 'final']
    ];

    public function getFunctions()
    {
        return [
            new TwigFunction('get_phase_name', [$this, 'getPhaseName']),
            new TwigFunction('get_phase_map', [$this, 'getPhaseMap']),
            new TwigFunction('get_phase_slug', [$this, 'getPhaseSlug']),
        ];
    }

    public function getPhaseName($phaseId)
    {
        if ($phaseId === null) {
            return '';
        }
        return isset($this->phaseMap[$phaseId]) ? $this->phaseMap[$phaseId]['name'] : '';
    }

    public function getPhaseSlug($phaseId)
    {
        return isset($this->phaseMap[$phaseId]) ? $this->phaseMap[$phaseId]['slug'] : 'unknown';
    }

    public function getPhaseMap()
    {
        return $this->phaseMap;
    }
}
