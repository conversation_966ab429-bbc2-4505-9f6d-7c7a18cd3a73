.is-loading:before{z-index:19;content:'';display:block;position:fixed;top:0;left:0;bottom:0;right:0;width:100%;height:100%;background-color:rgba(0, 0, 0, 0.3)}.is-loading .loader{display:block}.loader-container{position:fixed;top:50%;left:50%;transform:translate(-50%, -50%);z-index:999}.loader,.loader:after{border-radius:50%;width:10em;height:10em}.loader{display:none;font-size:10px;text-indent:-9999em;border-top:1.1em solid rgba(255, 255, 255, 0.2);border-right:1.1em solid rgba(255, 255, 255, 0.2);border-bottom:1.1em solid rgba(255, 255, 255, 0.2);border-left:1.1em solid #428bca !important;animation:load8 1.1s infinite linear;z-index:300}@-webkit-keyframes load8{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes load8{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.larger{width:500px;color:red !important}.sport-list-container{width:40%}body{background-color:#2c2c2c !important}.cv-container{margin-top:10px}#left{position:relative;float:left;left:auto;width:15%}.cv-show{display:block !important}#content{float:right;width:85%;background-color:#fff;border:8px solid #8c8c8c !important}#content-inner{border:8px solid #ccc;padding:0 10px 10px 10px;width:100%}#menu{background-color:#23527c !important;list-style:none;padding:0;margin:0}#menu li{padding:3px 15px !important;background-color:#428bca !important;border-bottom:1px solid #2f71ab !important;border-top:1px solid rgba(255, 255, 255, 0.14)}#menu li.nav-header{color:#fff;font-weight:bold;font-size:14px;margin-bottom:10px}#menu li a{display:block}.bg-blue li a{color:#fff !important}.pb-50{padding-bottom:50px}.mr-20{margin-right:20px}.mt-20{margin-top:20px}#getSports,#getMatches,.form-control,#toolbar{margin-bottom:10px !important}.form-control.multiselect-search{margin-bottom:0 !important}.multiselect-item.multiselect-group a{padding-left:0}#messages{margin-bottom:10px}#messages p:not('alert'){margin-bottom:0;padding:10px;background-color:#f9e3e3;color:#333;border-bottom:1px solid #ead5d5}#messages p:not('alert'):last-child{border-bottom:none}#fixtures-container p{margin-bottom:0;padding:10px;background-color:#e1e1fb;color:#333;border-bottom:1px solid #d5d7ea}.error-msg{background-color:#f9e3e3 !important}#toolbar #generate-season,#toolbar #generate-standings,#toolbar #generate-posts{display:none}#sport_domainSportSlugs,#sport_translations,#sport_domainSports{position:relative;padding-top:40px;margin-bottom:20px;border-top:3px solid #bfc7ce}#sport_domainSportSlugs::before,#sport_translations::before,#sport_domainSports::before{position:absolute;top:0;left:0;content:attr(name);display:block;font-size:14px;font-weight:bold;padding-top:9px}#sport_domainSportSlugs:nth-of-type(1),#sport_translations:nth-of-type(1),#sport_domainSports:nth-of-type(1){margin-top:100px}#sport_domainSportSlugs{margin-top:80px}.close{float:right;font-size:21px;font-weight:700;line-height:1;color:#000;text-shadow:0 1px 0 #fff;filter:alpha(opacity=20);opacity:0.2;position:relative;top:-28px;right:87%;transform:translateX(-50%)}.countries,.cities{width:80%}label[for="search-country-name"],label[for="search-city-name"]{font-weight:400;color:#ccc;display:block}#countries-result{position:absolute;background-color:#ccc;width:200px}#countries-result > div{cursor:pointer;padding:0 10px;margin:10px 0;color:#23527c !important}.cities{display:none}#teams-result{width:50%;float:left}#football-fields-result{width:50%;float:right}#teams-result div,#football-fields-result div{padding:5px}#teams-result div:hover,#football-fields-result div:hover{background-color:lightgreen;cursor:pointer}.selected{background-color:lightblue}#find-city{padding:0}.cv-modal{display:none;position:fixed;left:0;top:0;overflow:auto;width:100%;height:100%;padding-top:50px;background-color:#000;background-color:rgba(0, 0, 0, 0.4);}.cv-modal-content{top:50px;width:200px;height:200px;margin:auto;background-color:yellow}
span.multiselect-native-select{position:relative}span.multiselect-native-select select{border:0 !important;clip:rect(0 0 0 0) !important;height:1px !important;margin:-1px -1px -1px -3px !important;overflow:hidden !important;padding:0 !important;position:absolute !important;width:1px !important;left:50%;top:30px}.multiselect-container{position:absolute;list-style-type:none;margin:0;padding:0}.multiselect-container .input-group{margin:5px}.multiselect-container > li{padding:0}.multiselect-container > li > a.multiselect-all label{font-weight:700}.multiselect-container > li.multiselect-group label{margin:0;padding:3px 20px 3px 20px;height:100%;font-weight:700}.multiselect-container > li.multiselect-group-clickable label{cursor:pointer}.multiselect-container > li > a{padding:0}.multiselect-container > li > a > label{margin:0;height:100%;cursor:pointer;font-weight:400;padding:3px 20px 3px 40px}.multiselect-container > li > a > label.radio,.multiselect-container > li > a > label.checkbox{margin:0}.multiselect-container > li > a > label > input[type=checkbox]{margin-bottom:5px}.btn-group > .btn-group:nth-child(2) > .multiselect.btn{border-top-left-radius:4px;border-bottom-left-radius:4px}.form-inline .multiselect-container label.checkbox,.form-inline .multiselect-container label.radio{padding:3px 20px 3px 40px}.form-inline .multiselect-container li a label.checkbox input[type=checkbox],.form-inline .multiselect-container li a label.radio input[type=radio]{margin-left:-20px;margin-right:0}