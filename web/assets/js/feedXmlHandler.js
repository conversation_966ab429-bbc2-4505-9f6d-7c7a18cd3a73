var $collectionHolder;

// setup an "add a tag" link
var $addLink = $('<a href="#" class="add_feedxml_link">Add feed xml</a>');
var $newLinkLi = $('<li></li>').append($addLink);

jQuery(document).ready(function() {
      // Get the ul that holds the collection of tags
    $collectionHolder = $('ul.feedXmls');

    // add the "add a tag" anchor and li to the tags ul
    $collectionHolder.append($newLinkLi);

    // add a delete link to all of the existing tag form li elements
    // $collectionHolder.find('li').each(function() {
    //     addFeedXmlFormDeleteLink($(this));
    // });

    // count the current form inputs we have (e.g. 2), use that as the new
    // index when inserting a new item (e.g. 2)
    $collectionHolder.data('index', $collectionHolder.find(':input').length);

    $addLink.on('click', function(e) {
        // prevent the link from creating a "#" on the URL
        e.preventDefault();

        // add a new tag form (see next code block)
        addFeedXmlForm($collectionHolder, $newLinkLi);
    });
});

function addFeedXmlForm($collectionHolder, $newLinkLi) {
    // Get the data-prototype explained earlier
    var prototype = $collectionHolder.data('prototype');

    // get the new index
    var index = $collectionHolder.data('index');

    // Replace '__name__' in the prototype's HTML to
    // instead be a number based on how many items we have
    var newForm = prototype.replace(/__name__/g, index);

    // increase the index with one for the next item
    $collectionHolder.data('index', index + 1);

    // Display the form in the page in an li, before the "Add a tag" link li
    var $newFormLi = $('<li></li>').append(newForm);
    $newLinkLi.before($newFormLi);

    // add a delete link to the new form
    addFeedXmlFormDeleteLink($newFormLi);
}

function addFeedXmlFormDeleteLink($feedXmlFormLi) {
    var $removeFormA = $('<a href="#">delete xml</a>');
    $feedXmlFormLi.append($removeFormA);

    $removeFormA.on('click', function(e) {
        // prevent the link from creating a "#" on the URL
        e.preventDefault();
        $feedXmlFormLi.remove();
    });
}
