// .is-loading
// overlay
.is-loading:before {
	z-index: 19;
  content: '';
  display: block;
  position: fixed;
  top: 0;
  left: 0;
	bottom: 0;
	right: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.3);
}

.is-loading .loader { display: block; }

// .load-container
.loader-container {
  position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 999;
}

// .loader
.loader,
.loader:after {
  border-radius: 50%;
  width: 10em;
  height: 10em;
}
.loader {
	display: none;
  font-size: 10px;
  text-indent: -9999em;
  border-top: 1.1em solid rgba(255, 255, 255, 0.2);
  border-right: 1.1em solid rgba(255, 255, 255, 0.2);
  border-bottom: 1.1em solid rgba(255, 255, 255, 0.2);
  border-left: 1.1em solid $blue;
  // -webkit-transform: translateZ(0);
	// -webkit-animation: load8 1.1s infinite linear;
  // -ms-transform: translateZ(0);
  // transform: translateZ(0);

  animation: load8 1.1s infinite linear;

	z-index: 300;
}

@-webkit-keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
