@import 'modules/color';
@import 'modules/loader';

.larger { width: 500px; color: red !important; }

.sport-list-container { width: 40%; }

body {
	background-color: $greyDark;
}

.cv-container {
	margin-top: 10px;
}

#left {
	position: relative;
	float: left;
	left: auto;
	width: 15%;
}

.cv-show {
	display: block !important;
}

// content
#content {
	float: right;
	width: 85%;
	background-color: #fff;
	border: 8px solid $greyLight;
}

#content-inner {
	border: 8px solid #ccc;
	padding: 0 10px 10px 10px;
	width: 100%;
}

// menu 
#menu {
	background-color: $blueDarker;
	list-style: none;
	padding: 0;
	margin: 0;
}

#menu li {
	padding: 3px 15px !important;
	background-color: $blue;
	border-bottom: 1px solid $blueDark;
	border-top: 1px solid rgba(255,255,255,0.14);
}

#menu li.nav-header {
	color: #fff;
	font-weight: bold;
	font-size: 14px;
	margin-bottom: 10px;
}

#menu li a {
	display: block;
}

.bg-blue li a {
	color: #fff !important;
}

// forms
.pb-50 { padding-bottom: 50px; }
.mr-20 { margin-right: 20px; }
.mt-20 { margin-top: 20px; }

// Checker form
#getSports, #getMatches, .form-control, #toolbar { margin-bottom: 10px !important; }
.form-control.multiselect-search { margin-bottom: 0 !important; }
.multiselect-item.multiselect-group a { padding-left: 0 }

#messages {
	margin-bottom: 10px;	

	p:not('alert') {
    margin-bottom: 0;
    padding: 10px;
    background-color: #f9e3e3;
    color: #333;
    border-bottom: 1px solid #ead5d5;

		&:last-child { border-bottom: none; }
	}
}

#fixtures-container {
	p {
    margin-bottom: 0;
    padding: 10px;
    background-color: #e1e1fb;
    color: #333;
    border-bottom: 1px solid #d5d7ea;
	}
}

.error-msg {
	background-color: $redLight;
}

#toolbar {
	#generate-season, #generate-standings, #generate-posts {
		display: none; 
	}
}

%sportCollections {
	position: relative;
	padding-top: 40px;
	margin-bottom: 20px;
	border-top: 3px solid #bfc7ce;

	&::before {
		position: absolute;
    top: 0;
    left: 0;
    content: attr(name);
    display: block;
    font-size: 14px;
		font-weight: bold;
		padding-top: 9px;
	}

	&:nth-of-type(1) {
		margin-top: 100px;
	}
}

#sport_domainSportSlugs {
	margin-top: 80px;
}

#sport_domainSportSlugs, #sport_translations, #sport_domainSports {
	@extend %sportCollections;
}

.close {
	float: right;
	font-size: 21px;
	font-weight: 700;
	line-height: 1;
	color: #000;
	text-shadow: 0 1px 0 #fff;
	filter: alpha(opacity=20);
	opacity: .2;
	position: relative;
	top: -28px;
	right: 87%;
	transform: translateX(-50%);
}
.countries, .cities {
	width: 80%;
}

label[for="search-country-name"], label[for="search-city-name"] {
	font-weight: 400;
	color: #ccc;
	display: block;
}

#countries-result {
	position: absolute;
	background-color:#ccc;
	width: 200px;

	> div {
		cursor: pointer;
		padding: 0 10px;
		margin: 10px 0;
		color: $blueDarker;
	}
}

.cities {
	display: none;
}

#teams-result {
	width: 50%;
	float: left;
}

#football-fields-result {
	width: 50%;
	float: right;
}

#teams-result div, #football-fields-result div {
	padding: 5px;
}

#teams-result div:hover, #football-fields-result div:hover {
	background-color: lightgreen;
	cursor: pointer;
}

.selected {
	background-color: lightblue;
}

#find-city {
	padding: 0;
}

.cv-modal {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    overflow: auto;
    width: 100%;
    height: 100%;
    padding-top: 50px;
	background-color: rgb(0,0,0); /* Fallback color */
	background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
  }
  
.cv-modal-content {
	top: 50px;
	width: 200px;
	height: 200px;
	margin: auto;
	background-color: yellow;
}