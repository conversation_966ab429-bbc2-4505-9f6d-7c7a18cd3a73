fos.Router.setData({"base_url":"","routes":{"cities_get":{"tokens":[["variable","\/","[^\/]++","countryId"],["text","\/api\/v2\/cities"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["GET"],"schemes":[]},"countries_get":{"tokens":[["text","\/api\/v2\/countries"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["GET"],"schemes":[]},"sport_new":{"tokens":[["text","\/admin\/sports\/new"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["GET"],"schemes":[]},"sport_get":{"tokens":[["variable","\/","[^\/]++","id"],["text","\/admin\/sports"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["GET"],"schemes":[]},"sport_edit":{"tokens":[["text","\/edit"],["variable","\/","[^\/]++","id"],["text","\/admin\/sports"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["GET","POST"],"schemes":[]},"sport_delete":{"tokens":[["variable","\/","[^\/]++","id"],["text","\/admin\/sports"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["DELETE"],"schemes":[]},"sport_get_football":{"tokens":[["variable","\/","[^\/]++","withStandings"],["variable","\/","[^\/]++","domainId"],["text","\/admin\/sports\/v2\/all-football"]],"defaults":{"withStandings":null},"requirements":[],"hosttokens":[],"methods":["GET"],"schemes":[]},"sport_get_fixtures":{"tokens":[["variable","\/","[^\/]++","days"],["variable","\/","[^\/]++","sportIds"],["variable","\/","[^\/]++","domainId"],["text","\/admin\/sports\/v2\/sports"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["GET"],"schemes":[]},"sport_get_league_season_info":{"tokens":[["variable","\/","[^\/]++","sportId"],["text","\/admin\/sports\/v2\/sport-league-season-info"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["GET"],"schemes":[]},"sport_generatestandings":{"tokens":[["variable","\/","[^\/]++","sportId"],["text","\/admin\/sports\/generate-standings"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["GET"],"schemes":[]},"checker_generateseason":{"tokens":[["text","\/admin\/tools\/standings\/generate-season"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["GET"],"schemes":[]},"checker_generateleagues":{"tokens":[["text","\/admin\/tools\/standings\/generate-leagues"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["GET"],"schemes":[]},"checker_generateseason1":{"tokens":[["text","\/admin\/tools\/generate-season"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["GET"],"schemes":[]},"checker_generateleagues1":{"tokens":[["text","\/admin\/tools\/generate-leagues"]],"defaults":[],"requirements":[],"hosttokens":[],"methods":["GET"],"schemes":[]}},"prefix":"","host":"localhost","scheme":"http"});