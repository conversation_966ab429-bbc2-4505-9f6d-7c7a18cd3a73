(function ($) {
    var $body = $('body'),
        currentTeams = {},
        countryId = 0,
        countries = {},
        countryUrl = Routing.generate('countries_get'),
        cityName = $('#search-city-name'),
        cityNameSearch = $('#search-city-name'),
        cityNameSearchLbl = $('#lbl__search-city-name');

    $body.addClass('is-loading');

    $.ajax({
        type: 'GET',
        url: countryUrl,
        dataType: 'json',
        success: function(data) {
            countries = data
        },
        error: function(xhr, ajaxOptions, thrownError) {
            $body.removeClass('is-loading');
            alert(xhr.responseText);
        },
        complete: function(req, res) {
            $body.removeClass('is-loading');
        }
    });

    $(document).on('click', '.country-name', function(e) {
        var html = '';

        e.preventDefault();
        $body.addClass('is-loading');

        countryId = $(this).data("id");
        countryName = $(this).data("name");

        $('.selected-country-name').html(countryName);
        $('.country-name').hide();
        $('.search-results').show(5);

        var teamsUrl = Routing.generate('teams_by_country_get', {'countryId': countryId});
        
        // console.log(teamsUrl);
        // var citiesUrl = Routing.generate('cities_get', {'countryId': countryId});
        
        $.ajax({
            type: 'GET',
            url: teamsUrl,
            dataType: 'json',
            success: function(data) {
                html = '';
                currentTeams = data
                
                data.forEach(function(value) {
                    html += '<div data-team-id="' + value.teamId + '">' + value.name + '</div>';
                })
                
                $("#teams-result").append(html)
            },
            error: function(xhr, ajaxOptions, thrownError) {
                $body.removeClass('is-loading');
                currentTeams = {};
                alert(xhr.responseText);
            },
            complete: function(req, res) {
                $body.removeClass('is-loading');
            }
        });
        
        $body.addClass('is-loading');
        
        var footballFieldsUrl = Routing.generate('football_fields_by_country_get', {'countryId': countryId});

        $.ajax({
            type: 'GET',
            url: footballFieldsUrl,
            dataType: 'json',
            success: function(data) {
                html = '';

                data.forEach(function(value) {
                    html += '<div data-football-field-id=' + value.id + '>' + value.name + '</div>';
                })
        
                $("#football-fields-result").append(html)
            },
            error: function(xhr, ajaxOptions, thrownError) {
                $body.removeClass('is-loading');
                currentTeams = {};
                alert(xhr.responseText);
            },
            complete: function(req, res) {
                $body.removeClass('is-loading');
            }
        });
    });

    $(document).on('keyup', '.country-name', function(e) {
    });

    $('#countries-result.country-name').click(function() {
        countryId = $(this).attr("id");

        var countryDiv = $('#country-' + countryId),
            citiesUrl = $(this).data('city-url');

        $body.addClass('is-loading');

        $.ajax({
            type: 'GET',
            url: citiesUrl,
            dataType: 'json',
            success: function(data) {
                currentTeams = data
            },
            error: function(xhr, ajaxOptions, thrownError) {
                $body.removeClass('is-loading');
                currentTeams = {};
                alert(xhr.responseText);
            },
            complete: function(req, res) {
                $body.removeClass('is-loading');
            }
        });
    });

    $("#search-city-name").on("keyup", function() {
        var g = $(this).val().toLowerCase(),
            html = '';

        var mpeee = '';
        var citySearchResults = jQuery.grep(currentTeams, function(value) {
            $("#cities-result").html('');
            return (value['name'].toLowerCase().indexOf(g) >= 0)
        })

        citySearchResults.forEach(function(value) {
            html += '<div>' + value.name + ' <span>' + value.weatherApiId + '</span></div>';
        })

        $("#cities-result").append(html);
    });

    $("#search-country-name").on("keyup", function() {
        var g = $(this).val().toLowerCase(),
            html = '';
        
        var countrySearchResults = jQuery.grep(countries, function(value) {
            $("#countries-result").html('');
            return (value['nameGr'].toLowerCase().indexOf(g) >= 0)
        })

        countrySearchResults.forEach(function(value) {
            html += '<div id="country-' + value.id + '" class="country-name" data-id="' + value.id + '" data-name="' + value.nameGr + '">' + value.nameGr + '<div id="cities-result-' + value.id + '"></div></div>'
        })

        $("#countries-result").append(html);
    });

    var selectedTeam = null,
        selectedFootballField = null,
        selectedFootballFields = [];
  
    $(document).on('click', '#teams-result > div', function(e) {
        e.preventDefault();
            // console.log('clicked ', $(this).data("team-id"));
        
            selectedTeam 
                ? selectedTeam.removeClass('selected')
            : null;
    
        // clear selectedFootballFields;
        if (selectedFootballFields != null) {
            $.map(selectedFootballFields, function( val, i ) {
                console.log('removing val', val, 'i', i);
                var selectedFootballFieldEl =  $("div").find("[data-football-field-id='" + val + "']");
                selectedFootballFieldEl.removeClass('selected');
                selectedFootballFields = []
            });
        }
    
        // console.log('selected fields', selectedFootballFields);
        if (selectedTeam != null) {
            if (selectedTeam.is($(this))) {
                selectedTeam = null;
            
            return;
            }
        
            // console.log(selectedTeam.is($(this)), selectedTeam, $(this));
        }
        selectedTeam = $(this);
        selectedTeam.addClass('selected');

        $body.addClass('is-loading');
        
        var teamFootballFieldsUrl = Routing.generate('football_fields_by_team_get', {'teamId': selectedTeam.data("team-id")});

        $.ajax({
            type: 'GET',
            url: teamFootballFieldsUrl,
            dataType: 'json',
            success: function(data) {
                if (data) {
                    data.forEach(function(value) {
                        var selectedFootballFieldEl =  $("div").find("[data-football-field-id='" + value.id + "']");
                        selectedFootballFieldEl.click();
                    })
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                $body.removeClass('is-loading');
                currentTeams = {};
                alert(xhr.responseText);
            },
            complete: function(req, res) {
                $body.removeClass('is-loading');
            }
        });
    });
    
    $(document).on('click', '#football-fields-result > div', function(e) {
        e.preventDefault();
            
        var found = $.inArray($(this).data("football-field-id"), selectedFootballFields);
        if (found >= 0) {
            $(this).removeClass('selected')
            selectedFootballFields.splice(found, 1);
        } else {
            selectedFootballFields.push($(this).data("football-field-id"));
            $(this).addClass('selected');
        }
    });
    
    $(document).on('click', 'button', function(e) {
        e.preventDefault();
        if (selectedTeam == null || selectedFootballFields == null) return;
        
        $teamId = selectedTeam.data("team-id");
       
        // if more than one football fields selected, ask for default one
        // temporary disable this complex feature
        // if (selectedFootballFields.length > 1) {
        //     console.log('more than 2 fields selected. Select default');
        //     $('.cv-modal').show();
        // }

        // delete all football fields for team, then save new ones
        // generate new football fields for team
        
        var footballFieldIds = selectedFootballFields.join(", ");
        var deleteTeamFootballFieldsUrl = Routing.generate('team_football_fields_by_team_new_relation', {'teamId': $teamId, 'footballFieldIds': footballFieldIds});

        $.ajax({
            type: 'POST',
            url: deleteTeamFootballFieldsUrl,
            dataType: 'json',
            success: function(data) {
                console.log('delete football fields for team');
            },
            error: function(xhr, ajaxOptions, thrownError) {
                $body.removeClass('is-loading');
                currentTeams = {};
                alert(xhr.responseText);
            },
            complete: function(req, res) {
                $body.removeClass('is-loading');
            }
        });
    });

    $(document).on('click', '.close-modal', function(e) {
        console.log('closing modal');
        $('.cv-modal').hide();
    });

})(jQuery);