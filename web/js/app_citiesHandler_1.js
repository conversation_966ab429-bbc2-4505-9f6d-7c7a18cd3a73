(function ($) {
    var currentCities = {},
    countryId = 0;
    $('div.country-name').click(function() {
        countryId = $(this).data("id");

        var $body = $('body'),
            countryDiv = $('#country-' + countryId),
            citiesUrl = $(this).data('city-url');

        $body.addClass('is-loading');

        $.ajax({
            type: 'GET',
            url: citiesUrl,
            dataType: 'json',
            success: function(data) {
                var childElement = countryDiv.find('div')
                // console.log(childElement)
                // childElement.html(JSON.stringify(data))
                currentCities = data
                // console.log(countryDiv + ' div', data);
            },
            error: function(xhr, ajaxOptions, thrownError) {
                $body.removeClass('is-loading');
                currentCities = {};
                alert(xhr.responseText);
            },
            complete: function(req, res) {
                $body.removeClass('is-loading');
            }
        });
    });

    $("#search-criteria").on("keyup", function() {
        var g = $(this).val().toLowerCase();
        var mpeee = '';
        var lala = jQuery.grep(currentCities, function(value) {
            $("#cities-result-" + countryId).html('');
            return (value['name'].toLowerCase().indexOf(g) >= 0)
        })

        lala.forEach(function(value) {
            mpeee += '<div>' + value.name + '</div>';
        })

        $("#cities-result-" + countryId).append(mpeee);
        // console.log(lala)
    });
})(jQuery);