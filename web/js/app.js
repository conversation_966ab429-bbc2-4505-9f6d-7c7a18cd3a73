(function(e){e.fn.collection=function(t){var k={container:'body',allow_up:!0,up:'<a href="#">&#x25B2;</a>',before_up:function(e,t){return!0},after_up:function(e,t){return!0},allow_down:!0,down:'<a href="#">&#x25BC;</a>',before_down:function(e,t){return!0},after_down:function(e,t){return!0},allow_add:!0,add:'<a href="#">[ + ]</a>',before_add:function(e,t){return!0},after_add:function(e,t){return!0},allow_remove:!0,remove:'<a href="#">[ - ]</a>',before_remove:function(e,t){return!0},after_remove:function(e,t){return!0},allow_duplicate:!1,duplicate:'<a href="#">[ # ]</a>',before_duplicate:function(e,t){return!0},after_duplicate:function(e,t){return!0},before_init:function(e){},after_init:function(e){},min:0,max:100,add_at_the_end:!1,prefix:'collection',prototype_name:'__name__',name_prefix:null,elements_selector:'> div',children:null,init_with_n_elements:0,hide_useless_buttons:!0,drag_drop:!0,drag_drop_options:{'placeholder':'ui-state-highlight'},drag_drop_start:function(e,t){return!0},drag_drop_update:function(e,t){return!0},custom_add_location:!1};var m=function(){var e=''+Math.random()*1000*new Date().getTime();return e.replace('.','').split('').sort(function(){return 0.5-Math.random()}).join('')},x=function(t,n){if(!n.attr('id')){var i;do{i=t+'_'+m()}
while(e('#'+i).length>0);n.attr('id',i)};return n.attr('id')},v=function(t){try{var n=e(t)}catch(i){return null};if(n.length===0){return null}
else if(n.is('input[type="checkbox"]')){return(n.prop('checked')===!0?!0:!1)}
else if(n.is('input[type="radio"]')&&n.attr('name')!==undefined){return e('input[name="'+n.attr('name')+'"]:checked').val()}
else if(n.prop('value')!==undefined){return n.val()}
else{return n.html()}},g=function(t,n,i){try{var a=e(t)}catch(r){return};if(a.length===0){return}
else if(a.is('input[type="checkbox"]')){if(n){a.attr('checked',!0)}
else{a.removeAttr('checked')}}
else if(a.prop('value')!==undefined){if(i){a.attr('value',n)}
else{a.val(n)}}
else{a.html(n)}},n=function(e){return undefined===e||e},a=function(e){return(e+'').replace(/[.?*+^$[\]\\(){}|-]/g,'\\$&')},l=function(t,n,i,a){var r=function(t){var n=e(t);e.each(t.attributes,function(t,r){if(e.type(r.value)==='string'){n.attr(r.name.replace(i,a),r.value.replace(i,a))}});e.each(n.data(),function(t,r){if(e.type(r)==='string'){n.data(t.replace(i,a),r.replace(i,a))}})},o=t.eq(n);r(o[0]);o.find('*').each(function(){r(this)})},r=function(e,t,n,i,r,o){var d=new RegExp(a(n.name_prefix+'['+r+']'),'g'),f=n.name_prefix+'['+o+']';l(t,i,d,f);d=new RegExp(a(e.attr('id')+'_'+r),'g');f=e.attr('id')+'_'+o;l(t,i,d,f)},w=function(e,t,n,i,r){var o=new RegExp(a(t.name_prefix+'['+i+']'),'g'),l=t.name_prefix+'['+r+']';n=n.replace(o,l);o=new RegExp(a(e.attr('id')+'_'+i),'g');l=e.attr('id')+'_'+r;n=n.replace(o,l);return n},y=function(t){e(t).find(':input').each(function(e,t){g(t,v(t),!0)})},i=function(e,t,n,i){var a=e.data('collection-settings');r(e,t,a,n,n,'__swap__');r(e,t,a,i,i,n);r(e,t,a,n,'__swap__',i);t.eq(n).insertBefore(t.eq(i));if(i>n){t.eq(i).insertBefore(t.eq(n))}
else{t.eq(i).insertAfter(t.eq(n))};return e.find(a.elements_selector)},d=function(e,t,n,a,r){for(var o=a+1;(o<=r);o++){t=i(e,t,o,o-1)};return e.find(n.elements_selector)},f=function(e,t,n,a,r){for(var o=a-1;(o>=r);o--){t=i(e,t,o,o+1)};return e.find(n.elements_selector)},c=function(e,t,n,a){for(var r=a+1;r<t.length;r++){t=i(e,t,r-1,r)};return e.find(n.elements_selector)},s=function(e,t,n,a){for(var r=t.length-2;r>a;r--){t=i(e,t,r+1,r)};return e.find(n.elements_selector)},o=function(n,t,i){var s=n.find('.'+t.prefix+'-tmp').length===0,a=n.find(t.elements_selector);if(t.allow_add){if(s){n.append('<span class="'+t.prefix+'-tmp"></span>');if(t.add){n.append(e(t.add).addClass(t.prefix+'-action '+t.prefix+'-rescue-add').data('collection',n.attr('id')))}}};if(i){var f=e(t.container),c=n.find('.'+t.prefix+'-add, .'+t.prefix+'-rescue-add, .'+t.prefix+'-duplicate').first();while(a.length<t.init_with_n_elements){var l=a.length>0?a.last():undefined,d=a.length+1;a=p(f,c,n,t,a,l,d,!1)}};a.each(function(i){var r=e(this),o=r.find('.'+t.prefix+'-actions').addBack().filter('.'+t.prefix+'-actions');if(o.length===0){o=e('<div class="'+t.prefix+'-actions"></div>');r.append(o)};var l=[{'enabled':t.allow_remove,'selector':t.prefix+'-remove','html':t.remove,'condition':a.length>t.min},{'enabled':t.allow_up,'selector':t.prefix+'-up','html':t.up,'condition':a.index(r)!==0},{'enabled':t.allow_down,'selector':t.prefix+'-down','html':t.down,'condition':a.index(r)!==a.length-1},{'enabled':t.allow_add&&!t.add_at_the_end&&!t.custom_add_location,'selector':t.prefix+'-add','html':t.add,'condition':a.length<t.max},{'enabled':t.allow_duplicate,'selector':t.prefix+'-duplicate','html':t.duplicate,'condition':a.length<t.max}];e.each(l,function(a,l){if(l.enabled){var d=r.find('.'+l.selector);if(d.length===0&&l.html){d=e(l.html).appendTo(o).addClass(l.selector)};if(l.condition){d.removeClass(t.prefix+'-action-disabled');if(t.hide_useless_buttons){d.css('display','')}}
else{d.addClass(t.prefix+'-action-disabled');if(t.hide_useless_buttons){d.css('display','none')}};d.addClass(t.prefix+'-action').data('collection',n.attr('id')).data(t.prefix+'-element',x(n.attr('id')+'_'+i,r))}
else{r.find('.'+l.selector).css('display','none')}})});if(t.allow_add){var r=n.find('.'+t.prefix+'-rescue-add').css('display',''),o=n.find('.'+t.prefix+'-add');if(!t.add_at_the_end&&o.length>0||t.custom_add_location){r.css('display','none')};if(a.length>=t.max){n.find('.'+t.prefix+'-add, .'+t.prefix+'-rescue-add, .'+t.prefix+'-duplicate').css('display','none')}}},u=function(t,n,i){if(i.children){e.each(i.children,function(e,i){if(!i.selector){console.log('jquery.collection.js: given collection '+t.attr('id')+' has children collections, but children\'s root selector is undefined.');return!0};if(n!==null){n.find(i.selector).collection(i)}
else{t.find(i.selector).collection(i)}})}},p=function(t,i,o,r,l,p,f,h){if(l.length<r.max&&(h&&n(r.before_duplicate(o,p))||n(r.before_add(o,p)))){var j=o.data('prototype'),x=l.length,q=new RegExp(a(r.prototype_name),'g'),d=e(j.replace(q,x)),v=o.find('> .'+r.prefix+'-tmp'),k=e(d).find('[id]').first().attr('id');if(h){y(l.eq(f));var g=e('<div/>').append(l.eq(f).clone()).html(),b=w(o,r,g,f,x);d=e('<div/>').html(b).contents();v.before(d).find(r.prefix+'-actions').remove()}
else{v.before(d)};l=o.find(r.elements_selector);var m=d.find('.'+r.prefix+'-add, .'+r.prefix+'-duplicate');if(m.length>0){m.addClass(r.prefix+'-action').data('collection',o.attr('id'))};if(i.data(r.prefix+'-element')!==undefined){var f=l.index(e('#'+i.data(r.prefix+'-element')));if(f!==-1){l=s(o,l,r,f)}};u(o,d,r);if((h&&!n(r.after_duplicate(o,d)))||!n(r.after_add(o,d))){if(f!==-1){l=c(o,l,r,f+1)};d.remove()}};return l},b=function(e,t,i,a,r){if(i.length>t.min&&n(t.before_remove(e,a))){i=c(e,i,t,r);var o=i.last(),l=o.clone({withDataAndEvents:!0});o.remove();if(!n(t.after_remove(e,l))){e.find('> .'+t.prefix+'-tmp').before(l);i=e.find(t.elements_selector);i=s(e,i,t,r-1)}};return i},j=function(e,t,a,r,o){if(o!==0&&n(t.before_up(e,r))){a=i(e,a,o,o-1);if(!n(t.after_up(e,r))){a=i(e,a,o-1,o)}};return a},q=function(e,t,a,r,o){if(o!==(a.length-1)&&n(t.before_down(e,r))){a=i(e,a,o,o+1);if(!n(t.after_down(e,a))){a=i(e,a,o+1,o)}};return a},h=e(this);if(h.length===0){console.log('jquery.collection.js: given collection selector does not exist.');return!1};h.each(function(){var a=e.extend(!0,{},k,t);if(e(a.container).length===0){console.log('jquery.collection.js: a container should exist to handle events (basically, a <body> tag).');return!1};var s=e(this);if(s.data('collection')!==undefined){var r=e('#'+s.data('collection'));if(r.length===0){console.log('jquery.collection.js: given collection id does not exist.');return!0}}
else{r=s};a.before_init(r);if(r.data('prototype')===null){console.log('jquery.collection.js: given collection field has no prototype, check that your field has the prototype option set to true.');return!0};if(r.data('prototype-name')!==undefined){a.prototype_name=r.data('prototype-name')};if(r.data('allow-add')!==undefined){a.allow_add=r.data('allow-add');a.allow_duplicate=r.data('allow-add')?a.allow_duplicate:!1};if(r.data('allow-remove')!==undefined){a.allow_remove=r.data('allow-remove')};if(r.data('name-prefix')!==undefined){a.name_prefix=r.data('name-prefix')};if(!a.name_prefix){console.log('jquery.collection.js: the prefix used in descendant field names is mandatory, you can set it using 2 ways:');console.log('jquery.collection.js: - use the form theme given with this plugin source');console.log('jquery.collection.js: - set name_prefix option to  \'{{ formView.myCollectionField.vars.full_name }}\'');return!0};if(a.init_with_n_elements<a.min){a.init_with_n_elements=a.min};if(a.drag_drop&&a.allow_up&&a.allow_down){var l,c;if(typeof jQuery.ui==='undefined'||typeof jQuery.ui.sortable==='undefined'){a.drag_drop=!1}
else{r.sortable(e.extend(!0,{},{start:function(t,i){var o=r.find(a.elements_selector),d=i.item,f=e(this);if(!n(a.drag_drop_start(t,i,o,d))){f.sortable('cancel');return};i.placeholder.height(i.item.height());i.placeholder.width(i.item.width());l=o.index(d)},update:function(t,s){var u=r.find(a.elements_selector),p=s.item,h=e(this);h.sortable('cancel');if(!1===a.drag_drop_update(t,s,u,p)||!(c-l>0?n(a.before_up(r,p)):n(a.before_down(r,p)))){return};c=u.index(p);u=r.find(a.elements_selector);if(1===Math.abs(c-l)){u=i(r,u,l,c);if(!(c-l>0?n(a.after_up(r,p)):n(a.after_down(r,p)))){u=i(r,u,c,l)}}
else{if(l<c){u=d(r,u,a,l,c);if(!(c-l>0?n(a.after_up(r,p)):n(a.after_down(r,p)))){u=f(r,u,a,c,l)}}
else{u=f(r,u,a,l,c);if(!(c-l>0?n(a.after_up(r,p)):n(a.after_down(r,p)))){u=d(r,u,a,c,l)}}};o(r,a,!1)}},a.drag_drop_options))}};r.data('collection-settings',a);var h=e(a.container);h.off('click','.'+a.prefix+'-action').on('click','.'+a.prefix+'-action',function(t){var i=e(this),r=e('#'+i.data('collection')),n=r.data('collection-settings');if(undefined===n){var r=e('#'+i.data('collection')).find('.'+i.data('collection')+'-collection'),n=r.data('collection-settings');if(undefined===n){throw'Can\'t find collection: '+i.data('collection')}};var a=r.find(n.elements_selector),l=i.data(n.prefix+'-element')?e('#'+i.data(n.prefix+'-element')):undefined,d=l&&l.length?a.index(l):-1,f=i.is('.'+n.prefix+'-duplicate');if((i.is('.'+n.prefix+'-add')||i.is('.'+n.prefix+'-rescue-add')||f)&&n.allow_add){a=p(h,i,r,n,a,l,d,f)};if(i.is('.'+n.prefix+'-remove')&&n.allow_remove){a=b(r,n,a,l,d)};if(i.is('.'+n.prefix+'-up')&&n.allow_up){a=j(r,n,a,l,d)};if(i.is('.'+n.prefix+'-down')&&n.allow_down){a=q(r,n,a,l,d)};o(r,n,!1);t.preventDefault()});o(r,a,!0);u(r,null,a);a.after_init(r)});return!0}})(jQuery);
!function(o){'use strict';var n='[data-toggle="dropdown"]',t=function(t){var n=o(t).on('click.dropdown.data-api',this.toggle);o('html').on('click.dropdown.data-api',function(){n.parent().removeClass('open')})};t.prototype={constructor:t,toggle:function(r){var d=o(this),n,t,i;if(d.is('.disabled, :disabled'))return;t=d.attr('data-target');if(!t){t=d.attr('href');t=t&&t.replace(/.*(?=#[^\s]*$)/,'')};n=o(t);n.length||(n=d.parent());i=n.hasClass('open');a();if(!i)n.toggleClass('open');return!1}};function a(){o(n).parent().removeClass('open')};o.fn.dropdown=function(n){return this.each(function(){var a=o(this),r=a.data('dropdown');if(!r)a.data('dropdown',(r=new t(this)));if(typeof n=='string')r[n].call(a)})};o.fn.dropdown.Constructor=t;o(function(){o('html').on('click.dropdown.data-api',a);o('body').on('click.dropdown','.dropdown form',function(o){o.stopPropagation()}).on('click.dropdown.data-api',n,t.prototype.toggle)})}(window.jQuery);
!function(t){'use strict';if(typeof ko!=='undefined'&&ko.bindingHandlers&&!ko.bindingHandlers.multiselect){ko.bindingHandlers.multiselect={after:['options','value','selectedOptions','enable','disable'],init:function(e,i,s,c,d){var l=t(e),u=ko.toJS(i());l.multiselect(u);if(s.has('options')){var r=s.get('options');if(ko.isObservable(r)){ko.computed({read:function(){r();setTimeout(function(){var t=l.data('multiselect');if(t)t.updateOriginalOptions();l.multiselect('rebuild')},1)},disposeWhenNodeIsRemoved:e})}};if(s.has('value')){var h=s.get('value');if(ko.isObservable(h)){ko.computed({read:function(){h();setTimeout(function(){l.multiselect('refresh')},1)},disposeWhenNodeIsRemoved:e}).extend({rateLimit:100,notifyWhenChangesStop:!0})}};if(s.has('selectedOptions')){var p=s.get('selectedOptions');if(ko.isObservable(p)){ko.computed({read:function(){p();setTimeout(function(){l.multiselect('refresh')},1)},disposeWhenNodeIsRemoved:e}).extend({rateLimit:100,notifyWhenChangesStop:!0})}};var o=function(t){setTimeout(function(){if(t)l.multiselect('enable');else l.multiselect('disable')})};if(s.has('enable')){var a=s.get('enable');if(ko.isObservable(a)){ko.computed({read:function(){o(a())},disposeWhenNodeIsRemoved:e}).extend({rateLimit:100,notifyWhenChangesStop:!0})}
else{o(a)}};if(s.has('disable')){var n=s.get('disable');if(ko.isObservable(n)){ko.computed({read:function(){o(!n())},disposeWhenNodeIsRemoved:e}).extend({rateLimit:100,notifyWhenChangesStop:!0})}
else{o(!n)}};ko.utils.domNodeDisposal.addDisposeCallback(e,function(){l.multiselect('destroy')})},update:function(e,i,s,o,n){var l=t(e),a=ko.toJS(i());l.multiselect('setOptions',a);l.multiselect('rebuild')}}};function i(t,e){for(var i=0;i<t.length;++i){e(t[i],i)}};function e(e,i){this.$select=t(e);this.options=this.mergeOptions(t.extend({},i,this.$select.data()));if(this.$select.attr('data-placeholder')){this.options.nonSelectedText=this.$select.data('placeholder')};this.originalOptions=this.$select.clone()[0].options;this.query='';this.searchTimeout=null;this.lastToggledInput=null;this.options.multiple=this.$select.attr('multiple')==='multiple';this.options.onChange=t.proxy(this.options.onChange,this);this.options.onSelectAll=t.proxy(this.options.onSelectAll,this);this.options.onDeselectAll=t.proxy(this.options.onDeselectAll,this);this.options.onDropdownShow=t.proxy(this.options.onDropdownShow,this);this.options.onDropdownHide=t.proxy(this.options.onDropdownHide,this);this.options.onDropdownShown=t.proxy(this.options.onDropdownShown,this);this.options.onDropdownHidden=t.proxy(this.options.onDropdownHidden,this);this.options.onInitialized=t.proxy(this.options.onInitialized,this);this.options.onFiltering=t.proxy(this.options.onFiltering,this);this.buildContainer();this.buildButton();this.buildDropdown();this.buildSelectAll();this.buildDropdownOptions();this.buildFilter();this.updateButtonText();this.updateSelectAll(!0);if(this.options.enableClickableOptGroups&&this.options.multiple){this.updateOptGroups()};this.options.wasDisabled=this.$select.prop('disabled');if(this.options.disableIfEmpty&&t('option',this.$select).length<=0){this.disable()};this.$select.wrap('<span class="multiselect-native-select" />').after(this.$container);this.options.onInitialized(this.$select,this.$container)};e.prototype={defaults:{buttonText:function(e,i){if(this.disabledText.length>0&&(i.prop('disabled')||(e.length==0&&this.disableIfEmpty))){return this.disabledText}
else if(e.length===0){return this.nonSelectedText}
else if(this.allSelectedText&&e.length===t('option',t(i)).length&&t('option',t(i)).length!==1&&this.multiple){if(this.selectAllNumber){return this.allSelectedText+' ('+e.length+')'}
else{return this.allSelectedText}}
else if(e.length>this.numberDisplayed){return e.length+' '+this.nSelectedText}
else{var s='',l=this.delimiterText;e.each(function(){var e=(t(this).attr('label')!==undefined)?t(this).attr('label'):t(this).text();s+=e+l});return s.substr(0,s.length-this.delimiterText.length)}},buttonTitle:function(e,i){if(e.length===0){return this.nonSelectedText}
else{var s='',l=this.delimiterText;e.each(function(){var e=(t(this).attr('label')!==undefined)?t(this).attr('label'):t(this).text();s+=e+l});return s.substr(0,s.length-this.delimiterText.length)}},checkboxName:function(t){return!1},optionLabel:function(e){return t(e).attr('label')||t(e).text()},optionClass:function(e){return t(e).attr('class')||''},onChange:function(t,e){},onDropdownShow:function(t){},onDropdownHide:function(t){},onDropdownShown:function(t){},onDropdownHidden:function(t){},onSelectAll:function(){},onDeselectAll:function(){},onInitialized:function(t,e){},onFiltering:function(t){},enableHTML:!1,buttonClass:'btn btn-default',inheritClass:!1,buttonWidth:'auto',buttonContainer:'<div class="btn-group" />',dropRight:!1,dropUp:!1,selectedClass:'active',maxHeight:!1,includeSelectAllOption:!1,includeSelectAllIfMoreThan:0,selectAllText:' Select all',selectAllValue:'multiselect-all',selectAllName:!1,selectAllNumber:!0,selectAllJustVisible:!0,enableFiltering:!1,enableCaseInsensitiveFiltering:!1,enableFullValueFiltering:!1,enableClickableOptGroups:!1,enableCollapsibleOptGroups:!1,filterPlaceholder:'Search',filterBehavior:'text',includeFilterClearBtn:!0,preventInputChangeEvent:!1,nonSelectedText:'None selected',nSelectedText:'selected',allSelectedText:'All selected',numberDisplayed:3,disableIfEmpty:!1,disabledText:'',delimiterText:', ',templates:{button:'<button type="button" class="multiselect dropdown-toggle" data-toggle="dropdown"><span class="multiselect-selected-text"></span> <b class="caret"></b></button>',ul:'<ul class="multiselect-container dropdown-menu"></ul>',filter:'<li class="multiselect-item multiselect-filter"><div class="input-group"><span class="input-group-addon"><i class="glyphicon glyphicon-search"></i></span><input class="form-control multiselect-search" type="text"></div></li>',filterClearBtn:'<span class="input-group-btn"><button class="btn btn-default multiselect-clear-filter" type="button"><i class="glyphicon glyphicon-remove-circle"></i></button></span>',li:'<li><a tabindex="0"><label></label></a></li>',divider:'<li class="multiselect-item divider"></li>',liGroup:'<li class="multiselect-item multiselect-group"><label></label></li>'}},constructor:e,buildContainer:function(){this.$container=t(this.options.buttonContainer);this.$container.on('show.bs.dropdown',this.options.onDropdownShow);this.$container.on('hide.bs.dropdown',this.options.onDropdownHide);this.$container.on('shown.bs.dropdown',this.options.onDropdownShown);this.$container.on('hidden.bs.dropdown',this.options.onDropdownHidden)},buildButton:function(){this.$button=t(this.options.templates.button).addClass(this.options.buttonClass);if(this.$select.attr('class')&&this.options.inheritClass){this.$button.addClass(this.$select.attr('class'))};if(this.$select.prop('disabled')){this.disable()}
else{this.enable()};if(this.options.buttonWidth&&this.options.buttonWidth!=='auto'){this.$button.css({'width':'100%','overflow':'hidden','text-overflow':'ellipsis'});this.$container.css({'width':this.options.buttonWidth})};var e=this.$select.attr('tabindex');if(e){this.$button.attr('tabindex',e)};this.$container.prepend(this.$button)},buildDropdown:function(){this.$ul=t(this.options.templates.ul);if(this.options.dropRight){this.$ul.addClass('pull-right')};if(this.options.maxHeight){this.$ul.css({'max-height':this.options.maxHeight+'px','overflow-y':'auto','overflow-x':'hidden'})};if(this.options.dropUp){var e=Math.min(this.options.maxHeight,t('option[data-role!="divider"]',this.$select).length*26+t('option[data-role="divider"]',this.$select).length*19+(this.options.includeSelectAllOption?26:0)+(this.options.enableFiltering||this.options.enableCaseInsensitiveFiltering?44:0)),i=e+34;this.$ul.css({'max-height':e+'px','overflow-y':'auto','overflow-x':'hidden','margin-top':'-'+i+'px'})};this.$container.append(this.$ul)},buildDropdownOptions:function(){this.$select.children().each(t.proxy(function(e,i){var s=t(i),l=s.prop('tagName').toLowerCase();if(s.prop('value')===this.options.selectAllValue){return};if(l==='optgroup'){this.createOptgroup(i)}
else if(l==='option'){if(s.data('role')==='divider'){this.createDivider()}
else{this.createOptionValue(i)}}},this));t('li:not(.multiselect-group) input',this.$ul).on('change',t.proxy(function(e){var i=t(e.target),l=i.prop('checked')||!1,p=i.val()===this.options.selectAllValue;if(this.options.selectedClass){if(l){i.closest('li').addClass(this.options.selectedClass)}
else{i.closest('li').removeClass(this.options.selectedClass)}};var a=i.val(),s=this.getOptionByValue(a),o=t('option',this.$select).not(s),n=t('input',this.$container).not(i);if(p){if(l){this.selectAll(this.options.selectAllJustVisible,!0)}
else{this.deselectAll(this.options.selectAllJustVisible,!0)}}
else{if(l){s.prop('selected',!0);if(this.options.multiple){s.prop('selected',!0)}
else{if(this.options.selectedClass){t(n).closest('li').removeClass(this.options.selectedClass)};t(n).prop('checked',!1);o.prop('selected',!1);this.$button.click()};if(this.options.selectedClass==='active'){o.closest('a').css('outline','')}}
else{s.prop('selected',!1)};this.options.onChange(s,l);this.updateSelectAll();if(this.options.enableClickableOptGroups&&this.options.multiple){this.updateOptGroups()}};this.$select.change();this.updateButtonText();if(this.options.preventInputChangeEvent){return!1}},this));t('li a',this.$ul).on('mousedown',function(t){if(t.shiftKey){return!1}});t('li a',this.$ul).on('touchstart click',t.proxy(function(e){e.stopPropagation();var i=t(e.target);if(e.shiftKey&&this.options.multiple){if(i.is('label')){e.preventDefault();i=i.find('input');i.prop('checked',!i.prop('checked'))};var a=i.prop('checked')||!1;if(this.lastToggledInput!==null&&this.lastToggledInput!==i){var o=i.closest('li').index(),s=this.lastToggledInput.closest('li').index();if(o>s){var c=s;s=o;o=c};++s;var l=this.$ul.find('li').slice(o,s).find('input');l.prop('checked',a);if(this.options.selectedClass){l.closest('li').toggleClass(this.options.selectedClass,a)};for(var n=0,r=l.length;n<r;n++){var p=t(l[n]),h=this.getOptionByValue(p.val());h.prop('selected',a)}};i.trigger('change')};if(i.is('input')&&!i.closest('li').is('.multiselect-item')){this.lastToggledInput=i};i.blur()},this));this.$container.off('keydown.multiselect').on('keydown.multiselect',t.proxy(function(e){if(t('input[type="text"]',this.$container).is(':focus')){return};if(e.keyCode===9&&this.$container.hasClass('open')){this.$button.click()}
else{var s=t(this.$container).find('li:not(.divider):not(.disabled) a').filter(':visible');if(!s.length){return};var i=s.index(s.filter(':focus'));if(e.keyCode===38&&i>0){i--}
else if(e.keyCode===40&&i<s.length-1){i++}
else if(!~i){i=0};var o=s.eq(i);o.focus();if(e.keyCode===32||e.keyCode===13){var l=o.find('input');l.prop('checked',!l.prop('checked'));l.change()};e.stopPropagation();e.preventDefault()}},this));if(this.options.enableClickableOptGroups&&this.options.multiple){t('li.multiselect-group input',this.$ul).on('change',t.proxy(function(e){e.stopPropagation();var o=t(e.target),i=o.prop('checked')||!1,s=t(e.target).closest('li'),n=s.nextUntil('li.multiselect-group').not('.multiselect-filter-hidden').not('.disabled'),a=n.find('input'),p=[],l=[];if(this.options.selectedClass){if(i){s.addClass(this.options.selectedClass)}
else{s.removeClass(this.options.selectedClass)}};t.each(a,t.proxy(function(e,s){var o=t(s).val(),n=this.getOptionByValue(o);if(i){t(s).prop('checked',!0);t(s).closest('li').addClass(this.options.selectedClass);n.prop('selected',!0)}
else{t(s).prop('checked',!1);t(s).closest('li').removeClass(this.options.selectedClass);n.prop('selected',!1)};l.push(this.getOptionByValue(o))},this));this.options.onChange(l,i);this.updateButtonText();this.updateSelectAll()},this))};if(this.options.enableCollapsibleOptGroups&&this.options.multiple){t('li.multiselect-group .caret-container',this.$ul).on('click',t.proxy(function(e){var l=t(e.target).closest('li'),i=l.nextUntil('li.multiselect-group').not('.multiselect-filter-hidden'),s=!0;i.each(function(){s=s&&t(this).is(':visible')});if(s){i.hide().addClass('multiselect-collapsible-hidden')}
else{i.show().removeClass('multiselect-collapsible-hidden')}},this));t('li.multiselect-all',this.$ul).css('background','#f3f3f3').css('border-bottom','1px solid #eaeaea');t('li.multiselect-all > a > label.checkbox',this.$ul).css('padding','3px 20px 3px 35px');t('li.multiselect-group > a > input',this.$ul).css('margin','4px 0px 5px -20px')}},createOptionValue:function(e){var s=t(e);if(s.is(':selected')){s.prop('selected',!0)};var p=this.options.optionLabel(e),c=this.options.optionClass(e),h=s.val(),r=this.options.multiple?'checkbox':'radio',o=t(this.options.templates.li),l=t('label',o);l.addClass(r);o.addClass(c);if(this.options.enableHTML){l.html(' '+p)}
else{l.text(' '+p)};var i=t('<input/>').attr('type',r),a=this.options.checkboxName(s);if(a){i.attr('name',a)};l.prepend(i);var n=s.prop('selected')||!1;i.val(h);if(h===this.options.selectAllValue){o.addClass('multiselect-item multiselect-all');i.parent().parent().addClass('multiselect-all')};l.attr('title',s.attr('title'));this.$ul.append(o);if(s.is(':disabled')){i.attr('disabled','disabled').prop('disabled',!0).closest('a').attr('tabindex','-1').closest('li').addClass('disabled')};i.prop('checked',n);if(n&&this.options.selectedClass){i.closest('li').addClass(this.options.selectedClass)}},createDivider:function(e){var i=t(this.options.templates.divider);this.$ul.append(i)},createOptgroup:function(e){var s=t(e).attr('label'),l=t(e).attr('value'),i=t('<li class="multiselect-item multiselect-group"><a href="javascript:void(0);"><label><b></b></label></a></li>'),o=this.options.optionClass(e);i.addClass(o);if(this.options.enableHTML){t('label b',i).html(' '+s)}
else{t('label b',i).text(' '+s)};if(this.options.enableCollapsibleOptGroups&&this.options.multiple){t('a',i).append('<span class="caret-container"><b class="caret"></b></span>')};if(this.options.enableClickableOptGroups&&this.options.multiple){t('a label',i).prepend('<input type="checkbox" value="'+l+'"/>')};if(t(e).is(':disabled')){i.addClass('disabled')};this.$ul.append(i);t('option',e).each(t.proxy(function(t,e){this.createOptionValue(e)},this))},buildSelectAll:function(){if(typeof this.options.selectAllValue==='number'){this.options.selectAllValue=this.options.selectAllValue.toString()};var s=this.hasSelectAll();if(!s&&this.options.includeSelectAllOption&&this.options.multiple&&t('option',this.$select).length>this.options.includeSelectAllIfMoreThan){if(this.options.includeSelectAllDivider){this.$ul.prepend(t(this.options.templates.divider))};var e=t(this.options.templates.li);t('label',e).addClass('checkbox');if(this.options.enableHTML){t('label',e).html(' '+this.options.selectAllText)}
else{t('label',e).text(' '+this.options.selectAllText)};if(this.options.selectAllName){t('label',e).prepend('<input type="checkbox" name="'+this.options.selectAllName+'" />')}
else{t('label',e).prepend('<input type="checkbox" />')};var i=t('input',e);i.val(this.options.selectAllValue);e.addClass('multiselect-item multiselect-all');i.parent().parent().addClass('multiselect-all');this.$ul.prepend(e);i.prop('checked',!1)}},buildFilter:function(){if(this.options.enableFiltering||this.options.enableCaseInsensitiveFiltering){var i=Math.max(this.options.enableFiltering,this.options.enableCaseInsensitiveFiltering);if(this.$select.find('option').length>=i){this.$filter=t(this.options.templates.filter);t('input',this.$filter).attr('placeholder',this.options.filterPlaceholder);if(this.options.includeFilterClearBtn){var e=t(this.options.templates.filterClearBtn);e.on('click',t.proxy(function(e){clearTimeout(this.searchTimeout);this.$filter.find('.multiselect-search').val('');t('li',this.$ul).show().removeClass('multiselect-filter-hidden');this.updateSelectAll();if(this.options.enableClickableOptGroups&&this.options.multiple){this.updateOptGroups()}},this));this.$filter.find('.input-group').append(e)};this.$ul.prepend(this.$filter);this.$filter.val(this.query).on('click',function(t){t.stopPropagation()}).on('input keydown',t.proxy(function(e){if(e.which===13){e.preventDefault()};clearTimeout(this.searchTimeout);this.searchTimeout=this.asyncFunction(t.proxy(function(){if(this.query!==e.target.value){this.query=e.target.value;var i,s;t.each(t('li',this.$ul),t.proxy(function(e,l){var a=t('input',l).length>0?t('input',l).val():'',p=t('label',l).text(),n='';if((this.options.filterBehavior==='text')){n=p}
else if((this.options.filterBehavior==='value')){n=a}
else if(this.options.filterBehavior==='both'){n=p+'\n'+a};if(a!==this.options.selectAllValue&&p){var o=!1;if(this.options.enableCaseInsensitiveFiltering){n=n.toLowerCase();this.query=this.query.toLowerCase()};if(this.options.enableFullValueFiltering&&this.options.filterBehavior!=='both'){var h=n.trim().substring(0,this.query.length);if(this.query.indexOf(h)>-1){o=!0}}
else if(n.indexOf(this.query)>-1){o=!0};t(l).toggle(o).toggleClass('multiselect-filter-hidden',!o);if(t(l).hasClass('multiselect-group')){i=l;s=o}
else{if(o){t(i).show().removeClass('multiselect-filter-hidden')};if(!o&&s){t(l).show().removeClass('multiselect-filter-hidden')}}}},this))};this.updateSelectAll();if(this.options.enableClickableOptGroups&&this.options.multiple){this.updateOptGroups()};this.options.onFiltering(e.target)},this),300,this)},this))}}},destroy:function(){this.$container.remove();this.$select.show();this.$select.prop('disabled',this.options.wasDisabled);this.$select.data('multiselect',null)},refresh:function(){var e=t.map(t('li input',this.$ul),t);t('option',this.$select).each(t.proxy(function(i,s){var o=t(s),a=o.val(),l;for(var n=e.length;0<n--;){if(a!==(l=e[n]).val())continue;if(o.is(':selected')){l.prop('checked',!0);if(this.options.selectedClass){l.closest('li').addClass(this.options.selectedClass)}}
else{l.prop('checked',!1);if(this.options.selectedClass){l.closest('li').removeClass(this.options.selectedClass)}};if(o.is(':disabled')){l.attr('disabled','disabled').prop('disabled',!0).closest('li').addClass('disabled')}
else{l.prop('disabled',!1).closest('li').removeClass('disabled')};break}},this));this.updateButtonText();this.updateSelectAll();if(this.options.enableClickableOptGroups&&this.options.multiple){this.updateOptGroups()}},select:function(e,i){if(!t.isArray(e)){e=[e]};for(var n=0;n<e.length;n++){var s=e[n];if(s===null||s===undefined){continue};var l=this.getOptionByValue(s),o=this.getInputByValue(s);if(l===undefined||o===undefined){continue};if(!this.options.multiple){this.deselectAll(!1)};if(this.options.selectedClass){o.closest('li').addClass(this.options.selectedClass)};o.prop('checked',!0);l.prop('selected',!0);if(i){this.options.onChange(l,!0)}};this.updateButtonText();this.updateSelectAll();if(this.options.enableClickableOptGroups&&this.options.multiple){this.updateOptGroups()}},clearSelection:function(){this.deselectAll(!1);this.updateButtonText();this.updateSelectAll();if(this.options.enableClickableOptGroups&&this.options.multiple){this.updateOptGroups()}},deselect:function(e,i){if(!t.isArray(e)){e=[e]};for(var n=0;n<e.length;n++){var s=e[n];if(s===null||s===undefined){continue};var l=this.getOptionByValue(s),o=this.getInputByValue(s);if(l===undefined||o===undefined){continue};if(this.options.selectedClass){o.closest('li').removeClass(this.options.selectedClass)};o.prop('checked',!1);l.prop('selected',!1);if(i){this.options.onChange(l,!1)}};this.updateButtonText();this.updateSelectAll();if(this.options.enableClickableOptGroups&&this.options.multiple){this.updateOptGroups()}},selectAll:function(e,i){var e=typeof e==='undefined'?!0:e,s=t('li:not(.divider):not(.disabled):not(.multiselect-group)',this.$ul),l=t('li:not(.divider):not(.disabled):not(.multiselect-group):not(.multiselect-filter-hidden):not(.multiselect-collapisble-hidden)',this.$ul).filter(':visible');if(e){t('input:enabled',l).prop('checked',!0);l.addClass(this.options.selectedClass);t('input:enabled',l).each(t.proxy(function(e,i){var s=t(i).val(),l=this.getOptionByValue(s);t(l).prop('selected',!0)},this))}
else{t('input:enabled',s).prop('checked',!0);s.addClass(this.options.selectedClass);t('input:enabled',s).each(t.proxy(function(e,i){var s=t(i).val(),l=this.getOptionByValue(s);t(l).prop('selected',!0)},this))};t('li input[value="'+this.options.selectAllValue+'"]',this.$ul).prop('checked',!0);if(this.options.enableClickableOptGroups&&this.options.multiple){this.updateOptGroups()};if(i){this.options.onSelectAll()}},deselectAll:function(e,i){var e=typeof e==='undefined'?!0:e,s=t('li:not(.divider):not(.disabled):not(.multiselect-group)',this.$ul),l=t('li:not(.divider):not(.disabled):not(.multiselect-group):not(.multiselect-filter-hidden):not(.multiselect-collapisble-hidden)',this.$ul).filter(':visible');if(e){t('input[type="checkbox"]:enabled',l).prop('checked',!1);l.removeClass(this.options.selectedClass);t('input[type="checkbox"]:enabled',l).each(t.proxy(function(e,i){var s=t(i).val(),l=this.getOptionByValue(s);t(l).prop('selected',!1)},this))}
else{t('input[type="checkbox"]:enabled',s).prop('checked',!1);s.removeClass(this.options.selectedClass);t('input[type="checkbox"]:enabled',s).each(t.proxy(function(e,i){var s=t(i).val(),l=this.getOptionByValue(s);t(l).prop('selected',!1)},this))};t('li input[value="'+this.options.selectAllValue+'"]',this.$ul).prop('checked',!1);if(this.options.enableClickableOptGroups&&this.options.multiple){this.updateOptGroups()};if(i){this.options.onDeselectAll()}},rebuild:function(){this.$ul.html('');this.options.multiple=this.$select.attr('multiple')==='multiple';this.buildSelectAll();this.buildDropdownOptions();this.buildFilter();this.updateButtonText();this.updateSelectAll(!0);if(this.options.enableClickableOptGroups&&this.options.multiple){this.updateOptGroups()};if(this.options.disableIfEmpty&&t('option',this.$select).length<=0){this.disable()}
else{this.enable()};if(this.options.dropRight){this.$ul.addClass('pull-right')}},dataprovider:function(e){var s=0,l=this.$select.empty();t.each(e,function(e,o){var n;if(t.isArray(o.children)){s++;n=t('<optgroup/>').attr({label:o.label||'Group '+s,disabled:!!o.disabled});i(o.children,function(e){var s={value:e.value,label:e.label||e.value,title:e.title,selected:!!e.selected,disabled:!!e.disabled};for(var i in e.attributes){s['data-'+i]=e.attributes[i]};n.append(t('<option/>').attr(s))})}
else{var p={'value':o.value,'label':o.label||o.value,'title':o.title,'class':o.class,'selected':!!o.selected,'disabled':!!o.disabled};for(var a in o.attributes){p['data-'+a]=o.attributes[a]};n=t('<option/>').attr(p);n.text(o.label||o.value)};l.append(n)});this.rebuild()},enable:function(){this.$select.prop('disabled',!1);this.$button.prop('disabled',!1).removeClass('disabled')},disable:function(){this.$select.prop('disabled',!0);this.$button.prop('disabled',!0).addClass('disabled')},setOptions:function(t){this.options=this.mergeOptions(t)},mergeOptions:function(e){return t.extend(!0,{},this.defaults,this.options,e)},hasSelectAll:function(){return t('li.multiselect-all',this.$ul).length>0},updateOptGroups:function(){var i=t('li.multiselect-group',this.$ul),e=this.options.selectedClass;i.each(function(){var s=t(this).nextUntil('li.multiselect-group').not('.multiselect-filter-hidden').not('.disabled'),i=!0;s.each(function(){var e=t('input',this);if(!e.prop('checked')){i=!1}});if(e){if(i){t(this).addClass(e)}
else{t(this).removeClass(e)}};t('input',this).prop('checked',i)})},updateSelectAll:function(e){if(this.hasSelectAll()){var s=t('li:not(.multiselect-item):not(.multiselect-filter-hidden):not(.multiselect-group):not(.disabled) input:enabled',this.$ul),n=s.length,l=s.filter(':checked').length,i=t('li.multiselect-all',this.$ul),o=i.find('input');if(l>0&&l===n){o.prop('checked',!0);i.addClass(this.options.selectedClass)}
else{o.prop('checked',!1);i.removeClass(this.options.selectedClass)}}},updateButtonText:function(){var e=this.getSelected();if(this.options.enableHTML){t('.multiselect .multiselect-selected-text',this.$container).html(this.options.buttonText(e,this.$select))}
else{t('.multiselect .multiselect-selected-text',this.$container).text(this.options.buttonText(e,this.$select))};t('.multiselect',this.$container).attr('title',this.options.buttonTitle(e,this.$select))},getSelected:function(){return t('option',this.$select).filter(':selected')},getOptionByValue:function(e){var l=t('option',this.$select),o=e.toString();for(var i=0;i<l.length;i=i+1){var s=l[i];if(s.value===o){return t(s)}}},getInputByValue:function(e){var l=t('li input:not(.multiselect-search)',this.$ul),o=e.toString();for(var i=0;i<l.length;i=i+1){var s=l[i];if(s.value===o){return t(s)}}},updateOriginalOptions:function(){this.originalOptions=this.$select.clone()[0].options},asyncFunction:function(t,e,i){var s=Array.prototype.slice.call(arguments,3);return setTimeout(function(){t.apply(i||window,s)},e)},setAllSelectedText:function(t){this.options.allSelectedText=t;this.updateButtonText()}};t.fn.multiselect=function(i,s,l){return this.each(function(){var o=t(this).data('multiselect'),n=typeof i==='object'&&i;if(!o){o=new e(this,n);t(this).data('multiselect',o)};if(typeof i==='string'){o[i](s,l);if(i==='destroy'){t(this).data('multiselect',!1)}}})};t.fn.multiselect.Constructor=e;t(function(){t('select[data-role=multiselect]').multiselect()})}(window.jQuery);