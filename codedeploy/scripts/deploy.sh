#!/bin/bash

set -e

cd /var/www/html/feed-server/

# Get the environment
env=`echo -n $DEPLOYMENT_GROUP_NAME | awk '{print tolower($0)}'`

# Run for environment
case "$env" in
    "staging")
    # Database migrations
    su --command 'php bin/console doctrine:schema:update --force --env=staging' --shell /bin/bash www-data
    # Clear cache
    su --command 'php bin/console cache:clear --env=staging' --shell /bin/bash www-data
    # Set most used odd types
    su --command 'php bin/console wbs:oddtypes:setmostused --env=staging' --shell /bin/bash www-data
    ;;
    "production")
    # Database migrations
    su --command 'php bin/console doctrine:schema:update --force --env=prod' --shell /bin/bash www-data
    # Clear cache
    su --command 'php bin/console cache:clear --env=prod' --shell /bin/bash www-data
    # Set most used odd types
    su --command 'php bin/console wbs:oddtypes:setmostused --env=prod' --shell /bin/bash www-data
    ;;
esac
