version: 0.0
os: linux
files:
  - source: app
    destination: /var/www/html/feed-server/app
  - source: bin
    destination: /var/www/html/feed-server/bin
  - source: src
    destination: /var/www/html/feed-server/src
  - source: var
    destination: /var/www/html/feed-server/var
  - source: web
    destination: /var/www/html/feed-server/web
  - source: vendor
    destination: /var/www/html/feed-server/vendor/
  - source: composer.json
    destination: /var/www/html/feed-server/
  - source: composer.lock
    destination: /var/www/html/feed-server/
file_exists_behavior: OVERWRITE

permissions:
  - object: /var/www/html/feed-server/
    owner: www-data
    group: www-data
    mode : 664
    type:
      - file
  - object: /var/www/html/feed-server/
    owner: www-data
    group: www-data
    mode : 775
    type:
      - directory
   
hooks:
  AfterInstall:
    - location: scripts/deploy.sh
      timeout: 300
      runas: root
