Vagrant.configure("2") do |config|
  config.vm.box = "bento/ubuntu-18.04"
  config.vm.box_version = "201803.24.0"

  config.vbguest.auto_update = false

  config.vm.network "forwarded_port", guest: 80, host: 8021, host_ip: "127.0.0.1"
  config.vm.network "forwarded_port", guest: 3306, host: 3321, host_ip: "127.0.0.1"
  config.vm.network "forwarded_port", guest: 22, host: 2221, host_ip: "127.0.0.1"

  config.vm.network "private_network", ip: "*************"

  config.vm.network :public_network

  config.vm.synced_folder "./", "/var/www/feedo", :nfs => { :mount_options => ["dmode=777","fmode=666"] }

  config.vm.provider "virtualbox" do |vb|
	  vb.customize ["modifyvm", :id, "--cableconnected1", "on"]
	  vb.memory = 4096
    vb.cpus = 2
    vb.name = "Feed Server Updated"
  end

  config.vm.hostname = "feed.gr.test"
  config.hostsupdater.remove_on_suspend = true

  # Enable provisioning with a shell script. Additional provisioners such as
  # Puppet, Chef, Ansible, Salt, and Docker are also available. Please see the
  # documentation for more information about their specific syntax and use.
  # config.vm.provision "shell", inline: <<-SHELL
  #   apt-get update
  #   apt-get install -y apache2
  # SHELL
  config.vm.provision "shell", path: "./deploy/bootstrap.sh"
end
