#!/bin/bash
#==============================================================================
#   USAGE:  global-setup.sh
#
#   DESCRIPTION:  Sets up the codeship environment environment
#
#   VERSION: 1.0
#   COMPANY: Better Collective A/S
#==============================================================================
set -ex
#------------------------------------------------------------------------------
# Setup variables
#------------------------------------------------------------------------------
SCRIPTS_DIR_PATH="$(dirname $(realpath $0))"
source "$SCRIPTS_DIR_PATH/environment.sh"
#------------------------------------------------------------------------------
# Setup PHP and Build app
#------------------------------------------------------------------------------
phpenv local $PHP_VERSION
composer self-update 2.2.9
cd app/config && cp codeship.parameters.yml parameters.yml
cd ../..
composer install --prefer-dist --no-interaction
