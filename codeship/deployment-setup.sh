#!/bin/bash
#==============================================================================
#   USAGE:  deployment-setup.sh
#
#   DESCRIPTION:  Sets codeship environment before deployment
#
#   VERSION: 1.0
#   COMPANY: Better Collective A/S
#==============================================================================
set -ex
#------------------------------------------------------------------------------
# Setup variables
#------------------------------------------------------------------------------
SCRIPTS_DIR_PATH="$(dirname $(realpath $0))"
source "$SCRIPTS_DIR_PATH/environment.sh"

#------------------------------------------------------------------------------
# Setup CodeDeploy
#------------------------------------------------------------------------------
cp codedeploy/appspec/appspec.yml ./
cp -r codedeploy/scripts/ ./
#------------------------------------------------------------------------------
# Setup PHP and Build app
#------------------------------------------------------------------------------
phpenv local $PHP_VERSION
composer self-update 2.2.9
cd app/config && mv codeship.parameters.yml parameters.yml
cd ../..
composer install --prefer-dist --no-interaction
rm app/config/parameters.yml