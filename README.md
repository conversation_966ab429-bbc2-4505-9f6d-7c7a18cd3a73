feedo
=====

A Symfony project created on October 7, 2016, 1:26 pm.

Installation
============

```
<NAME_EMAIL>:wbsike/feed-symfony.git

cp Vagrantfile.dist Vagrantfile
cp app/config/parameters.yml.dist app/config/parameters.yml
```

Then we need to bring the Database from the hetzner backup box. Open up a bash script and execute the following inside the project root path.

To avoid being asked for a password, you need to add your public rfc key to the storage box authorized_keys

```
SFTP="<EMAIL>"
SFTP_MYSQL_DB_PATH="/feedo/databases"
_E_SFTP_MYSQL_DB_PATH='/feedo/databases/'
sqlzipfilewithpath=$(echo ls -1tr /feedo/databases | sftp -q <EMAIL> | grep -v 'mongodb' | tail -1)

sqlzipfilewithpath=${sqlzipfilewithpath/$_E_SFTP_MYSQL_DB_PATH/''}

sftp -q $SFTP <<!
cd $SFTP_MYSQL_DB_PATH
get $sqlzipfilewithpath
!

gzip -d $sqlzipfilewithpath

sqlfile=${sqlzipfilewithpath/'.gz'/''}
echo $sqlfile

sed -i "s/^SQLFINALNAME.*/SQLFINALNAME='$sqlfile'/g" ./deploy/bootstrap.sh
```

We're ready to run
```
vagrant up
```
and wait until everything is installed and setup

NOTE: It is advised to truncate the **domain** table, add new LOCAL domains (websites) and update the 

**domain_sport**

**domain_sport_slug**

**domain_sport_terms**

**domain_ft_fixture**

**domain_ft_fixture_relationship**

tables accordingly.

NOTE: The cronjobs that need to be setup locally depends on your needs, so grab them from the hetzner server. Also you need to vagrant ssh and sudo service apache2 restart -> TO DO FIX